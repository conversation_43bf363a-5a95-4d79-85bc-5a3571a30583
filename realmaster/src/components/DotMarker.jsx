import React,{Component} from 'react';
import PropTypes from 'prop-types';

import {
  StyleSheet,
  View,
  Text,
  Image,
  // ImageBackground,
} from 'react-native';

const propTypes = {
  amount: PropTypes.string,
  fontSize: PropTypes.number,
};

const defaultProps = {
  fontSize: 11,
};

class DotMarker extends Component {
  constructor(props){
    super(props);
    this.state = {
      initialRender:true,
      src:'',
    }
  }
  forceUpdate2=()=>{
    // console.log('xxxxx',this.state)
    this.setState({initialRender: false})
  }
  componentDidMount(){
    // const { fontSize, amount, isTopUp, offMarket, suffix, adrltr} = this.props;
  }
  render() {
    const { fontSize, amount, isTopUp, offMarket, isSold, suffix,
      adrltr, verified, showing, showingPassed, isFromSoldLayer,
      group, clickable
    } = this.props;
    var isSelected = this.props.isSelected;
    // if(verified){
    //   console.log(verified,this.props)
    // }

    let background = '#7269cb'; //purple
    let border = '#776DD0';
    if(isSelected){
      background = '#e03131'
      border = '#776DD0'
    }
    let width = 10, height = 10;
    if(!clickable){
      width = 5;
      height = 5;
    }
    return (
      <View style={styles.container}>
        <View style={[styles.circle,{backgroundColor:background,borderColor:border,width,height}]}>
        </View>
      </View>
    );
  }
}

DotMarker.propTypes = propTypes;
DotMarker.defaultProps = defaultProps;

const styles = StyleSheet.create({
  container: {
    width:20,
    height:20,
    flexDirection: 'row',
    alignSelf: 'flex-start',
    alignContent:'center',
    justifyContent:'center',
    // backgroundColor:'red',
  },
  circle:{
    alignSelf:'center',
    // width:10,
    // height:10,
    borderRadius:10,
    borderWidth:1,
  }
});

export default DotMarker;
