import storageIns from './storage'
import DEFAULT_TRANS_STRINGS from '../assets/languages.json'
import { mainRequest } from './request';
import Constants from '../config/constants';

const SUPPORTED_LANGS = ['zh','zh-cn','en','kr']
var DEFAULT_TRANS_STRINGS_NEW_FORMAT = {}
for(let lang of SUPPORTED_LANGS){
  DEFAULT_TRANS_STRINGS_NEW_FORMAT[lang] = {}
}
for (let key in DEFAULT_TRANS_STRINGS){
  if(DEFAULT_TRANS_STRINGS.hasOwnProperty(key)){
    let v = DEFAULT_TRANS_STRINGS[key]
    for(let lang of SUPPORTED_LANGS){
      if(v[lang]){
        DEFAULT_TRANS_STRINGS_NEW_FORMAT[lang][key.toLowerCase()] = {k:key,v:v[lang]}
      }
    }
    // delete DEFAULT_TRANS_STRINGS[key]
  }
}
// console.log('------->',DEFAULT_TRANS_STRINGS_NEW_FORMAT['zh']['save search & watched area'])
const APP_LANG = Constants.AppLang;
const TRANS_CACHE_KEY = 'l10n.localCache'

const DEFAULT_URL = '/1.5/translate'
var gAppLang = 'en';
var debounce = null;
// interface gTransList = {
//   [key: string]: {
//     k:string, //= trans string
//     c:string  //= ctx of k
//   }
// }
var gTransList = {}
var gAbList = {}

storageIns.getItem(APP_LANG).then(
  (lang) => {
    gAppLang = lang;
  }
);
async function getTranslateFromServerAsync({tList,abList={}}){
  var data = {
    keys:tList,
    abkeys:abList,
    varsLang:gAppLang,
    tlmt:DEFAULT_TRANS_STRINGS_NEW_FORMAT.tlmt,
    clmt:DEFAULT_TRANS_STRINGS_NEW_FORMAT.clmt
  };
  try {
    let ret = await mainRequest({
      url: DEFAULT_URL,
      method: 'post',
      data
  });

    if(!ret.ok){
      return
    }
    if (ret.clearCache) {
      storageIns.removeItem(TRANS_CACHE_KEY)
      initL10n({noQueryServer:1});
    }
    if (!gAppLang) {
      gAppLang = ret.locale;
    }
    applyNewTranslateToDefault(ret)
  } catch (e){
    console.log('Error when fetch l10n:',e);
  }
  // RMPost(DEFAULT_URL,{body:data},(err,ret)=>{
  // });
}
function isTranslated(v){
  return !/^(\w|\&|\.|\s|\?|\/|\,|\-|\!)+$/.test(v)
}
// @params
// type SUPPORTED_LANGS = string[]
// interface transCache {
//   tlmt:number //=translated date in ms 19218391823918
//   clmt:Date   //=clear date 2020-102-33-10 T
//   keys:string: {
//     [key: string]: string;
//   }
// }
function applyNewTranslateToDefault(transCache={}){
  if(SUPPORTED_LANGS.indexOf(transCache.locale)<0){
    return
  }
  let lang = transCache.locale
  let langTransObj = DEFAULT_TRANS_STRINGS_NEW_FORMAT[lang] || {}
  if(transCache.keys){
    for (let key in transCache.keys){
      let v = transCache.keys[key]
      if(langTransObj[key]){
        if(v !== langTransObj[key].v){
          // console.log('v is diff!',v,'<-->',langTransObj[key].v)
          if(isTranslated(v)){
            // console.log('v is translated! key=',key+'\t',' v=', v,'<-->',langTransObj[key].v)
            langTransObj[key].v = v
          } else {
            // ignore not translated case
            // console.log('v is not translated! v=',v,'<-->',langTransObj[key].v)
          }
        }
      }
    }
  }
  if(transCache.abkeys){
  }
  DEFAULT_TRANS_STRINGS_NEW_FORMAT.tlmt = transCache.tlmt
  DEFAULT_TRANS_STRINGS_NEW_FORMAT.clmt = transCache.clmt
  storageIns.setCacheItem(TRANS_CACHE_KEY,JSON.stringify(DEFAULT_TRANS_STRINGS_NEW_FORMAT),true);
}
function applyStorageTranslateToDefault(transCache={}){
  // return
  for (let key in transCache){
    let v = transCache[key]
    // continue
    if (SUPPORTED_LANGS.indexOf(key) > -1){
      // console.log('key==',key)
      // console.log(DEFAULT_TRANS_STRINGS_NEW_FORMAT[key],'<default---new>',v)
      DEFAULT_TRANS_STRINGS_NEW_FORMAT[key] = Object.assign({},DEFAULT_TRANS_STRINGS_NEW_FORMAT[key],v)
    } else {
      DEFAULT_TRANS_STRINGS_NEW_FORMAT[key] = v
    }
  }
}
async function readTransFromStorageAsync(){
  try {
    let transCache = await storageIns.getItem(TRANS_CACHE_KEY);
    if(transCache){
      // DEFAULT_TRANS_STRINGS_NEW_FORMAT =
      transCache = JSON.parse(transCache)
      // console.log('transCache=',transCache)
      applyStorageTranslateToDefault(transCache)
    }
  } catch (e){
    console.error('Error trans cache',e)
  }
}
function generateIndexKeyAndCtx(key=''){
  let ret = {}
  if(/:/.test(key)){
    let arr = key.split(':')
    let keyStr = arr[0]
    let ctx = arr[1]
    ret = {k:keyStr,c:ctx,index:keyStr.toLowerCase()}
  } else {
    ret = {k:key,index:key.toLowerCase()}
  }
  return ret
}
function getAllKeys(){
  let ret = {}
  for (let key in DEFAULT_TRANS_STRINGS){
    let {index,k,c} = generateIndexKeyAndCtx(key)
    // console.log(key,index,k,c)
    ret[index] = {k}
    if(c){
      ret[index].c = c
    }
  }
  return ret;
}
async function initL10n(opt={}){
  let tList = getAllKeys()
  // console.log('++++',tList)
  await readTransFromStorageAsync()
  if(opt.noQueryServer){
    return
  }
  let ret = await getTranslateFromServerAsync({tList})
}
async function syncGetAppLang(){
  let lang = await storageIns.syncGetItem(APP_LANG);
  if(!gAppLang){
    gAppLang = lang
  }
  return lang;
}
function setAppLang(lang){
  if (['en','zh','zh-cn','kr'].indexOf(lang) < 0) {
    console.error('unsupported lang:' + lang);
    return;
  }
  gAppLang = lang;
  // console.log('++++',gAppLang);
  // NOTE: setAppLang will clear cache and reload if different lang
  // RMStorage.syncRemoveItem(TRANS_CACHE_KEY)
  storageIns.setCacheItem(APP_LANG,lang,true);
}
function getAppLang(){
  return gAppLang
}

function findTrans(str,ctx,lang){
  let ret = {str,ok:0}
  if(!str || typeof(str) !== 'string'){
    return ret;
  }
  str = str.toLowerCase()
  if (!lang){
    lang = gAppLang||'en';
  }
  let langTransObj = DEFAULT_TRANS_STRINGS_NEW_FORMAT[lang] || {}
  if (ctx){
    ctx = ctx.toLowerCase()
    let fstr = str + ':' + ctx;
    if (langTransObj[fstr] && langTransObj[fstr]['v']){
      ret.str = langTransObj[fstr]['v'];
      ret.ok = 1
      return ret;
    }
  }
  // if(/Save\sSearch\s\&/i.test(str)){
  //   console.log('str=',str,' trans=',langTransObj[str])
  // }
  if (langTransObj[str] && langTransObj[str]['v']){
    ret.str = langTransObj[str]['v'];
    ret.ok = 1
    return ret;
  }
  if(lang != 'en'){
    // console.log(langTransObj)
    // console.log('no tranlstation for: '+str+' ->   ',lang)
  }
  return ret
}
function getStrIndex(key, ctx) {
  if (typeof key == 'string') {
    return key.toLowerCase() + (ctx ? ':' + ctx.toLowerCase() : '')
  } else {
    console.error(key, ' is not string');
    return null;
  }
}
// @params
// interface arguments {
//  str:string //= string to be translated
//  ctx:string //= context of string
//  lang?:string //= lang we desired, could be null
//  _isAb?:boolean //= is abbr(abbrevation), in abbr.csv table
// }
// @return
// type ret = string
function l10n(str,ctx,lang,_isAb){
  // return ''
  let ret = findTrans(str,ctx,lang)
  // append to toBe translated list
  if(!ret.ok){
    let index = getStrIndex(str,ctx)
    if (_isAb) {
      gAbList[index] = {k:str, c:ctx};
    } else {
      gTransList[index] = {k:str, c:ctx};
    }
    // clearTimeout(debounce);
    // debounce = setTimeout(function () {
    //   debounce = null;
    //   getTranslateFromServerAsync({tList:gTransList,abList:gAbList})
    // }, 1200);
  }
  return ret.str;
}

export {
  getAppLang,
  setAppLang,
  l10n,
  syncGetAppLang,
  initL10n
}
