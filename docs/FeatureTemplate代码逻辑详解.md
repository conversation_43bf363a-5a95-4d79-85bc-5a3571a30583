# FeatureTemplate.jsx 代码逻辑详解

## 1. 文件概述与继承关系

该文件定义了一个继承自 `MapFeature` 的类（应命名为 `MapSchool`，当前类名为 `REPLAE_THIS`，建议修正）。该类用于地图上学校相关的功能，包括学校数据的获取、渲染和交互。

---

## 2. 主要成员与状态说明

- **构造函数**：初始化父类，设置初始 state（如 `tracksViewChanges`）。
- **state 变量**：
  - `tracksViewChanges`：控制地图标记是否需要重新渲染。
  - `schs`：存储学校数据。
  - `schoolID`、`sch`：当前选中的学校 ID 和详细信息。

---

## 3. 主要方法功能

### 3.1 gotSchools(schnbnds)
- **功能**：处理接口返回的学校数据。
- **逻辑**：保存边界数据到 `this.bnds`，过滤学校名称后存入 state 的 `schs`。

### 3.2 clearSchools()
- **功能**：清空学校相关数据。
- **逻辑**：清空边界和学校数据。

### 3.3 regionChanged(event, map)
- **功能**：地图区域变化时触发，决定是否请求学校数据。
- **逻辑**：
  - 如果缩放级别小于 15，清空学校数据。
  - 否则，调用 `/1.5/mapSearch/findSchools` 接口，获取当前区域的学校数据，成功后调用 `gotSchools` 处理。

### 3.4 renderOnMap()
- **功能**：在地图上渲染学校标记。
- **逻辑**：
  - 如果 feature 未开启或不显示标记，直接返回。
  - 遍历 `this.state.schs`，为每个学校生成一个 `Marker`，点击时触发 `schClicked`。
  - 每个 `Marker` 内部用 `IconText` 组件显示学校名和图标。

### 3.5 renderModal()
- **功能**：渲染底部弹窗，显示学校详细信息。
- **逻辑**：
  - 如果没有选中学校，返回 null。
  - 有选中学校时，弹出 `BottomPane`，内容区域可自定义（目前为空）。

### 3.6 fnClose()
- **功能**：关闭弹窗的回调。
- **逻辑**：清空当前选中学校的 state，并关闭追踪。

### 3.7 schClicked(schId)
- **功能**：点击学校标记时的回调。
- **逻辑**：根据 `schId` 找到对应学校，设置为当前选中，并关闭追踪。

---

## 4. 组件渲染流程与执行顺序

1. **地图初始化**：组件初始化，继承自 `MapFeature`，设置初始状态。
2. **地图区域变化**：用户拖动或缩放地图时，`regionChanged` 被调用：
   - 若缩放级别过小，清空学校数据。
   - 否则，发起接口请求，获取当前区域的学校数据，数据返回后调用 `gotSchools` 存入 state。
3. **渲染学校标记**：`renderOnMap` 根据 state 中的学校数据，渲染所有学校的地图标记。
4. **点击学校标记**：用户点击某个学校标记时，触发 `schClicked`，将该学校设为当前选中，弹出底部详情弹窗。
5. **弹窗关闭**：用户关闭弹窗时，调用 `fnClose`，清空当前选中学校。

---

## 5. 其他说明

- **样式**：只定义了一个简单的 `container` 样式，实际标记和弹窗样式需结合其他组件实现。
- **依赖组件**：
  - `MapFeature`：地图功能基类。
  - `Marker`、`IconText`、`BottomPane`：地图标记、图标文本、底部弹窗等 UI 组件。
- **接口**：
  - `/1.5/mapSearch/findSchools`：用于获取当前地图区域内的学校数据。

---

## 6. 总结

该文件的核心逻辑是：
**监听地图区域变化 → 请求学校数据 → 渲染学校标记 → 支持点击标记弹窗查看详情 → 支持关闭弹窗。**

如需进一步了解某个函数或具体交互细节，可继续查阅或提问。 