import groovy.json.JsonSlurper
import groovy.json.JsonOutput

apply plugin: "com.facebook.react"
apply plugin: "com.google.gms.google-services"
apply plugin: "com.android.application"
apply plugin: "org.jetbrains.kotlin.android"

// 添加 react-native-config
project.ext.envConfigFiles = [
    debug: ".env.development",
    release: ".env.production"
]
apply from: project(':react-native-config').projectDir.getPath() + "/dotenv.gradle"

/**
 * This is the configuration block to customize your React Native Android app.
 * By default you don't need to apply any configuration, just uncomment the lines you need.
 */
react {
    /* Folders */
    //   The root of your project, i.e. where "package.json" lives. Default is '..'
    // root = file("../")
    //   The folder where the react-native NPM package is. Default is ../node_modules/react-native
    // reactNativeDir = file("../node_modules/react-native")
    //   The folder where the react-native Codegen package is. Default is ../node_modules/@react-native/codegen
    // codegenDir = file("../node_modules/@react-native/codegen")
    //   The cli.js file which is the React Native CLI entrypoint. Default is ../node_modules/react-native/cli.js
    // cliFile = file("../node_modules/react-native/cli.js")

    /* Variants */
    //   The list of variants to that are debuggable. For those we're going to
    //   skip the bundling of the JS bundle and the assets. By default is just 'debug'.
    //   If you add flavors like lite, prod, etc. you'll have to list your debuggableVariants.
    // debuggableVariants = ["liteDebug", "prodDebug"]

    /* Bundling */
    //   A list containing the node command and its flags. Default is just 'node'.
    // nodeExecutableAndArgs = ["node"]
    //
    //   The command to run when bundling. By default is 'bundle'
    // bundleCommand = "ram-bundle"
    //
    //   The path to the CLI configuration file. Default is empty.
    // bundleConfig = file(../rn-cli.config.js)
    //
    //   The name of the generated asset file containing your JS bundle
    // bundleAssetName = "MyApplication.android.bundle"
    //
    //   The entry file for bundle generation. Default is 'index.android.js' or 'index.js'
    // entryFile = file("../js/MyApplication.android.js")
    //
    //   A list of extra flags to pass to the 'bundle' commands.
    //   See https://github.com/react-native-community/cli/blob/main/docs/commands.md#bundle
    // extraPackagerArgs = []

    /* Hermes Commands */
    //   The hermes compiler command to run. By default it is 'hermesc'
    // hermesCommand = "$rootDir/my-custom-hermesc/bin/hermesc"
    //
    //   The list of flags to pass to the Hermes compiler. By default is "-O", "-output-source-map"
    // hermesFlags = ["-O", "-output-source-map"]
}

/**
 * Set this to true to Run Proguard on Release builds to minify the Java bytecode.
 */
def enableProguardInReleaseBuilds = false

/**
 * The preferred build flavor of JavaScriptCore (JSC)
 *
 * For example, to use the international variant, you can use:
 * `def jscFlavor = 'org.webkit:android-jsc-intl:+'`
 *
 * The international variant includes ICU i18n library and necessary data
 * allowing to use e.g. `Date.toLocaleString` and `String.localeCompare` that
 * give correct results when using with locales other than en-US. Note that
 * this variant is about 6MiB larger per architecture than default.
 */
def jscFlavor = 'org.webkit:android-jsc:+'

// 读取 appversion.json
def getVersionInfo() {
    def appVersionFile = file("${rootDir}/../appversion.json")
    def versionInfo = new JsonSlurper().parseText(appVersionFile.text)
    return versionInfo
}

// 更新 appversion.json
def updateVersionCode(int newVersionCode) {
    def appVersionFile = file("${rootDir}/../appversion.json")
    def versionInfo = new JsonSlurper().parseText(appVersionFile.text)
    versionInfo.versionCode = newVersionCode
    
    try {
        // 格式化 JSON 并写入文件
        def prettyJson = JsonOutput.prettyPrint(JsonOutput.toJson(versionInfo))
        appVersionFile.write(prettyJson)
        return true
    } catch (Exception e) {
        println "Error updating version code: ${e.message}"
        return false
    }
}


android {
    ndkVersion rootProject.ext.ndkVersion

    compileSdkVersion rootProject.ext.compileSdkVersion

    namespace "com.realmaster"
    defaultConfig {
        applicationId "com.realmaster"
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        def versionInfo = getVersionInfo()
        versionCode versionInfo.versionCode
        versionName versionInfo.version

       // 添加打印信息，仅在debug环境下
        if (project.env.get("ENV") == "development") {
            println "=== Build Version Info ==="
            println "Version Name: ${versionInfo.version}"
            println "Version Code: ${versionInfo.versionCode}"
            // 不打印实际的API密钥，只显示配置状态
            println "MAPS API KEY 已配置"
            println "Current env variables: ${project.env.get("ENV")}"
            println "======================="
        }

        ndk {
            abiFilters "arm64-v8a", "x86_64"
        }
        
        // 使用resValue设置环境变量
        resValue "string", "MAPS_API_KEY", project.env.get("ANDROID_MAPS_API_KEY") ?: ""
        // missingDimensionStrategy 'react-native-camera', 'general'
    }
    signingConfigs {
        debug {
            storeFile file('debug.keystore')
            storePassword 'android'
            keyAlias 'androiddebugkey'
            keyPassword 'android'
        }
       production {
            keyAlias 'AndroidKey'
            keyPassword 'Qf123Se456'
            // Caution! In production, you need to generate your own keystore file.
            storeFile file('../android.jks')
            storePassword 'Qf123Se456'
            // v1SigningEnabled true
            v2SigningEnabled true
        }
    }
    buildTypes {
        // TODO: need better way of doing this, 因为lib需要的签名都是production的
        debug {
            signingConfig signingConfigs.debug
            shrinkResources false
            minifyEnabled false
        }
        release {
            signingConfig signingConfigs.production
            shrinkResources true // 移除未使用的资源文件
            minifyEnabled true // 开启混淆
            proguardFiles(getDefaultProguardFile('proguard-android.txt'), "proguard-rules.pro")
            // 优化dex文件
            crunchPngs true // PNG图片优化
            // // 移除未使用的备用资源
            // resConfigs "zh", "en"
        }
    }
}

// 在 release 任务之前执行版本更新
gradle.taskGraph.whenReady { taskGraph ->
    if (taskGraph.hasTask(':app:assembleRelease')) {
        def versionInfo = getVersionInfo()
        def newVersionCode = versionInfo.versionCode
        newVersionCode++
        updateVersionCode(newVersionCode)
    }
}


dependencies {
    // The version of react-native is set by the React Native Gradle Plugin
    implementation("com.facebook.react:react-android")

    implementation project(':react-native-notifications')
    // implementation 'com.google.firebase:firebase-core:16.0.0'
    api 'com.google.firebase:firebase-core:21.1.1'

    implementation platform('com.google.firebase:firebase-bom:32.0.0')
    //https://stackoverflow.com/questions/64822045/could-not-find-com-google-firebasefirebase-analytics-ktx-required-by-project
    // implementation("com.google.firebase:firebase-analytics-ktx")
    implementation 'com.google.firebase:firebase-analytics-ktx:22.1.0'

    implementation 'com.google.android.gms:play-services-location:21.0.1'


    debugImplementation("com.facebook.flipper:flipper:${FLIPPER_VERSION}")
    debugImplementation("com.facebook.flipper:flipper-network-plugin:${FLIPPER_VERSION}") {
        exclude group:'com.squareup.okhttp3', module:'okhttp'
    }

    debugImplementation("com.facebook.flipper:flipper-fresco-plugin:${FLIPPER_VERSION}")
    if (hermesEnabled.toBoolean()) {
        implementation("com.facebook.react:hermes-android")
    } else {
        implementation jscFlavor
    }

    // implementation project(':react-native-vector-icons')
}

project.ext.vectoricons = [
	iconFontNames: [
        'icomoon.ttf',
//     'AntDesign.ttf',
//     'Entypo.ttf',
//     'EvilIcons.ttf',
//     'Feather.ttf',
        'FontAwesome.ttf',
//     'FontAwesome5_Brands.ttf',
//     'FontAwesome5_Regular.ttf',
//     'FontAwesome5_Solid.ttf',
//     'FontAwesome6_Brands.ttf',
//     'FontAwesome6_Regular.ttf',
//     'FontAwesome6_Solid.ttf',
//     'Fontisto.ttf',
//     'Foundation.ttf',
//     'Ionicons.ttf',
//     'MaterialCommunityIcons.ttf',
//     'MaterialIcons.ttf',
//     'Octicons.ttf',
//     'SimpleLineIcons.ttf',
//     'Zocial.ttf',
  ] // Add fonts needed
  // iconFontsDir: "../../node_modules/react-native-vector-icons/Fonts"
]
// project.ext.reanimated = [
//     buildFromSource: true
// ]
apply from: file("../../node_modules/@react-native-community/cli-platform-android/native_modules.gradle"); applyNativeModulesAppBuildGradle(project)
apply from: file("../../node_modules/react-native-vector-icons/fonts.gradle");
// apply from: "../../node_modules/react-native-vector-icons/fonts.gradle"

