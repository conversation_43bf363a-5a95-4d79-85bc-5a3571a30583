import { useEffect } from 'react';
import { Keyboard } from 'react-native';

export const useKeyboardListener = (postMessage) => {
  useEffect(() => {
    const notifyWebview = (e, opt = {}) => {
      if (typeof e === 'object' && e.endCoordinates) {
        const msg = { 
          tp: 'native.keyboardshow', 
          p: { 
            isShow: !opt.hide, 
            keyboardHeight: e.endCoordinates.height 
          } 
        };
        postMessage(msg);
      }
    };

    // 注册键盘监听器
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      (e) => e && notifyWebview(e)
    );
    
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      (e) => e && notifyWebview(e, { hide: true })
    );

    // 清理函数
    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);
};