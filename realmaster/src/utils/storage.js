import AsyncStorage from '@react-native-async-storage/async-storage';
import Constants from '../config/constants';

/**
 * 使用单例模式替代共享变量的方式：实例共享、延迟实例化、封装、集成、测试
 */
class Storage {
  constructor() {
    if (!Storage.instance) {
      this.ramCache = {}; //保留在内存中的变量
      this.storeCache = {}; //仅作同步到AsyncStore用
      this.cacheTimer = null;
      Storage.instance = this;
    }

    return Storage.instance;
  }

  async getItem(key) {
    const value = await AsyncStorage.getItem(key);
    return value;
  }
  async getItemAll() {
    const keys = await AsyncStorage.getAllKeys();
    const data = await AsyncStorage.multiGet(keys);
  }

  async setItem(key, value) {
    return await AsyncStorage.setItem(key, value);
  }
  async removeItem(key) {
    delete this.ramCache[key];
    return await AsyncStorage.removeItem(key);
  }

  getItemObj(key, opt = {}, cb) {
  global.rmLog(`storage.js:37~~~getItemObj`,key, opt);
    if (!cb) {
      cb = opt;
      opt = {};
    }
    if (opt.readCache || key === Constants.ListPageData || key === Constants.MapsearchFilter) {
      let tmp = this.getCacheItem(key);
      if (tmp) {
        return cb(null, tmp);
      }
    }
    AsyncStorage.getItem(key, (err, val) => {
      if (val) {
        try {
          val = JSON.parse(val);
        } catch (e) {
          return cb(e);
        }
      }
      return cb(err, val);
    });
  }
  /**
   * 设置缓存项。
   *
   * @param {string} key 缓存项的键。
   * @param {string | Object} value 缓存项的值。
   * @param {boolean} isStore 是否将缓存项存储到持久化缓存中，默认为false。
   * @param {Function} callback 回调函数，在缓存项设置完成后调用，默认为空函数。
   */
  setCacheItem(key, value, isStore = false, callback = () => {}) {
    const isValidValue = (key, value) => {
      return true
      // return key === Constants.CacheKey.LastLoadDomain ? Constants.ValidAppDomains.includes(value) : true;
    };
    if (!isValidValue(key, value)) return;
    this.ramCache[key] = value;
    
    //是否同步到AsyncStorage，默认同步
    if (!isStore) return callback();

    //ramCache设置完直接返回callback,减少等待时间
    // callback();

    this.storeCache[key] = value;
    clearTimeout(this.cacheTimer);
    this.cacheTimer = setTimeout(() => {
      //如果3秒内有多次同步cache操作，取消上次操作，最后一次性将this.ramCache同步到AsyncStorage
      const keys = Object.keys(this.storeCache);
      const promises = keys.map(key => this.setItem(key, this.storeCache[key]));
      Promise.all(promises)
        .then(() => {
          //同步完成，清空storeCache,调用回调函数
          this.storeCache = {};
          //将这个callback提前
          callback();
          global.rmLog(`storage.js:93~~~setCacheItem`, 'setCacheItem同步完成', Date.now());
        })
        .catch(err => {
          console.log(err);
        });
    }, 1000);
  }

  getCacheItem(key) {
    return this.ramCache[key];
  }

  async syncStorage2Cache() {
    let keys = [];
    let values;
    try {
      keys = await AsyncStorage.getAllKeys();

      values = await AsyncStorage.multiGet(keys);
    } catch (e) {
      console.error(e);
      return Promise.reject(err);
    }
    for (let i = 0; i < values.length; i++) {
      const pair = values[i];
      this.ramCache[pair[0]] = pair[1];
    }
    return Promise.resolve(this.ramCache);
  }
}

const instance = new Storage();

export default instance;
