import React, {Component} from 'react';
import {Platform, StyleSheet, Text, View,TouchableOpacity,FlatList,Dimensions} from 'react-native';
const {width} = Dimensions.get('window');
import Prop from './Prop'
import Proj from './Proj'
import Sch from './Sch'
import HistRight from './HistRight'
import RmIcon from '../RmIcon'

export default HistList = (props) => {
  const {histList,lang,closePopup,schoolAction,referer} = props;
  // TODO: VirtualizedLists should never be nested inside plain ScrollViews with the same orientation - use another VirtualizedList-backed container instead
  // https://github.com/GeekyAnts/NativeBase/issues/2947
  // NOTE: use View instead of FlatList?
  // console.log(histList)
  function renderHistItem(item){
    item.val.histTs = item.ts;
    if (item.icon == 'prop') {
      return <Prop referer={referer} prop={item.val} lang={lang} closePopup={closePopup} page={'hist'} search={props.search} removeHist={props.removeHist} />
    }
    if (item.icon == 'proj') {
      return <Proj proj={item.val} lang={lang} closePopup={closePopup} page={'hist'} search={props.search} removeHist={props.removeHist}/>
    }
    if (item.icon == 'sch') {
      return <Sch sch={item.val} lang={lang} schoolAction={schoolAction} closePopup={closePopup} page={'hist'} search={props.search} removeHist={props.removeHist}/>
    }
    if (item.icon == 'command') {
      const cmd = item.val;
      return (
        <TouchableOpacity onPress={()=>props.search('command',item.val)}>
          <View style={styles.hist}>
            <View style={styles.histLeft}>
              <RmIcon style={styles.histLeftIcon} name={'rmhistory'} size={20} color={'rgb(130,130,130)'} />
              <Text style={styles.histTxt}>{cmd.cmd}</Text>
            </View>
            <HistRight item={cmd} removeHist={props.removeHist} />
          </View>
        </TouchableOpacity>
      )
    }
    if (item.icon == 'addr') {
      const addr = item.val;
      return (
        <TouchableOpacity onPress={()=>props.search('addr',item.val)}>
          <View style={styles.hist}>
            <View style={styles.histLeft}>
              <RmIcon style={styles.histLeftIcon} name={'rmhistory'} size={20} color={'rgb(130,130,130)'} />
              <Text style={styles.histTxt}>{addr.addr}{addr.city?', '+addr.city:''}</Text>
            </View>
            <HistRight item={addr} removeHist={props.removeHist} />
          </View>
        </TouchableOpacity>
      )
    }
  }
  return(
    <View style={styles.hists}>
      <FlatList
        keyboardShouldPersistTaps='always'
        data={histList}
        keyExtractor={(item) => {
          if(item.k){
            return item.k.toString()
          } else if (item.ts){
            return item.ts.toString()
          } else {
            return Date.now()+''
          }
        }}
        renderItem={({item}) => {
          return renderHistItem(item)
        }}
      />
    </View>
  );
}
const styles = StyleSheet.create({
  hists:{
    backgroundColor:'#fff',
    marginTop:10,
  },
  hist:{
    flexDirection:'row',
    justifyContent:'space-between',
    alignItems:'center',
    padding:10,
    borderColor:'rgb(240,240,240)',
    borderBottomWidth:0.5,
    borderStyle:'solid'
  },
  histLeft:{
    flexDirection:'row',
    alignItems:'center'
  },
  histLeftIcon:{
    marginRight:10
  },
  histTxt:{
    width:width-100
  }
})