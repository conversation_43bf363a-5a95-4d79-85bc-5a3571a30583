import React from 'react';
import PropTypes from 'prop-types';

import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  Image,
  Animated,
} from 'react-native';
import { eventEmitter } from '../utils/common';


// NOTE: it's propTypes.func not propTypes.function
// inconsistent with array/number/object/string/symbol,   bool/func
// https://reactjs.org/docs/typechecking-with-proptypes.html
const propTypes = {
  mapTypeCallback: PropTypes.func,
};

const defaultProps = {
};

class FlashMessage extends React.Component {
  constructor(props){
    super(props);
    this.props = props;
    this.state = {
      showFlashMessage:false,
      string:props.msg || '',
      fadeAnim: new Animated.Value(1),
    };
    this.hideMsgTimeout = null;
    // this.flashMessage = this.flashMessage.bind(this);
    eventEmitter.on("flash-message",this.flashMessage)
  }
  componentDidMount() {
  }
  componentWillUnmount() {
    clearTimeout(this.hideMsgTimeout);
    eventEmitter.removeListener("flash-message", this.flashMessage);
  }
  flashMessage = (opt) => {
    clearTimeout(this.hideMsgTimeout)
    this.hideMsgTimeout = setTimeout(()=>{
      this.setState({
        showFlashMessage:false,
      })
    },2000)
    // Starts the animation
    this.setState({
      fadeAnim: new Animated.Value(1),
      string:opt.msg,
      showFlashMessage:true
    },()=>{
      Animated.timing(                  // Animate over time
        this.state.fadeAnim,            // The animated value to drive
        {
          toValue: 0,                   // Animate to opacity: 1 (opaque)
          duration: 3000,              // Make it take a while
          useNativeDriver: true,
        }
      ).start();
    })
  }
  render() {
    if(!this.state.showFlashMessage){
      return null;
    }
    var { height } = this.props;
    if (!height) {
      height = 375
    }
    var string = this.state.string;
    let { fadeAnim } = this.state;
    return (
      <Animated.View                 // Special animatable View
        style={[
          styles.overLayWrapper,
          {top:height/2-40},
          {opacity: fadeAnim},         // Bind opacity to animated value
        ]}
      >
        <Text style={{fontSize:16, color:'white'}}>{string}</Text>
        {/* {this.props.children} */}
      </Animated.View>
    );
  }
}

FlashMessage.propTypes = propTypes;
FlashMessage.defaultProps = defaultProps;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'column',
    alignSelf: 'flex-start',
    // width:20,
    // height:20
  },
  overLayWrapper:{
    position: 'absolute',
    width:200,
    height:80,
    paddingLeft:10,
    paddingRight:10,
    // right:8,
    zIndex:15,
    flexDirection:'row',
    alignSelf:'center',
    alignItems:'center',
    justifyContent:'center',
    backgroundColor:'rgba(0,0,0,0.8)',
    borderRadius:12,
  },
});

export default FlashMessage;
