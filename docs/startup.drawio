<mxfile host="65bd71144e">
    <diagram name="启动时序" id="startup-sequence">
        <mxGraphModel dx="1049" dy="894" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="user" value="" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;" parent="1" vertex="1">
                    <mxGeometry x="100" y="80" width="30" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="app" value="App" style="shape=umlLifeline;perimeter=lifelinePerimeter;whiteSpace=wrap;html=1;container=1;collapsible=0;recursiveResize=0;outlineConnect=0;" parent="1" vertex="1">
                    <mxGeometry x="200" y="100" width="100" height="1000" as="geometry"/>
                </mxCell>
                <mxCell id="rmweb" value="Rmweb" style="shape=umlLifeline;perimeter=lifelinePerimeter;whiteSpace=wrap;html=1;container=1;collapsible=0;recursiveResize=0;outlineConnect=0;" parent="1" vertex="1">
                    <mxGeometry x="400" y="100" width="100" height="1000" as="geometry"/>
                </mxCell>
                <mxCell id="splash" value="SplashScreen" style="shape=umlLifeline;perimeter=lifelinePerimeter;whiteSpace=wrap;html=1;container=1;collapsible=0;recursiveResize=0;outlineConnect=0;" parent="1" vertex="1">
                    <mxGeometry x="600" y="100" width="100" height="1000" as="geometry"/>
                </mxCell>
                <mxCell id="network_error" value="RMNetwork" style="shape=umlLifeline;perimeter=lifelinePerimeter;whiteSpace=wrap;html=1;container=1;collapsible=0;recursiveResize=0;outlineConnect=0;" parent="1" vertex="1">
                    <mxGeometry x="800" y="100" width="100" height="1000" as="geometry"/>
                </mxCell>
                <mxCell id="start_alt" value="alt" style="shape=umlFrame;whiteSpace=wrap;html=1;width=40;height=20;" parent="1" vertex="1">
                    <mxGeometry x="80" y="140" width="890" height="940" as="geometry"/>
                </mxCell>
                <mxCell id="normal_start" value="[正常点击启动]" style="text;html=1" parent="1" vertex="1">
                    <mxGeometry x="130" y="140" width="120" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="start_app" value="" style="html=1;verticalAlign=bottom;endArrow=block;curved=0;rounded=0;" parent="1" edge="1">
                    <mxGeometry x="0.4126" y="10" relative="1" as="geometry">
                        <mxPoint x="115.5" y="190" as="sourcePoint"/>
                        <mxPoint x="250" y="190" as="targetPoint"/>
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="init_frame" value="par" style="shape=umlFrame;whiteSpace=wrap;html=1;width=60;height=30;" vertex="1" parent="1">
                    <mxGeometry x="180" y="200" width="700" height="280" as="geometry"/>
                </mxCell>
                <mxCell id="read_cache" value="syncStorage2Cache" style="html=1;align=left;spacingLeft=2;endArrow=block;rounded=0;" edge="1" parent="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="250" y="220" as="sourcePoint"/>
                        <mxPoint x="250" y="240" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="orientation_lock" value="lockToPortrait" style="html=1;align=left;spacingLeft=2;endArrow=block;rounded=0;" edge="1" parent="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="250" y="260" as="sourcePoint"/>
                        <mxPoint x="250" y="280" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="init_l10n" value="initL10n" style="html=1;align=left;spacingLeft=2;endArrow=block;rounded=0;" edge="1" parent="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="250" y="300" as="sourcePoint"/>
                        <mxPoint x="250" y="320" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="read_geo" value="readGeoPosition" style="html=1;align=left;spacingLeft=2;endArrow=block;rounded=0;" edge="1" parent="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="250" y="340" as="sourcePoint"/>
                        <mxPoint x="250" y="360" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="init_notification" value="registerPnToken" style="html=1;align=left;spacingLeft=2;endArrow=block;rounded=0;" edge="1" parent="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="250" y="380" as="sourcePoint"/>
                        <mxPoint x="250" y="400" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="check_network" value="checkNetwork" style="html=1;align=left;spacingLeft=2;endArrow=block;rounded=0;" edge="1" parent="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="250" y="420" as="sourcePoint"/>
                        <mxPoint x="250" y="440" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="network_alt" value="alt" style="shape=umlFrame;whiteSpace=wrap;html=1;width=40;height=20;" vertex="1" parent="1">
                    <mxGeometry x="180" y="500" width="700" height="160" as="geometry"/>
                </mxCell>
                <mxCell id="show_splash" value="[网络正常]" style="text;html=1" vertex="1" parent="1">
                    <mxGeometry x="230" y="500" width="100" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="nav_splash" value="navigation to splash" style="html=1;verticalAlign=bottom;endArrow=block;curved=0;rounded=0;" edge="1" parent="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="250" y="540" as="sourcePoint"/>
                        <mxPoint x="649.5" y="540" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="separator" value="" style="endArrow=none;dashed=1;html=1;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="180" y="580" as="sourcePoint"/>
                        <mxPoint x="880" y="580" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="show_error" value="[网络异常]" style="text;html=1" vertex="1" parent="1">
                    <mxGeometry x="230" y="580" width="100" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="nav_error" value="navigation to network" style="html=1;verticalAlign=bottom;endArrow=block;curved=0;rounded=0;" edge="1" parent="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="250" y="620" as="sourcePoint"/>
                        <mxPoint x="849.5" y="620" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="start_separator1" value="" style="endArrow=none;dashed=1;html=1;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="80" y="680" as="sourcePoint"/>
                        <mxPoint x="970" y="680" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="notification_start" value="[点击通知启动]" style="text;html=1" vertex="1" parent="1">
                    <mxGeometry x="130" y="680" width="120" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="notification_trigger" value="点击通知" style="html=1;verticalAlign=bottom;endArrow=block;curved=0;rounded=0;" edge="1" parent="1">
                    <mxGeometry x="0.5539" y="10" relative="1" as="geometry">
                        <mxPoint x="115.5" y="720" as="sourcePoint"/>
                        <mxPoint x="250" y="720" as="targetPoint"/>
                        <mxPoint x="1" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="handle_notification" value="处理通知参数" style="html=1;align=left;spacingLeft=2;endArrow=block;rounded=0;" edge="1" parent="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="250" y="740" as="sourcePoint"/>
                        <mxPoint x="250" y="760" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="nav_to_rmweb" value="直接跳转到指定页面" style="html=1;verticalAlign=bottom;endArrow=block;curved=0;rounded=0;" edge="1" parent="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="250" y="780" as="sourcePoint"/>
                        <mxPoint x="449.5" y="780" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="start_separator2" value="" style="endArrow=none;dashed=1;html=1;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="80" y="820" as="sourcePoint"/>
                        <mxPoint x="970" y="820" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="deeplink_start" value="[DeepLink链接启动]" style="text;html=1" vertex="1" parent="1">
                    <mxGeometry x="130" y="820" width="140" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="deeplink_trigger" value="" style="html=1;verticalAlign=bottom;endArrow=block;curved=0;rounded=0;" edge="1" parent="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="115" y="860" as="sourcePoint"/>
                        <mxPoint x="249.5" y="860" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="handle_deeplink" value="处理链接参数" style="html=1;align=left;spacingLeft=2;endArrow=block;rounded=0;" edge="1" parent="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="250" y="880" as="sourcePoint"/>
                        <mxPoint x="250" y="900" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="deeplink_alt" value="alt" style="shape=umlFrame;whiteSpace=wrap;html=1;width=40;height=20;" vertex="1" parent="1">
                    <mxGeometry x="180" y="920" width="700" height="140" as="geometry"/>
                </mxCell>
                <mxCell id="popup_branch" value="[isPopupPushMessageUrl]" style="text;html=1" vertex="1" parent="1">
                    <mxGeometry x="230" y="920" width="160" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="popup_action" value="popup" style="html=1;verticalAlign=bottom;endArrow=block;curved=0;rounded=0;" edge="1" parent="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="250" y="960" as="sourcePoint"/>
                        <mxPoint x="449.5" y="960" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="separator3" value="" style="endArrow=none;dashed=1;html=1;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="180" y="1000" as="sourcePoint"/>
                        <mxPoint x="880" y="1000" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="normal_branch" value="[普通链接]" style="text;html=1" vertex="1" parent="1">
                    <mxGeometry x="230" y="1000" width="100" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="route_pop_action" value="routePopToTop" style="html=1;verticalAlign=bottom;endArrow=block;curved=0;rounded=0;" edge="1" parent="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="250" y="1040" as="sourcePoint"/>
                        <mxPoint x="449.5" y="1040" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>