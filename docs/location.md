# 位置服务工具模块

该模块提供了地理位置相关的功能,包括获取、监听、缓存位置信息等。

## 安装依赖

```bash
npm install react-native-geolocation-service
```
## 主要功能

### 获取当前位置

```javascript
import { getGeoPosition } from '../utils/location';
// 基本用法
getGeoPosition((position) => {
console.log('当前位置:', position);
});
// 带选项的用法
getGeoPosition({ noAlert: true }, (position) => {
console.log('静默获取位置:', position);
});

```
### 监听位置变化

```javascript
import { watchGeoPosition, clearWatch } from '../utils/location';
// 开始监听
watchGeoPosition();
// 停止监听
clearWatch();
```
### 读取缓存位置

```javascript
import { readGeoPosition, getCacheGeoPos } from '../utils/location';
// 异步读取
readGeoPosition((position) => {
console.log('存储的位置:', position);
});
// 同步获取
const cachedPosition = getCacheGeoPos();
```

## API 说明

### getGeoPosition(options?, callback)

获取当前地理位置。

**参数:**
- `options` (Object, 可选): 配置选项
  - `noAlert` (boolean): 是否禁用错误提示
- `callback` (Function): 接收位置数据的回调函数

**返回值:** void

**回调参数:**
```javascript
{
  coords: {
    latitude: number;    // 纬度
    longitude: number;   // 经度
    altitude?: number;   // 海拔(可选)
    accuracy?: number;   // 精确度(可选)
  }
}
```

### watchGeoPosition()

开始监听用户位置变化。

**返回值:** void

### clearWatch()

停止监听用户位置变化。

**返回值:** void

### readGeoPosition(callback)

从存储中读取地理位置数据。

**参数:**
- `callback` (Function): 接收位置数据的回调函数

**返回值:** void

### getCacheGeoPos()

获取缓存的地理位置数据，并验证是否在加拿大境内。

**返回值:** 位置对象或 null

## 错误处理

模块会处理以下几种错误情况：

1. 没有可用的位置服务提供商
   - 错误消息: "没有可用的位置服务提供商"
   
2. 用户拒绝访问位置服务
   - 错误消息: "地理位置错误：用户拒绝访问位置服务"
   - 错误代码: 1

3. 用户已禁用位置服务
   - 错误消息: "地理位置错误：用户已禁用位置服务"
   - 错误代码: 2

4. 位置服务请求超时
   - 错误消息: "地理位置错误：位置服务请求超时"
   - 错误代码: 3

错误信息会通过 alert 显示（除非设置 noAlert 选项），并记录到日志。

## 注意事项

1. 位置信息会被缓存到本地存储。
2. 默认只接受加拿大境内的位置信息。
3. 使用高精度定位模式。
4. 位置请求超时时间为 20 秒。
5. 缓存有效期为 10 秒。

## 示例代码

```javascript
// 完整使用示例
import { 
  getGeoPosition,
  watchGeoPosition,
  clearWatch,
  readGeoPosition,
  getCacheGeoPos
} from '../utils/location';

// 获取位置
getGeoPosition((position) => {
  if (position.err) {
    console.error('获取位置失败:', position.err);
    return;
  }
  console.log('当前位置:', position);
});

// 监听位置变化
watchGeoPosition();

// 读取缓存
readGeoPosition((cached) => {
  console.log('缓存的位置:', cached);
});

// 获取有效的缓存位置
const validPosition = getCacheGeoPos();

// 停止监听
clearWatch();
```

## 依赖模块

- `react-native-geolocation-service`: 提供地理位置服务
- `../config/constants`: 配置常量
- `./i18n`: 国际化支持
- `./logger`: 日志记录
- `./storage`: 本地存储
- `./business`: 业务逻辑（位置验证）
