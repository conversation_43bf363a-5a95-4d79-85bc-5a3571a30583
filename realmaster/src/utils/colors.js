const light = {
  main_red: '#E03131', //主色，用于title/icon/标签
  secondary_red: '#F4604C', //辅助色，用于地图位置 rgb(255,90,90) (250,62,62)
  secondary_yellow: '#FFC001', //辅助色，用于统计图
  secondary_pink: '#FFEEE7', //辅助色，用于标签背景
  main_green: '#5DB75D', //主色，按钮/标签/统计（逐渐弃用）
  main_green_2: '#40BC93', //主色，用于按钮/按钮边框/按钮文字/TAB
  secondary_green: '#56B6AD', //辅助色，用于统计
  secondary_green_2: '#BFF8E5', //辅助色，用于边框/背景
  secondary_green_3: '#E9FAE3', //辅助色，用于标签背景
  secondary_green_4: '#F1FFFC', //辅助色，用于背景
  main_blue: '#428BCA', //主色，用于按钮
  secondary_blue: '#5B9BD5', //辅助色，用于统计/提示行文字（可点击）
  secondary_blue_2: '#01B1F1', //辅助色，用于统计
  secondary_blue_3: '#84C9EB', //辅助色，用于统计
  secondary_blue_4: '#D4DFF5', //辅助色，用于标签背景
  main_black: '#0C0C0C', //一级标题，不可点击
  main_grey: '#6f6f6f', //二级标题，用于标题/文章正文
  main_grey_2: '#B5B7B8', //三级标题，用于标题/文章正文
  main_grey_3: '#D7D8D9', //四级标题，用于提示性文字
  secondary_grey: '#F5F5F5', //辅助色，用于分割线/首选背景颜色
  secondary_grey_2: '#F1F1F1' //辅助色，用于次选背景颜色
}

const dark = {
  main_red: 'rgb(0,122,255)', //主色，用于title/icon/标签
  secondary_red: 'rgb(88,86,214)', //辅助色，用于地图位置 rgb(255,90,90)
  secondary_yellow: '#FFC001', //辅助色，用于统计图
  secondary_pink: '#FFEEE7', //辅助色，用于标签背景
  main_green: '#5DB75D', //主色，按钮/标签/统计（逐渐弃用）
  main_green_2: '#40BC93', //主色，用于按钮/按钮边框/按钮文字/TAB
  secondary_green: '#56B6AD', //辅助色，用于统计
  secondary_green_2: '#BFF8E5', //辅助色，用于边框/背景
  secondary_green_3: '#E9FAE3', //辅助色，用于标签背景
  secondary_green_4: '#F1FFFC', //辅助色，用于背景
  main_blue: '#428BCA', //主色，用于按钮
  secondary_blue: '#5B9BD5', //辅助色，用于统计/提示行文字（可点击）
  secondary_blue_2: '#01B1F1', //辅助色，用于统计
  secondary_blue_3: '#84C9EB', //辅助色，用于统计
  secondary_blue_4: '#D4DFF5', //辅助色，用于标签背景
  main_black: '#0C0C0C', //一级标题，不可点击
  main_grey: '#6f6f6f', //二级标题，用于标题/文章正文
  main_grey_2: '#B5B7B8', //三级标题，用于标题/文章正文
  main_grey_3: '#D7D8D9', //四级标题，用于提示性文字
  secondary_grey: '#F5F5F5', //辅助色，用于分割线/首选背景颜色
  secondary_grey_2: '#F1F1F1' //辅助色，用于次选背景颜色
}

const themes = {
  light,
  dark
}

const getTheme = (theme) => {
  return themes[theme]
}

//TODO: store theme in zustand

export default getTheme('light')
