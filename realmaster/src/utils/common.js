import {Alert, Linking} from 'react-native';
import iconMoon from '../assets/iconMoon.json';
import Constants from '../config/constants.js';
import {mainRequest} from './request';
import EventEmitter from 'eventemitter3';
import {createIconSetFromIcoMoon} from 'react-native-vector-icons';
import {setAppLang, l10n, initL10n} from './i18n';

/**
 * Converts a string to an object.
 *
 * @param {string} [str=''] - The string to convert.
 * @param {string} [strSpt=';'] - The separator used to split the string.
 * @param {string} [objSpt='='] - The separator used to split the key-value pairs.
 * @returns {Object} - The object created from the string.
 */
const str2Obj = (str = '', strSpt = ';', objSpt = '=') => {
  const array = str.split(strSpt);
  const obj = {};
  for (const i of array) {
    const [key, value] = i.split(objSpt);
    obj[key.trim()] = value?.trim();
  }
  return obj;
};

/**
 * 通过URL获取域名
 *
 * @param {string} url - 待处理的URL字符串
 * @returns {string|undefined} - 返回匹配的域名字符串带或者不带https?，如果没有匹配则返回undefined
 */
const getDomainByUrl = url => {
  const pattern = /^(?:https?:\/\/)?([^\/:]+)/; // 这里的 [^\/:] 会排除冒号，导致端口号被丢弃
  const match = url.match(pattern);
  if (match) {
    return match[0];
  }
};

/**
 * Asynchronously retrieves the fastest domain based on the provided application mode.
 *
 * @param {string} appmode - The application mode.
 * @returns {Promise<Object>} - An object containing the result and parameters of the fastest domain.
 */
const getFastestDomain = async appmode => {
  const startTime = Date.now();
  let firstResult = null;
  let bestResult = null;
  let maxFactor = 0;
  
  // 创建一个竞争 Promise
  const domainPromises = Constants.BootUpUrls.map(obj => {
    // 记录所有域名中的最大权重因子
    maxFactor = Math.max(maxFactor, obj.factor || 1);
    
    const req = {
      sts: Date.now(),
      dm: obj.dm,
      url: `${obj.protocol}://${obj.dm}/appdm?appmode=${appmode}`,
      f: obj.factor,
      protocol: obj.protocol,
    };
    
    return mainRequest({url: req.url})
      .then(res => {
        const responseTime = Date.now() - startTime;
        const result = {
          req,
          res,
          responseTime,
          adjustedTime: responseTime / req.f // 考虑权重的响应时间
        };
        
        // 如果是第一个响应，记录它
        if (!firstResult) {
          firstResult = result;
        }
        
        // 如果当前响应比最佳结果更好（或者尚无最佳结果），更新最佳结果
        if (!bestResult || result.adjustedTime < bestResult.adjustedTime) {
          bestResult = result;
        }
        
        return result;
      })
      .catch(() => null); // 失败的请求返回null
  });
  
  // 添加一个超时Promise
  const timeoutPromise = new Promise((_, reject) => {
    setTimeout(() => reject(new Error('所有请求超时')), 5000);
  });
  
  // 等待第一个成功的响应
  const firstResponsePromise = Promise.race([
    ...domainPromises,
    timeoutPromise
  ]);
  
  try {
    // 等待第一个响应
    const firstResponse = await firstResponsePromise;
    
    // 如果第一个响应是null（请求异常），需要特殊处理
    if (firstResponse === null) {
      // 继续等待其他请求，可能有成功的
      // 移动环境下网络不稳定，但也不能让用户等待太久
      // 2000ms已经是相对合理的等待时间，足够给其他并行请求完成的机会
      // 同时不会让用户等待过久
      await new Promise(resolve => setTimeout(resolve, 2000));
    } else {
      // 给更慢但权重更高的域名一些时间来完成
      await new Promise(resolve => {
        if (!firstResult) {
          // 如果还没有第一个结果，等待短暂时间
          setTimeout(resolve, 100);
        } else {
          // 根据第一个响应时间和最大权重的乘积动态计算等待时间，再加100ms余量
          const waitTime = Math.min(2000, firstResult.responseTime * maxFactor + 50);
          setTimeout(resolve, waitTime);
        }
      });
    }
    
    // 返回最佳结果，如果没有则使用第一个结果
    const finalResult = bestResult || firstResult;
    
    if (!finalResult) {
      throw new Error('没有成功的响应');
    }
    
    global.rmLog(`common.js:getFastestDomain`, {
      selectedDomain: finalResult.req.dm,
      responseTime: finalResult.responseTime,
      adjustedTime: finalResult.adjustedTime,
      factor: finalResult.req.f,
      maxFactor: maxFactor
    });
    
    return finalResult;
  } catch (error) {
    global.rmLog(`common.js:getFastestDomain error`, error);
    throw new Error('所有请求都失败了');
  }
};

// given a url, return an object
// @return {Object}
const urlParamToObject = url => {
  if (!url || !url.indexOf) {
    return {};
  }
  if (url.indexOf('?') > -1) {
    url = url.split('?')[1];
  }
  var ret = {};
  url = url.split('&');
  for (let i of url) {
    var tmp = i.split('=');
    var p = tmp[0];
    var v = decodeURIComponent(tmp[1]);
    if (v.indexOf(',') > 0) {
      // NOTE: community has comma in it
      // if p == 'cmty'
      ret[p] = v.split(',');
      if (p == 'loc') {
        ret[p] = v.split(',').map(v => {
          return parseFloat(v);
        });
      }
    } else {
      ret[p] = v;
    }
  }
  return ret;
};

const eventEmitter = new EventEmitter({maxListeners: 99999, wildcard: true, delimiter: '.', newListener: true});

const Icon = createIconSetFromIcoMoon(iconMoon);

const showDialog = (props) => {
  const defaultProps = {
    type: 'alert',
    title: 'Alert',
    message: '',
    confirmText: 'OK',
    handleConfirm: () => {},
    cancelText: 'Cancel',
    handleCancel: () => {},
  }
  if (typeof props === 'string') {
    return Alert.alert(l10n(props));
  }
  if (typeof props !== 'object') return;

  const mergedProps = {
    ...defaultProps,
    ...props
  }

  if (!['alert', 'prompt'].includes(mergedProps.type)) {
    return Alert.alert('请使用正确的Alert方法!');
  }

  if (mergedProps.type === 'alert') {
    Alert.alert(l10n(mergedProps.title), l10n(mergedProps.message), [
      {
        text: l10n(mergedProps.confirmText),
        onPress: mergedProps.handleConfirm,
      },
      {
        text: l10n(mergedProps.cancelText),
        onPress: mergedProps.handleCancel,
      },
    ]);
  }
};

const isPopupPushMessageUrl = (url) => {
  if(/\/prop\/detail\/inapp/.test(url)){
    return true;
  }
  if(/1\.5\/showing\/detail/.test(url)){
    return true;
  }
  if(/nativePopup/i.test(url)){
    return true
  }
  return false;
}

const isFubUrl = (url='')=>{
  if(/\/prop\/detail/.test(url)){
    if(/fub\_src=fub/.test(url)){
      return true;
    }
  }
  return false;
}

const isInChinaByTimeZone = () => {
  try {
    const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    const chineseTimeZones = ['china', 'shanghai', 'beijing', 'chongqing', 'urumqi', 'harbin'];
    return chineseTimeZones.some(zone => timeZone.toLowerCase().includes(zone));
  } catch (e) {
    console.warn('获取时区失败', e);
    return false; // 默认不在中国
  }
};

export {
  str2Obj,
  getDomainByUrl,
  getFastestDomain,
  urlParamToObject,
  // sendLogger,
  eventEmitter,
  Icon,
  showDialog,
  isPopupPushMessageUrl,
  isFubUrl,
  isInChinaByTimeZone
};
