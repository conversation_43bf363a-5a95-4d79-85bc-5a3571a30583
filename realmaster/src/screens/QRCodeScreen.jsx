import React, {Component} from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  //VibrationIOS,
  Vibration,
  Alert,
  Dimensions,
} from 'react-native';
import {Camera, CameraType} from 'react-native-camera-kit';
import {l10n} from '../utils/i18n';
// import { useScanBarcodes, BarcodeFormat } from 'vision-camera-code-scanner';

// var QRCodeScreen = React.createClass({
class QRCodeScreen extends Component {
  constructor(props) {
    super(props);
    this.state = {
      cancelButtonVisible: true,
      cancelButtonTitle: l10n('Close'),
      timeout: null,
      newCameraPermission: null,
    };
    this.datas = {};
  }
  async componentDidMount() {
    this.count = 0;
    this.props = {
      ...this.props,
      ...this.props.route.params,
    };
    this.datas.onCancel = this.props.onCancel;
    this.datas.onSucess = this.props.onSucess;
    // var cameraPermission = await Camera.getCameraPermissionStatus()
    // var newCameraPermission = cameraPermission;
    // if (cameraPermission == 'not-determined') {
    //   newCameraPermission = await Camera.requestCameraPermission()
    // }
    // this.setState({newCameraPermission});

    // console.log('####did mount',cameraPermission);
    // const microphonePermission = await Camera.getMicrophonePermissionStatus()
    // const newMicrophonePermission = await Camera.requestMicrophonePermission()
    // console.log('####did mount');
    // console.log(this.datas.onCancel);
  }
  _onPressCancel = () => {
    var self = this;
    requestAnimationFrame(function () {
      //$this.props.navigator.pop();
      if (self.datas && self.datas.onCancel) {
        self.datas.onCancel();
      }
    });
  };
  componentWillUnmount() {
    clearTimeout(this.timeout);
  }
  _onBarCodeRead = result => {
    var self = this;
    if (result.nativeEvent) {
      result = {data: result.nativeEvent.codeStringValue, target: result.nativeEvent.target};
    }

    // console.log(result)
    // return
    if (self.datas.barCodeFlag) {
      self.datas.barCodeFlag = false;

      self.timeout = setTimeout(function () {
        //VibrationIOS.vibrate();
        Vibration.vibrate([1000]);
        //$this.props.navigator.pop();
        // console.log(result.data);
        self.datas.onSucess(result.data);
      }, 1000);
    }
  };
  render() {
    this.datas.barCodeFlag = true;
    // const devices = useCameraDevices()
    // const device = devices.back
    // let noCamera = this.state.newCameraPermission !== 'granted';
    // if (device == null) return <View><Text>Loading...</Text></View>
    // if (noCamera) return <View><Text>No access to camera</Text></View>
    return (
      // <RNCamera
      //   onBarCodeRead={this._onBarCodeRead.bind(this)}
      //   style={styles.camera}
      //   captureAudio={false}>
      //   <View style={styles.rectangleContainer}>
      //     <View style={styles.rectangle}/>
      //   </View>
      //   { this.state.cancelButtonVisible &&
      //     (<CancelButton onPress={()=>{this._onPressCancel()}} title={this.state.cancelButtonTitle} />)
      //   }
      // </RNCamera>
      <View style={styles.container}>
        <Camera
          // Barcode props
          style={styles.camera}
          scanBarcode={true}
          // onReadCode={(event) => {Alert.alert('QR code found'); console.log(event.nativeEvent.codeStringValue)}} // optional
          onReadCode={this._onBarCodeRead.bind(this)}
          showFrame={true} // (default false) optional, show frame with transparent layer (qr code or barcode will be read on this area ONLY), start animation for scanner, that stops when a code has been found. Frame always at center of the screen
          laserColor='red' // (default red) optional, color of laser in scanner frame
          frameColor='white' // (default white) optional, color of border of scanner frame
        ></Camera>
        {this.state.cancelButtonVisible && (
          <CancelButton
            onPress={() => {
              this._onPressCancel();
            }}
            title={this.state.cancelButtonTitle}
          />
        )}
      </View>

      // <Camera
      //   style={StyleSheet.absoluteFill}
      //   device={device}
      //   isActive={true}
      // />
    );
  }
}
class CancelButton extends Component {
  // var CancelButton = React.createClass({
  render() {
    return (
      <TouchableOpacity onPress={this.props.onPress}>
        <View style={styles.cancelButton}>
          <Text style={styles.cancelButtonText}>{this.props.title}</Text>
        </View>
      </TouchableOpacity>
    );
  }
}

var styles = StyleSheet.create({
  container: {
    flex: 1,
    // height: Dimensions.get('window').height,
    height: '100%',
    // width: Dimensions.get('window').width,
    // justifyContent: 'center',
    // alignItems: 'center',
  },
  camera: {
    //height: 568,
    flex: 1,
    alignItems: 'center',
  },

  rectangleContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'transparent',
  },

  rectangle: {
    height: 250,
    width: 250,
    borderWidth: 2,
    borderColor: '#00FF00',
    backgroundColor: 'transparent',
  },

  cancelButton: {
    position: 'absolute',
    flexDirection: 'row',
    alignSelf: 'center',
    justifyContent: 'center',
    backgroundColor: 'white',
    borderRadius: 3,
    padding: 15,
    width: 100,
    bottom: 40,
  },
  cancelButtonText: {
    fontSize: 17,
    fontWeight: '500',
    color: '#0097CE',
  },
});
export default QRCodeScreen;
