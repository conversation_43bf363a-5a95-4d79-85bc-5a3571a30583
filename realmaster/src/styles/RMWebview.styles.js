import { StyleSheet, Dimensions } from 'react-native';
import { RMStatusBar, getStatusBarHeight } from '../components/RMStatusBar';

const screenWidth = Dimensions.get('window').width;
const statusBarHeight = getStatusBarHeight();

export const styles = StyleSheet.create({
  // 基础容器样式
  container: {
    ...StyleSheet.absoluteFillObject,
    flex: 1,
    justifyContent: 'center',
    backgroundColor: 'white',
  },

  // WebView相关样式
  webView: {
    flex: 1,
    opacity: 1,
    marginTop: RMStatusBar.getMarginOffset(),
  },
  webView2bar: {
    flex: 1,
    top: 0,
  },
  webViewTrueHide: {
    flex: 1,
    opacity: 0,
  },
  webViewHide: {
    flex: 1,
    backgroundColor: 'transparent',
    opacity: 0.2,
  },
  webViewHidden: {
    flex: 0,
  },

  // 导航栏样式
  navBar: {
    backgroundColor: '#E03131',
  },
  bar: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'flex-end',
  },

  // 加载指示器样式
  indicatorWrapper: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    width: screenWidth,
  },
  indicatorWrapperWithBar: {
    top: 44 + statusBarHeight,
  },
  indicatorSpinner: {
    padding: 3,
  },

  // 错误页面样式
  errorContainer: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: '#fff',
  },
  errorContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorImage: {
    width: 100,
    height: 100,
  },
  errorTitle: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  reloadButton: {
    marginTop: 10,
    backgroundColor: '#E03131',
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 5,
  },
  reloadButtonText: {
    color: 'white',
  },

  // 网络错误提示样式
  indicatorContent: {
    flexDirection: 'column',
    alignItems: 'center',
  },
  indicatorButton: {
    marginTop: 40,
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 5,
    borderWidth: 1,
    borderColor: '#bfbfbf',
  },
  indicatorButtonText: {
    color: '#bfbfbf',
  },
  indicatorNetworkText: {
    paddingTop: 10,
    color: '#bfbfbf',
  },
});