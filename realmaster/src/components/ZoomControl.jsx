import React from 'react';
import PropTypes from 'prop-types';

import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  Image,
  Platform,
} from 'react-native';
import {getStatusBarHeight} from './RMStatusBar';
import { Icon } from '../utils/common';
// NOTE: it's propTypes.func not propTypes.function
// inconsistent with array/number/object/string/symbol,   bool/func
// https://reactjs.org/docs/typechecking-with-proptypes.html
const propTypes = {
  mapTypeCallback: PropTypes.func,
  locateMeCallback: PropTypes.func,
  zoomCallback: PropTypes.func,
};

const defaultProps = {
  mapTypeCallback:null,
  locateMeCallback:null,
  zoomCallback:null,
};

class ZoomControl extends React.Component {
  constructor(props){
    super(props);
    this.state = {
      // isDrawActive:false
    }
  }
  render() {
    // return null;
    const { mapTypeCallback, locateMeCallback, zoomCallback,
      drawPolygonOnMapCallback, //invoke mapProps to draw polygon on map
      isShowDrawPolygon, // show the draw polygon button or not
      isDrawActive  // currently drawing polygon or not
    } = this.props;
    // var icon = this.props.icon;
    // var mapTypeIcon = this.props.mapTypeId == 'standard' ? require('../img/btns/fa-globe.png') : require('../img/btns/fa-map-o.png');
    var mapTypeIcon = this.props.mapTypeId == 'standard' ? 'globe':'map-o2';
    var mapStyle = {paddingLeft:1,paddingBottom:1}
    if(Platform.OS == 'android'){
      // fontSize = 20
      mapStyle.marginTop = -1
    }
    // let fontSize = 21
    if(mapTypeIcon == 'map-o2'){
      // mapStyle = {paddingBottom:2,marginTop:-2,paddingLeft:1}
      if(Platform.OS == 'android'){
        // fontSize = 20
        // mapStyle.marginTop = -3
        mapStyle.marginLeft = -1
        // mapStyle.paddingLeft = 0
      }
    }
    let statusBarHeight = getStatusBarHeight()
    // console.log('statusBarHeight:',statusBarHeight)
    var overLayWrapper = {
      position: 'absolute',
      width:40,
      height:90,
      right:8,
      top:150+statusBarHeight,
      zIndex:15,
      flexDirection:'column',
      justifyContent:'space-between',
      // backgroundColor:'blue',
      // elevation:1
    }
    let drawPolyColor = '#0a0a09'
    if(isDrawActive){
      drawPolyColor = '#007aff'
    }
    // NOTE: height 40 each
    if (isShowDrawPolygon){
      overLayWrapper.height = 130
    }
    return (
        <View style={overLayWrapper} key={"ZoomButtons"}>
            {mapTypeCallback &&
                <View style={[styles.buttonOverlay,styles.overLayType]} key={"mapTypeSelect"}>
                    <TouchableOpacity
                        onPress={() => mapTypeCallback()}
                        style={[styles.overLayCircle,styles.overLayShadow]}
                        >
                        {/* <Image style={styles.buttonImage} source={mapTypeIcon} /> */}
                        <Icon name={mapTypeIcon} size={21} color="black" style={mapStyle}/>
                    </TouchableOpacity>
                </View>
            }
            {locateMeCallback &&
                <View style={[styles.buttonOverlay,styles.overLayLocate]} key={"locateMe"}>
                    <TouchableOpacity
                        onPress={() => locateMeCallback()}
                        style={[styles.overLayCircle,styles.overLayShadow]}
                        >
                        {/* <Image style={styles.buttonImage} source={require('../img/btns/fa-crosshair.png')} /> */}
                        <Icon name="locate" size={21} color="black"/>
                    </TouchableOpacity>
                </View>
            }
            {false && zoomCallback &&
              <View style={[styles.buttonOverlay,styles.overLayZoomIn]} key={"zoomMapIn"}>
                <TouchableOpacity
                    onPress={() => zoomCallback('In')}
                    style={[styles.overLayCircle,styles.overLayShadow]}
                    >
                    {/* <Text style={styles.overLayText}>+</Text> */}
                    <Icon name="rmplus" size={18} color="black"/>
                </TouchableOpacity>
              </View>
            }
            {false && zoomCallback &&
              <View style={[styles.buttonOverlay,styles.overLayZoomOut]} key={"zoomMapOut"}>
                <TouchableOpacity
                    onPress={() => zoomCallback('Out')}
                    style={[styles.overLayCircle,styles.overLayShadow]}
                    >
                    {/* <Text style={styles.overLayText}>-</Text> */}
                    <Icon name="rmminus" size={18} color="#0a0a09"/>
                </TouchableOpacity>
              </View>
            }
            {isShowDrawPolygon &&
              <View style={[styles.buttonOverlay,styles.overLayZoomOut]} key={"zoomShowDrawPoly"}>
                <TouchableOpacity
                    onPress={() => {
                      // this.setState({isDrawActive:!this.state.isDrawActive})
                      drawPolygonOnMapCallback('')
                    }}
                    style={[styles.overLayCircle,styles.overLayShadow]}
                    >
                    {/* <Text style={styles.overLayText}>-</Text> */}
                    <Icon name="area" size={18} color={drawPolyColor}/>
                </TouchableOpacity>
              </View>
            }
        </View>
    );
  }
}

ZoomControl.propTypes = propTypes;
ZoomControl.defaultProps = defaultProps;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'column',
    alignSelf: 'flex-start',
    // width:20,
    // height:20
  },
  buttonOverlay:{
    // position: 'absolute',
    // opacity: 0.5,
    width: 40,
    height: 40,
    backgroundColor:'transparent',
    // backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
    // flex:1,
    flexDirection: 'row',
    // elevation:1
    // zIndex:15
  },
  overLayType:{
    // top: 50,
    // right: 3,
  },
  overLayLocate:{
    // top: 100,
    // right: 3,
  },
  overLayZoomIn:{
    // top: 200,
    // right: 3,
  },
  overLayZoomOut:{
    // top:250,
    // right: 3,
  },
  buttonImage:{
    flex:1,
    width: 20,
    height: 20
  },
  overLayCircle:{
    alignItems:'center',
    justifyContent:'center',
    // box-shadow: 1px 1px 1px #d2d2d2;
    backgroundColor:'white',
    borderRadius:40,
    width: 40,
    height: 40,
    padding: 10,
  },
  overLayShadow:{
    shadowColor:'#d2d2d2',//'#d2d2d2',
    shadowOffset:{width:1,height:3},
    shadowRadius:3,
    shadowOpacity:0.8,
    backgroundColor: 'white',
    elevation:2
  },
  overLayText:{
    marginTop:-5,
    // paddingLeft:4,
    fontSize:23,
    color:'#616161'
  },
});

export default ZoomControl;
