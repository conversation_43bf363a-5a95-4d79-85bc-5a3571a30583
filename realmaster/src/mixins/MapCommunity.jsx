/*
Show community/building
1. find current prov/region/city/community/zip/buildings
2. show available values
3. show values based on selection
4. when click show half page for summary details(last 10 selected values + important values)
5. when half page clicked show full page details/comments/fav...
*/
/*

*/
import React, {Component} from 'react';
import {
  StyleSheet,
  View,
  Text,
  Linking,
  Image,
  Alert,
  Platform,
  ScrollView,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import MapFeature from './MapFeature';
import { Marker, Polygon } from 'react-native-maps';
// import schoolOrange0 from '../img/schoolOrange0.png';
// import IconText from '../views/IconText';
import BottomPane from '../components/BottomPane';
import { requestStdFn } from '../utils/request';
import serverDomainIns from '../utils/serverDomain';
import {l10n} from '../utils/i18n';
import { eventEmitter } from '../utils/common';


const { width, height } = Dimensions.get('window');
class MapCommunity extends MapFeature{
  constructor(map,props,onOff){
    super(map,"MapCommunity",props,onOff);
    this.state = {
      houses: [],
      tracksViewChanges:true,
      curHouse:{},
    }
    // this.featureOn = true;
  }
  onOffView(){
    // on/off setting to show on map
    return {icon:'compass',name:l10n('Community'),toggleOnOff:(p)=>{this.toggleOnOff(p)},on:this.featureOn}
  }
  toggleOnOff(onOff){
    if(onOff == 'off'){
      this.featureOn = true;
    }
    if (this.featureOn = !this.featureOn){
      // this.map.onRegionChangeComplete();
      var base = "/1.5/community";
      var url =  base+'?city=';
      // console.log(url);
      var cb = (val)=>{
        // console.log('mapsearch cb val: ',val);
        if (val == ':cancel') {
          return;
        }
        if (/^redirect|^cmd-redirect:/.test(val)) {
          // return window.location = val.split('redirect:')[1]
          var url = val.split('redirect:')[1];
          // console.log('close and redirect from propDetail: '+url)
          return self.closePopup(url);
          // return window.location = url;
          // return;
        }
        try {
          // var val = 'loc=43.5723199141038,-79.5785565078259&zoom=15&saletp=lease';
          var d = urlParamToObject(val);
          // Alert.alert(JSON.stringify(d));
          // window.bus.$emit('school-prop', d);
          // TODO: redirect map?
          // self.searchSchoolProp(d);
        } catch (e) {
          // console.error(e);
        }
      }
      var opt = {
        hide:false,
        sel:'#callBackString',
        tp:'pageContent',
        // noClose:true,
        title:l10n('RealMaster'),
        // toolbar:false,
        url: serverDomainIns.getFullUrl(url),
      }
      eventEmitter.emit("app.message",{msg:JSON.stringify(opt),cb:cb});
    }else{
      this.setState({})
    }
  }
  regionChanged(event,map){
    if (!this.featureOn) return;
    // console.log("regionChanged zoom:" + event.zoom);
    // var self = event.self;
    return;
    super.regionChanged(event,map);
    // var featureCount = Object.keys(self.features).length;
    // if (event.zoom < 15){
    //   this.setState({houses:[],curHouse:null});
    //   return;
    // }
    var ne = [event.bbox[3],event.bbox[2]];
    var sw = [event.bbox[1],event.bbox[0]];

    // TODO: get community
    requestStdFn('getStigma',{ne,sw}).then(ret=> {
      this.setState({houses:ret.items});
      this.trackOff();
    }).catch(err=> {
      console.log('err stigm: ',err);
        return;
    })

  }
  openModel(house) {
    return ()=> {
      this.closeOtherModals();
      this.setState({curHouse:house,tracksViewChanges:true});
      this.trackOff();
      return;
    }
  }
  renderModal() {
    return null;
    if (!this.featureOn) return null;
    var curHouse = this.state.curHouse
    if (curHouse && curHouse._id) {
      return (
        <BottomPane key={'stigmatized'} statusBar={{hidden:true}} height={height/2} title={l10n('Stigmatized House')} cbClose={()=>{this.closeModal()}}>
          <ScrollView>
            <View style={styles.block}>
              <Text style={styles.addr}>{curHouse.addr}</Text>
              <Text style={styles.ptp}>{curHouse.ptp}</Text>
            </View>
            <View style={styles.block}>
              <Text style={styles.title}>{curHouse.title}</Text>
              <Text style={styles.type}>{curHouse.type}</Text>
            </View>
            <View style={styles.block}>
              <Text style={styles.desc}>{curHouse.m}</Text>
            </View>
            <View style={styles.block}>
              {curHouse.ref1?
              <TouchableOpacity onPress={()=>Linking.openURL(curHouse.ref1)}>
                <Text style={styles.ref}>{l10n('Reference 1')}</Text>
              </TouchableOpacity>
              :null}
              {curHouse.ref2?
              <TouchableOpacity style={{marginTop:7, marginBottom:15}} onPress={()=>Linking.openURL(curHouse.ref2)}>
                <Text style={styles.ref}>{l10n('Reference 2')}</Text>
              </TouchableOpacity>
              :null}
            </View>
          </ScrollView>
        </BottomPane>
      );
    }
  }
  closeModal(){
    this.setState({curHouse:{},tracksViewChanges:true})
    this.trackOff();
  }
  renderOnMap() {
    return null;
    if (!this.featureOn) return null;
    var view = [];
    const {houses} = this.state;
    const self = this;
    for (let house of houses) {
      view.push(self.renderMarker(house));
    }
    return view;
  }
  renderMarker(house={}){
    if (!(house.lat || house.loc)){
      console.log("Bad House, can not render:",house);
    }
    let loc = {longitude:(house.lng || house.loc[1]),latitude:(house.lat || house.loc[0])};
    let isSelected = this.state.curHouse._id == house._id;
    // console.log('isSelected: ',isSelected);
    return (
      <Marker
        stopPropagation={true}
        tracksViewChanges={ this.state.tracksViewChanges }
        key={house._id}
        onPress={this.openModel(house)}
        coordinate={loc}
      >
        {/* <IconText
          icon={schoolOrange0}
          text={house.type}
          borderColor={'#000'}
          backgroundColor={'#000'}
        /> */}
        <View style={[styles.stigmatizedMarker,{backgroundColor:isSelected?'#e03131':'#000'}]}>
          {/* <Text style={styles.stigmatizedMarkerText}>{house.type}</Text> */}
        </View>
      </Marker>
    );
  }
  unmount(){
    super.unmount();
  }
}
const styles = StyleSheet.create({
  marker:{
    width: 24,
    height: 24,
    zIndex: 1,
  },
  block:{
    borderBottomColor:'#ddd',
    borderBottomWidth:1,
    padding:10
  },
  addr:{
    // marginBottom:5
  },
  ref:{
    color:'rgb(66, 139, 202)'
  },
  ptp:{
    color:'#777',
    fontSize:14,
  },
  title:{
    fontSize:18,
    fontWeight:'bold',
  },
  stigmatizedMarker:{
    backgroundColor:'#000',
    // paddingTop: 3,
    // paddingBottom: 3,
    // paddingRight: 8,
    // paddingLeft: 8,
    // borderRadius:11,
    height: 19,
    width: 19,
    padding:3,
    borderRadius:18,
    borderWidth: 2.5,
    borderColor: 'white',
  },
  stigmatizedMarkerText:{
    color:'#fff',
    fontSize:11
  }
});

export default MapCommunity;
