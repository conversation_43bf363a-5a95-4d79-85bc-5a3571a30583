/*

*/
import React, {Component} from 'react';
import {
  StyleSheet,
  View,
  Text,
  Linking,
  Image,
  Alert,
  Platform,
  ScrollView,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import MapFeature from './MapFeature';
import { <PERSON>er, Polygon } from 'react-native-maps';
// import schoolOrange0 from '../img/schoolOrange0.png';
// import IconText from '../views/IconText';
// import {RMPost,getServerDomain} from '../RMNetwork';
import serverDomainIns from '../utils/serverDomain';
// import {sprintf} from '../lib/sprintf'
import { Icon, eventEmitter } from '../utils/common';
import BottomPane from '../components/BottomPane';
import {requestStdFn} from '../utils/request';
import {l10n} from '../utils/i18n';
import BottomPaneWhite from '../components/BottomPaneWhite';
import ListDragPan from './mapListDragPanResponder';

const { width, height } = Dimensions.get('window');
class MapStigmatized extends MapFeature{
  constructor(map,props,onOff){
    super(map,"MapStigmatized",props,onOff);
    this.state = {
      houses: [],
      tracksViewChanges:true,
      curHouse:{},
      propFeatureOn: true,
      stigmCnt: 0,
    }
    this.needLogin = true;
    this.listDragPan = React.createRef();
    // console.log('++++props.propFeatureOn',props.propFeatureOn)
    if(props.propFeatureOn != null){
      this.state.propFeatureOn = props.propFeatureOn
    }
    eventEmitter.on("map.props.fetureOn",this.recordPropFeatureOn.bind(this))
    // this.featureOn = true;
  }
  recordPropFeatureOn(val){
    // console.log('event',val)
    this.setState({propFeatureOn:val})
  }
  onOffView(){
    // on/off setting to show on map
    return {
      icon:'rm-warning',
      iconSize:23,
      iconListStyle:{marginTop:2},
      iconStyle:{marginTop:-2},
      name:l10n('Stigmatized'),
      toggleOnOff:(p)=>{this.toggleOnOff(p)},on:this.featureOn}
  }
  createStigma(){
    var url ='/1.5/stigma/edit?tab=stigma&src=nativemap';
    if(this.lastPos){
      url  += `&lat=${this.lastPos.lat}&lng=${this.lastPos.lng}`
    }
    var cb = (val)=>{
      // console.log('mapsearch cb val: ',val);
      if (val == ':cancel') {
        return;
      }
      try {
        // var val = 'loc=43.5723199141038,-79.5785565078259&zoom=15&saletp=lease';
      } catch (e) {
        // console.error(e);
      }
    }
    var opt = {
      hide:false,
      sel:'#callBackString',
      tp:'pageContent',
      // noClose:true,
      title:l10n('RealMaster'),
      // toolbar:false,
      url: serverDomainIns.getFullUrl(url),
    }
    eventEmitter.emit("app.message",{msg:JSON.stringify(opt),cb:cb});
  }
  renderFeatureNoPropDisplay(){
    return null;
    let resultCount = (this.state.houses.length||0)
    if(resultCount >= 50){
      resultCount = resultCount+'+'
    }
    return(
      <View key={'createStig'}
        style={{flexDirection:'row',
        justifyContent:'flex-end',
        marginTop:0,
        flex:10,marginLeft:10,marginRight:0}}>
        {/* <Text style={{paddingRight:10,paddingTop:13,color:'#666',fontSize:15,fontWeight:'normal'}}>{resultCount+' '+l10n('Results')}</Text> */}
        <Icon name="rmplus" size={21} color="black" style={[styles.navBarButton]}   onPress={()=>{this.createStigma()}} />
      </View>
    )
  }
  toggleOnOff(onOff){
    if(onOff == 'off'){
      this.featureOn = true;
    }
    if (this.featureOn = !this.featureOn){
      super.toggleOnOff(this.featureOn,this.needLogin)
      this.map.onRegionChangeComplete();
    }else{
      this.setState({curHouse:{}})
    }
  }
  // post search stigma
  regionChanged(event,map){
    if (!this.featureOn) return;
    // console.log("regionChanged zoom:" + event.zoom);
    // var self = event.self;
    super.regionChanged(event,map);
    // var featureCount = Object.keys(self.features).length;
    // if (event.zoom < 15){
    //   this.setState({houses:[],curHouse:null});
    //   return;
    // }
    var ne = [event.bbox[3],event.bbox[2]];
    var sw = [event.bbox[1],event.bbox[0]];
    requestStdFn('getStigma',{ne,sw}).then(ret=> {
      this.setState({houses:ret.items, stigmCnt: ret.cnt || 0});
      this.trackOff();
    }).catch(err=> {
      console.log('err stigm: ',err);
        return;
    })

  }
  openModel(house) {
    return ()=> {
      this.closeOtherModals();
      this.setState({curHouse:house,tracksViewChanges:true});
      this.trackOff();
      return;
    }
  }
  renderStigListItem(h={}){
    return (<View style={{padding:10, borderBottomColor:'#f1f1f1',borderBottomWidth:1}}>
      <TouchableOpacity onPress={this.openModel(h)}>
        <Text style={styles.title}>{h.addr}</Text>
        <Text style={styles.type2}>{h.ptp}</Text>
        <View style={{justifyContent:'space-between',flexDirection:'row',paddingTop:5}}>
          <Text style={{flex:9,fontSize:14,color:'rgb(119,119,119)'}} numberOfLines={1}>{h.title} </Text>
          <Text style={[styles.type2,{flex:2,paddingTop:3,textAlign:'right'}]}>{h.type}</Text>
        </View>
      </TouchableOpacity>
    </View>)
  }
  renderModal() {
    if (!this.featureOn) return null;
    var curHouse = this.state.curHouse
    if (curHouse && curHouse._id) {
      return (
        <BottomPaneWhite key={'stigmatized'}
          height={250}
          title={(curHouse.addr||' ')}
          title1b={(curHouse.ptp||' ')}
          cbClose={()=>{
            this.closeModal();
          }}>
          <ScrollView style={{}}>
            {/* <View style={styles.block}>
              <Text style={styles.addr}>{curHouse.addr}</Text>
              <Text style={styles.ptp}>{curHouse.ptp}</Text>
            </View> */}
            <View style={styles.block} key={'stigTitle'}>
              <Text style={styles.title}>{curHouse.title}</Text>
              <Text style={styles.type}>{curHouse.type}</Text>
            </View>
            <View style={styles.block} key={'stigRemarks'}>
              <Text style={styles.desc}>{curHouse.m}</Text>
            </View>
            <View
              style={[styles.block,{borderBottomColor:'white',flexDirection:'row',marginBottom:15}]}
              key={'stigRef'}
            >
              {curHouse.ref1?
              <TouchableOpacity onPress={()=>Linking.openURL(curHouse.ref1)}>
                <Text style={styles.ref}>{l10n('Reference')+' 1'}</Text>
              </TouchableOpacity>
              :null}
              {curHouse.ref2?
              <TouchableOpacity style={{marginLeft:12}} onPress={()=>Linking.openURL(curHouse.ref2)}>
                <Text style={styles.ref}>{l10n('Reference')+' 2'}</Text>
              </TouchableOpacity>
              :null}
            </View>
          </ScrollView>
        </BottomPaneWhite>);
      return (
        <BottomPane key={'stigmatized'} statusBar={{hidden:true}} height={height/2} title={l10n('Stigmatized House')} cbClose={()=>{this.closeModal()}}>
          <ScrollView>
            <View style={styles.block}>
              <Text style={styles.addr}>{curHouse.addr}</Text>
              <Text style={styles.ptp}>{curHouse.ptp}</Text>
            </View>
            <View style={styles.block}>
              <Text style={styles.title}>{curHouse.title}</Text>
              <Text style={styles.type}>{curHouse.type}</Text>
            </View>
            <View style={styles.block}>
              <Text style={styles.desc}>{curHouse.m}</Text>
            </View>
            <View style={styles.block}>
              {curHouse.ref1?
              <TouchableOpacity onPress={()=>Linking.openURL(curHouse.ref1)}>
                <Text style={styles.ref}>{l10n('Reference 1')}</Text>
              </TouchableOpacity>
              :null}
              {curHouse.ref2?
              <TouchableOpacity style={{marginTop:7, marginBottom:15}} onPress={()=>Linking.openURL(curHouse.ref2)}>
                <Text style={styles.ref}>{l10n('Reference 2')}</Text>
              </TouchableOpacity>
              :null}
            </View>
          </ScrollView>
        </BottomPane>
      );
    }
    const {houses} = this.state;
    if (houses && !this.state.propFeatureOn){//&& houses.length
      let view = [];
      for(let h of houses){
        view.push(this.renderStigListItem(h))
      }
      let count = houses.length;
      let base = -38;
      let leftTop = 5;
      // console.log('state houses:',this.state.houses)
      if (Platform.OS != 'ios'){
        leftTop = 3
      }
      let left = (
        <View style={{
          marginTop:base+leftTop,
          // backgroundColor:'blue',
          zIndex:105,
          paddingLeft:10}}>
          {/* <Text>{sprintf(l10n("%d stigs found"),count)}</Text> */}
          <Text style={{fontSize:15,paddingBottom:8,marginTop:-3}}>{count}/{this.state.stigmCnt || count} {l10n('Results')}</Text>
        </View>)
      let right = (
        <View style={{flexDirection:'row'}}>
          <TouchableOpacity style={{
            // marginTop:-10,
            marginTop:base,
            zIndex:105,
            paddingRight:10,flexDirection:'row'}} onPress={()=>{this.createStigma()}}>
            {/* <Icon name="rmplus" size={16} color="black" style={[styles.navBarButton]} /> */}
            <Text style={{color:'#3899EC', fontSize:16, fontWeight:'bold'}}>{l10n('Report')}</Text>
          </TouchableOpacity>
          <TouchableOpacity style={{
            height:40, zIndex:120,
            paddingLeft:3,paddingRight:10,
            // paddingTop:3,
            paddingBottom:3,
            marginTop:base,
            // backgroundColor:'blue',
            // alignItems:'flex-end'
            }}
            onPress={(e)=>{
              // console.log(this.listDragPan.current)
              this.listDragPan.current.toggleListView(e)
            }}>
            <View>
              <Text style={{color:'#3899EC', fontSize:16, fontWeight:'bold'}}>{l10n('LIST')}</Text>
            </View>
          </TouchableOpacity>
        </View>
      )
      // (
      //   <View style={{paddingLeft:10}}>
      //     {/* <Text style={{fontSize:11,color:'#777'}}>{l10n('Long press on map to set location')}</Text> */}
      //   </View>
      // )
      return (
        <ListDragPan
          ref={this.listDragPan}
          id={'mapStigList'}
          bottom={0}
          PAN_SHOW_VALUE={110}
          enableLayerMenuControl={true}
          layerMenuMap={this.map}
          layerMenuStyle={{marginTop: -1,marginBottom: 35}}
          renderTopContent={() => (
            <View 
              key="stigTopContent"
              style={{width:'100%',
                flexDirection:'row',
                justifyContent:'space-between',
                paddingLeft:5,paddingRight:5}}
            >
              {left}
              {right}
            </View>
          )}
          closeListView={()=>{
            this.setState({schList:null});
          }}
        >
          <ScrollView
            showsVerticalScrollIndicator={false}
            style={{
              paddingTop:10,
              // backgroundColor:'lightblue',
              width:'100%'}}>
            {view}
            <View style={{height:100}}></View>
          </ScrollView>
        </ListDragPan>
      )
    }
    return null
  }
  setShowPanel(){
    this.closeModal()
  }
  closeModal(){
    this.setState({curHouse:{},tracksViewChanges:true})
    this.trackOff();
  }
  renderOnMap() {
    if (!this.featureOn) return null;
    var view = [];
    const {houses} = this.state;
    const self = this;
    for (let house of houses) {
      view.push(self.renderMarker(house));
    }
    return view;
  }
  renderMarker(house={}){
    if (!(house.lat || house.loc)){
      console.log("Bad House, can not render:",house);
    }
    let loc = {longitude:(house.lng || house.loc[1]),latitude:(house.lat || house.loc[0])};
    let isSelected = this.state.curHouse._id == house._id;
    // console.log('isSelected: ',isSelected);
    return (
      <Marker
        stopPropagation={true}
        tracksViewChanges={ this.state.tracksViewChanges }
        key={house._id}
        onPress={this.openModel(house)}
        coordinate={loc}
        style={{zIndex:10}}
      >
        {/* <IconText
          icon={schoolOrange0}
          text={house.type}
          borderColor={'#000'}
          backgroundColor={'#000'}
        /> */}
        <View style={[styles.stigmatizedMarker,{backgroundColor:isSelected?'#e03131':'#000'}]}>
          {/* <Text style={styles.stigmatizedMarkerText}>{house.type}</Text> */}
        </View>
      </Marker>
    );
  }


  unmount(){
    eventEmitter.removeListener('map.props.fetureOn',this.recordPropFeatureOn);
    super.unmount();
  }
}
const styles = StyleSheet.create({
  navBarButton: {
    // width: 44,
    // height: 44,
    // padding: 10,
    // paddingLeft:8,
    // paddingRight:10,
    // paddingTop:2,
    // fontSize: 20,
  },
  marker:{
    width: 24,
    height: 24,
    // zIndex: 1,
  },
  block:{
    borderBottomColor:'white',
    borderBottomWidth:1,
    padding:10
  },
  addr:{
    // marginBottom:5
  },
  ref:{
    color:'rgb(66, 139, 202)'
  },
  ptp:{
    color:'#777',
    fontSize:14,
  },
  title:{
    fontSize:16,
    fontWeight:'bold',
  },
  desc:{
    fontSize:14,
  },
  type:{
    fontSize:12,
    color:'#777',
  },
  type2:{
    fontSize:12,fontWeight:'200',color:'rgb(119,119,119)',
    paddingTop:5,
  },
  stigmatizedMarker:{
    backgroundColor:'#000',
    // paddingTop: 3,
    // paddingBottom: 3,
    // paddingRight: 8,
    // paddingLeft: 8,
    // borderRadius:11,
    height: 19,
    width: 19,
    padding:3,
    borderRadius:18,
    borderWidth: 2.5,
    borderColor: 'white',
  },
  stigmatizedMarkerText:{
    color:'#fff',
    fontSize:11
  }
});

export default MapStigmatized;
