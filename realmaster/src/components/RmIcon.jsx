import React, {Component} from 'react';
import {Platform, StyleSheet} from 'react-native';
import { Icon } from '../utils/common';


export default class RmIcon extends Component {
  constructor(props) {
    super(props);
  }
  componentDidMount() {

  }
  render() {
    const {style,name,size,color} = this.props;
    let sz = 15;
    if (!color) {
      color = '#fff';
    }
    if (size) {
      sz = size;
    }
    return <Icon style={style} name={name} size={sz} color={color} />
  }
}
const styles = StyleSheet.create({
})
