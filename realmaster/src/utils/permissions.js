import {Platform} from 'react-native';
import permissions, {check, request, PERMISSIONS, RESULTS, openSettings} from 'react-native-permissions';

const os = Platform.OS;

const rmPermissions = {
  ...permissions,
};


const handleLocationPermission = ({granted = () => {}, denied = () => {}, blocked = () => {}}) => {
  const location = os === 'ios' ? PERMISSIONS.IOS.LOCATION_WHEN_IN_USE : PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION;

  return {
    check: async () => {
      const res = await check(location);
      if (res === RESULTS.GRANTED) {
        granted();
      }
      if (res === RESULTS.DENIED) {
        const res = await request(location);
        if (res === RESULTS.GRANTED) {
          granted();
        }
        if (res === RESULTS.BLOCKED) {
          blocked();
        }
      }
      if (res === RESULTS.BLOCKED) {
        blocked();
      }
    },
  };
};

export {rmPermissions, handleLocationPermission};
