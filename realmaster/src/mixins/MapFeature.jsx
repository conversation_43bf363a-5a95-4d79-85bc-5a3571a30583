import React, {Component} from 'react';
// import {deepAssign} from './helper';
import {
  StyleSheet,
  View,
  Text,
  Image,
  ScrollView,
  ActivityIndicator,
  TouchableOpacity,
  // TouchableHighlight,
  Dimensions,
  Alert,
  FlatList,
  Platform
} from 'react-native';
import { Icon, eventEmitter } from '../utils/common';
import {l10n} from '../utils/i18n';
import appConfigIns from '../config/appConfig';
// import console = require('console');
// extends React.Component
class MapFeature{
  constructor(map,name,props,onOff=false){
    //super(props);
    this.props = props;
    // console.log('++++++MapFeature',props);
    this.state = {};
    this.name = name || ('MapFeature' + Date.now());
    this.featureOn = onOff;
    this.setMap(map);
    // console.log('Feature Name-> :'+this.name)
  }
  setMap(map){
    this.map = map;
    map.addFeature(this);
  }
  getMap(){
    return this.map;
  }
  // main feature will accupay top/bottom menu, and can Not share the menu space
  isMainFeature(){
    return false;
  }
  // Convenient method for easy usage
  setState(newState,cb){
    Object.assign(this.state,newState);
    this.map.featureSetState(this,newState,cb);
  }
  // register this feature as accept onPress event
  regOnPress(){
    // console.log('regOnPress triggered',this.name)
    this.map.addOnPressFeature(this);
    this._onPressFeature = true;
  }
  // to set or release focus mode
  // when focused, top/bottom will only show this feature. onPress/onLongPress will only call this method.
  // Model/Map will be no effected
  setFocus(focus = true){
    this.focused = focus;
    if (focus){
      this.map.focusOn(this);
    }else{
      this.map.focusOff(this);
    }
  }
  toggleOnOff(onOff,needLogin){
    let isLoggedIn = appConfigIns.getAppConfig('isLoggedIn')
    // console.log('super toggleOnOff:',onOff,needLogin,isLoggedIn)
    if(onOff && needLogin && !isLoggedIn){
      console.warn('Need Login')
      return eventEmitter.emit("app.closeAndRedirectRoot",{url:'/1.5/user/login'});
    }
  }
  flashMessage(msg){
    eventEmitter.emit("flash-message",{msg:msg});
    // Alert.alert(msg);
  }
  closePopup(opt){
    // console.log(this)
    // console.log('mapFeature',this.map);
    this.map.closePopup(opt);
  }
  closeOtherModals(){
    this.map.closeOtherModals(this);
  }
  bottomButton(kn){
    let w = kn.width || styles.bottomNavLink.width;
    // let style = kn.style || {};
    // console.log(kn.k);
    const txtStyle = Object.assign({color:kn.color,fontSize:12,alignSelf:'center'}, kn.textStyle || {});
    const wrapperStyle = kn.style||{};
    return (
    <TouchableOpacity style={[styles.bottomNavLink,{width:w},wrapperStyle]}
      onPress={kn.act}
      key={"btn"+kn.n}
      disabled={kn.disabled}>
      {kn.icon && <Icon name={kn.icon} size={kn.iconSize||20} color={kn.color} style={[styles.botNavBarButton,kn.iconStyle]}/>}
      <View style={[{position:'relative'},kn.borderStyle||{}]}>
        {kn.checked &&
          <View>
            <View
              style={styles.rightTopCheckMark}
              key={'checkMark'}
            >
            </View>
            <Icon
              name={'check'} size={7} color={'white'}
              style={styles.checkMark}
              key={'forSaleCheckMarkIcon'}
            />
          </View>
        }
        <Text numberOfLines={1} style={txtStyle}>{l10n(kn.n)}</Text>
      </View>
    </TouchableOpacity>);
  }
  bottomButtons(view,btns){
    let width = Math.round(10000/btns.length)/100 + "%";
    btns.map((kn)=>{
      if (!kn.width){kn.width = width};
      view.push(this.bottomButton(kn));
    })
    return view;
  }
  // bottomSchoolButtons(btns){
  //   return (<FlatList
  //     key={'bottomButtons'}
  //     horizontal={true}
  //     data={btns}
  //     renderItem={({item})=> (
  //       <TouchableOpacity style={[styles.bottomNavLink,{width:(item.width||styles.bottomNavLink.width)}]}
  //          onPress={item.act}>
  //       {item.icn && <Icon name={item.icn} size={19} color={item.clr} style={styles.botNavBarButton}   />}
  //       <Text numberOfLines={1} style={{color:item.clr,fontSize:11}}>{l10n(item.n)}</Text>
  //       </TouchableOpacity>)}
  //     keyExtractor={item => "btn"+item.k}
  //     />)
  // }

  // Callbacks: MapFeatue implements shall overwrite the following methos as needed
  onOffView(){
    // on/off setting to show on map
    return null; // {icon/image,name,callback,on:true/false}
  }
  onPressMenu(){
    // when onPress menu popup, this menu shows up for choose
    return l10n(this.name);
  }
  closeModal(){
    // if child has modal, shall implement this method
  }
  onPress(e){
    // if child listen onPress, implment this method
    // { target: 53,
    // coordinate: { latitude: 43.51938069196711, longitude: -79.66070249676704 },
    // position: { y: 300.33441162109375, x: 289.6678771972656 } }
  }
  // call before map is closed. Do some clear up
  unmount(){
    this.map.removeFeature(this);
    if (this._onPressFeature){
      this.map.removeOnPressFeature(this);
    }
    clearTimeout(this._trackOffTimer);
  }
  // this interface shall be called when map boundary changed
  // event: {bbox,zoom}
  regionChanged(event,map){
    if (map && this.map != map){
      this.setMap(map);
    }
  }
  // render button on headers
  renderButton(){
    return null;
  }
  renderHeaderMenu(){
    return null;
  }
  // render 2nd row of buttons on headers
  renderButton2(){
    return null
  }
  renderButton3(){
    return null
  }
  // render markers that within <MapView.>
  renderOnMap(map){
    return null;
  }
  setShowPanel(opt={}){
    return null
  }
  // fixed floating objects on map
  renderOverlay(){
    return null;
  }
  // similar to modal but full screen that covers whole map
  renderFullScreenModal(){
    return null;
  }
  // modal that is absolute, covers bottom contents
  renderModal(){
    return null;
  }
  // !deprecated, prop bottomBar always active;  bottomBar
  renderBottomBar(){
    return null;
  }
  // [Ok,Cacel]
  renderFeaturePanelClose(cbOk,cbCancel){
    return <View style={{flexDirection: 'row',height:44,alignItems: 'stretch'}}>
      <TouchableOpacity key={'cbConfirm'} style={{backgroundColor:'#e03131',width: '50%'}} onPress={(e)=>{cbOk(e)}}>
        <View>
          <Text style={{color: 'white',fontSize:16,textAlign:'center',paddingTop:10}}>{l10n('OK')}</Text>
        </View>
      </TouchableOpacity>
      <TouchableOpacity key={'cbCancel'} style={{width:'50%'}} onPress={()=>{cbCancel()}}>
        <View>
          <Text style={{color: 'black',fontSize:16,textAlign:'center',paddingTop:10}}>{l10n('Close')}</Text>
        </View>
      </TouchableOpacity>
    </View>
  }
  // feature details select panel(eg. private/pub school; transit layer types)
  renderFeaturePanel(){
    return null
  }
  // internal method, no need to overwrite
  trackOff(){
    clearTimeout(this._trackOffTimer);
    this._trackOffTimer = setTimeout(()=>{
      this.state.tracksViewChanges = false;
      //console.log('++++timeout '+this.state.tracksViewChanges);
      this.setState({tracksViewChanges:false});
    },1000);
  }

  // /* TODO:
  //   register features with map, so map can turn on/off features
  //   reserved key: nm, val, tp, vals, min, max, m
  //   {[key]:
  //     {nm:FeatureName,
  //      val:defaultValueTrueFalse,
  //      key:{nm:val:key:}...}}}
  // */
  // regFeature(setting){
  //   this.featureSetting = deepAssign({},setting);
  //   this.map.regFeature(this,this.featureSetting);
  // }
  // /* TODO:
  //   to turn features on/off. Shall not change when omited from this setting.
  //   {key:val,child:{key:val}}
  //   return: shall return merged new settings
  // */
  // setFeature = (set) => {
  //   // console.log('setFeature: ',set)
  //   return this._assignFeature(this.featureSetting,set);
  // }

  // _assignFeature(orig,set){
  //   let updated = false;
  //   for (k in set){
  //     let origV = orig[k];
  //     if ("object" == typeof(origV)){
  //       origV.val = set[k];
  //       updated = true;
  //     }else{
  //       console.log("Unknown feature:" + k + " <= " + set[k]);
  //     }
  //   }
  //   return updated;
  // }
};
const topDistance = -3;
const rightDistance = -8;
const checkWidth = 14;
const styles = StyleSheet.create({
  bottomNavLink:{
    // position:'relative',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    width:'20%',
    paddingBottom:3,
  },
  botNavBarButton:{
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    height: 29,
    paddingTop: 3,
  },
  checkMark:{
    position:'absolute',
    top:topDistance,
    right:rightDistance,
  },
  rightTopCheckMark:{
    height: checkWidth,
    // alignSelf:'flex-end',
    // alignItems: 'flex-end',
    // justifyContent: 'flex-end'
    position:'absolute',
    top:topDistance,
    right:rightDistance,
    width:checkWidth,
    borderTopColor: '#81C77E',
    borderTopWidth: checkWidth/2,
    borderRightColor: '#81C77E',
    borderRightWidth: checkWidth/2,
    borderBottomColor:'transparent',
    borderBottomWidth:checkWidth/2,
    borderLeftColor: 'transparent',
    borderLeftWidth: checkWidth/2,
    // borderWidth: 2,
    // borderColor:  '#81C77E',
    backgroundColor: 'transparent',
  }
});

export default MapFeature;
