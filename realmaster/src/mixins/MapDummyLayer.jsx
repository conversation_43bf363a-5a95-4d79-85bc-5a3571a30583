/*

*/
import React, {Component} from 'react';
import {
  StyleSheet,
  View,
  Text,
  Linking,
  Image,
  Alert,
  Platform,
  ScrollView,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import MapFeature from './MapFeature';
// import { Marker, Polygon } from 'react-native-maps';
// import IconText from '../views/IconText';
// import BottomPane from '../views/BottomPane';
// import {requestStdFn} from '../RMNetwork';
import {l10n} from '../utils/i18n';
import {eventEmitter } from '../utils/common';
import ListDragPan from './mapListDragPanResponder'
import LayerMenu from '../components/LayerMenu';
// import BottomPaneWhite from '../views/BottomPaneWhite';

const { width, height } = Dimensions.get('window');
class MapDummyLayer extends MapFeature{
  constructor(map,props,onOff){
    super(map,"MapDummyLayer",props,onOff);
    this.state = {
      propFeatureOn: true,
    }
    this.featureOn = false;
    if(props.propFeatureOn != null){
      this.state.propFeatureOn = props.propFeatureOn
    }
    eventEmitter.on("map.props.fetureOn",this.recordPropFeatureOn.bind(this))
  }
  recordPropFeatureOn(val){
    this.setState({propFeatureOn:val})
  }
  onOffView(){
    // on/off setting to show on map
    return {
      icon:'layers-off',
      iconSize:25,
      iconListStyle:{marginTop:2},
      iconStyle:{marginTop:-2},
      name:l10n('Close Layers'),
      type:'MapDummyLayer',
      toggleOnOff:(p)=>{this.toggleOnOff(p)},on:this.featureOn
    }
  }
  toggleOnOff(onOff){
    if(onOff == 'off'){
      this.featureOn = true;
    }
    if (this.featureOn = !this.featureOn){
      super.toggleOnOff(this.featureOn,this.needLogin)
      this.map.onRegionChangeComplete();
    }else{
      this.setState({})
    }
  }
  regionChanged(event,map){
    if (!this.featureOn) return;
    // var self = event.self;
    // super.regionChanged(event,map);
  }
  renderModal() {
    if (!this.featureOn) return null;
    let activeFeatureName = this.map.state.activeFeatureName;
    if (activeFeatureName == 'MapDummyLayer' && !this.state.propFeatureOn){
      return (
        <ListDragPan
          id={'mapDmyList'}
          bottom={0}
          PAN_SHOW_VALUE={80}
          LIST_HEIGH={110}
        >
          <LayerMenu map={this.map} style={{marginTop: -25}}/>
        </ListDragPan>
      )
    }
  }
  closeModal(){
    this.setState({})
    this.trackOff();
  }
  renderOnMap() {
    return null;
  }
  renderMarker(){
    return null;
  }
  unmount(){
    eventEmitter.removeListener('map.props.fetureOn',this.recordPropFeatureOn);
    super.unmount();
  }
}
const styles = StyleSheet.create({
  marker:{
    width: 24,
    height: 24,
    zIndex: 1,
  }
});

export default MapDummyLayer;
