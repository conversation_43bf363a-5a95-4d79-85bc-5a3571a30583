/**
 * splash展示逻辑：
 * 1. 如果是冷启动（universalLink或者notification），或者初次安装，则不显示splash（由于splash是优先显示遮挡页面加载的，
 *  在app启动顺序中优先级较高，而获取外部URL需要时间。所以启动过程， 并不是完全不显示，可能会有300-2000ms【取决于什么时候获取到外部URL】）
 * 2. 如果是热启动，或者后台启动，则根据后台设置的时间间隔，展示splash
 * 3. 基于2，如果是通过universalLink或者notification启动，则不展示splash
 * 4. splash默认展示时间为3s，每次展示都需要拉取服务端广告并缓存，用于下次展示
 * 5. 如果有有广告内容，则将展示时间延长至5s。反之缩短回3s
 */
import React, { useEffect, useRef, useState } from 'react';
import { View, Text, Pressable, StyleSheet, Linking } from 'react-native';
import CountDown from '../components/CountDown';
import SplashBackground from '../components/SplashBackground';
import { getAds, getUpdatedAds } from '../utils/files';
import { getStatusBarHeight } from '../components/RMStatusBar'
import { l10n } from '../utils/i18n';
import { urlFix } from '../utils/business';

const SplashScreen = ({ timeDuration = 1, refresh }) => {
  const [showSplash, setShowSplash] = useState(false);
  const [adsResult, setAdsResult] = useState({});
  const [duration, setDuration] = useState(timeDuration);

  useEffect(() => {
    //如果是冷启动，或者初次安装，则不显示splash
    if (refresh === 'splash') {
      setShowSplash(false);
    } else {
      getInitalAds();
    }
  }, [refresh]);

  const getInitalAds = async () => {
    const localAds = await getAds();
    setAdsResult(localAds);
    setDuration(localAds._id === 'defaultAds' ? 1 : 5);
    // global.rmLog(`[SplashScreen.jsx:36~getInitalAds]`, localAds._id);
    setShowSplash(true);
    getUpdatedAds(localAds);
  };

  const handleSplashSkip = () => {
    setShowSplash(false);
    // global.rmLog(`[SplashScreen.jsx:42~handleSplashSkip]`, 'skip');
  }

  const handleSplashBackground = () => {
    setShowSplash(false);
    const { _id, inapp, tgt } = adsResult;

    if (inapp && tgt) {
      return Linking.openURL(tgt);
    }

    if (_id !== 'defaultAds') {
      return Linking.openURL(urlFix(`/adJump/${_id}`));
    };
  }

    // const handleSetDuration = (time) => {
    //   setDuration(time);
    // };

    const Skipper = () => {
      return (
        <Pressable style={styles.skipper} onPress={handleSplashSkip}>
          <Text style={styles.skipperText}>{l10n('Skip')}</Text>
          <CountDown
            seconds={duration}
            // syncDuration={handleSetDuration}
            onFinish={handleSplashSkip}
          />
        </Pressable>
      )
    };

    // global.rmLog(`[SplashScreen.jsx:67~render]`,showSplash,  adsResult._id, adsResult.localSrc);

    return showSplash ? (
      <SplashBackground
        onSplashPress={handleSplashBackground}
        splashImage={adsResult._id === 'defaultAds' ? adsResult.localSrc : { uri: adsResult.base64 }}
      >
        <Skipper />
      </SplashBackground>
    ) : null;
  };

  const styles = StyleSheet.create({
    skipper: {
      display: 'flex',
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      // width:60,
      height: 34,
      backgroundColor: 'rgba(0,0,0,0.5)',
      borderRadius: 5,
      paddingLeft: 10,
      paddingRight: 10,
      marginRight: 10,
      marginTop: getStatusBarHeight() + 10,
    },
    skipperText: {
      color: 'rgba(255,255,255,1)',
      fontSize: 14,
    },
  });

  export default SplashScreen;
