# MapFeature.jsx 代码逻辑详解

## 1. 文件概述

该文件定义了地图功能层的基类 `MapFeature`，为所有地图相关功能（如学校、地铁、房源等）提供统一的生命周期、状态管理、事件注册、UI渲染等基础能力。所有具体功能层均应继承自本类。

---

## 2. 主要成员与状态说明

- **构造函数**：初始化 feature 名称、状态、开关，注册到 map 实例。
- **成员变量**：
  - `map`：所属地图主实例。
  - `name`：功能层名称。
  - `featureOn`：当前功能层是否激活。
  - `state`：本地状态对象。
  - 其他如 `focused`、`_onPressFeature`、`_trackOffTimer`。

---

## 3. 主要方法功能

### 3.1 生命周期与注册
- **setMap/getMap**：设置/获取所属地图实例，并注册到地图。
- **unmount**：功能层卸载时，从地图注销自身，清理事件和定时器。

### 3.2 状态管理
- **setState**：合并新状态，并通过地图主实例统一 setState。
- **trackOff**：延迟关闭 tracksViewChanges，优化地图标记渲染性能。

### 3.3 功能层开关与聚焦
- **toggleOnOff**：切换功能层开关，支持登录校验。
- **setFocus**：设置/取消当前功能层为聚焦状态，影响 UI 显示和事件分发。
- **isMainFeature**：判断是否为主功能层（独占顶部/底部菜单）。

### 3.4 事件注册与分发
- **regOnPress**：注册当前功能层为可响应地图点击事件。
- **onPress/onPressMenu**：地图点击/菜单点击事件，子类可重写。
- **regionChanged**：地图区域变化时的回调，子类可重写。

### 3.5 UI 渲染相关
- **renderOnMap**：渲染地图上的标记/多边形等，子类实现。
- **renderButton/renderHeaderMenu/renderButton2/3**：渲染顶部按钮，子类实现。
- **renderModal/renderFullScreenModal/renderOverlay**：渲染底部弹窗、全屏弹窗、浮动层等。
- **renderBottomBar**：渲染底部栏，子类实现。
- **renderFeaturePanel/renderFeaturePanelClose**：渲染功能筛选面板及其关闭按钮。
- **bottomButton/bottomButtons**：底部按钮的通用渲染方法，支持选中态、图标、禁用等。

### 3.6 其他辅助方法
- **flashMessage**：全局消息提示。
- **closePopup/closeOtherModals**：关闭弹窗、关闭其他功能层弹窗。

---

## 4. 组件渲染与执行顺序

1. **构造函数**：初始化名称、状态、注册到地图。
2. **地图初始化时**：通过 `setMap` 注册到主地图，加入功能层管理。
3. **地图交互**：如点击、区域变化等事件，通过注册分发到对应功能层（如 `onPress`、`regionChanged`）。
4. **UI 渲染**：主页面调用各功能层的 `renderOnMap`、`renderButton`、`renderModal` 等方法，渲染对应 UI。
5. **状态变更**：通过 `setState` 统一管理，确保与主地图状态同步。
6. **功能层切换/聚焦**：通过 `toggleOnOff`、`setFocus` 控制当前激活或聚焦的功能层。
7. **卸载清理**：`unmount` 时注销自身，清理事件和定时器。

---

## 5. 依赖与扩展

- **依赖组件**：
  - `Icon`、`eventEmitter`、`l10n`、`appConfigIns` 等工具和全局事件。
  - `TouchableOpacity`、`View`、`Text` 等基础 UI 组件。
- **扩展方式**：所有地图功能层（如学校、地铁、房源等）均应继承本类，并重写相关方法实现自定义逻辑。

---

## 6. 总结

本文件为地图功能层的基类，统一了功能层的生命周期、状态、事件、UI渲染等基础能力。通过继承和重写，支持灵活扩展各种地图业务功能，便于团队协作和维护。

如需了解具体功能层实现，请查阅对应子类文件。 