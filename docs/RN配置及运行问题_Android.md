- [1. 启动react-native-webview报错 'RNCWebView'](#1-启动react-native-webview报错-rncwebview)
- [2.编译报错Task :app:configureCMakeDebug\[arm64-v8a\] FAILED](#2编译报错task-appconfigurecmakedebugarm64-v8a-failed)
- [3.编译报错RNScreens 3.14.1: Unresolved reference: attr.](#3编译报错rnscreens-3141-unresolved-reference-attr)
- [4.编译报错RNScreens 3.14.1: Unresolved reference: attr.](#4编译报错rnscreens-3141-unresolved-reference-attr)
- [5. Android studio 配置http proxy不生效](#5-android-studio-配置http-proxy不生效)
- [6. build签名signingConfig问题](#6-build签名signingconfig问题)
- [7. Map不显示](#7-map不显示)


## 1. 启动react-native-webview报错 'RNCWebView'

**Description**: 

`Invariant Violation: TurboModuleRegistry.getEnforcing(...): 'RNCWebView' could not be found. Verify that a module by this name is registered in the native`

<img src='./mdResource/RNCWebview.jpg' width=40% />

**Reference**:
 
https://github.com/react-native-webview/react-native-webview/issues/2983

**Solution**: 

react-native-webview降级到13.6.4


## 2.编译报错Task :app:configureCMakeDebug[arm64-v8a] FAILED

**Description**: 

`Task :app:configureCMakeDebug[arm64-v8a] FAILED`

<img src='./mdResource/buildCMakeDebug.jpg' width=40% />

**Reference**:
 
https://github.com/facebook/react-native/issues/37124

**Solution**: 

`android/app/gradle.properties` newArchEnabled=false

## 3.编译报错RNScreens 3.14.1: Unresolved reference: attr.

**Description**: 

`RNScreens 3.14.1: Unresolved reference: attr.`

<img src='./mdResource/RNScreensAttr.png' width=40% />

**Reference**:
 
https://github.com/software-mansion/react-native-screens/issues/1515

**Solution**: 

注释代码中这段代码，patch-package react-native-screens

## 4.编译报错RNScreens 3.14.1: Unresolved reference: attr.

**Description**: 

`RNScreens 3.14.1: Unresolved reference: attr.`

<img src='./mdResource/RNScreensAttr.png' width=40% />

**Reference**:
 
https://github.com/software-mansion/react-native-screens/issues/1515

**Solution**: 

注释代码中这段代码，patch-package react-native-screens

## 5. Android studio 配置http proxy不生效

**Description**: 

关闭代理后，仍然从代理拉取资源

<img src='./mdResource/androidHttpProxy.jpg' width=40% />

**Solution**: 

Finder前往 `/Users/<USER>/.gradle/gradle.properties` 删除代理配置

## 6. build签名signingConfig问题

**Description**: 

debug模式下，使用的production的签名，需要一个签名文件

<img src='./mdResource/androidSign.png' width=40% />

**Solution**: 

改为`signingConfig signingConfigs.debug`

## 7. Map不显示

**Description**: 

运行App后，打开Map，不展示地图。是地图key的问题

**Solution**: 

在`android/app/src/main/AndroidManifest.xml`中
```xml
 <!-- AIzaSyDgi2mHkM6kdJhpxc3ubEbvid9w7GQ4-fY -->
<meta-data
    android:name="com.google.android.geo.API_KEY"
    android:value="AIzaSyBOslrM1NF12A_SX_SnhFXKoBxmfdeD7Gc" /> <!--改为测试key-->
```
