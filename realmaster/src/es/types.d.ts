export declare type GeolibLongitudeInputValue = number | string;
export declare type GeolibLatitudeInputValue = number | string;
export declare type GeolibAltitudeInputValue = number;
export declare type GeolibGeoJSONPoint = [GeolibLongitudeInputValue, GeolibLatitudeInputValue, GeolibAltitudeInputValue?];
export declare type LongitudeKeys = 'lng' | 'lon' | 'longitude' | 0;
export declare type LatitudeKeys = 'lat' | 'latitude' | 1;
export declare type AltitudeKeys = 'alt' | 'altitude' | 'elevation' | 'elev' | 2;
export declare type GeolibInputLongitude = {
    lng: GeolibLongitudeInputValue;
} | {
    lon: GeolibLongitudeInputValue;
} | {
    longitude: GeolibLongitudeInputValue;
};
export declare type GeolibInputLatitude = {
    lat: GeolibLatitudeInputValue;
} | {
    latitude: GeolibLatitudeInputValue;
};
export declare type GeolibInputAltitude = {
    alt?: GeolibAltitudeInputValue;
} | {
    altitude?: GeolibAltitudeInputValue;
} | {
    elevation?: GeolibAltitudeInputValue;
} | {
    elev?: GeolibAltitudeInputValue;
};
export declare type UserInputCoordinates = GeolibInputLongitude & GeolibInputLatitude & GeolibInputAltitude;
export declare type GeolibInputCoordinates = UserInputCoordinates | GeolibGeoJSONPoint;
export declare type GeolibDistanceFn = (point: GeolibInputCoordinates, dest: GeolibInputCoordinates) => number;
export declare type Timestamp = number;
export declare type GeolibInputCoordinatesWithTime = GeolibInputCoordinates & {
    time: Timestamp;
};
