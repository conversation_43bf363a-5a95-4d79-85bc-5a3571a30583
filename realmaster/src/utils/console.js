import { Platform } from 'react-native';

// 保存原始的 console 方法
const originalConsole = {
  log: console.log,
  error: console.error,
  warn: console.warn,
  info: console.info,
};

// 自定义 console 方法
const enhanceConsoleMethod = (methodName) => {
  return (...args) => {
    const error = new Error();
    const stack = error.stack.split('\n');

    // 根据不同平台，解析堆栈中的调用信息（通常在第二或第三行）
    let stackLine;
    if (Platform.OS === 'ios') {
      stackLine = stack[2];
    } else if (Platform.OS === 'android') {
      stackLine = stack[3];
    } else {
      stackLine = stack[2];
    }

    // 解析堆栈行信息
    const matchResult = stackLine.match(/\((.*):(\d+):(\d+)\)$/);

    if (matchResult) {
      const [_, filePath, lineNumber, columnNumber] = matchResult;
      const fileName = filePath.split('/').pop();

      // 打印包含文件名、行号和列号的信息
      originalConsole[methodName](
        `[${fileName}:${lineNumber}:${columnNumber}]`,
        ...args
      );
    } else {
      // 如果无法解析堆栈行信息，使用默认打印
      originalConsole[methodName](...args);
    }
  };
};

// 重写 console 方法
console.log = enhanceConsoleMethod('log');
console.error = enhanceConsoleMethod('error');
console.warn = enhanceConsoleMethod('warn');
console.info = enhanceConsoleMethod('info');

// 测试打印
// console.log('This is a log message');
// console.error('This is an error message');
// console.warn('This is a warning message');
// console.info('This is an info message');
