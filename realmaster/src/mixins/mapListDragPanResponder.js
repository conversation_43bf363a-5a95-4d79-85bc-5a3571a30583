import React from 'react';
import PropTypes from 'prop-types';

import { View, Animated, PanResponder, StyleSheet, TouchableOpacity, Dimensions, Platform} from 'react-native';
import {getBottomBarHeight} from '../components/RMStatusBar';
import LayerMenu from '../components/LayerMenu';
var bottomBarHeight = getBottomBarHeight()
const {width,height} = Dimensions.get('window');



class ListDragPan extends React.Component {
  _responder = null;
  _listener = null;
  _direction = null;

  constructor(props) {
    super(props);
    var self = this;
    // the drag limit when pull up
    this.DRAG_LIMIT = 100;
    // if > show, show list
    this.DRAG_SHOW = 30;
    this.DRAG_THRESHOLD = 2;
    // height of modal list
    this.LIST_HEIGH = Math.max(height-(50*4+250),300);
    // modal style height
    if(props.LIST_HEIGH){
      this.LIST_HEIGH = props.LIST_HEIGH
    }
    this.PAN_MAX_HEIGHT = this.LIST_HEIGH+98;
    // modal style off screen height
    this.PAN_HIDE_VALUE = -this.PAN_MAX_HEIGHT+(props.PAN_SHOW_VALUE || 55);
    // modal height when fold
    this.PAN_MIN_HEIGH = this.PAN_MAX_HEIGHT+this.PAN_HIDE_VALUE
    // console.log(`LIST_HEIGH=${this.LIST_HEIGH},${this.PAN_MAX_HEIGHT},${this.PAN_HIDE_VALUE}`)
    this.deceleration = 0.997; //g factor, gravity
    this.draging = false;
    
    this.state = {
      pan:new Animated.ValueXY(),
      canDrag:true,
      showLayerMenu: props.enableLayerMenuControl ? true : false,
    }
    this._val = { x:0, y:0 }
    this.state.pan.addListener((value) => {

      this._val = value
      // this.state.pan.setValue({ x:0, y:value.y})
      // console.log('pan listened',this._val.y)
    });
    // this.state.pan.setOffset({x:0,y:this.DRAG_THRESHOLD});
    if(!this.props.openListView){
      this.DRAG_LIMIT = Math.max(this.DRAG_LIMIT,this.LIST_HEIGH)
    } else {
      this.LIST_HEIGH = this.DRAG_LIMIT
    }
    if(props.DRAG_LIMIT){
      this.DRAG_LIMIT = props.DRAG_LIMIT || 100
    }
  }

  UNSAFE_componentWillMount() {
    var self = this
    this._responder = PanResponder.create({
      // Should child views be prevented from becoming responder on first touch?
      // onStartShouldSetPanResponderCapture: (evt, gestureState) => true,

      // - Does this view want to become responder on the start of a touch?
      // onStartShouldSetPanResponder: () => true,

      onPanResponderGrant: (e, gestureState) => {
        // console.log('onPanResponderGrant',gestureState)
        // if(!this.state.canDrag){
        //   return console.error('last animation not done yet!')
        // }
        // this.setState({canDrag:false},()=>{
        let pan = this.state.pan

        pan.stopAnimation((value)=>{
          // console.log('pan stop:',pan.y._value+pan.y._offset,value)
        })
        this.state.pan.setOffset({x: 0, y:( pan.y._value+pan.y._offset)});
        // })
	    	this.state.pan.setValue({x: 0, y: 0});
        // 当手指按下时，隐藏LayerMenu
        this.handleLayerMenuDragStart(true);
        
        if(this.props.onGrant){
          this.props.onGrant(e,gestureState)
        }
      },
      isOpen:()=>{
        // console.log(this.state.pan.getTranslateTransform())
      },
      // - Called for every touch move on the View when it is not the responder
      //   does this view want to "claim" touch responsiveness?
      onMoveShouldSetPanResponder: (_, gestureState) => {
        const { dx, dy } = gestureState
        let panResponder = (dy > 2 || dy < -2)
        // console.log('panResponder',panResponder)
        // TODO: return false if scrolling child view
        // console.log(this.isOpen())
        return panResponder
        // return this.state.pan.y.value;
        return panResponder
      },
      // Should child views be prevented from becoming responder on first touch?
      // true = child do nothing, false = child can be responder
      // onMoveShouldSetPanResponderCapture: (_, gestureState) => {
      //   const { dx, dy } = gestureState
      //   let panResponderCap = (dy > 2 || dy < -2) //|| (dx > 2 || dx < -2)
      //   return true
      //   console.log('panResponderCap',dy,panResponderCap,this.state.pan.y._value)
      //   if(this.state.pan.y._value == -396){
      //     return false
      //   }
      //   return panResponderCap
      // },

      onPanResponderMove: (_e, gesture) => {
        // let {
        //   panY,
        //   yBounds,
        //   overshootY,
        // } = this.props;

        // if (this.props.onPanResponderMove) {
        //   this.props.onPanResponderMove(_, { dx, dy, x0, y0 });
        // }

        // let [yMin, yMax] = yBounds;
        // console.log(this.state.canDrag)
        // if(!this.state.canDrag){
        //   return console.error('move last animation not done yet!')
        // }
        this.handleResponderMove(this.state.pan.y, gesture);
        this.draging = true
        return
        // console.log(Math.abs(gesture.dy))
        // return
        // NOTE: this showListViewThrottle  does nothing if no openListView passed
        if ( (Math.abs( gesture.dy ) > this.DRAG_THRESHOLD) &&
             (Math.abs( gesture.dy ) <= this.DRAG_LIMIT ) )
        {
          if(Math.abs( gesture.dy ) > this.DRAG_SHOW && gesture.dy < -1){
            // TODO: return and show list, interrupt animation
            self.showListViewThrottle()
          }
          if(Math.abs( gesture.dy ) > this.DRAG_SHOW && gesture.dy > 2){
            // console.log('+++++',gesture.dy)
            // TODO: self.hideBottomPan
          }
          console.log('this.state.pan.y',this.state.pan.y._value)
          return Animated.event([
            null, { dy: this.state.pan.y }
          ],{useNativeDriver: false})(_e, gesture)
        }
        // else if ((Math.abs( gesture.dy ) > this.DRAG_LIMIT )) {
        //   return Animated.event([
        //     null, { dy: this.state.pan.y }
        //   ])(e, gesture)
        // }
      },

      onPanResponderRelease: (_, { vx, vy, dx, dy }) => {
        // if(self.props.openListView){
        //   Animated.spring(
        //     this.state.pan, // Auto-multiplexed
        //     {
        //       friction: 10,
        //       useNativeDriver:true,
        //       toValue: { x: 0, y: 0 }
        //     } // Back to zero
        //   ).start();
        //   return
        // }
        
        // 根据最终位置决定是否显示LayerMenu
        // 当面板位置接近初始状态时显示LayerMenu，否则隐藏
        const currentPanY = this.state.pan.y._value + this.state.pan.y._offset;
        const shouldShowLayerMenu = currentPanY >= -30; // 阈值可调整
        this.handleLayerMenuDragEnd(shouldShowLayerMenu);
        
        if(this.props.onRelease){
          this.props.onRelease(_)
        }
        this.draging = false
        let {
          panX,
          panY,
          xBounds,
          yBounds,
          overshootX,
          overshootY,
          horizontal,
          vertical,
          lockDirection,
          xMode,
          yMode,
          snapSpacingX,
          snapSpacingY,
        } = this.props;

        // let cancel = false;
        // const dir = this._direction;

        // let [yMin, yMax] = yBounds;
        // if (this.props.onReleaseY) {
        //   cancel = this.props.onReleaseY({ vx, vy, dx, dy }) === false;
        // }

        this.handleResponderRelease(
          this.state.pan,
          dy,
          vy,//velocity
          yMode,
          20
        );

      },
    });
  }
  
  /**
   * 处理图层菜单拖拽开始事件
   * 当拖拽开始且启用图层菜单控制时，自动隐藏图层菜单
   * @param {boolean} isDragging - 是否正在拖拽
   */
  handleLayerMenuDragStart = (isDragging) => {
    if (isDragging && this.props.enableLayerMenuControl) {
      this.hideLayerMenu();
    }
  }

  /**
   * 处理图层菜单拖拽结束事件
   * 根据参数决定是否显示或隐藏图层菜单
   * @param {boolean} shouldShow - 是否应该显示图层菜单
   */
  handleLayerMenuDragEnd = (shouldShow) => {
    if (this.props.enableLayerMenuControl) {
      if (shouldShow) {
        this.showLayerMenuFunc();
      } else {
        this.hideLayerMenu();
      }
    }
  }

  /**
   * 隐藏图层菜单
   * 通过设置state.showLayerMenu为false来隐藏图层菜单组件
   */
  hideLayerMenu = () => {
    if (!this.props.enableLayerMenuControl) return;
    
    this.setState({ showLayerMenu: false });
  }

  /**
   * 显示图层菜单
   * 通过设置state.showLayerMenu为true来显示图层菜单组件
   */
  showLayerMenuFunc = () => {
    if (!this.props.enableLayerMenuControl) return;
    this.setState({ showLayerMenu: true });
  }

  /**
   * 渲染图层菜单组件
   * 根据配置和状态决定是否渲染LayerMenu组件
   * @returns {React.ReactElement|null} 返回LayerMenu组件或null
   */
  renderLayerMenu() {
    if (!this.props.enableLayerMenuControl || !this.state.showLayerMenu) {
      return null;
    }
    if (!this.props.layerMenuMap) {
      return null;
    }
    return (
      <View key="layerMenuContainer">
        <LayerMenu 
          key="layerMenuComponent"
          map={this.props.layerMenuMap} 
          style={this.props.layerMenuStyle || {}}
        />
      </View>
    );
  }
  
  /**
   * 计算面板完全展开时的目标Y位置
   * 根据PAN_SHOW_VALUE配置返回不同的展开位置
   * @returns {number} 返回目标Y坐标位置（负值）
   */
  getTargetShowPosition() {
    if (this.props.PAN_SHOW_VALUE) {
      return -(this.LIST_HEIGH - 45);
    } else {
      return -this.LIST_HEIGH;
    }
  }

  showListViewThrottle(){
    var self = this;
    clearTimeout(self.listViewTimeout)
    this.listViewTimeout = setTimeout(() => {
      // self.showListView()
      if(self.props.openListView){
        self.props.openListView()
      }
    }, 100);
  }
  // y0 - the screen coordinates of the responder grant
  // dy - accumulated distance of the gesture since the touch started
  // vy - current velocity of the gesture
  // moveY - the latest screen coordinates of the recently-moved touch
  // user can drag  up and down in one drag
  handleResponderMove(anim, {dy,vy,y0,moveY}) {
    let delta = dy
    // min=-200, max=0
    let min = -1*this.LIST_HEIGH;
    let max = 0;
    let val = anim._offset + delta;
    // console.log(this.draging,anim._value)
    // if(!this.draging && anim._value == -this.LIST_HEIGH){
    //   // console.log('!!!initial draging down')
    //   // val = anim._offset + delta +anim._value;
    // }
    if (val > max) {
      val = max + (val - max) / 10;
    }
    if (val < min) {
      val = min - (min - val) / 10;
    }
    val = val - anim._offset;
    // console.log(`max=${max}\tval=${val}\tdelta=${delta}\t_val=${anim._value}`) //PAN_MIN_HEIGH,delta,
    anim.setValue(val);
    // console.log('++++transform',this.state.pan.getLayout().top._value)
    // console.log('++++transform',this.state.pan.getTranslateTransform()[1].translateY._value)
  }
  handleResponderMove2(anim, delta) {
    // console.log('anim._value',Math.abs(anim._value) >= this.PAN_MIN_HEIGH+this.LIST_HEIGH/2)
    let min = this.PAN_MIN_HEIGH; //48
    let max = this.DRAG_LIMIT;
    // correct delta value
    // we are in open state
    // if(Math.abs(anim._value) >= this.PAN_MIN_HEIGH+this.LIST_HEIGH/2){
    //   delta = -this.LIST_HEIGH+delta
    //   max = this.LIST_HEIGH
    // }
    let isUp = delta < -1;
    if(isUp && Math.abs(anim._value) >= this.PAN_MIN_HEIGH+this.LIST_HEIGH/2){
      delta = -this.LIST_HEIGH+delta
      max = this.LIST_HEIGH
    }
    let deltaAbs = Math.abs(delta)
    let val = anim._offset + deltaAbs;
    if (val > max) {
      val = max + (val - max) / 11;
    }
    if (!isUp && val < min) {
      val = min - (min - val) / 11; //distance to move min-(0->5)  => 48-46
      // console.log('val=',val)
    }
    val = val - anim._offset;
    val = isUp?(-1*val):(val-41.5);
    // if not isUp, 41-44 to 0->10
    // console.log(`${this.PAN_MIN_HEIGH}\tmax=${max}\tval=${val}\tdelta=${delta}\t_val=${anim._value}`) //PAN_MIN_HEIGH,delta,
    // if(val>20){
    //   val = 20
    // }
    // if(anim._value <= -this.LIST_HEIGH){
    //   return
    // }
    anim.setValue(val);
  }

  handleResponderRelease(
    anim,
    dy,
    velocity,
    mode,
    snapSpacing
  ) {
    var self = this;
    // return
    this.setState({canDrag:false})

    // Animated.spring(
    //   this.state.pan, // Auto-multiplexed
    //   {
    //     friction: 10,
    //     useNativeDriver:true,
    //     toValue: { x: 0, y: 0 }
    //   } // Back to zero
    // ).start();
    // let velocity2 = Math.min(Math.abs(velocity),1.6)
    // console.log(`anim._val=${anim.y._value}\tdy=${dy}\tvelo=${velocity}}`)
    // return
    anim.flattenOffset();
    // 计算正确的面板展开位置用于判断
    const targetShowY = this.getTargetShowPosition();
    if(anim.y._value < targetShowY){
      // console.log('++++ <= drag show over max len',targetShowY, anim.y._offset)
      // anim.setOffset({x:0,y:(anim.y._value + anim._offset || 0)});
      if(this.props.openListView){
        self.showListViewThrottle()
        return this.hideList(anim,velocity)
      }
      this.showList(anim,velocity)
      // anim.setValue({x:0,y:-this.LIST_HEIGH})
    } else if(anim.y._value >= 0){
      this.hideList(anim,velocity)
    } else {
      // we are in middle of drag, >0 but < DRAG_SHOW
      // console.log('+++++here calc what to do')
      // 计算正确的最小值（面板完全展开位置）
      const minValue = this.getTargetShowPosition();
      this.handleSnappedScroll(
        anim,
        minValue,
        0,
        velocity,
        snapSpacing,
        dy
      );

    }
  }
  showList(anim,velocity,opt2){
    // 计算面板完全展开时的正确位置
    const targetY = this.getTargetShowPosition();
    let cb = ({finished})=>{
      anim.setValue({x:0,y:targetY})
      // anim.setOffset({x:0,y:(anim.y._value + (anim.y._offset || 0))});
      this.setState({canDrag:true})
      // console.log('finished show:',finished, 'anim.y._value:',anim.y._value, ' -this.LIST_HEIGH:',-this.LIST_HEIGH,' anim.y._offse:', anim.y._offset)
      // console.log('++++transform',this.state.pan.getTranslateTransform()[1].translateY._value)
    }
    let opt = {
      friction: 10,
      toValue: { x: 0, y: targetY } ,
      useNativeDriver:true,
      velocity,
    }
    if(opt2=='from'){
      // Animated.decay(anim, {
      //   // ...this.props.momentumDecayConfig,
      //   velocity,
      //   useNativeDriver:true,
      // }).start(() => {
      //   // anim.removeListener(this._listener);
      //   // anim.setValue({x:0,y:0})
      //   // this.setState({canDrag:true})
      //   // console.log()
      // });
      // return
    }
    // setTimeout(() => {
    //   anim.setValue({x:0,y:-this.LIST_HEIGH})
    // }, 800);
    // 检查LayerMenu状态 - 面板完全展开时应隐藏LayerMenu
    this.handleLayerMenuDragEnd(false);
    Animated.spring(anim, opt).start(cb);
  }
  hideList(anim,velocity,opt){
    // console.log('++++ >= drag show return to 0',opt)
    // setTimeout(() => {
    //   anim.setValue({x:0,y:0})
    // }, 800);
    // 检查LayerMenu状态 - 面板回到初始位置时应显示LayerMenu
    this.handleLayerMenuDragEnd(true); // 面板收起，显示LayerMenu
    Animated.spring(anim, {
      useNativeDriver:true,
      friction: 10,
      toValue: { x: 0, y:0 },
      velocity,
    }).start(({finished})=>{
      anim.setValue({x:0,y:0})
      this.setState({canDrag:true})
      
      // console.log('finished hide:',finished, anim.y._value)
    });
  }


  handleSnappedScroll(anim, min, max, velocity, spacing, dy) {
    // console.log('-----',anim.y._value,min,max,velocity,spacing)
    // Animated.spring(anim, {
    //   toValue: {x:0,y:0},
    //   velocity: velocity,
    //   useNativeDriver:true,
    // }).start(()=>{
    //   anim.setValue({x:0,y:0})
    //   this.setState({canDrag:true})
    // });
    // console.log('-----val',anim.y._value, ' velocity:',velocity, ' spacing:',spacing)
    // return
    let endX = this.momentumCenter(anim.y._value, velocity, spacing);
    endX = Math.max(endX, min);
    endX = Math.min(endX, max);
    let endY;
    let minVelocity = 1.3806;
    let maxVelocity = 3.3;
    let isSlow = false
    if(Math.abs(velocity)<minVelocity){
      isSlow = true
      velocity = velocity>0?minVelocity:-minVelocity
    }
    if(Math.abs(velocity)>maxVelocity){
      isSlow = false
      velocity = velocity>0?maxVelocity:-maxVelocity
    }
    if(velocity<0 && isSlow && dy<-1*this.LIST_HEIGH/4 && !this.props.openListView){
      // console.log('here1')
      return this.showList(anim,velocity)
    }
    if(velocity>0 && isSlow && dy>this.LIST_HEIGH/5 && !this.props.openListView){
      // console.log('here2')
      return this.hideList(anim,velocity)
    }
    const bounds = [endX - spacing / 2, endX + spacing / 2];
    const endV = this.velocityAtBounds(anim.y._value, velocity, bounds);
    // console.log('++++++bnd: [',bounds[0],bounds[1],']','(',velocity,'-vs-',endV,')',-this.LIST_HEIGH/2,bounds[0]<(-this.LIST_HEIGH/2))
    let isShow = bounds[0]<-60 && bounds[0]<(-this.LIST_HEIGH/2)
    if(isShow){
      if(this.props.openListView){
        this.props.openListView()
        this.hideList(anim,3)
        return
      }
      // this.showList(anim,velocity,'from')
      // console.log('++++++calc show')
      // 计算面板完全展开时的正确位置
      endY = this.getTargetShowPosition();
    } else {
      // this.hideList(anim,velocity)
      // console.log('++++++calc hide')
      endY = 0
    }
    // return
    this._listener = anim.addListener(( value ) => {
      value = value.y
      // 计算正确的面板展开位置用于边界判断
      const targetShowY = this.getTargetShowPosition();
      // console.log('value: ',value,this.LIST_HEIGH)
      if(value < targetShowY-30){
        // console.log('---- listen failed to catrch value show; val=',value)
        return this.showList(anim,endV)
      }
      if(value > this.LIST_HEIGH+30){
        // console.log('---- listen failed to catrch value hide; val=',value)
        return this.hideList(anim,endV)
      }
      if (value > bounds[0] && value < bounds[1]) {
        // console.log('000000 bounce here spring')
        // 检查LayerMenu状态
        const shouldShowLayerMenu = endY >= -30;
        this.handleLayerMenuDragEnd(shouldShowLayerMenu);
        Animated.spring(anim, {
          toValue: {x:0,y:endY},
          velocity: endV,
          useNativeDriver:true,
        }).start(()=>{
          anim.setValue({x:0,y:endY})
          // anim.setOffset({x:0,y:(anim.y._value + (anim.y._offset || 0))});
          this.setState({canDrag:true})
        });
      }
    });

    Animated.decay(anim, {
      // ...this.props.momentumDecayConfig,
      velocity:{x:0,y:velocity},
      useNativeDriver:true,
      deceleration:this.deceleration,
    }).start(() => {
      // if(isShow){
      //   this.showList(anim,velocity,'from')
      // } else {
      //   this.hideList(anim,velocity)
      // }
      // this.setState({canDrag:true})
      anim.removeListener(this._listener);
      // anim.setValue({x:0,y:0})
      // this.setState({canDrag:true})
    });
  }

  // (x +- spacing), closest int rounded, eg: closestCenter(-183.11 ,100)=200, closestCenter(-143.11 ,100)=100
  closestCenter(x, spacing) {
    const plus = x % spacing < spacing / 2 ? 0 : spacing;
    return Math.round(x / spacing) * spacing + plus;
  }

  momentumCenter(x0, vx, spacing) {
    let t = 0;
    let x1 = x0;
    let x = x1;
    let maxRound = 1000
    while (maxRound>0) {
      t += 16; //16 ms for 60fps animation
      x =
        x0 +
        (vx / (1 - this.deceleration)) *
          (1 - Math.exp(-(1 - this.deceleration) * t));
      // physics: x = ut+1/2 vt^2
      if (Math.abs(x - x1) < 0.1) {
        x1 = x;
        break;
      }
      x1 = x;
      maxRound--
    }
    // console.log('++=total',1000-maxRound)
    return this.closestCenter(x1, spacing);
  }

  velocityAtBounds(x0, vx, bounds) {
    let t = 0;
    let x1 = x0;
    let x = x1;
    let vf;
    let rounds = 1000
    while (rounds>0) {
      t += 16;
      x =
        x0 +
        (vx / (1 - this.deceleration)) *
          (1 - Math.exp(-(1 - this.deceleration) * t));
      vf = (x - x1) / 16;
      if (x > bounds[0] && x < bounds[1]) {
        break;
      }
      if (Math.abs(vf) < 0.1) {
        break;
      }
      x1 = x;
      rounds--
    }
    // console.log('+++totall rounds left',rounds)
    return vf;
  }

  //
  // componentWillUnmount() {
  //
  // },
  //
  // componentDidUnmount() {
  //
  // },
  toggleListView = (e) => {
    if(this.props.openListView){
      this.props.openListView()
    } else {
      // console.log('clicked!!',-1*this.state.pan.y._value)
      // -1*this.state.pan.y._value
      if(-1*this._val.y >= this.LIST_HEIGH/1.5){
        this.hideList(this.state.pan,2.0,{force:1})
      } else {
        this.showList(this.state.pan,2.0,{force:1})
      }
    }
  }
  render() {
    let panStyle = {
      transform: this.state.pan.getTranslateTransform()
      // height:68+this._val.y
    }
    // console.log('++++transform',this.state.pan.getLayout().top._value)
    panStyle.height = this.PAN_MAX_HEIGHT;
    panStyle.marginBottom = this.PAN_HIDE_VALUE//-this.state.pan.getLayout().top._value;
    let holderStyle = {
      width:'100%',
      flexDirection:'column',
      alignItems:'flex-start',
      // position:'absolute',
      // backgroundColor:'red',
      // marginTop:-3,
      justifyContent:'flex-start',paddingLeft:0,paddingRight:0
    }
    if(Platform.OS == 'ios'){
      holderStyle.zIndex = 105;
    }
    let dragPanId = this.props.id||this.props.key||'randId'+Date.now()
    let renderLayerMenu = null;
    if (this.props.enableLayerMenuControl && this.state.showLayerMenu){
      renderLayerMenu = this.renderLayerMenu();
    }
    // return null
    return (<Animated.View
        id={dragPanId}
        key={dragPanId}
        // {...this._responder.panHandlers}
        style={[styles.loadingTipWrapperNew,{bottom:this.props.bottom+bottomBarHeight},panStyle]}>
        {/* TODO: add on drag to this */}
        <View key={dragPanId+'dragHolder'}
          {...this._responder.panHandlers}
          style={[{
            height:55,
            // backgroundColor:'transparent',
            // backgroundColor:'pink',
            width:'100%',
            zIndex:100,
            justifyContent:'flex-start',
            // marginTop:-3,
            // alignContent:'center',
          }]}
          >
          <View
            // {...this.panResponder.panHandlers}
            style={{
              alignSelf:'center',
              // backgroundColor:'red',
              height:13,
              width:68,
            }}
            key={dragPanId+'dragBar'}
          >
            <View style={{alignSelf:'center',width:50,height:6,marginTop:5,backgroundColor:'#D3D3D3',borderRadius:3}}></View>
          </View>
          <TouchableOpacity onPress={(e)=>{
              this.toggleListView(e)
            }}
            style={{
              zIndex:105,
              backgroundColor:'transparent',
              // backgroundColor:'green',
              marginTop:-10,
              width:'100%',
              height:50
            }}
            key={dragPanId+'dragBtn'}
          >
          </TouchableOpacity>
        </View>
        <View key={dragPanId+'contentsOfBottom'} style={holderStyle}>
          {this.props.renderTopContent && (
            <View key={dragPanId+'topContent'}>
              {this.props.renderTopContent()}
            </View>
          )}
          {renderLayerMenu && (
            <View key={dragPanId+'layerMenu'} style={{width: '100%'}}>
              {renderLayerMenu}
            </View>
          )}
          {this.props.children}
        </View>
      </Animated.View>
    )
  }
}
var styles = StyleSheet.create({
  loadingTipWrapperNew:{
    shadowColor:'#c1c1c1',
    shadowOffset:{
      width:3,
      height:-3,
    },
    shadowOpacity:0.7,
    shadowRadius:3,
    flexDirection: 'column',
    alignItems:'center',
    justifyContent:'flex-start',
    backgroundColor:'white',
    position:'absolute',
    bottom:50,
    left:0,
    right:0,
    zIndex:15,
    borderRadius:13,
    // borderBottomColor:'#f1f1f1',
    // borderBottomWidth:1,
   },
})
export default ListDragPan;
