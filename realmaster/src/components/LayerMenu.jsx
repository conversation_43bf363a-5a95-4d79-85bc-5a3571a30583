import React from 'react';
import { View, ScrollView, TouchableOpacity, Text, StyleSheet } from 'react-native';
import { Icon, eventEmitter } from '../utils/common';

const BUTTON_MARGIN_RIGHT = 8; // 对应 styles.layerMenuBtn 的 marginRight
/**
 * 地图图层菜单组件
 * 用于显示和控制地图上不同图层的显示/隐藏状态
 * @class LayerMenu
 * @extends {React.Component}
 */
class LayerMenu extends React.Component {
  /**
   * 构造函数
   * @param {Object} props - 组件属性
   * @param {Object} props.map - 地图对象，包含所有图层特性
   * @param {Object} [props.style] - 自定义样式
   */
  constructor(props) {
    super(props);
    this.scrollViewRef = React.createRef();
    this.buttonWidths = {}; // 存储每个按钮的实际宽度
    this.containerWidth = 0; // 滚动容器宽度
  }
  /**
   * 获取图层菜单数据
   * 根据预定义的图层配置，从地图对象中提取对应的图层信息
   * @returns {Array} 返回图层数据数组，每个元素包含isActive、feature、onOff属性
   */
  getLayerMenuData() {
    if (!this.props.map || !this.props.map.allFeatures) return [];
    
    const layerConfig = ['MapProps', 'MapDummyLayer', 'Schools', 'MapTransit', 'MapCoop', 'MapStigmatized'];
    return layerConfig.map(config => {
      const feature = this.props.map.allFeatures.find(f => f.name === config);
      if (!feature) {
        return { isActive: false, feature: null };
      }
      const onOff = feature.onOffView ? feature.onOffView() : null;
      const isActive = onOff ? onOff.on : false;
      return {
        isActive,
        feature,
        onOff
      };
    }).filter(layer => layer.feature !== null);
  }

  /**
   * 计算选中按钮应该滚动到的位置（居中显示）
   * @param {number} activeIndex - 选中按钮的索引
   * @param {Array} otherLayers - 其余图层按钮
   * @returns {number} 目标滚动位置
   */
  calculateScrollPosition = (activeIndex, otherLayers) => {
    // 计算到选中按钮之前的累计宽度
    let targetX = 0;
    for (let i = 0; i < activeIndex; i++) {
      const layerName = otherLayers[i].feature.name;
      targetX += this.buttonWidths[layerName] || 0;
    }
    // 当前选中按钮的宽度
    const currentButtonWidth = this.buttonWidths[otherLayers[activeIndex].feature.name] || 0;
    
    // 计算总宽度
    const totalWidth = otherLayers.reduce((sum, layer) => {
      return sum + (this.buttonWidths[layer.feature.name] || 0);
    }, 0);
    // 确保不超出滚动边界
    const maxScroll = Math.max(0, totalWidth - this.containerWidth);
    let scrollX;
    // 检查是否是最后一个按钮
    const isLastButton = activeIndex === otherLayers.length - 1;
    if (isLastButton) {
      // 如果是最后一个按钮，确保整个按钮都可见
      scrollX = Math.max(0, totalWidth - this.containerWidth);
    } else {
      // 其他按钮使用居中显示
      const centerOffset = this.containerWidth / 2 - currentButtonWidth / 2;
      scrollX = Math.min(maxScroll, Math.max(0, targetX - centerOffset));
    }
    return scrollX;
  }

  /**
   * 滚动到选中按钮位置
   * 在图层选择改变后自动滚动到选中按钮居中位置
   */
  scrollToActiveButton = () => {
    if (!this.scrollViewRef.current) return;
    const layerMenuData = this.getLayerMenuData();
    // let activeIndex = -1;
    const otherLayers = layerMenuData.filter(layer => layer.feature.name !== 'MapProps');
    const activeIndex = otherLayers.findIndex(layer => layer.isActive);
    if (activeIndex >= 0 && this.containerWidth > 0) {
      // 检查是否所有按钮宽度都已测量
      const allWidthsMeasured = otherLayers.every(layer => 
        this.buttonWidths[layer.feature.name] !== undefined
      );
      if (allWidthsMeasured) {
        const scrollX = this.calculateScrollPosition(activeIndex, otherLayers);
        if (this.scrollViewRef.current) {
          this.scrollViewRef.current.scrollTo({
            x: scrollX,
            y: 0,
            animated: false
          });
        }
      }
    }
  }

  /**
   * 组件更新后的回调
   * 当图层状态改变时，自动滚动到选中按钮
   */
  componentDidUpdate(prevProps) {
    this.scrollToActiveButton();
  }

  /**
   * 获取滚动容器布局信息
   * 用于计算滚动位置
   */
  onScrollContainerLayout = (event) => {
    this.containerWidth = event.nativeEvent.layout.width;
  }

  onButtonLayout = (layerName, event) => {
    const { width } = event.nativeEvent.layout;
    this.buttonWidths[layerName] = width + BUTTON_MARGIN_RIGHT;
    // 当按钮宽度更新后，检查是否需要重新滚动
    // this.scrollToActiveButton();
  }

	/**
   * 处理图层选择事件
   * 当用户点击图层按钮时调用此方法来切换图层的显示状态
   * @param {Object} layer - 被选择的图层对象
   * @param {Object} layer.feature - 图层特性对象
   * @param {Object} layer.onOff - 图层开关状态对象
   */
	onLayerSelect(layer) {
    if (!this.props.map || !layer.feature) return;
    const item = {
      feature: layer.feature,
      onOff: layer.onOff
    };
    if (!item.onOff) return;
    if (this.props.map.selectLayer) {
      this.props.map.selectLayer(item);
    }
  }

  /**
   * 渲染单个图层按钮
   * @param {Object} layer - 图层对象
   * @param {boolean} layer.isActive - 图层是否激活
   * @param {Object} layer.feature - 图层特性
   * @param {Object} layer.onOff - 图层开关配置
   * @returns {React.ReactElement} 返回图层按钮的React元素
   */
  renderLayerButton = (layer) => {
    let isActive = layer.isActive;
    const buttonStyle = isActive ? styles.highlightBgc : styles.normalBgc;
    const textColor = isActive ? '#007AFF' : '#666';
    const iconColor = isActive ? '#007AFF' : '#999';
    
    return (
      <TouchableOpacity
        key={layer.feature.name}
        onPress={() => {
					if (this.onLayerSelect) {
						this.onLayerSelect(layer);
					}
				}}
      >
        <View 
          style={[styles.layerMenuBtn, buttonStyle]}
          onLayout={(event) => this.onButtonLayout(layer.feature.name, event)}
        >
          <Icon name={layer.onOff.icon} size={15} color={iconColor}/>
          <Text style={[styles.buttonText, {color: textColor}]} numberOfLines={1}>
            {layer.onOff.name}
          </Text>
        </View>
      </TouchableOpacity>
    );
  }

  /**
   * 渲染组件
   * 渲染图层菜单界面，包括属性图层按钮和其他图层的滚动列表
   * @returns {React.ReactElement|null} 返回React元素或null
   */
  render() {
    const layerMenuData = this.getLayerMenuData();
    if (!layerMenuData || layerMenuData.length === 0) return null;
    const [propertyLayer, ...otherLayers] = layerMenuData.reduce((acc, layer) => {
      if (layer.feature.name === 'MapProps') {
        acc.unshift(layer);
      } else {
        acc.push(layer);
      }
      return acc;
    }, []);
    const containerStyle = this.props.style ? [styles.layerMenuContainer, this.props.style] : styles.layerMenuContainer;
    return (
      <View key={'layerMenuRow'} style={containerStyle}>
        {propertyLayer && this.renderLayerButton(propertyLayer)}
        <View style={styles.separator} />
        <View style={styles.scrollContainer} onLayout={this.onScrollContainerLayout}>
          <ScrollView
            ref={this.scrollViewRef}
            horizontal={true}
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.scrollContent}
          >
            {otherLayers.map(layer => this.renderLayerButton(layer))}
          </ScrollView>
        </View>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  layerMenuContainer: {
    width: '100%',
    paddingHorizontal: 15,
    height: 35,
    flexDirection: 'row',
    alignItems: 'center',
  },
  separator: {
    width: 0.5,
    height: 20,
    backgroundColor: '#f5f5f5',
  },
  scrollContainer: {
    flex: 1,
    paddingLeft: 8
  },
  layerMenuBtn: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 18,
    marginRight: 8,
    flexDirection:'row',
    alignItems:'center',
  },
  highlightBgc: {
    backgroundColor: '#E1EFFF',
  },
  normalBgc: {
    backgroundColor: '#f5f5f5',
  },
  buttonText:{
    fontSize: 12,
    marginLeft: 4,
  },
  scrollContent:{
    alignItems: 'center',
    flexDirection: 'row',
  }
});

export default LayerMenu; 