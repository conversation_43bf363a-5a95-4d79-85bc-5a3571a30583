import {Linking,Alert} from 'react-native';
import {l10n} from '../../utils/i18n';
// import {getServerDomain,getServerProtocol} from '../../RMNetwork';
import serverDomainIns from '../../utils/serverDomain'
import { eventEmitter } from '../../utils/common';
import { mapUtil } from '../../utils/business';
// import {getAppConfig} from '../../RMSystem'
import appConfigIns from '../../config/appConfig'
import analytics from '@react-native-firebase/analytics';
// trackEventOnGoogle=(category,action, label,value)->
// Fire base log custom event, referd gtag from appWeb
var stigmaRegex = /map\/webMap.*stigma/;
var transitRegex = /map\/transit/;
var coopRegex = /map\/coop/;

async function FBLogEvent(category,action,label,value) {
  let params = {
    'event_category': category
  }
  if(label){
    params.event_label = label;
  }
  if(value){
    params.value = value;
  }
  // console.log(action,params);
  //TODO:
  // await analytics().logEvent(action, params);
}
// FBLogEvent('nativeAutoComplete','gotoProp')
// TODO: if prop.login, close-and-redirect root /login
async function gotoProp(prop,lang,closePopup) {
  var self = this;
  // console.log('###gotoProp',prop)
  FBLogEvent('nativeAutoComplete','gotoProp')

  var propId;
  if (/^RM/.test(prop.id)) {
    propId = prop.id;
  } else if (prop.p == 'project') {
    propId = prop.pid;
  }else {
    propId = prop._id;
  }
  var base = "/1.5/prop/detail/inapp?lang=";
  if (prop.isProj || prop.tp1 || prop.p == 'project') {
    base = '/1.5/prop/projects/detail?inframe=1&lang='
  }
  if (Object.keys(prop).length < 2) {
    // console.error('Error: '+JSON.stringify(prop));
  }
  var url = base+lang+"&id="+propId;
  if (prop.inapp == false) {
    Linking.openURL(prop.tgt);
    return;
  }
  // TODO: if user perform login,reset hist prop.login
  var cb = (val)=>{
    // console.log('gotoprop cb val: ',val);
    if (val == ':cancel') {
      return;
    }
    if (/^redirect|^cmd-redirect:/.test(val)) {
      // return window.location = val.split('redirect:')[1]
      var url = val.split('redirect:')[1];
      // console.log('close and redirect from propDetail: '+url)
      if (/login/.test(url)) {
        var opt = {
          tp:'closeAndRedirectRoot',
          url: serverDomainIns.getFullUrl(url),
        }
        // console.log('++closeAndRoot')
        return eventEmitter.emit("app.message",{msg:JSON.stringify(opt),cb});
      }
      closePopup(url);
    }
    if (/^loc/.test(val)) {
      // return alert(val);
      var url = '/1.5/mapSearch?'+val
      closePopup(url);
    }
  }
  var opt = {
    hide:false,
    sel:'#callBackString',
    tp:'pageContent',
    // noClose:true,
    title:l10n('RealMaster'),
    url: serverDomainIns.getFullUrl(url),
  }
  eventEmitter.emit("app.message",{msg:JSON.stringify(opt),cb:cb});
}

function gotoSch(sch,closePopup,schoolAction) {
  var self = this;
  FBLogEvent('nativeAutoComplete','gotoSch')
  let isLoggedIn = appConfigIns.getAppConfig('isLoggedIn')
  if(!isLoggedIn){
    return eventEmitter.emit("app.closeAndRedirectRoot",{url: serverDomainIns.getFullUrl('/1.5/user/login')});
  }
  var url = '/1.5/school/inapp/detail?d=/1.5/index&id='+sch._id;
  if (sch.private) url = '/privateSchools/detail/'+sch._id+'?src=nativeMap';
  var cb = (val)=>{
    // console.log('gotoSch mapsearch cb val: ',val);
    if (val == ':cancel') {
      // console.log('canceled');
      return;
    }
    try {
      // var val = 'loc=43.5723199141038,-79.5785565078259&zoom=15&saletp=lease';
      if (/^cmd-redirect:/.test(val)) {
        // return alert(val);
        var url = val.split('cmd-redirect:')[1];
        closePopup(url);
      }
      if (/loc=/.test(val)) {
        // return alert(val);
        var url = '/1.5/mapSearch?'+val
        if (schoolAction) {
          schoolAction(url)
        } else {
          closePopup(url);
        }
      }
      // var callBackStr = ':ctx:'+val
      // alert(callBackStr);
      // window.rmCall(callBackStr);
      // var d = self.urlParamToObject(val);
      // window.bus.$emit('school-prop', d);
    } catch (e) {
      console.error(e);
    }
  }
  var opt = {
    hide:false,
    sel:'#callBackString',
    tp:'pageContent',
    title:l10n('RealMaster'),
    // toolbar:false,
    url: serverDomainIns.getFullUrl(url),
  }
  eventEmitter.emit("app.message",{msg:JSON.stringify(opt),cb:cb});
}

function gotoUrl(url,closePopup,data={}) {
  var tp = 'pageContent';
  var cb = (val)=>{
    // console.log('mapsearch cb val: ',val);
    if (/:cancel/.test(val)) {
      return;
    }
  }
  if (/^admin$/.test(url)) { // without ^$, any string with admin will trigger admin page
    tp = 'admin';
  }else if (/^video$/.test(url)){
    tp = 'video';
  }
  // console.log('++++gotoUrl',url,data)
  FBLogEvent('nativeAutoComplete','gotoUrl')
  var hasGmap = mapUtil.getGoogleMapProviderAvailable();
  var gps,rootDirect;
  var opt = {
    hide:false,
    sel:'#callBackString',
    tp,
    toolbar:false,
    url: serverDomainIns.getFullUrl(`${url}&src=nativeAutocomplete`),
  }
  // console.log('hasGmap:',hasGmap,getAppConfig('useWebMap')==false,/stigma\/house/.test(url))
  // console.log('@@@@@@@',getAppConfig('isLoggedIn'))
  let mapOrRedirectHandler = (channel='map.locateMe',merge={})=>{
    global.rmLog(`helper.js:172~~~mapOrRedirectHandler`, channel, merge, data);
    if(/mapSearch/.test(data.referer)){
      eventEmitter.emit(channel,opt);
      closePopup();
      return;
    }
    if(/index/.test(data.referer)){
      if (hasGmap && !appConfigIns.getAppConfig('useWebMap')) {
        opt.tp = 'mapSearch'
        opt.gps = /gps=0/.test(url) ? 0 : 1;
        global.rmLog(`helper.js:182~~~mapOrRedirectHandler`, opt);
      } else {
        rootDirect = true;
      }
    }
    opt = Object.assign(opt,merge)
    return done()
  }
  let done = ()=>{
    if(
      ( /mapSearch|index/.test(data.referer) && (/mode=list/.test(url)||rootDirect) )
    ){
      var opt2 = {
        tp:'closeAndRedirectRoot',
        url: serverDomainIns.getFullUrl(url),
      }
      global.rmLog(`helper.js:198~~~done`, opt2);
      eventEmitter.emit("app.message",{msg:JSON.stringify(opt2),cb});
      return;
    }
    closePopup();
    // console.log('+++emit app.message',opt)
    global.rmLog(`helper.js:204~~~done`, opt);
    eventEmitter.emit("app.message",{msg:JSON.stringify(opt),cb});
  }

  if (
    // hasGmap &&
    // (getAppConfig('useWebMap') == false) &&
    // (getAppConfig('isLoggedIn') == true) &&
    stigmaRegex.test(url))
  {
    let isLoggedIn = appConfigIns.getAppConfig('isLoggedIn')
    if(!isLoggedIn){
      return eventEmitter.emit("app.closeAndRedirectRoot",{url: serverDomainIns.getFullUrl('/1.5/user/login')});
    }
    return mapOrRedirectHandler('map.showStigma',{stig:true})
  }
  if (/gps=1\&tp=mapSearch/.test(url)) {
    global.rmLog(`helper.js:217~~~gotoUrl`, url);
    return mapOrRedirectHandler('map.locateMe',{})
  }

  global.rmLog(`helper.js:225~~~gotoUrl`, url);




  //if mapsearch tp=sold
  // closeandredirect
  // var paramsStr = url.split('?')[1];
  // var paramsArry = paramsStr.split('&');
  // for (let p of paramsArry) {
  //   var tem = p.split('=');
  //   if (tem[0]=='tp') {
  //     tp = tem[1];
  //   }
  // }
  // if (gps) {
  //   opt.gps = gps;
  // }
  done()
}
function gotoMap(prop={},closePopup,referer) {
  var url = '/1.5/mapSearch?loc='+prop.lat+','+prop.lng+'&zoom=14&cMarker=1&d=/1.5/index';
  function round(d, p) {
    if (p == null) {
      p = 5;
    }
    return Math.round(d * Math.pow(10, p)) / Math.pow(10, p);
  };
  if (prop.lat == null || prop.lng == null) {
    return Alert.alert('This prop has no lat');
  }
  prop.lat = parseFloat(prop.lat);
  prop.lng = parseFloat(prop.lng);
  var saletp = (prop.saletp_en || []).join(' ');
  var isSale = /Sale/.test(saletp);
  // console.log(prop)
  var opt = {
    lat:prop.lat,
    lng:prop.lng,
    tp:'mapSearch',
    cMarker:1,
    delta:0.005,
    saletp:isSale?'sale':'lease'
  }
  if(prop.tp == 'prop'){
    opt.hMarker = 1;
    delete opt.cMarker;
  }
  if(prop.status_en !== 'A'){
    opt.dom = -90;
  }
  if(/^RM/.test(prop.id)) {
    opt.appmode = 'rm'
  }
  // if (prop.tp == 'addr') {
    // opt.cMarker = 1
  // } else {
    // opt.hlPropKey = round(prop.lat)+','+round(prop.lng)
    // opt.hlProp = prop
    // //url:getServerDomain()+url,
  // }
  var cb = (val)=>{
    // console.log('mapsearch cb val: ',val);
    if (/:cancel/.test(val)) {
      return;
    }
  }
  // console.log('++++++++'+referer)
  var hasGmap = mapUtil.getGoogleMapProviderAvailable();
  var isMap = mapUtil.isMapSearchMode(url)
  var useNativeMap = hasGmap && isMap;
  if(!useNativeMap){
    var opt2 = {
      // hide:false,
      // sel:'#callBackString',
      tp:'closeAndRedirectRoot',
      // toolbar:false,
      url: serverDomainIns.getFullUrl(url),
    }
    // console.log('+++++++',opt2.url)
    eventEmitter.emit("app.message",{msg:JSON.stringify(opt2),cb});
    return;
  }
  closePopup()
  if (referer == 'index') {
    eventEmitter.emit("app.message",{msg:JSON.stringify(opt),cb});
  } else {
    // console.log('++++++MapSearch mode recenter')
    eventEmitter.emit("map.searchSchoolProp",opt);
  }
}
function gotoAutocomplete(props={}) {
  var opt = {
    tp:'autocomplete',
    referer:props.referer
    // url:getServerDomain()+url,
  }
  var cb = (val)=>{
    // console.log('mapsearch cb val: ',val);
    if (/:cancel/.test(val)) {
      return;
    }
    //if (/:locateMe/.test(val)) {
     // eventEmitter.emit("map.locateMe",{});
    //}
  }
  eventEmitter.emit("app.message",{msg:JSON.stringify(opt),cb});
}

export {
  stigmaRegex,
  coopRegex,
  transitRegex,
  gotoProp,
  gotoSch,
  gotoUrl,
  gotoMap,
  gotoAutocomplete
}
