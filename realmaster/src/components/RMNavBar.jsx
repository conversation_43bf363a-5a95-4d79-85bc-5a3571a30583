import React, {Component} from 'react';
import {Text, View, StyleSheet, Pressable} from 'react-native';
import {RMStatusBar} from './RMStatusBar';
import {Icon, eventEmitter} from '../utils/common';

const renderButton = (data, style = {}, textColor = '') => {
  let color = textColor || '#fff';
  return (
    <View style={styles.navBarButtonContainer}>
      {!data || data.props ? (
        data
      ) : (
        <View style={[{flexDirection: 'row', alignItems: 'center'}, style]}>
          {data.cbBack && (
            <Icon
              name='back'
              size={21}
              color={color}
              style={[styles.navBarButton, style]}
              hitSlop={{top: 10, bottom: 10, left: 0, right: 0}}
              onPress={data.cbBack}
            />
          )}
          {data.cbClose && (
            <Pressable
              style={{height: 44}}
              hitSlop={{top: 0, bottom: 0, left: 0, right: 0}}
              onPress={data.cbClose}>
              {/* data.cbClose */}
              <Icon
                name='closeX'
                size={20}
                color={color}
                style={[styles.navBarButton, style]}
              />
            </Pressable>
          )}
          {data.cbConfirm && (
            <Icon
              name='check'
              size={22}
              color={color}
              style={[styles.navBarButton, style]}
              onPress={data.cbConfirm}
            />
          )}
        </View>
      )}
    </View>
  );
};

const renderTitle = (data, bold, textColor) => {
  if (!data || data.props) {
    // return (
    //   <TextInput
    //     style={styles.customTitle}
    //     onChangeText={(text) => this.setState({text})}
    //     value={this.state.text}
    //   />);
    // return <Text style={styles.customTitle}>{data}</Text>
    return <View style={styles.customTitle}>{data}</View>;
  }

  var colorStyle = {};
  if (data.tintColor) {
    colorStyle.color = data.tintColor;
  }
  if (textColor) {
    colorStyle.color = textColor;
  }

  return (
    <View style={styles.navBarTitleContainer}>
      <Text style={[styles.navBarTitleText, data.style, colorStyle, {fontWeight: bold ? 'bold' : 'normal'}]}>
        {data.title}
      </Text>
    </View>
  );
};

class RMNavBar extends Component {
  constructor(props) {
    super(props);
    this.styles = {};
    this.state = {tintColor: null};
  }

  //组件卸载
  componentWillUnmount() {
    eventEmitter.removeListener('navbar.setBarColor', this.changeBarColor);
  }
  //组件加载完成
  componentDidMount() {
    const {tintColor} = this.props;
    let state = {tintColor: null};
    if (tintColor) {
      state.tintColor = tintColor;
    }
    this.setState(state);
    eventEmitter.addListener('navbar.setBarColor', this.changeBarColor);
  }

  //使用箭头函数，自动绑定当前实例，避免使用bind绑定this
  changeBarColor = (opt = {}) => {
    this.setState({tintColor: opt?.barColor});
  };

  render() {
    const {containerStyle, title, leftButton, rightButton, textColor, style, bold} = this.props;
    var tintColor = this.state.tintColor;
    const customTintColor = tintColor ? {backgroundColor: tintColor} : null;

    let statusBar = null;
    // console.log('++++++',this.props,tintColor)
    if (!this.props.statusBar.hidden) {
      tintColor = tintColor || this.props.statusBar.tintColor;
      statusBar = (
        <RMStatusBar
          style={this.props.statusBar.style}
          tintColor={tintColor}
          hideAnimation={this.props.statusBar.hideAnimation}
          showAnimation={this.props.statusBar.showAnimation}
        />
      );
    }
    const navBarStyle = this.props.hide ? styles.navBarHide : [styles.navBarContainer, containerStyle, customTintColor];
    return (
      <View style={navBarStyle}>
        {statusBar}
        <View style={[styles.navBar, style]}>
          {renderTitle(title, bold, textColor)}
          {renderButton(leftButton, {justifyContent: 'flex-start'}, textColor)}
          {renderButton(rightButton, {justifyContent: 'flex-end'}, textColor)}
        </View>
      </View>
    );
  }
}
//class类组件defaultProps,函数式组件直接在参数重解构 const NavBar = ({style={}, tinColor='',...}) => {}
RMNavBar.defaultProps = {
  style: {},
  tintColor: '',
  leftButton: null,
  rightButton: null,
  title: null,
  statusBar: {
    style: 'default',
    hidden: false,
    hideAnimation: 'slide',
    showAnimation: 'slide',
  },
  containerStyle: {},
};

const NavbarHeight = 44;

const styles = StyleSheet.create({
  navBarHide: {
    flex: 0,
    backgroundColor: 'transparent',
    height: 0,
    width: 0,
    opacity: 0,
  },
  navBarContainer: {
    backgroundColor: 'transparent', //'#E03131',
  },
  // statusBar: {
  //   height: RMStatusBar.getHeight(),
  //   backgroundColor:  '#FFFFFF', // test
  // },
  navBar: {
    height: NavbarHeight,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'stretch',
  },
  // {height: 38, borderColor: 'white', borderWidth: 1}
  customTitle: {
    borderColor: 'white',
    borderWidth: 1,
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 7,
    height: 38,
    alignItems: 'center',
  },
  navBarButtonContainer: {
    flexDirection: 'row',
    //justifyContent: 'center',
    alignItems: 'stretch',
  },
  navBarButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    //margin: 10,
    // alignSelf: 'center',
    width: NavbarHeight,
    height: NavbarHeight,
    padding: 12,
    textAlign: 'center',
    textAlignVertical: 'center',
    // backgroundColor:'blue'
    // paddingLeft:15,
    // paddingRight:15,
    // paddingTop:10,
    // paddingBottom:15,
    // fontSize: 24,
  },
  navBarButtonText: {
    fontSize: 17,
    letterSpacing: 0.5,
  },
  navBarTitleContainer: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  navBarTitleText: {
    fontSize: 17,
    // letterSpacing: 0.5,
    paddingTop: 2,
    color: '#FFF',
    fontWeight: '500',
  },
});

export default RMNavBar;
