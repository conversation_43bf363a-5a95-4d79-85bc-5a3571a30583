{"name": "realmaster", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "ios-sim": "react-native run-ios --simulator 'iPhone 16'", "lint": "eslint .", "start": "npx react-native start --experimental-debugger", "test": "jest", "test:coverage": "jest --coverage", "postinstall": "patch-package"}, "dependencies": {"@invertase/react-native-apple-authentication": "^2.3.0", "@react-native-async-storage/async-storage": "^1.23.1", "@react-native-camera-roll/camera-roll": "^7.8.3", "@react-native-clipboard/clipboard": "^1.14.1", "@react-native-community/netinfo": "^11.3.2", "@react-native-cookies/cookies": "^6.2.1", "@react-native-firebase/analytics": "^18.4.0", "@react-native-firebase/app": "^18.4.0", "@react-native-google-signin/google-signin": "^10.0.1", "@react-navigation/native": "^6.1.17", "@react-navigation/native-stack": "^6.11.0", "@react-navigation/stack": "^6.4.0", "axios": "^1.7.2", "eventemitter3": "^5.0.1", "immer": "^10.1.1", "react": "18.2.0", "react-native": "0.74.2", "react-native-blob-util": "^0.19.11", "react-native-config": "^1.5.5", "react-native-calendar-events": "git://github.com/fxfred/react-native-calendar-events.git", "react-native-camera-kit": "^13.0.0", "react-native-check-app-install": "github:fxfred/react-native-check-app-install", "react-native-device-info": "^11.1.0", "react-native-fbsdk-next": "^13.0.0", "react-native-fs": "^2.20.0", "react-native-geolocation-service": "^5.3.1", "react-native-gesture-handler": "^2.17.1", "react-native-localize": "^3.2.0", "react-native-maps": "^1.17.1", "react-native-notifications": "^5.1.0", "react-native-orientation-locker": "^1.7.0", "react-native-permissions": "^4.1.5", "react-native-play-services": "^1.0.4", "react-native-quick-actions": "^0.3.13", "react-native-safe-area-context": "^4.10.8", "react-native-screens": "^3.22.0", "react-native-share": "^10.2.1", "react-native-vector-icons": "^10.1.0", "react-native-webview": "^13.6.4", "react-native-wechat-lib": "git://github.com/fxfred/react-native-wechat-lib.git", "zustand": "^4.5.4"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/babel-preset": "0.74.84", "@react-native/eslint-config": "0.74.84", "@react-native/metro-config": "0.74.84", "@react-native/typescript-config": "0.74.84", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "jest": "^29.6.3", "patch-package": "^8.0.0", "prettier": "2.8.8", "react-test-renderer": "18.2.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}