import {l10n} from '../utils/i18n';

function _basePicUrl (isCip, protocol) {
  var p, ref;
  // p = (typeof window !== "undefined" && window !== null ? (ref = window.location) != null ? ref.protocol : void 0 : void 0) || 'https:';
  p = protocol?protocol+':':'https:';
  if (isCip) {
    return p + "//img.realmaster.cn";
  } else {
    return p + "//img.realmaster.com";
  }
};

function _getSortTimeStamp (ts) {
  var str;
  str = '' + ((new Date(ts)).getTime() / 1000);
  str = str.split('.')[0];
  return str.substr(3);
};

function _ddfPicUrl (ml_base, ddfID, num, ts) {
  return ml_base + "/img/" + (ddfID.substr(-3)) + "/" + ddfID + "_" + num + ".jpg?" + (_getSortTimeStamp(ts));
};

function _trbPicUrl (ml_base, ml_num, num, ts) {
  if (num === 1) {
    return ml_base + "/mls/" + num + "/" + (ml_num.slice(-3)) + "/" + ml_num + ".jpg?" + (_getSortTimeStamp(ts));
  } else {
    return ml_base + "/mls/" + num + "/" + (ml_num.slice(-3)) + "/" + ml_num + "_" + num + ".jpg?" + (_getSortTimeStamp(ts));
  }
};


function listingPicUrl (prop, isCip, num, w, h) {
  var ml_base;
  if (w == null) {
    w = 160;
  }
  if (h == null) {
    h = 120;
  }
  ml_base = _basePicUrl(isCip);
  if (('ddf' === prop.phosrc) && prop.ddfID) {
    return _ddfPicUrl(ml_base, prop.ddfID.substr(3), num, prop.phomt);
  } else if (('bre' === prop.phosrc) && prop.picUrl && prop.pho) {
    return prop.picUrl.replace('[$width]', w).replace('[$high]', h).replace('[pic#]', num - 1);
  } else {
    return _trbPicUrl(ml_base, prop.sid || ml_num, num, prop.phomt);
  }
};
// var nums = [1,2,3,4,5,6,7]
// for (let index = 0; index < l.length; index++) {
//   var p = l[index];
//   for (let j = 0; j < nums.length; j++) {
//     const i = nums[j];
//     if (p.indexOf('${'+i+'}') > -1 && prop['$'+i]) {
//       p = p.split('${'+i+'}').join(prop['$'+i])
//     }
//   }
//   ret.push(p)
// }
//
function listingPicUrlReplace(prop={},protocol='https'){
  var l = prop.picUrls || [];
  var ret = [];
  for (let index = 0; index < l.length; index++) {
    const p = l[index];
    var m, str;
    if ((m = p.match(/^(\$[0-9a-zA-Z])(.*)$/)) && (str = prop[m[1]])) {
      var re = new RegExp("\\{\\{"+m[1].substr(1)+"\\}\\}",'gi')
      var url = str.replace(re, m[2]);
      if(!/^http/.test(url)){
        url=protocol+':'+url;
      }
      ret.push(url);
    } else {
      ret.push(p);
    }
  }
  // console.log(prop.picUrls,ret);
  return ret;
}
function listingPicUrls (prop, isCip, w, h, protocol) {
  var brePicUrl, i, j, k, ml_base, num, picAry, ref, ref1, ref2;
  if (w == null) {
    w = 640;
  }
  if (h == null) {
    h = 480;
  }
  picAry = [];
  var elem, len, ref;
  // console.log('photonumbers:',prop.photonumbers)
  if (prop.photonumbers) {
    if (Array.isArray(prop.photonumbers)) {
      if (prop.photonumbers.length) {
        prop.photonumbers.length = Math.min(50, prop.photonumbers.length);
        ref = prop.photonumbers;
        for (j = 0, len = ref.length; j < len; j++) {
          elem = ref[j];
          // picAry.push('https://d2.realmaster.com/img/icon_index_rent.png')
          picAry.push("https://trebphotos.stratusdata.com/Live/Default.ashx?type=ListingPhoto&entityName=Listing&entityID=" + prop.sid + "&index=" + elem);
        }
      }
    } else {
      picAry.push("https://trebphotos.stratusdata.com/Live/Default.ashx?type=ListingPhoto&entityName=Listing&entityID=" + prop.sid + "&index=" + prop.photonumbers);
    }
  } else if (prop.pho) {
    // console.log(prop.phomt)
    if(!prop.phomt){
      prop.phomt = new Date();
    }
    ml_base = _basePicUrl(isCip,protocol);
    if (('ddf' === prop.phosrc) && prop.ddfID) {
      for (num = i = 1, ref = prop.pho; 1 <= ref ? i <= ref : i >= ref; num = 1 <= ref ? ++i : --i) {
        picAry.push(_ddfPicUrl(ml_base, prop.ddfID.substr(3), num, prop.phomt));
      }
    } else if (('bre' === prop.phosrc) && prop.picUrl && prop.pho) {
      brePicUrl = prop.picUrl.replace('[$width]', w).replace('[$high]', h);
      if(/^\/\//.test(brePicUrl)){
        brePicUrl = 'https:'+brePicUrl
      }
      for (num = j = 0, ref1 = prop.pho; 0 <= ref1 ? j < ref1 : j > ref1; num = 0 <= ref1 ? ++j : --j) {
        picAry.push(brePicUrl.replace('[pic#]', num));
      }
    } else {
      for (num = k = 1, ref2 = prop.pho; 1 <= ref2 ? k <= ref2 : k >= ref2; num = 1 <= ref2 ? ++k : --k) {
        picAry.push(_trbPicUrl(ml_base, prop.sid + '', num, prop.phomt));
      }
    }
  }
  // console.log(picAry)
  return picAry;
};

function getTrebPicUrl (num, ml_num, isCip) {
  var ml_base;
  ml_base = "https://img.realmaster.com";
  if (isCip) {
    ml_base = "https://img.realmaster.cn";
  }
  if (num === 1) {
    return ml_base + "/mls/" + num + "/" + (ml_num.slice(-3)) + "/" + ml_num + ".jpg";
  } else {
    return ml_base + "/mls/" + num + "/" + (ml_num.slice(-3)) + "/" + ml_num + "_" + num + ".jpg";
  }
};

// NOTE: not used
function getTrebPicUrls (num, ml_num, isCip) {
  var i, j, picAry, ref;
  picAry = [];
  if (num) {
    for (i = j = 1, ref = num; 1 <= ref ? j <= ref : j >= ref; i = 1 <= ref ? ++j : --j) {
      picAry.push(getTrebPicUrl(i, ml_num, isCip));
    }
  }
  return picAry;
};

/**
 * 转换房产图片URL列表
 * @param {Object} $scope - 上下文对象，用于获取ml_num
 * @param {Object} imgs - 图片信息对象
 * @param {Array} imgs.l - 图片路径列表
 * @param {string} imgs.base - 基础URL
 * @param {string} imgs.mlbase - MLS基础URL
 * @param {string} imgs.ml_num - MLS编号
 * @param {string} imgs.fldr - 文件夹路径
 * @param {string} opt - 操作类型，目前只支持'reset'
 * @returns {Array} 处理后的图片URL列表
 */
function convert_rm_imgs ($scope, imgs, opt) {
  if (opt === 'reset') {
    if (!imgs?.l) {
      return [];
    }

    const ret = [];
    const base = imgs.base;
    const mlbase = imgs.mlbase;
    const ml_num = imgs.ml_num || $scope.ml_num;

    for (const i of imgs.l) {
      if (i[0] === '/') {
        // 处理MLS图片路径
        const picIndex = parseInt(i.substr(1));
        const suffix = picIndex === 1 ? 
          `${i}/${ml_num.slice(-3)}/${ml_num}.jpg` :
          `${i}/${ml_num.slice(-3)}/${ml_num}_${i.substr(1)}.jpg`;
        ret.push(mlbase + suffix);
      } else if (i.indexOf("http") > -1) {
        // 保持完整的http(s)链接不变
        ret.push(i);
      } else {
        // 处理相对路径
        const url = imgs.fldr && i.startsWith(imgs.fldr) ? 
          i.substring(imgs.fldr.length) : 
          '/' + i;
        ret.push(base + url);
      }
    }
    return ret;
  }
  return [];
}
function serializeData (cfg={}) {
  if ('object' !== typeof cfg.data) {
    return '';
  }
  var obj = cfg.data;
  var prefix = cfg.prefix;
  var str = "";
  for (var key in obj) {
    if (str != "") {
      str += "&";
    }
    str += prefix+'-'+key + "=" + encodeURIComponent(obj[key] || null);
  }
  return str;
}
// given a url, return an object
// @return {Object}
function urlParamToObject (url) {
  if (!url || !url.indexOf) {
    return {};
  }
  if (url.indexOf('?') > -1) {
    url = url.split('?')[1]
  }
  var ret = {};
  url = url.split('&');
  for (let i of url) {
    var tmp = i.split('=');
    var p = tmp[0];
    var v = decodeURIComponent(tmp[1]);
    if (v.indexOf(',')>0) {
      // NOTE: community has comma in it
      // if p == 'cmty'
      ret[p] = v.split(',');
      if (p == 'loc') {
        ret[p] = v.split(',').map((v)=>{return parseFloat(v)});
      }
    } else {
      ret[p] = v;
    }
  }
  return ret;
}
var strArray = ['ptype','dom','sort','city','prov','cmty',
  'bdrms','gr','bthrms','saletp','src','ltp','neartype',
  'front_ft','depth','lotsz_code','irreg','m','recent',
  'lpChg','sold','sch','saleDesc','min_poss_date','max_poss_date',
  'psn','addr','remarks','rltr','domYear','soldLoss'
];//src,ltp
var intArray = ['min_lp','max_lp','max_mfee','yr_f',
  'yr_t','sq_f','sq_t','isPOS','isEstate','depth_f','depth_t',
  'frontFt_f','frontFt_t'
];
var boolArray = ['no_mfee','oh','clear','save','mapView','cmstn','soldOnly'];
// var filterFromUrl = ['bdrms','gr','bthrms',
//   'min_lp','max_lp','dom',
//   'min_poss_date','max_poss_date','isEstate','isPOS',
//   'yr_f','yr_t','addr','remarks','psn','rltr',
//   'oh','neartype','sold','sch','lpChg','recent'];
var allFieldsArray = ['exposures','bsmt','ptype2','bnds'].concat(strArray).concat(intArray).concat(boolArray)
function addKToSerializedData(data){
  let ret = {}
  // console.log('++++addKToSerializedData invoked! data:',data)
  for (let existedKey in data){
    if (existedKey.indexOf('k-') == 0) {
      ret[existedKey] = data[existedKey]
    }
  }
  for (let key of allFieldsArray) {
    // console.log('xxxxx',key)
    if (Object.hasOwnProperty.call(data, key)) {
      const element = data[key];
      ret['k-'+key] = element
    }
  }
  return ret
}
function parseSerializedFilter (data){
  var state = {propTmpFilter:{},propTmpFilterVals:{},quickFilter:{ptype2:[]}};
  var opt = {};
  // console.log('++++parseSerializedFilter invoked! data:',data)
  for (let key in data) {
    // When key is ''
    if (!key) {
      continue;
    }
    let prefix = key.split('-')[0];
    let field = 'propTmpFilter';
    let value = data[key];
    key = key.split('-')[1];
    if (prefix == 'v') {
      field = 'propTmpFilterVals';
    } else if (prefix == 'opt') {
      if (boolArray.indexOf(key) > -1) {
        value = (value=='false'||value=='null'||!value)?false:true;
      } else if (key == 'bbox'||key == 'bnds') {
        if (Array.isArray(value)) {
          value = value.map((v)=>{return parseFloat(v)})
        }
        // console.log('+++++parseSerializedFilter',prefix,key,value)
      }
      opt[key] = value;
      continue;
    }
    if (['ptype2','exposures','bsmt','bbox','bnds'].includes(key) && value != null) {
      // console.log('------',key,Array.isArray(value),value)
      if ('string' == typeof value) {
        if (value == '' || value == 'null') {
          value = [];
        } else if (value.indexOf(',') > 0) {
          value = value.split(',')
        } else {
          value = [value];
        }
      } else if (Array.isArray(value)) {
        if (key == 'bbox'||key == 'bnds'){
          value = value.map((v)=>{return parseFloat(v)})
        }
        // value is already [] do nothing
      }
      // alert(field+':'+key+':'+typeof(value)+':'+value);
      if(field == 'propTmpFilter' && key=='ptype2'){
        state.quickFilter.ptype2 = value;
      }

      state[field][key] = value;
      // console.log('xxxxxxx',state[field][key])
    } else if (strArray.indexOf(key) > -1) {
      if (value == 'null') {
        value = '';
      }
      // console.log(field+' : '+key+' : '+value);
      state[field][key] = value.toString();
      if(field == 'propTmpFilter' && key == 'dom'){
        state.quickFilter.dom = value;
      }
      if(field == 'propTmpFilter' && key == 'src'){
        state.appmode = value=='rm'?'rm':'mls';
      }
    } else if (intArray.indexOf(key) > -1) {
      if (parseInt(value) && value != 'null') {
        value = parseInt(value) || null;
      } else if (value == 'null') {
        value = '';
      }
      state[field][key] = value;
    } else if (boolArray.indexOf(key) > -1) {
      let boolValue = (value=='false'||value=='null'||!value)?false:true;
      state[field][key] = boolValue
      if(field == 'propTmpFilter' && key == 'soldOnly'){
        state.quickFilter.soldOnly = boolValue;
      }
    }
  }
  return {opt,state};
}
function getIconSuffix(p, isFull){
  function _hasCmstn(){
    if(p.cmstn){
      return true
    }
    if(p.market && p.market.cmstn) {
      return true
    }
    return false
  }
  var rmtype;
  var suffix;
  // if (p.adrltr) {
  //   console.log(p.adrltr)
  // }
  if (!p.src) {
    return '';
  }
  if(p.ltp == 'exlisting'){
    rmtype = 'Exclusive';
    suffix = 'E';
  } else if (p.ltp == 'assignment'){
    rmtype = 'Assignment';
    suffix = 'A';
  } else if (p.ltp == 'rent' && !_hasCmstn()){
    rmtype = 'Landlord Direct';
    suffix = 'L';
  } else if (p.ltp == 'rent' && _hasCmstn()){
    rmtype = 'Exclusive Rental';
    suffix = 'R';
  }
  if (isFull) {
    return rmtype;
  }
  return suffix;
}
function isBusiness(l={}){
  // l.pclass && l.pclass.indexOf('b') > -1
  let ptype = '';
  if (Array.isArray(l.ptype2)){
    ptype = l.ptype2.join(',')
  } else {
    ptype = l.ptype || l.tp1
  }
  // console.log('----',ptype)
  return /Office|Retail|Commercial|Business/.test(ptype);
}
function getIconChar(l) {
  // TODO: NOTE: wont be here, isProj jumped to getProjName
  if (l.isProj) {
    l.saletp_en = ['Sale'];
    l.ptype2_en = [(l.tp1||''),(l.tp2||'')];
    if (!(l.tp1 || l.tp2)) {
      return 'P';
    }
    if (isBusiness(l)) {
      // l.pclass = ['b']
      // l.ptype = 'Business'
      return l10n('B')
    }
  }
  var saletp = l.saletp_en || l.saletp || '';
  var ptype2 = l.ptype2_en || l.ptype2;
  //saletp.indexOf('Sale') > -1) {
  var word = ""
  if (saletp.length){
    var ptype2 = ptype2.join(',');
    // SDCBTL
    if (/Semi-/.test(ptype2)) {
      word = 'S';
    } else if (/Detached|Det\sComm/.test(ptype2)) {
      word = 'D';
    } else if (/Apt|Apartment|Condo/.test(ptype2)) {
      word = 'C';
    } else if (/Att|Townhouse/.test(ptype2)) {
      word = 'T';
    } else if (isBusiness(l)) {
      word = 'B';
    }
    // console.log('++++',ptype2)
    // else if (/Parking/.test(ptype2)) {
    //   word = 'P';
    // }
    // else if (/Office/.test(ptype2)) {
    //   word = 'O';
    // }
  }
  //else {
    // word = 'L';
    // if (l.pclass.indexOf('b') > -1) {
    //   word = 'L';
    // } else {
    //   word = 'R';
    // }
  return l10n(word);
}
function nearestOhDate(prop){
  if (!prop.oh) {
    return false;
  }
  for (var i = 0; i < prop.oh.length; i++) {
    var oh = prop.oh[i]
    if (!this.isPassed(oh[1])) {
      return oh;
    }
  }
  return null;
}
function strFormatDate(now){
  var ret = now.getFullYear()+'-';
  ret += ('0'+(now.getUTCMonth()+1)).slice(-2)+'-';
  ret += ('0' + now.getUTCDate()).slice(-2);
  return ret;
}
function isPassed(oh){
  var now = new Date();
  var nowOh = this.strFormatDate(now);
  if (nowOh > oh.split(' ')[0]) {
    return true;
  }
  return false;
}
function isValidPolygonBnds(array = []) {
  if (array.length < 4) {
    return false;
  }
  if (array.length > 200) {
    // TOO many points?
    return false;
  }
  if (array[0] !== array[array.length - 2]) {
    return false;
  }
  if (array[1] !== array[array.length - 1]) {
    return false;
  }
  return true;
};

/**
 * 判断是否为RM类型的房产
 * @param {Object} prop - 房产对象
 * @returns {boolean}
 */
function isRMProp(prop = {}) {
  return /^RM/.test(prop.id);
}

/**
 * 获取房产的主图片URL
 * @param {Object} prop - 房产对象
 * @param {Object} options - 配置选项
 * @param {boolean} options.isCip - 是否使用中国服务器
 * @param {string} options.protocol - 协议(http/https)
 * @param {string} options.noPicUrl - 默认图片URL
 * @returns {string} 图片URL
 */
function getPropPicUrl(prop = {}, options = {}) {
  const { isCip = false, protocol = 'https', noPicUrl } = options;
  
  // 1. 处理picUrls
  if (prop.picUrls?.length) {
    const urls = listingPicUrlReplace(prop, protocol);
    if (urls[0]) return urls[0];
  }
  
  // 2. 处理项目或RM类型房产
  if (prop.isProj || isRMProp(prop)) {
    let fld;
    if (!prop.isProj) {
      fld = {
        ...prop.pic,
        ml_num: prop.sid || prop.ml_num
      };
    } else {
      fld = prop.image;
    }
    
    const urls = convert_rm_imgs({ ml_num: prop.ml_num }, fld, 'reset');
    if (urls[0]) return urls[0];
  }
  
  // 3. 处理普通MLS房产
  const urls = listingPicUrls(prop, isCip, null, null, protocol);
  if (urls[0]) return urls[0];
  
  // 4. 返回默认图片
  return noPicUrl;
}

export {
  serializeData,
  urlParamToObject,
  parseSerializedFilter,
  addKToSerializedData,
  getIconChar,
  convert_rm_imgs,
  listingPicUrls,
  getIconSuffix,
  listingPicUrlReplace,
  isValidPolygonBnds,
  getPropPicUrl,
  isRMProp,
};
