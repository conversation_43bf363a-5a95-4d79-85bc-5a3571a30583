import { Platform } from 'react-native';
import DeviceInfo from 'react-native-device-info';

const coreVer = DeviceInfo.getBuildNumber();

const iosCode = `
null==window.rmCall&&(window.rmCall=function(e){var n;return(null!=(n=window.ReactNativeWebView)?n.postMessage:void 0)?window.ReactNativeWebView.postMessage(encodeURIComponent(":ctx:"+e)):window.location="react-js-navigation://rmCall?"+encodeURIComponent(":ctx:"+e)}),document.addEventListener("rm-message",function(e){var n,t,o;if(n=e.detail){try{n=decodeURIComponent(n)}catch(e){return void e}if(o=function(e){return window.rmCall(e)},":cancel"===n)return o(":cancel");if(":html"===n)return o(document.documentElement.innerHTML);if(":html:wechat"===n)return o(document.querySelector("#js_article").innerHTML);if(t=n.match(/^\:sel\:(.*)$/))return o(document.querySelector(t[1]).textContent)}});
`;

const androidCode = `
if (window.rmCall == null) {
  window.rmCall = function(html) {
    var ref;
    if ((ref = window.ReactNativeWebView) != null ? ref.postMessage : void 0) {
      return window.ReactNativeWebView.postMessage(encodeURIComponent(':ctx:' + html));
    } else {
      return window.postMessage(encodeURIComponent(':ctx:' + html));
    }
  };
}
document.addEventListener('rm-message', function(event) {
  var data, m, sendBack;
  if (data = (event.data || event.detail)) {
    try {
      data = decodeURIComponent(data);
    } catch (error1) {
      error = error1;
      return;
    }
    sendBack = function(html) {
      return window.rmCall(html);
    };
    if (data === ':cancel') {
      return sendBack(':cancel');
    } else if (data === ':html') {
      return sendBack(document.documentElement.innerHTML);
    } else if (data === ':html:wechat') {
      return sendBack(document.querySelector('#js_article').innerHTML);
    } else if (m = data.match(/^\:sel\:(.*)$/)) {
      return sendBack(document.querySelector(m[1]).textContent);
    }
  }
});
`;


//RMSrv.rmMessage本质也是调用window.ReactNativeWebView.postMessage或者window.postMessage.
//所以RMSrv.rmMessage���不到的情况下，catch中window.ReactNativeWebView.postMessage也会报错
const createPostMessageCode = (msg) => `
    (function() {
      //如果页面2秒后RMSrv还没有加载和初始化完成，则走异常处理步骤
      const timeout = 2000, delter = 100;
      let attempts = 0, timer = null;
      function sendMessage(){
        clearTimeout(timer);
          if (attempts * delter >= timeout) {
            var event = new CustomEvent('rm-message',{detail:"${msg}"});
            document.dispatchEvent(event);
          } else {
            if (window.RMSrv && window.RMSrv.rmMessage) {
              window.RMSrv.rmMessage("${msg}");
            } else {
              attempts++;
              timer = setTimeout(sendMessage, 100);
            }
          }
        }
      sendMessage()
    })();void(0);
    `;

const jsCode = Platform.select({
    ios: iosCode,
    android: androidCode,
});

// 检查RMSrv对象和goReady方法是否存在并执行
const createGoReadyCode = (version) => 
  `;if(window.RMSrv && typeof(RMSrv.goReady) === 'function'){RMSrv.goReady(${version})};`;

const injectedJavaScript = jsCode + createGoReadyCode(coreVer);

const androidInjectedJavaScript = createGoReadyCode(coreVer + 1);

export { injectedJavaScript, androidInjectedJavaScript, createPostMessageCode };