const BootUpUrls = [
  { dm: 'd7.realmaster.com', protocol: 'https', factor: 2.0 },
  // { dm: 'd3.realmaster.com', protocol: 'https', factor: 3.0 },
  // { dm: 'realmaster.com', protocol: 'https', factor: 1.2 },
  // { dm: 'ch.realmaster.cn', protocol: 'https', factor: 1 },
  // {dm: 'app.test:8888', protocol: 'http', factor: 1.0},
];

const SharedCookieKeys = ['appmode', 'k', 'cmate.sid', 'apsv'];
const SharedCookieDomains = [
  'app.realmaster.cn',
  'app.realmaster.com',
  'ch.realmaster.cn',
  'realmaster.com',
  // 'd2.realmaster.com'
];
const testDests = ['app.test', '************'];
for (let i = 1; i < 9; i++) {
  // 'd3-d8.',
  // 'd2.realmaster.com',
  // 'd1.realmaster.com',
  testDests.push(`d${i}.realmaster.com`);
}
const ValidAppDomains = SharedCookieDomains.slice().concat(testDests);

const DynamicIsland = 48;
const IphoneX = 32;
const Iphone14 = 44; //NOTE: ios may not block content visually, but actually does block press events
const DefaultBarHeight = 20;
const FootHeight = 15;

// https://gist.github.com/adamawolf/3048717
const IosModels = {
  'iPhone7,1': {nm: 'iPhone 6 Plus', notch: DefaultBarHeight, foot: 0},
  'iPhone7,2': {nm: 'iPhone 6', notch: DefaultBarHeight, foot: 0},
  'iPhone8,1': {nm: 'iPhone 6s', notch: DefaultBarHeight, foot: 0},
  'iPhone8,2': {nm: 'iPhone 6s Plus', notch: DefaultBarHeight, foot: 0},
  'iPhone8,4': {nm: 'iPhone SE (GSM)', notch: DefaultBarHeight, foot: 0},
  'iPhone9,1': {nm: 'iPhone 7', notch: DefaultBarHeight, foot: 0},
  'iPhone9,2': {nm: 'iPhone 7 Plus', notch: DefaultBarHeight, foot: 0},
  'iPhone9,3': {nm: 'iPhone 7', notch: DefaultBarHeight, foot: 0},
  'iPhone9,4': {nm: 'iPhone 7 Plus', notch: DefaultBarHeight, foot: 0},
  'iPhone10,1': {nm: 'iPhone 8', notch: DefaultBarHeight, foot: 0},
  'iPhone10,2': {nm: 'iPhone 8 Plus', notch: DefaultBarHeight, foot: 0},
  'iPhone10,3': {nm: 'iPhone X Global', notch: IphoneX},
  'iPhone10,4': {nm: 'iPhone 8', notch: DefaultBarHeight, foot: 0},
  'iPhone10,5': {nm: 'iPhone 8 Plus', notch: DefaultBarHeight, foot: 0},
  'iPhone10,6': {nm: 'iPhone X GSM', notch: IphoneX},
  'iPhone11,2': {nm: 'iPhone XS', notch: Iphone14},
  'iPhone11,4': {nm: 'iPhone XS Max', notch: Iphone14},
  'iPhone11,6': {nm: 'iPhone XS Max Global', notch: Iphone14},
  'iPhone11,8': {nm: 'iPhone XR', notch: Iphone14},
  'iPhone12,1': {nm: 'iPhone 11', notch: IphoneX, foot: FootHeight},
  'iPhone12,3': {nm: 'iPhone 11 Pro', notch: Iphone14},
  'iPhone12,5': {nm: 'iPhone 11 Pro Max', notch: Iphone14},
  'iPhone12,8': {nm: 'iPhone SE 2nd Gen', notch: DefaultBarHeight, foot: 0},
  'iPhone13,1': {nm: 'iPhone 12 Mini', notch: IphoneX},
  'iPhone13,2': {nm: 'iPhone 12', notch: Iphone14},
  'iPhone13,3': {nm: 'iPhone 12 Pro', notch: Iphone14},
  'iPhone13,4': {nm: 'iPhone 12 Pro Max', notch: Iphone14},
  'iPhone14,2': {nm: 'iPhone 13 Pro', notch: Iphone14},
  'iPhone14,3': {nm: 'iPhone 13 Pro Max', notch: Iphone14},
  'iPhone14,4': {nm: 'iPhone 13 Mini', notch: IphoneX},
  'iPhone14,5': {nm: 'iPhone 13', notch: Iphone14},
  'iPhone14,6': {nm: 'iPhone SE 3rd Gen', notch: DefaultBarHeight, foot: 0},
  'iPhone14,7': {nm: 'iPhone 14', notch: Iphone14, foot: FootHeight},
  'iPhone14,8': {nm: 'iPhone 14 Plus', notch: Iphone14, foot: FootHeight},
  'iPhone15,2': {nm: 'iPhone 14 Pro', notch: DynamicIsland, foot: FootHeight},
  'iPhone15,3': {
    nm: 'iPhone 14 Pro Max',
    notch: DynamicIsland,
    foot: FootHeight,
  },
  'iPhone15,4': {nm: 'iPhone 15', notch: DynamicIsland, foot: FootHeight},
  'iPhone15,5': {
    nm: 'iPhone 15 Plus',
    notch: DynamicIsland,
    foot: FootHeight,
  },
  'iPhone16,1': {nm: 'iPhone 15 Pro', notch: DynamicIsland, foot: FootHeight},
  'iPhone16,2': {
    nm: 'iPhone 15 Pro Max',
    notch: DynamicIsland,
    foot: FootHeight,
  },

  'iPhone17,1': {nm: 'iPhone 16 Pro', notch: DynamicIsland, foot: FootHeight},
  'iPhone17,2': {
    nm: 'iPhone 16 Pro Max',
    notch: DynamicIsland,
    foot: FootHeight,
  },
};

const Datas = [
  'useTileOverlay',
  'tileUrl',
  'useWebMap',
  'isCip',
  'isLoggedIn',
  'backgroundTs',
  'dualHomepage',
  'nativeWechatAppId',
  'nativeWechatDomain',
  'iosModelsAndNotch'
];

const CommandMap = {
  'exitApp':1,
  'getSystemVersion':1,
  'getDeviceId':1,
  'getUniqueId':1,
  'hasGoogleService':1,
  'hasPlayServices':1,
  'err':1,
  'log':1,
  'ver':1,
  'coreVer':1,
  'pushToken':1,
  'fetch':1,
  'permissions.request.notification':1,
  'permissions.request.requestMultiple':1,
  'calendar.authorizationStatus':1,
  'calendar.authorizeEventStore':1,
  'calendar.saveEvent':1,
  'calendar.findCalendars':1,
  'calendar.saveCalendar':1,
  'calendar.findEventById':1,
  'calendar.removeEvent':1,
  'calendar.fetchAllEvents':1,
  'permissions.request.location':1,
  'permissions.check.location':1,
  'downloadImage':1,
  'permissions.check.notification':1,
  'permissions.openSettings':1,
  'permissions.openSettings.notification':1,
  'permissions.openSettings.location':1,
  'storage.getItemObj':1,
  'storage.setItemObj':1,
  'storage.removeItemObj':1,
  'refreshSystemValue':1,
  'AppInstalledChecker':1,
  'setSystemValue':1,
  'showSystemValue':1,
  'vibrate':1,
  'facebook.share':1,
  'wechat.share':1,
  'wechat.auth':1,
  'facebook.auth':1,
  'google.auth':1,
  'apple.auth':1,
  'wechat.has':1,
  'share':1,
  'singleShare':1,
  'qrcode':1,
  'keyboard.dismiss':1,
  'disableScroll':1,
  'openInBrowser':1,
  'popup':1,
  'pageContent':1,
  'closeAndRedirect':1,
  'closePopup':1,
  'closeAndRedirectRoot':1,
  'alert':1,
  'confirm':1,
  'geoPosition':1,
  'setAppLang':1,
  'autocomplete':1,
  'admin':1,
  'video':1,
  'map':1,
  'mapSearch':1
}

const constants = {
  AppName:'RealMaster',
  Datas,
  BootUpUrls,
  CommandMap,
  SharedCookieKeys,
  SharedCookieDomains,
  ValidAppDomains,
  IosModels,
  DefaultServerDomain: 'https://realmaster.com',
  // DefaultServerDomain: 'https://d1.realmaster.com',
  ChangeAppMode:'app.appmode',
  PnToken:'pnToken',
  AppLang:"@appParams:lang",
  ListPageData:'listPageData',
  PermNotifResult:'permissions.notification',
  PermNotifResultSettings:'permissions.notification.settings',
  GeoPositon:'geoPosition',
  LastMapPosition:'lastMapPosition',
  MapScaleStatus: 'map_scale_status',
  MapProp: 'map.feature.prop',
  MapsearchFilter:'mapSearch.filter',
  MapPropPreview: 'map.feature.prop.preview',
  MapTransitStopModal: 'map.feature.transit.stopModal',
  NearbyPropItem:'map.feature.prop.nearby_prop',
  CurrentSavedSearch:'map.feature.prop.current_saved_search',
  MapPropStop: 'map.feature.prop_stop',
  AcShowGlAddr:'shwGglAddr',
  AcGlExpire:'expireTime',
  AcAddr:'autocompleteAddrs',
  AcHists:'hists',
  AcCurrTab:'currentTab',
  AcAdtp:'adTp',
  EventGetHomeSchools: 'school.getHomeSchools',
  EventSet: 'feature.set',// i.e. {school:true/false}
  //存入RamCache的键，且同步到AsyncStorage
  CacheKey: {
    LastLoadDomain: 'lastLoadDomain',
    LastLoadUrl:'lastLoadUrl',
    DeviceUserAgent:'deviceUserAgent',
    AppLang: '@appParams:lang', //语言
    TransData: 'l10n.localCache', //翻译所用数据
  },
  Url: {
    Translate: '/1.5/translate'
  }
};

export default constants;
