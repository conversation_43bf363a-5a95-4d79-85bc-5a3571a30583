# 地图图层选择功能流程图

## 项目概述
这是一个React Native地图组件，支持多种图层显示和切换，包括房源(Property)、学校(School)、公交(Mass Transit)、合作住房(Co-op housing)和污名化属性(Stigmatized)等图层。

## 核心组件结构

### 主要文件
- `RMMapSearchNative.jsx` - 主要的地图组件
- `MapFeature.jsx` - 所有图层功能的基类
- 各种图层混入文件 (`MapProps.jsx`, `MapSchool.jsx`, `MapTransit.jsx`, `MapCoop.jsx`, `MapStigmatized.jsx` 等)

## 完整流程图

### 1. 初始化阶段

```mermaid
graph TD
    A[组件加载 componentDidMount] --> B[创建图层Feature实例]
    B --> C[MapProps - 房源图层]
    B --> D[MapSchool - 学校图层]
    B --> E[MapTransit - 公交图层]
    B --> F[MapCoop - 合作住房图层]
    B --> G[MapStigmatized - 污名化图层]
    B --> H[MapDummyLayer - 虚拟图层]
    
    C --> I[添加到 allFeatures 数组]
    D --> I
    E --> I
    F --> I
    G --> I
    H --> I
    
    I --> J[设置初始激活图层 activeFeatureName]
    J --> K[渲染地图界面]
```

### 2. 悬浮按钮渲染流程

```mermaid
graph TD
    A[render 方法执行] --> B[遍历 allFeatures]
    B --> C[调用每个 feature.onOffView方法]
    C --> D{返回图层配置对象?}
    D -->|是| E[收集到 onOffMenuData 数组]
    D -->|否| F[跳过该图层]
    
    E --> G[确定当前激活图层 activeLayerItem]
    G --> H[渲染右侧悬浮按钮容器]
    H --> I[上半部分: 当前激活图层图标 AnimatedIcon]
    H --> J[下半部分: 关闭图层图标 layers-off]
    
    I --> K[绑定点击事件: toggleLayerSelect方法]
    J --> L[绑定点击事件: selectDummyLayer方法]
```

### 3. 用户点击上方图标流程

```mermaid
graph TD
    A[用户点击上方图标] --> B[触发 toggleLayerSelect方法]
    B --> C[获取当前状态]
    C --> D[showLayerSelect = !showLayerSelect]
    C --> E[showBackdrop = !showBackdrop]
    
    D --> F[调用 setState 更新状态]
    E --> F
    F --> G[清除图层动画定时器]
    G --> H{showLayerSelect 为 true?}
    
    H -->|是| I[调用 clearFeaturesPanels 关闭其他面板]
    H -->|否| J[仅更新状态]
    
    I --> K[重新渲染组件]
    J --> K
```

### 4. 底部弹窗渲染流程

```mermaid
graph TD
    A[组件重新渲染] --> B{state.showLayerSelect 为 true?}
    B -->|是| C[创建 layerModel]
    B -->|否| D[layerModel = null]
    
    C --> E[渲染 BottomPane 组件]
    E --> F[创建 FlatList]
    F --> G[使用 onOffMenuData 作为数据源]
    G --> H[每项使用 renderLayerItem 渲染]
    
    H --> I[图层图标 + 名称 + 开关]
    I --> J[绑定点击事件: selectLayer]
    
    E --> K[渲染底部按钮区域]
    K --> L[Reset Map 按钮]
    K --> M[Close 按钮]
    
    L --> N[绑定: resetMapLayer方法]
    M --> O[绑定: toggleLayerSelect方法]
```

### 5. 图层选择流程

```mermaid
graph TD
    A[用户点击图层项] --> B[触发 selectLayer方法]
    B --> C{是房源或距离图层?}
    
    C -->|是| D{是开关切换?}
    D -->|是| E[调用 item.onOff.toggleOnOff方法]
    D -->|否| F[直接返回，不处理]
    
    C -->|否| G{是否与当前激活图层相同?}
    G -->|不同| H[关闭其他图层 switchOffAllLayers]
    G -->|相同| I[仅切换面板显示状态]
    
    H --> J[激活选中图层 item.onOff.toggleOnOff方法]
    I --> K[调用 clearFeaturesPanels方法]
    
    J --> L{是学校或公交图层?}
    L -->|是| M[关闭图层选择面板 clearLayerSelect]
    L -->|否| N[关闭所有模态框 clearModals]
    
    M --> O[更新状态: activeFeatureName 等]
    N --> O
    K --> O
    E --> O
    
    O --> P[重新渲染组件]
```

### 6. 特殊功能流程

#### 6.1 重置地图流程
```mermaid
graph TD
    A[用户点击 Reset Map] --> B[调用 resetMapLayer方法]
    B --> C[调用 switchOffAllLayers方法]
    C --> D[遍历所有 allFeatures]
    D --> E[关闭除房源外的所有图层]
    E --> F[强制开启学校和房源图层]
    F --> G[设置 activeFeatureName = 'Schools']
    G --> H[调用 clearModals方法 关闭弹窗]
    H --> I[重新渲染地图]
```

#### 6.2 关闭所有图层流程
```mermaid
graph TD
    A[用户点击下方 layers-off 图标] --> B[调用 selectDummyLayer方法]
    B --> C{当前是否已是虚拟图层?}
    C -->|是| D[重新打开图层选择面板]
    C -->|否| E[调用 resetMapLayer 切换到虚拟图层]
    
    D --> F[调用 toggleLayerSelect方法]
    E --> G[关闭所有功能图层]
    E --> H[设置 activeFeatureName = 'MapDummyLayer']
    
    F --> I[重新渲染]
    G --> I
    H --> I
```

### 7. 关闭弹窗流程

```mermaid
graph TD
    A[触发关闭事件] --> B{触发方式}
    B -->|点击 Close 按钮| C[调用 toggleLayerSelect方法]
    B -->|点击背景遮罩| D[调用 clearModals方法]
    B -->|点击 BottomPane 关闭| D
    
    C --> E[切换 showLayerSelect 状态]
    D --> F[设置 showLayerSelect = false]
    D --> G[设置 showBackdrop = false]
    
    E --> H[重新渲染，隐藏弹窗]
    F --> H
    G --> H
```

## 关键数据结构

### onOffView() 返回的配置对象
```javascript
{
  icon: 'rm-school',           // 图标名称
  iconSize: 24,                // 图标大小
  name: '学校',                // 显示名称
  toggle: true,                // 是否支持开关切换
  toggleOnOff: (onOff) => {}, // 切换回调函数
  on: true,                    // 当前开启状态
  type: 'mapProperty'          // 类型标识
}
```

### 状态管理
```javascript
state = {
  showLayerSelect: false,    // 是否显示图层选择面板
  showBackdrop: false,       // 是否显示背景遮罩
  activeFeatureName: 'Schools', // 当前激活的图层名称
  // ... 其他状态
}
```

## 核心函数说明

| 函数名 | 功能 | 参数 | 返回值 |
|--------|------|------|--------|
| `toggleLayerSelect()` | 切换图层选择面板显示状态 | 无 | 无 |
| `selectLayer(item, opt)` | 选择特定图层 | item: 图层配置对象, opt: 选项 | 无 |
| `resetMapLayer(ignore, forceOnLayer)` | 重置地图图层 | ignore: 忽略的图层, forceOnLayer: 强制开启的图层 | 无 |
| `switchOffAllLayers(ignore, forceOnLayer)` | 关闭所有图层 | 同上 | 无 |
| `clearModals(opt)` | 关闭所有模态框 | opt: 选项对象 | 无 |
| `selectDummyLayer()` | 选择虚拟图层(关闭所有功能) | 无 | 无 |

## 图层类型说明

1. **MapProps** - 房源图层：显示房源标记
2. **Schools** - 学校图层：显示学校标记和详情面板
3. **MapTransit** - 公交图层：显示公交线路和站点
4. **MapCoop** - 合作住房图层：显示合作住房标记
5. **MapStigmatized** - 污名化图层：显示污名化区域
6. **MapDummyLayer** - 虚拟图层：用于关闭所有功能

## 用户交互路径

1. **查看图层** → 点击右侧悬浮按钮上方图标 → 底部弹出图层选择面板
2. **切换图层** → 在面板中点击具体图层 → 地图显示对应图层内容
3. **开关图层** → 使用图层右侧的开关按钮 → 控制图层显示/隐藏
4. **重置地图** → 点击 Reset Map 按钮 → 恢复默认的学校+房源图层
5. **关闭面板** → 点击 Close 按钮或背景遮罩 → 隐藏图层选择面板
6. **关闭所有图层** → 点击右侧悬浮按钮下方图标 → 进入纯地图模式

这套流程确保了用户可以方便地在不同图层间切换,同时保持良好的用户体验和性能表现。 