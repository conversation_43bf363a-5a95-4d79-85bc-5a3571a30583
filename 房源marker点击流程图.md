# 房源Marker点击流程图 - 底部房源信息卡片显示

## 概述
本文档详细描述了在React Native地图应用中，用户点击房源marker后，底部弹出房源信息卡片的完整函数调用流程。

## 主要文件
- `realmaster/src/mixins/MapProps.jsx` - 地图房源功能混入组件

## 完整流程图

下图展示了从用户点击房源marker到底部房源卡片显示的完整流程：

```mermaid
graph TD
    A["用户点击房源Marker"] --> B["markerPressed()事件触发"]
    B --> C["清除其他模态框<br/>eventEmitter.emit('map.clearModals')"]
    C --> D["发起社区边界数据请求<br/>mainRequest('/1.5/props/cmtyBnds')"]
    D --> E["更新组件状态<br/>setState({curProp, selectedMarkerId})"]
    E --> F["调用toggleModal(null, 'open')"]
    F --> G["设置propHalfDetail: true"]
    G --> H["React组件重新渲染"]
    H --> I["renderModal()被调用"]
    I --> J["_renderPropPreview()被调用"]
    J --> K["_renderPropPreviewForProp()被调用"]
    K --> L["渲染房源信息卡片<br/>显示在屏幕底部"]
    
    L --> M{"用户操作"}
    M -->|点击卡片| N["propChanged()<br/>跳转到房源详情页"]
    M -->|点击关闭按钮| O["toggleModal('close')<br/>设置propHalfDetail: false"]
    M -->|点击学校按钮| P["getHomeSchools()<br/>获取学校信息"]
    
    O --> Q["隐藏房源卡片<br/>清理社区边界数据"]
    
    style A fill:#e1f5fe
    style L fill:#c8e6c9
    style N fill:#fff3e0
    style O fill:#ffebee
    style P fill:#f3e5f5
```

## 函数调用流程

### 1. 地图渲染阶段

```mermaid
graph TD
    A[renderOnMap] --> B[遍历mapProps和mapSoldProps]
    B --> C[renderPropMarker]
    C --> D[创建Marker组件]
    D --> E[绑定onPress事件: markerPressed]
```

#### 1.1 renderOnMap() - 地图渲染主函数
**位置**: `MapProps.jsx:6606-6650`

**功能**: 
- 渲染所有地图上的房源marker
- 处理社区边界显示
- 避免重复渲染

**核心代码**:
```javascript
renderOnMap() {
    if (!this.featureOn) return;
    if (!this.state.showMapMarkers) return;
    
    // 遍历mapProps和mapSoldProps
    for (let i of ['mapProps', 'mapSoldProps']) {
        Object.keys(this.state[i]).map((propid) => {
            let prop = this.state[i][propid]
            if (prop.ids.length == 1) {
                view.push(this.renderPropMarker(prop, { tp: i }))
            } else {
                view.push(this.renderPropNumberIcon(prop, { tp: i }))
            }
        })
    }
    return view;
}
```

#### 1.2 renderPropMarker() - 渲染单个房源marker
**位置**: `MapProps.jsx:2618-2685`

**功能**:
- 创建房源marker组件
- 绑定点击事件处理函数
- 处理选中状态显示

**核心代码**:
```javascript
renderPropMarker(prop, opt = {}) {
    let onPress = this.markerPressed(prop, isFromSoldLayer)
    
    return (<Marker
        coordinate={{ latitude: prop.lat, longitude: prop.lng }}
        onPress={onPress}  // 绑定点击事件
        key={prop.objs[0]._id}
    >
        {marker}
    </Marker>)
}
```

### 2. 点击事件处理阶段

```mermaid
graph TD
    A[用户点击marker] --> B[markerPressed事件触发]
    B --> C[清除其他模态框]
    C --> D[处理社区边界请求]
    D --> E[更新当前房源状态curProp]
    E --> F[调用toggleModal]
    F --> G[设置propHalfDetail=true]
```

#### 2.1 markerPressed() - marker点击事件处理
**位置**: `MapProps.jsx:6399-6495`

**功能**:
- 处理marker点击事件
- 发起社区边界数据请求
- 更新当前选中房源状态
- 触发房源预览显示

**核心逻辑**:
```javascript
markerPressed(data, isFromSoldLayer = false) {
    return (e) => {
        // 1. 清除其他模态框
        eventEmitter.emit('map.clearModals', { src: 'mapSearch', backdrop: false });
        
        // 2. 处理多个房源的情况
        if (data.ids.length > 1) {
            this.showListView({ props: data.objs });
            return;
        }
        
        // 3. 更新当前房源状态
        var curProp = data.objs[0];
        this.setState({
            curProp,
            selectedMarkerId: curProp._id,
            tracksViewChanges: true,
            usePropImg,
            isFromSoldLayer,
        });
        
        // 4. 显示房源预览模态框
        this.toggleModal(null, 'open');
    }
}
```

#### 2.2 toggleModal() - 模态框显示控制
**位置**: `MapProps.jsx:5595-5610`

**功能**:
- 控制房源预览卡片的显示/隐藏
- 管理propHalfDetail状态

**核心代码**:
```javascript
async toggleModal(a, b) {
    let open;
    if (b == null) {
        open = !this.state.propHalfDetail
    } else if (b == 'open') {
        open = true
        this.closeOtherModals();
    }
    
    if (!open) {
        this.clearPropertyAndCommunity();
    } else {
        this.setState({ propHalfDetail: open });
    }
}
```

### 3. 房源卡片渲染阶段

```mermaid
graph TD
    A[propHalfDetail状态变为true] --> B[组件重新渲染]
    B --> C[renderModal被调用]
    C --> D[_renderPropPreview被调用]
    D --> E[_renderPropPreviewForProp被调用]
    E --> F[渲染完整房源信息卡片]
```

#### 3.1 renderModal() - 模态框渲染入口
**位置**: `MapProps.jsx:4888-4900`

**功能**:
- 渲染各种模态框组件
- 根据状态决定渲染内容

**核心代码**:
```javascript
renderModal(id) {
    if (!this.featureOn) return null;
    var a = [], v;
    if (v = this._renderSaleTypeSelect()) { a.push(v); }
    if (v = this._renderPropPreview()) { a.push(v); }  // 房源预览
    if (a.length == 0) {
        if (v = this._renderMapListDragPan()) { a.push(v); }
    }
    return a;
}
```

#### 3.2 _renderPropPreview() - 房源预览主函数
**位置**: `MapProps.jsx:5507-5513`

**功能**:
- 获取当前房源数据
- 调用详细渲染函数

**核心代码**:
```javascript
_renderPropPreview() {
    let prop = this.state.curProp;
    return this._renderPropPreviewForProp(prop)
}
```

#### 3.3 _renderPropPreviewForProp() - 房源卡片详细渲染
**位置**: `MapProps.jsx:5295-5506`

**功能**:
- 渲染完整的房源信息卡片
- 处理房源图片、价格、房间信息等
- 添加交互按钮（学校、关闭等）

**主要渲染内容**:
1. **基础验证**: 检查房源ID和propHalfDetail状态
2. **房源信息处理**: 卧室、浴室、车库数量格式化
3. **价格显示**: 处理已售房源的划线效果
4. **图片显示**: 房源主图或默认加载图
5. **详细信息布局**:
   - 学校信息按钮
   - 关闭按钮
   - TOP/Open House标签
   - 价格和房间配置
   - 税费和描述信息
   - 地址和房产类型
   - 社区信息

**核心样式设置**:
```javascript
style={[styles.propPreviewWrapper, { height: wrapperHeight }]}
```

其中`propPreviewWrapper`样式定义为:
```javascript
propPreviewWrapper: {
    position: 'absolute',
    bottom: 0,
    backgroundColor: 'white',
    left: 0,
    right: 0,
    zIndex: 20,
    height: 170,
}
```

### 4. 用户交互处理

#### 4.1 点击房源卡片 - propChanged()
**位置**: `MapProps.jsx:3332-3394`

**功能**:
- 跳转到房源详情页面
- 构建详情页URL
- 处理回调逻辑

#### 4.2 关闭房源卡片 - toggleModal('close')
**功能**:
- 设置propHalfDetail为false
- 清理社区边界数据
- 隐藏房源预览卡片

## 关键状态变量

### propHalfDetail
- **类型**: Boolean
- **作用**: 控制房源预览卡片的显示/隐藏
- **初始值**: false
- **触发渲染**: 当值为true时，触发房源卡片渲染

### curProp
- **类型**: Object
- **作用**: 存储当前选中的房源数据
- **更新时机**: 点击marker时更新
- **用途**: 为房源卡片提供数据源

### selectedMarkerId
- **类型**: String
- **作用**: 标记当前选中的marker ID
- **用途**: 控制marker的选中状态显示

## 样式定义

房源预览卡片的主要样式定义在`styles.propPreviewWrapper`中：

```javascript
propPreviewWrapper: {
    position: 'absolute',  // 绝对定位
    bottom: 0,            // 底部对齐
    backgroundColor: 'white',
    left: 0,
    right: 0,
    zIndex: 20,           // 确保在最上层
    height: 170,          // 固定高度
}
```

## 总结

整个流程可以概括为：
1. **渲染阶段**: renderOnMap → renderPropMarker → 绑定markerPressed事件
2. **事件处理**: markerPressed → 更新状态 → toggleModal
3. **卡片显示**: renderModal → _renderPropPreview → _renderPropPreviewForProp
4. **用户交互**: 点击卡片跳转详情 或 点击关闭按钮隐藏卡片

这个流程确保了用户点击地图上的房源marker后，能够流畅地在底部显示包含详细信息的房源卡片，提供良好的用户体验。 