/*

*/
import React, {Component} from 'react';
import {
  StyleSheet,
  View,
  Text,
  Platform,
  Dimensions
} from 'react-native';
import { eventEmitter } from '../utils/common'
import {l10n} from '../utils/i18n';
import { <PERSON><PERSON><PERSON>, <PERSON>er } from 'react-native-maps';
import MapFeature from './MapFeature';
import storageIns from '../utils/storage';
import Constants from '../config/constants';

function haversine_distance(mk1, mk2) {
  var R = 3958.8; // Radius of the Earth in miles
  var rlat1 = mk1.latitude * (Math.PI/180); // Convert degrees to radians
  var rlat2 = mk2.latitude * (Math.PI/180); // Convert degrees to radians
  var difflat = rlat2-rlat1; // Radian difference (latitudes)
  var difflon = (mk2.longitude-mk1.longitude) * (Math.PI/180); // Radian difference (longitudes)

  var d = 2 * R * Math.asin(Math.sqrt(Math.sin(difflat/2)*Math.sin(difflat/2)+Math.cos(rlat1)*Math.cos(rlat2)*Math.sin(difflon/2)*Math.sin(difflon/2)));
  var unit = d > 1 ? 'km' : 'm';
  if (d < 1) {
    d = d * 1000;
  }
  return d.toFixed(2)+unit;
}

const topScalePosition = {
  iphoneX: 125,
  dynamicIsland: 135
}
const topScalePositionNoProps = {
  iphoneX: 80,
  dynamicIsland: 90
}

const IS_IOS = (Platform.OS === 'ios') ? true : false;

const getTopScalePosition = () =>{
  if (IS_IOS) {
    return topScalePosition[IPHONE_X] || 115
  } else {
    return 90;
  }
}

const getTopScalePositionNoProps = () => {
  if (IS_IOS) {
    return topScalePositionNoProps[IPHONE_X] || 70;
  } else if (Platform.OS === 'android') {
    return 50;
  }
}

class MapDistance extends MapFeature{
  constructor(map,props,onOff){
    super(map,"MapDistance",props,onOff);
    this.state = {
      horizontalDis:null,
      verticalDis:null,
      mapScaleStatus:this.getMapScaleStatus(),
      showMapScale:this.getMapScaleStatus() != 'off' ? true : false,
      propFeatureOn:onOff,
      map:map
    }
    this.timeout = null;
    eventEmitter.on("map.props.fetureOn",this.recordPropFeatureOn.bind(this))
  }
  recordPropFeatureOn(val){
    this.setState({propFeatureOn:val},()=>{
      setTimeout(() => {
        this.getMapCoordiantes();
      }, 1);
    });

  }
  getMapScaleStatus() {
    return storageIns.getCacheItem(Constants.MapScaleStatus || 'dynamic');
  }
  //get three coordinates
  getMapViewCoordianteForPoint(map,cb) {
    const basePointUnit = 20;
    const {width,height} = Dimensions.get('window');
    const topLeftPoint = {x:basePointUnit,y:basePointUnit};
    let bottomLeftPoint = {x:basePointUnit,y:height-200};
    if (!this.state.propFeatureOn) {
      bottomLeftPoint.y = height - 160;
    }
    const topRightPoint = {x:width-basePointUnit,y:basePointUnit};

    this.getCoordinateForPoint(map,topLeftPoint,(topLeftCoord)=>{
      this.getCoordinateForPoint(map,topRightPoint,(topRightCoord)=>{
        this.getCoordinateForPoint(map,bottomLeftPoint,(bottomLeftCoord)=>{
          cb({topLeftCoord,topRightCoord,bottomLeftCoord})
        });
      });
    });
  }
  getCoordinateForPoint(map,point,cb) {
    map.coordinateForPoint(point).then((coord)=>{
      cb(coord);
    })
  }
  checkDynamicHideScale() {
    if (this.state.mapScaleStatus == 'dynamic') {
      if (this.timeout) {
        clearTimeout(this.timeout);
      }
      this.timeout = setTimeout(() => {
        this.setState({showMapScale:false});
      }, 3000);
    }
  }
  getMapCoordiantes() {
    this.getMapViewCoordianteForPoint(this.map.map,({topLeftCoord,topRightCoord,bottomLeftCoord})=>{
      var verticalDis = haversine_distance(topLeftCoord,topRightCoord);
      var horizontalDis = haversine_distance(topLeftCoord,bottomLeftCoord);
      var showMapScale = (this.state.mapScaleStatus === 'off') ? false : true;
      this.setState({verticalDis, horizontalDis,topLeftCoord,topRightCoord,bottomLeftCoord,showMapScale},()=>{
        this.checkDynamicHideScale()
      });
    })
  }

  regionChanged(event,map){
    super.regionChanged(event,map);
    this.getMapCoordiantes();

    // var bbox = event.bbox;
    // var topRight = {longitude:bbox[2],latitude: bbox[3]};
    // var bottomLeft = {longitude:bbox[0],latitude: bbox[1]};
    // var topLeft = {longitude:bbox[2],latitude: bbox[1]};
    // var bottomRight = {longitude:bbox[0],latitude: bbox[3]};
    // var horizontalDis = haversine_distance(topRight,topLeft);
    // var verticalDis = haversine_distance(topRight,bottomRight);
    // this.setState({verticalDis, horizontalDis});
  }
  onOffView(){
    return {
      icon:'rmhouse-sq',
      iconSize:26,
      paddingLeft:3,
      name:l10n('Scale'),
      type:'mapScale',
      setStatus:(status)=>{this.setStatus(status)},
      status:this.getMapScaleStatus(),
      getActiveColor:(status)=>{return this.getActiveColor(status)},
      getScaleBtnContainerStyle:(status)=>{return this.getScaleBtnContainerStyle(status)}
    }
  }
  getScaleBtnContainerStyle(status) {
    let scaleBtnContainerStyle = {
      color:'#000'
    }
    if (IS_IOS) {
      scaleBtnContainerStyle = {
        ...scaleBtnContainerStyle,
        borderColor:'#ccc',
        borderWidth:1,
        borderStyle:'solid'
      }
    }
    if (this.getMapScaleStatus() == status) {
      scaleBtnContainerStyle.borderColor = '#2fa800';
    }
    return scaleBtnContainerStyle;
  }
  getActiveColor(status) {
    return this.getMapScaleStatus() == status ? '#2fa800' : '#ccc';
  }
  setStatus(status){
    const showMapScale = (status == 'off') ? false : true;
    this.setState({mapScaleStatus:status,showMapScale},()=>{
      this.checkDynamicHideScale();
    })
  }
  unmount(){
    super.unmount();
    eventEmitter.removeListener('map.props.fetureOn',this.recordPropFeatureOn);
  }
  renderOnMap() {
    var {showMapScale,topLeftCoord,topRightCoord,bottomLeftCoord} = this.state;
    if (!showMapScale) {
      return [];
    }
    if (!topLeftCoord) {
      return [];
    }
    const markerAnchor={x:0.5,y:0.5};
    let view = [
      <Marker anchor={markerAnchor} coordinate={topLeftCoord}>
        <View style={[styles.markerStyle]}>
        </View>
      </Marker>,
      <Marker anchor={markerAnchor} coordinate={topRightCoord}>
        <View style={[styles.markerStyle]}>
        </View>
      </Marker>,
      <Marker anchor={markerAnchor} coordinate={bottomLeftCoord}>
        <View style={[styles.markerStyle]}>
        </View>
      </Marker>
    ];
    const polylineView = <Polyline
      coordinates={[
        topRightCoord,
        topLeftCoord,
        bottomLeftCoord
      ]}
      strokeColor="#000" // fallback for when `strokeColors` is not supported by the map-provider
      strokeWidth={3}
      lineCap={'round'}
	  />
    view.push(polylineView);
    return view;
  }

  renderOverlay(){
    var {verticalDis,horizontalDis,showMapScale,propFeatureOn} = this.state;
    if (!showMapScale) {
      return [];
    }
    var view = [];
    if (verticalDis) {
      let topMeasure = {
        width:'100%',
        position: 'absolute',
        top: getTopScalePosition(),
        zIndex: 10
      };
      if (!propFeatureOn) {
        topMeasure.top = getTopScalePositionNoProps()
      }
      view.push(<View style={[topMeasure,styles.distanceContainer]}>
        <View style={styles.distanceTxt}><Text>{verticalDis}</Text></View>
      </View>);
    }
    if (horizontalDis) {
      view.push(
      <View style={[styles.leftMeasure,styles.distanceContainer]}>
        <View style={styles.distanceTxt}><Text>{horizontalDis}</Text></View>
      </View>);
    }
    return view;
  }
}
const styles = StyleSheet.create({
  distanceContainer:{
    flexDirection:'row',
    justifyContent:'center',
  },
  distanceTxt:{
    backgroundColor:'rgba(255,255,255,1)',
    padding:8,
    borderWidth:1,
    borderColor:'#000',
    borderRadius:10,
    color:'#000'
  },
  leftMeasure:{
    position: 'absolute',
    left: -10,
    top: '50%',
    transform: [{ rotate: '-90deg'}],
    zIndex: 10
  },
  bottomLeftCoord:{
    position:'absolute',
    bottom:100,
    zIndex: 10
  },
  markerStyle:{
    backgroundColor:'#000',
    zIndex:10,
    height: 20,
    width: 20,
    borderRadius:18,
  },
  scaleBtnContainerStyle:{
    color:'#000',
    borderColor:'#ccc',
    borderWidth:1,
    borderStyle:'solid',
    paddingTop:2,
    paddingBottom:2,
    paddingLeft:1,
    paddingRight:1
  }
});

export default MapDistance;
