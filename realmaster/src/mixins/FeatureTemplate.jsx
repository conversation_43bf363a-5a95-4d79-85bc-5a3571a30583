/*
*/
import React, {Component} from 'react';
import {
  StyleSheet,
  View,
  Text,
  Image,
  ScrollView,
} from 'react-native';
import MapFeature from './MapFeature';
import { Marker } from 'react-native-maps';
//import PriceMarker from '../views/PriceMarker';
import IconText from '../components/IconText';
import BottomPane from '../components/BottomPane';
// import {RMPost} from '../RMNetwork';
import { mainRequest } from '../utils/request';


class REPLAE_THIS extends MapFeature{
  constructor(map,props){
    super(map,"REPLAE_THIS",props);
    this.state = {
      tracksViewChanges:false,
    }
  }
  gotSchools(schnbnds){
    //console.log(schnbnds);
    this.bnds = schnbnds.bnds;
    this.setState({schs:filterSchoolNames(schnbnds.schs)});
  }
  clearSchools(){
    this.bnds = null;
    this.setState({schs:null});
  }
  regionChanged(event,map){
    // console.log("regionChanged zoom:" + event.zoom);
    super.regionChanged(event,map);
    if (event.zoom < 15){
      this.clearSchools();
      return;
    }
    // fetch points or remove/add shapes
    mainRequest({
      url: '/1.5/mapSearch/findSchools',
      method: 'post',
      data: {bbox:event.bbox,mode:'list'},
    }).then(ret=> {
      this.gotSchools(ret);
    }).catch(err=> {
      console.log(err);
    })
  }
  renderOnMap(){
    let self = this;
    if (!this.featureOn) return;
    if (!this.state.showMapMarkers) return;
    if (this.state.schs){
      let view = [];
      for (schId in this.state.schs){
        view.push (
          <Marker
            tracksViewChanges={ this.state.tracksViewChanges }
            style={{}}
            key={schId}
            onPress={self.schClicked(schId)}
            coordinate={{longitude:sch.loc[1],latitude:sch.loc[0]}}
            //icon={schImg}
          >
            <IconText icon={schImg} text={sch.snm} backgroundColor={bkcolor}/>
            {/* <Image
              resizeMode='stretch'
              style={styles.marker}
              source={schImg}
            /> */}
          </Marker>
        );
      };
      return view;
    }
    return <View key={this.name}></View>;
  }
  renderModal(){
    if (!this.state.schoolID){
      return null;
    }
    let sch = this.state.sch;
    return (<BottomPane
      key={'SchoolBoundaries'}
      title={sch.nm}
      height={200}
      cbClose={this.fnClose()}
      >
      <ScrollView contentContainerStyle={styles.contentContainer}>
      </ScrollView>
    </BottomPane>);
  }
  fnClose(){
    let self = this;
    return () => {
      self.setState({schoolID:null,sch:null,tracksViewChanges:true});
      this.trackOff();
    };
  }
  schClicked(schId){
    return (e) => {
      var self = this;
      let sch = this.state.schs[schId];
      if (!sch) return;
      // console.log("School Clicked:"+schId);
      this.setState({schoolID:schId,sch:sch,tracksViewChanges:true});
      this.trackOff();
    }
  }

}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'column',
    alignSelf: 'flex-start',
  },
});

export default MapSchool;
