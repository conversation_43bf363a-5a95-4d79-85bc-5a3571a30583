import {
  GoogleSignin,
  GoogleSigninButton,
  statusCodes,
} from '@react-native-google-signin/google-signin';

// clientSecret: '9IE0XrIOOpA5ZmN0YWzyZXcM'
var GOOGLE_OPTS = {
  scopes: [
    'https://www.googleapis.com/auth/userinfo.profile',
    'https://www.googleapis.com/auth/userinfo.email'
  ],//['https://www.googleapis.com/auth/drive.readonly'], // what API you want to access on behalf of the user, default is email and profile
  webClientId: '344011320921-vh4gmos4t6rej56k2oa3brharpio1nfn.apps.googleusercontent.com', // client ID of type WEB for your server (needed to verify user ID and offline access)
  // androidClientId:'344011320921-q669kuh4co7kr0o08ctdovhi8qroe04r.apps.googleusercontent.com', // Android client ID
  // 344011320921-q669kuh4co7kr0o08ctdovhi8qroe04r.apps.googleusercontent.com => Android
  offlineAccess: false, // if you want to access Google API on behalf of the user FROM YOUR SERVER
  // hostedDomain: 'realmaster.com', // specifies a hosted domain restriction
  // loginHint: '', // [iOS] The user's ID, or email address, to be prefilled in the authentication UI if possible. [See docs here](https://developers.google.com/identity/sign-in/ios/api/interface_g_i_d_sign_in.html#a0a68c7504c31ab0b728432565f6e33fd)
  // forceCodeForRefreshToken: true, // [Android] related to `serverAuthCode`, read the docs link below *.
  // accountName: '', // [Android] specifies an account name on the device that should be used
  // iosClientId: '<FROM DEVELOPER CONSOLE>', // [iOS] optional, if you want to specify the client ID of type iOS (otherwise, it is taken from GoogleService-Info.plist)
}
GoogleSignin.configure(GOOGLE_OPTS);

// TODO: signout??

var GoogleLogin = async (opt,cb) => {
  try {
    await GoogleSignin.hasPlayServices();
    var userInfo = null;
    if(opt.silent){
      userInfo = await GoogleSignin.signInSilently();
    } else {
      userInfo = await GoogleSignin.signIn();
    }
    // this.setState({ userInfo });
    // console.log(userInfo)
    userInfo.ok = 1;
    cb(userInfo)
  } catch (error) {
    console.log(error,'google error')
    console.log(statusCodes,'google statusCodes')
    if (error.code === statusCodes.SIGN_IN_CANCELLED) {
      // user cancelled the login flow
      // console.log("Login cancelled");
      return cb({cancelled:true})
    } else if (error.code === statusCodes.IN_PROGRESS) {
      // operation (e.g. sign in) is in progress already
      // console.log("Login still in progress");
      return cb({err:'In progress'})
    } else if (error.code === statusCodes.PLAY_SERVICES_NOT_AVAILABLE) {
      // play services not available or outdated
      return cb({err:'Google Play Service not available'});
    } else {
      // some other error happened
      return cb({err:error});
    }
  }
};

var hasPlayServices = (cb) => {
  // try {
  GoogleSignin.hasPlayServices({ showPlayServicesUpdateDialog: false }).then((ret)=>{
    // google services are available
    // console.log('->',ret);
    cb(true)
    // return true;
  }).catch((e)=>{
    console.error('Error test service',e)
    cb(false)
  });

  // } catch (err) {
  //   console.error('play services are not available');
  //   cb(false)
  //   // return false;
  // }
}
export {GoogleLogin,hasPlayServices}
