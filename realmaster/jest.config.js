module.exports = {
  preset: 'react-native',
  
  // 仅忽略转换不兼容的第三方模块
  transformIgnorePatterns: [
    'node_modules/(?!(react-native|@react-native|react-native-vector-icons|react-native-.*)/)',
  ],
  
  // 简化覆盖率收集配置
  collectCoverageFrom: [
    'src/utils/**/*.{js,jsx}',
    '!**/deprecated/**',
  ],
  
  coverageDirectory: '__coverage__',
  coverageReporters: ['text'],  // 移除了较少使用的clover
  
  // 保留当前的注释说明覆盖率要求
  
  testTimeout: 15000,
  
  // 添加有用的其他配置
  testPathIgnorePatterns: [
    '/node_modules/'
  ],
  
  // 自动清理mock状态
  clearMocks: true,
};
