import React,{Component} from 'react';
import PropTypes from 'prop-types';

import {
  StyleSheet,
  View,
  Text,
  Image,
  // ImageBackground,
} from 'react-native';
import { Icon } from '../utils/common';
const propTypes = {
  amount: PropTypes.string,
  fontSize: PropTypes.number,
};

const defaultProps = {
  fontSize: 11,
};

class PriceMarker extends Component {
  constructor(props){
    super(props);
    this.state = {
      initialRender:true,
      src:'',
    }
  }
  forceUpdate2=()=>{
    // console.log('xxxxx',this.state)
    this.setState({initialRender: false})
  }
  componentDidMount(){
    // const { fontSize, amount, isTopUp, offMarket, suffix, adrltr} = this.props;
  }
  render() {
    const { fontSize, amount, isTopUp, offMarket, isSold, suffix,
      adrltr, verified, showing, showingPassed, isFromSoldLayer,
      group
    } = this.props;
    var isSelected = this.props.isSelected;
    // if(verified){
    //   console.log(verified,this.props)
    // }
    if (isSelected) {
      background = '#5c6972';
      border = '#484e53';
    } else if (isTopUp) {
      background = '#ffa40d';
      border = '#bb780c';
    } else if (isFromSoldLayer){
      background = '#7269cb'; //purple
      border = '#776DD0';
    } else if (isSold){
      background = '#3899EC';
      border = '#1687e9';
    } else if (offMarket){
      background = '#d8d8d8';
      border = '#8d8d8d';
    } else {
      background = '#FF5A5F'
      border = '#D23F44'
    }
    let arrow = {
      borderTopColor:background
    }
    let arrowBorder = {
      borderTopColor:border
    }
    var showAvt = !!(adrltr && adrltr.avt);
    if (!isTopUp) {
      showAvt = false;
    }
    var showSuff = !showAvt && !!suffix;
    if(verified || showing || showingPassed){
      showSuff = false
    }
    var src = showAvt?{uri:adrltr.avt}:'';
    if (showAvt) {
      // src = {uri:"https://thirdwx.qlogo.cn/mmopen/vi_32/rM6MrJEPQeibnCLdQ6QfAW5WJIgoa1hHribZMEcgx61gvLsUFiarGSbvenpSeOYSrOdSsic5p9ejxYL3Xvahgh3n4w/132"}
      // console.log('++++++',src)
      // this.setState({src:src})
    }
    return (
      <View style={styles.container}>
        <View style={[styles.bubble,{
            backgroundColor: background,
            borderColor: border}, (showAvt||showSuff)?styles.bubbleSuff:{}]}>
          {/*<Text style={styles.dollar}>$</Text>*/}
          <Text style={[styles.amount, { fontSize }]}>{amount||''}</Text>
          {showAvt &&
            <View style={styles.imgWrapper}>
              <Image style={styles.adrltr}
                source={src}
                // onLayout={() => this.setState({initialRender: false})}
                // onLoadEnd={() => this.forceUpdate2()}
                >
                {/* <Text style={{width:0, height:0}}>{Math.random()}</Text> */}
              </Image>
            </View>
          }
          {showSuff &&
            <View style={styles.suffixWrapper}>
              <Text style={[styles.suffix]}>{suffix||''}</Text>
            </View>
          }
          {verified &&
            <View style={styles.verifiedWrapper}>
              {/* <Text style={[styles.suffix]}>{'✔'}</Text> */}
              <Icon name="check-circle" size={14} color="#FFF"/>
            </View>
          }
          {(showing || showingPassed) &&
            <View style={styles.verifiedWrapper}>
              {/* <Text style={[styles.suffix]}>{'✔'}</Text> */}
              <Icon name="rmhistory" size={14} color={showing?"#FFF":'#999999'}/>
            </View>
          }
          {(group) &&
            <View style={styles.verifiedWrapper}>
              {/* <Text style={[styles.suffix]}>{'✔'}</Text> */}
              <Icon name="rmgroup" size={16} iconStyle={{fontWeight:'bold'}} color={group?"#FFF":'#999999'}/>
            </View>
          }
        </View>
        <View style={[styles.arrowBorder,arrowBorder]} />
        <View style={[styles.arrow,arrow]} />
      </View>
    );
  }
}

PriceMarker.propTypes = propTypes;
PriceMarker.defaultProps = defaultProps;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'column',
    alignSelf: 'flex-start',
  },
  bubble: {
    flex: 0,
    flexDirection: 'row',
    alignSelf: 'flex-start',
    backgroundColor: 'transparent',
    paddingTop: 3,
    paddingBottom: 3,
    paddingRight: 8,
    paddingLeft: 8,
    borderRadius: 11,
    borderColor: 'transparent',
    borderWidth: 0.5,
  },
  bubbleSuff:{
    justifyContent:'space-between',
    paddingRight:3,
  },
  dollar: {
    color: '#FFFFFF',
    fontSize: 10,
  },
  amount: {
    color: '#FFFFFF',
    fontSize: 11,
  },
  suffix: {
    color:'#CE0000',
    fontSize:10,
    marginTop:-0.5,
  },
  suffixWrapper:{
    marginLeft:5,
    backgroundColor: 'white',
    borderRadius:6,
    width:12,
    height:12,
    marginTop:1,
    justifyContent:'center',
    alignItems:'center',
  },
  verifiedWrapper:{
    marginLeft:5,
    marginRight:-3,
    // backgroundColor: 'white',
    // borderRadius:6,
    width:14,
    height:14,
    // marginTop:1,
    justifyContent:'center',
    alignItems:'center',
  },
  imgWrapper:{
    marginLeft:5,
    width:14,
    height:14,
    marginTop: 1,
    overflow:'hidden',
  },
  adrltr:{
    width:14,
    height:14,
    borderRadius: 7,
    borderWidth: 1,
    borderColor: 'white',
    // marginLeft:4,
  },
  arrow: {
    backgroundColor: 'transparent',
    borderWidth: 4,
    borderColor: 'transparent',
    borderTopColor: '#FF5A5F',//#
    alignSelf: 'center',
    marginTop: -9,
  },
  arrowBorder: {
    backgroundColor: 'transparent',
    borderWidth: 4,
    borderColor: 'transparent',
    borderTopColor: '#D23F44',//#
    alignSelf: 'center',
    marginTop: -0.5,
  },
});

export default PriceMarker;
