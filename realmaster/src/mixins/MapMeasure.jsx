/*
    Measure length, area on map.
    TO DO:
    4. save with auto-name: city/community or lat/lng

*/
import React, {Component} from 'react';
import {
  StyleSheet,
  View,
  Text,
  Image,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import MapFeature from './MapFeature';
import { Marker,Polygon, Polyline} from 'react-native-maps';
//import PriceMarker from '../views/PriceMarker';
// import IconText from '../views/IconText';
import BottomPane from '../components/BottomPane';
// import {RMPost} from '../RMNetwork';
import getAreaOfPolygon from '../es/getAreaOfPolygon';
import getPathLength from '../es/getPathLength';
import getDistance from '../es/getDistance';
import {DistanceUnits,AreaUnits,distanceBySmallestUnit,distanceByUnit,areaBySmallestUnit,areaByUnit,round} from './helper';
import { l10n } from '../utils/i18n';
/*
const dist = geolib.getDistance(
  { latitude, longitude },
  { latitude: item.LatL, longitude: item.Long2 }
);
*/
mapPin1 = require("../assets/images/pins/pin1.png")
mapPin2 = require("../assets/images/pins/pin2.png")

class MapMeasure extends MapFeature{
  constructor(map,props,onOff){
    super(map,"Measure",props,onOff);
    this.state = {
      tracksViewChanges:false,
    }
    // this.regOnPress();
    if(onOff){
      this.setFeatureOn()
    }
    // this.featureOn = false;
  }
  onPressMenu(){
    return l10n('Measure on Map');
  }
  onOffView(){
    // on/off setting to show on map
    return {
      icon:'rmcat-measure',
      name:l10n('Measure'),
      paddingLeft:2,
      iconSize:21,
      toggleOnOff:(p)=>{this.toggleOnOff(p)},on:this.featureOn
    }
  }
  toggleOnOff(){
    if(onOff == 'off'){
      this.featureOn = false;
    }
    if (this.featureOn){
      this.closeFeature();
    }else{
      this.setFeatureOn();
    }
  }
  closeFeature(){
    this.reset();
    this.setFocus(false);
    this.featureOn = false;
    this.map.onRegionChangeComplete();
  }
  reset(){
    this.setState({mmDots:null,cur:-1})
  }
  removeLast(){
    if (this.state.mmDots){
      this.state.mmDots.pop()
      mmDots = this.state.mmDots
      if (this.state.cur >= mmDots.length){
        this.state.cur--;
      }
      if (mmDots.length == 0){
        mmDots = null;
      }
      this.setState({mmDots:mmDots,cur:this.state.cur});
    }
  }
  save(){
    // TODO: fields: nm/tp/mmDots/cur/distanceUnit/Index/areaUnit/index
  }
  load(){
    // TODO: a list from server
  }
  changeDistanceUnit(){
    this.distanceUnitIndex++;
    if (this.distanceUnitIndex >= DistanceUnits.length){
      this.distanceUnitIndex = 0;
    }
    this.distanceUnit = DistanceUnits[this.distanceUnitIndex];
    this.setState({distanceUnitIndex:this.distanceUnitIndex});
  }
  changeAreaUnit(){
    this.areaUnitIndex++;
    if (this.areaUnitIndex >= AreaUnits.length){
      this.areaUnitIndex = 0;
    }
    this.areaUnit = AreaUnits[this.areaUnitIndex];
    this.setState({areaUnitIndex:this.areaUnitIndex});
  }
  renderButton(){
    if (!this.featureOn) return null;
    if (!(this.state.mmDots) || (this.state.mmDots.length == 0)){
      return <Text style={[styles.displayTouch,styles.display,{
        alignSelf:'center',
        // backgroundColor:'green',
        textAlign:'center' }]}>{l10n("Click on map to start.")}</Text>;
    }
    let r = [];
    if (this.state.mmDots.length==1){
      this.distanceUnit = null;
      // NOTE: bad habbit, modify state value in render
      this.areaUnit = null;
      r.push(<TouchableOpacity style={styles.displayTouch} key={"distanceLatLng"}>
        <Text style={styles.display}>{round(this.state.mmDots[0][1],6)+","+round(this.state.mmDots[0][0],6)}</Text>
        </TouchableOpacity>
      );
    }
    if (this.state.mmDots.length>=2){
      myGetDistance = (f,t)=> getDistance(f,t,0.01)
      let distance = getPathLength(this.state.mmDots,myGetDistance);
      // console.log("Distance"+distance);
      if (this.distanceUnit){
        dis = {distance:distanceByUnit(distance,this.distanceUnit),unit:this.distanceUnit}
      }else{
        dis = distanceBySmallestUnit(distance);
        this.distanceUnit = dis.unit;
        this.distanceUnitIndex = dis.index;
      }
      r.push(<TouchableOpacity style={styles.displayTouch} onPress={e=>this.changeDistanceUnit()} key={"distanceDisp"}>
        <Text style={styles.display}>{round(dis.distance,2)+' '+l10n(dis.unit,'distance')}</Text>
      </TouchableOpacity>);
    }
    if (this.state.mmDots.length>=3){
      let area = getAreaOfPolygon(this.state.mmDots);
      let areaUnit = '';
      let are;
      let suffix = '';
      if (this.areaUnit){
        areaUnit = this.areaUnit;
        are = {area:areaByUnit(area,this.areaUnit),unit:areaUnit}
      }else{
        are = areaBySmallestUnit(area);
        // NOTE: bad habbit, TODO: 1, not to modify in render; 2. use state.areaUnit
        this.areaUnit = areaUnit = are.unit;
        this.areaUnitIndex = are.index;
      }
      if(areaUnit.indexOf(2)>0){
        suffix='²';
        areaUnit = areaUnit.replace('2','')
        areaUnit = l10n(areaUnit,'distance')
      } else {
        areaUnit = l10n(areaUnit,'area')
      }
      are.suffix = suffix;
      // console.log('++++',are)
      r.push(<TouchableOpacity style={styles.displayTouch} onPress={e=>this.changeAreaUnit()} key={"areaDisp"}>
        <Text style={styles.display}>{round(are.area,2)+' '+areaUnit+are.suffix}</Text>
      </TouchableOpacity>);
    }
    return r;
  }
  renderBound(view){
    if (!(this.state.mmDots) || (this.state.mmDots.length == 0)){return null;}
    let outer = [];
    this.state.mmDots.forEach( loc =>{
      loc = {longitude:loc[1],latitude:loc[0]};
      outer.push(loc);
    });
    if (this.state.mmDots.length < 2){
      return null;
    }
    view.push(<Polyline
      key={'measureDotsLine'}
      coordinates={outer}
      //lineCap={'none'}
      //geodesic={true}
      strokeWidth={2}
      strokeColor="#FF3333"
      fillColor="rgba(255,100,100,0.8)"
      />
    );
    if (this.state.mmDots.length >= 3){
      view.push(<Polygon
      key={'measureDotsPolygon'}
      coordinates={outer}
      //lineCap={'none'}
      //geodesic={true}
      strokeWidth={2}
      strokeColor="#FF3333"
      fillColor="rgba(255,100,100,0.2)"
      />);
    }
  }

  renderOnMap(){
    let self = this;
    if (this.state.mmDots){
      let view = [];
      //console.log(this.state.mmDots)
      this.state.mmDots.forEach((dot,i)=>{
        let img = mapPin1;
        let style = {width:25,height:40};
        let cur = false;
        if (this.state.cur == i){
          img = mapPin2;
          // style = {width:56,height:90};
          style = {width:28,height:45};
          cur = true;
        }
        view.push (
          <Marker
            draggable
            tracksViewChanges={ this.state.tracksViewChanges }
            // style={style}
            key={"pin"+i}
            onDragEnd={(e) => self.pinDragEnd(e,i)}
            onPress={(e)=> self.selectPin(e,i)}
            coordinate={{longitude:dot[1],latitude:dot[0]}}
            // icon={img}
            // image={img}
            anchor={{x:0.5,y:1}}
          >
            {<Image
              resizeMode='stretch'
              style={style}
              source={img}
            />}
          </Marker>
        );
      });
      this.renderBound(view);
      //console.log(view);
      return view;
    }
    return <View key={this.name}></View>;
  }
  selectPin(e,i){
    e.stopPropagation();
    this.setState({cur:i});
  }
  setFeatureOn(){
    if (!this.focused){
      this.setFocus(true);
      this.state.mmDots = [];
      this.featureOn = true;
      this.setState({mmDots:[]});
    }
  }
  onPress(e,map){
    if(!this.featureOn){return}
    this.setFeatureOn();
    this.setState({tracksViewChanges:true});
    mmDots = this.state.mmDots || [];
    mmDots.push([e.coordinate.latitude,e.coordinate.longitude]);
    this.setState({mmDots:mmDots,cur:(mmDots.length-1)});
    this.trackOff();
  }
  pinDragEnd(e,i){
    this.state.mmDots[i] = [e.nativeEvent.coordinate.latitude,e.nativeEvent.coordinate.longitude];
    this.setState({mmDots:this.state.mmDots})
  }
  // regionChanged(event,map){
  //   if (!this.focused){return;}
  //   console.log(event);
  // }
  renderBottomBar(){
    if (!this.focused){
      return;
    }
    let r = [];
    let btns = [
      {k:'Close',n:'Close',icon:'close',act:()=>{this.closeFeature()}},
      {k:'Left',n:'Left',icon:'long-arrow-left',act:()=>{this.move({x:-1})}},
      {k:'Up',n:'Up',icon:'long-arrow-up',act:()=>{this.move({y:-1})}},
      {k:'Down',n:'Down',icon:'long-arrow-down',act:()=>{this.move({y:1})}},
      {k:'Right',n:'Right',icon:'long-arrow-right',act:()=>{this.move({x:1})}},
      {k:'RemoveLast',n:'Remove',icon:'arrow-circle-left',act:()=>{this.removeLast()}}, //arrow-circle-left
      {k:'Reset',n:'Reset',icon:'undo',act:()=>{this.reset()}},
      {k:'Help',n:'Help',icon:'question',act:()=>{this.setState({measureHelp:true})}},
    ]
    this.bottomButtons(r,btns);
    return r;
  }
  async move(m){
    if (this.inMove){return;}
    this.inMove = true;
    let point;
    if (this.state.mmDots && (0<=this.state.cur) && (point = this.state.mmDots[this.state.cur])){
      let p = await this.map.getMapRef().pointForCoordinate({latitude:point[0],longitude:point[1]});
      if (m.x){
        p.x += m.x;
      }
      if (m.y){
        p.y += m.y;
      }
      point = await this.map.getMapRef().coordinateForPoint(p);
      this.state.mmDots[this.state.cur] = [point.latitude,point.longitude];
      this.setState({mmDots:this.state.mmDots});
      this.inMove = false;
    }
  }
  renderModal(){
    if (!this.state.measureHelp) {return null;}
    return (<BottomPane  key={'masureHelp'} statusBar={{hidden:true}} height={200} title={l10n('Measure')} cbClose={()=>{this.setState({measureHelp:null});}}>
      <View style={{padding:10,fontSize:14}}>
        <Text>{l10n("Hold big pin for 2 seconds, when the map moved, you can drag the pin to a new location.")}</Text>
        <Text style={{marginTop:10}}>{l10n("Click distance and area results to change unit.")}</Text>
      </View>
    </BottomPane>);
  }
}

const styles = StyleSheet.create({
  marker:{
    //flex:1,
    width: 32,
    height: 51,
    zIndex: 1,
  },

  displayTouch:{
    flex:1,
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    //width:'20%',
    paddingBottom:2,
  },
  display:{
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    height: 30,
    paddingTop: 6,
    color: '#ffffff',
  },
});

export default MapMeasure;
