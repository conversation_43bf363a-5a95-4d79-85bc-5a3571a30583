import Constants from './constants'
import { eventEmitter } from '../utils/common'
import env from './env'

class AppConfig {
  constructor() {
    if (!AppConfig.instance) {
      this.config = {
        useWebMap: null,
        useTileOverlay: null,
        tileUrl: 'https://mptl.realmaster.cn/vt/lyrs=m&x={x}&y={y}&z={z}',
        splashJsonUrlBase: 'https://realmaster.cn',
        splashJsonUrlBaseTest: 'https://d2.realmaster.com',
        splashPicDownloadUrl: 'https://dl.realmaster.cn',
        maximumZ: 19,
        mapShowStigmatized: true,
        mapShowCoop: true,
        backgroundTs: 6 * 60 * 60, //6h,单位s
        webViewCheckaliveTimeout: 3,
        hasWechat: true,
        noSplashBefore: 0,
        appStateTs: Date.now(),
        // isRealCip:null,
        // isCip/isLoggedIn/isVipUser/
        nativeWechatAppId: 'wxcf33ce325754da25',
        nativeWechatDomain: 'https://realmaster.com/',
        iosModelsAndNotch: Constants.IosModels,
        useDefaultMap: false,
      }
      AppConfig.instance = this
    }
    return AppConfig.instance
  }

  getAppConfig(key) {
    return this.config[key]
  }

  setAppConfig(configData = {}) {
    if(configData.dualHomepage){
      eventEmitter.emit(Constants.ChangeAppMode,{val:null});
    }
    this.config = { ...this.config, ...configData }
  }
  appConfig() {
    return this.config
  }
}

const instance = new AppConfig()

export default instance

