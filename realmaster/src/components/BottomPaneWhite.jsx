import React,{Component} from 'react';
import PropTypes from 'prop-types';
import {
    StyleSheet,
    View,
    Text,
    TouchableOpacity,
    Dimensions
  } from 'react-native';
// import RMNavBar from '../RMNavBar';
import { Icon } from '../utils/common';

const propTypes = {
  title: PropTypes.string,
  height: PropTypes.number,
};

const defaultProps = {
  height: 240,
};
const { width, height } = Dimensions.get('window');
class BottomPaneWhite extends Component {
  render(){
    //console.log(this.props.children);
    let computedWidth = width/2;
    if(this.props.title1b){
      computedWidth = width - 40;
    }
    let onPress = ()=>{console.log('no on press bind')}
    if(this.props.onPress){
      onPress = this.props.onPress
    }
    return (
      <View style={[styles.bottomPaneWrapper,styles.roundCorner,{height:this.props.height}]}>
        <View key='headerBarElement' style={styles.headerBarElement}>
          <TouchableOpacity onPress={onPress}>
          <View style={{overflow:'hidden',width:computedWidth,flexDirection:'column'}}>
            <View>
              <Text style={{fontSize:15}} ellipsizeMode='tail' numberOfLines={1}>
                {this.props.title}
              </Text>
            </View>
            {this.props.title1b && (
            <View>
              <Text style={{fontSize:12,color:'#666'}} ellipsizeMode='tail' numberOfLines={1}>
                  {this.props.title1b}
                </Text>
            </View>)}
          </View>
          </TouchableOpacity>
          <View style={{flexDirection:'row'}}>
            {this.props.title2 && (
              <View style={{paddingRight:7,paddingTop:3,maxWidth:(width/2)-30}}>
                <Text style={{fontSize:12,color:'#B5B7B8'}} ellipsizeMode='tail' numberOfLines={1}>
                  {this.props.title2}
                </Text>
              </View>
            )}
            <TouchableOpacity onPress={this.props.cbClose} style={{marginRight:0}}>
              <Icon name="rmclose" size={20} color="#ddd"
                style={[]}
                hitSlop={{top:10, bottom: 10, left: 0, right: 0}}
                />
            </TouchableOpacity>
          </View>
        </View>
        <View style={{height:45}}></View>
        {/* <RMNavBar
        style={[styles.navBar]}
        title={{title: this.props.title}}
        rightButton={{cbClose: this.props.cbClose}}
        hide={this.props.hideTitleBar}
        statusBar={this.props.statusBar}
        /> */}
        {this.props.children}
      </View>
    );
  }
};

var styles = StyleSheet.create({
navBar: {
    backgroundColor: '#E03131',
},
bottomPaneWrapper:{
  position: 'absolute',
  bottom: 0,
  // top:74,
  backgroundColor: 'white',
  left: 0,
  right: 0,
  zIndex: 999,
  height: 240,
  shadowColor:'#484848',
  shadowOffset:{width:1,height:3},
  shadowRadius:5,
  shadowOpacity:0.4,
  // elevation:4,
},
roundCorner:{
  borderTopLeftRadius:7,
  borderTopRightRadius:7,
},
headerBarElement: {
  // border-bottom: 0.5px solid #f5f5f5;
  borderBottomColor: 'white',//'#f5f5f5',
  borderBottomWidth:0.5,
  position: 'absolute',
  zIndex:1001,
  left: 0,
  right: 0,
  top: 0,
  // bottom: 0,
  height:45,
  // marginBottom:45,
  padding:10,
  flexDirection:'row',
  justifyContent: 'space-between',
  alignItems: 'center',
},
});
export default BottomPaneWhite;
