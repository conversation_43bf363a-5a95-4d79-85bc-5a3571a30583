import React,{Component} from 'react';
import PropTypes from 'prop-types';

import {
  StyleSheet,
  View,
  Text,
  Image,
  // ImageBackground,
} from 'react-native';
import { Icon } from '../utils/common';

const propTypes = {
  name: PropTypes.string,
  fontSize: PropTypes.number,
};

const defaultProps = {
  fontSize: 11,
};

const DEFAULT_STOP_COLOR = '#007aff';
class PriceMarker extends Component {
  constructor(props){
    super(props);
    this.state = {
      initialRender:true,
      src:'',
    }
  }
  forceUpdate2=()=>{
    // console.log('xxxxx',this.state)
    this.setState({initialRender: false})
  }
  componentDidMount(){
    // const { fontSize, amount, isTopUp, offMarket, suffix, adrltr} = this.props;
  }
  render() {
    const { fontSize, name, icon, color, _id} = this.props;
    var isSelected = this.props.isSelected;
    // console.log(isTopUp)
    if (isSelected) {
      background = '#5c6972';
      border = 'white';//'#484e53';
    } else {
      background = color || DEFAULT_STOP_COLOR;//'#FF5A5F'//#F4604D
      border = 'white';//'#D23F44'
    }
    arrow = {
      borderTopColor:background
    }
    arrowBorder = {
      borderTopColor:border
    }
    return (
      <View style={styles.container}>
        <View style={[styles.bubble,{
            backgroundColor: background,
            borderColor: border}]}>
          {/*<Text style={styles.dollar}>$</Text>*/}
          <Icon name={icon||'rmbus'}
            size={12}
            color="white"
            style={{}}
            // hitSlop={{top: 0, bottom: 0, left: 0, right: 0}}
          />
          {name?<Text style={[styles.amount, { fontSize }]}>{name||''}</Text>:null}
        </View>
        <View style={[styles.arrowBorder,arrowBorder]} />
        <View style={[styles.arrow,arrow]} />
      </View>
    );
  }
}

PriceMarker.propTypes = propTypes;
PriceMarker.defaultProps = defaultProps;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'column',
    alignSelf: 'flex-start',
    backgroundColor:'transparent',
    // width:26,
    // height:27,
  },
  bubble: {
    flex: 0,
    flexDirection: 'row',
    alignSelf: 'flex-start',
    backgroundColor: 'transparent',
    paddingTop: 3,
    paddingBottom: 3,
    paddingRight: 5,
    paddingLeft: 5,
    borderRadius: 11,
    // width:50,
    // height:50,
    borderColor: 'white',
    borderWidth: 2,
    // shadowColor: "#484848",
    // shadowOffset: {
    //   width: 1,
    //   height: 3,
    // },
    // shadowOpacity: 0.2,
    // shadowRadius: 2.84,
    elevation: 5,
  },
  dollar: {
    color: '#FFFFFF',
    fontSize: 10,
  },
  amount: {
    color: '#FFFFFF',
    fontSize: 11,
  },
  suffix: {
    color:'#CE0000',
    fontSize:10,
  },
  suffixWrapper:{
    marginLeft:5,
    backgroundColor: 'white',
    borderRadius:6,
    width:12,
    height:12,
    justifyContent:'center',
    alignItems:'center',
  },
  imgWrapper:{
    marginLeft:5,
    width:14,
    height:14,
    marginTop: 1,
    overflow:'hidden',
  },
  adrltr:{
    width:14,
    height:14,
    borderRadius: 7,
    borderWidth: 1,
    borderColor: 'white',
    // marginLeft:4,
  },
  arrow: {
    backgroundColor: 'transparent',
    borderWidth: 4,
    borderColor: 'transparent',
    borderTopColor: '#FF5A5F',//#
    alignSelf: 'center',
    marginTop: -10,
  },
  arrowBorder: {
    backgroundColor: 'transparent',
    borderWidth: 4,
    borderColor: 'transparent',
    borderTopColor: '#D23F44',//#
    alignSelf: 'center',
    marginTop: -0.5,
  },
});

export default PriceMarker;
