import React, {Component} from 'react';
import {Platform, StyleSheet, Text, View,ImageBackground,FlatList} from 'react-native';
import Prop from './Prop'
import Proj from './Proj'
import Sch from './Sch'
import Geo from './Geo'
import Community from './Community'
// import SectionTitle from './SectionTitle'
import SectionMore from './SectionMore'
import {l10n} from '../../utils/i18n';

export default class ResList extends Component {
  constructor(props) {
    super(props);
  }
  componentDidMount() {
  }
  render() {
    const {lst,tp,tl,more,closePopup,lang,schoolAction,referer,searchStr} = this.props;
    let res;
    let firstMatchedAddr = null
    // this shows a single address when searching for a specific address
    if (tp == 'prop' && lst && lst.length > 0) {
      let tmpItem = lst[0]
      if(tmpItem.lat && tmpItem.lng){
        firstMatchedAddr = <Geo referer={referer} georet={tmpItem} lang={lang} closePopup={closePopup} search={this.props.search} />;
      }
      // console.log('+++++++',tmpItem)
    }
    if (lst && lst.length > 0) {
      res = (
        <View style={{paddingBottom:60,backgroundColor:'white'}}>
          {firstMatchedAddr&&
            <View style={{
              paddingBottom:0,marginBottom:1,
              border:0,
              borderStyle:'solid',
              borderBottomColor:'rgb(240,240,240)',
              borderBottomWidth:0.5,
            }}>
              {firstMatchedAddr}
            </View>
          }
          <FlatList
            keyboardDismissMode='on-drag'
            keyboardShouldPersistTaps='always'
            contentContainerStyle={{paddingBottom: 120}}
            data={lst}
            style={{borderTopWidth:0,marginTop:0}}
            // extraData={this.props.lst}
            keyExtractor={(item,idx) => {
              if(item._id){
                return item._id.toString()
              } else {
                return idx.toString()
              }
            }}
            renderItem={({item}) => {
              if (tp == 'prop') {
                return <Prop referer={referer} prop={item} lang={lang} closePopup={closePopup} search={this.props.search} />;
              } else if (tp == 'proj') {
                return <Proj proj={item} lang={lang} search={this.props.search} searchStr={searchStr}/>;
              } else if (tp == 'sch') {
                return <Sch schoolAction={schoolAction} sch={item} lang={lang} closePopup={closePopup} search={this.props.search}/>;
              } else if (tp == 'geo'){
                return <Geo referer={referer} georet={item} lang={lang} closePopup={closePopup} search={this.props.search} />;
              } else if (tp == 'cmtys'){
                return <Community referer={referer} community={item} lang={lang} closePopup={closePopup} search={this.props.search} />;
              }
            }
            }
          />
          {/* NOTE: TODO: FIX: should not use lenggth, use result hasMore from backend */}
          {(lst.length >= 4) && (['prop','proj'].includes(tp))?
            <SectionMore closePopup={this.props.closePopup} more={more} />
          :null}
        </View>
      )
    } else {
      res = <Text style={styles.noResult}>{l10n('No results found')}</Text>
    }
    return(
      <View>
        {/* <SectionTitle tl={tl} /> */}
        {res}
      </View>
    );
  }
}
const styles = StyleSheet.create({
  noResult:{
    backgroundColor:'#fff',
    padding:20,
    textAlign:'center',
    color:'rgb(177,177,177)',
    fontSize:12
  }
})
