# MapSchool.jsx 代码逻辑详解

## 1. 文件概述

该文件为地图学校图层的核心功能模块，继承自 `MapFeature`，负责地图上学校数据的获取、筛选、渲染、交互、详情弹窗等。支持公立、私立、大学、学院等多类型学校的展示与筛选。

---

## 2. 主要状态与成员变量

- **state 主要字段**：
  - `schs`：当前地图范围内的学校数据（对象，key为学校ID）。
  - `schList`：当前"home school"列表。
  - `sch`：当前选中的学校详情。
  - `showPane`：是否显示学校详情弹窗。
  - `showPanel`：是否显示学校筛选面板。
  - `bns`：当前学校的边界数据。
  - `bn`：当前选中的边界。
  - `loc`：地图上定位点。
  - `featureSetting`：学校筛选条件配置。
  - 其他如 `tracksViewChanges`、`menuItem`、`propFeatureOn` 等。
- **成员变量**：
  - `listDragPan`：拖拽列表引用。
  - `zoomAlertCount`：缩放提示计数。
  - `featureOn`：当前功能层是否激活。

---

## 3. 主要方法功能

### 3.1 学校数据获取与处理
- **doSearchSchools/doSearchNew/doSearch**：根据地图区域和筛选条件，请求后端接口，获取不同类型学校数据。
- **gotSchools/gotPrivateSchools/gotUniversity/gotSchoolsResult**：处理接口返回的学校数据，合并进 state。
- **getHomeSchools/gotHomeSchools**：根据地图点获取"home school"列表。
- **getBound/gotBound**：获取并处理学校边界数据。
- **getPrivateSchoolDetail/gotPrivateSchoolDetail**：获取私立学校详情。

### 3.2 状态与筛选管理
- **toggleOnOff/onOffView**：控制学校图层的开关。
- **setMainValue/resetFilterToAll/toggleFilterOnOff**：切换主筛选项、重置筛选、切换单项筛选。
- **setShowPanel**：控制筛选面板显示。
- **recordPropFeatureOn**：监听房源图层开关，联动学校图层。

### 3.3 地图与UI渲染
- **renderOnMap/renderMarker/renderBounds/renderBound**：渲染学校标记、边界、多边形等地图元素。
- **renderModal**：渲染底部弹窗，包括学校详情、边界选择、学校列表等。
- **renderFeaturePanel/renderModelMenu**：渲染顶部筛选面板及其菜单。
- **renderSchoolItem/renderSchoolTypes/renderGrade**：渲染学校列表项、标签、年级等。
- **keyFact**：渲染学校详情中的关键信息。

### 3.4 交互与事件
- **regionChanged**：地图区域变化时，自动刷新学校数据。
- **fnSelSch/fnSelSchBoundary/fnSelBoundary/fnSelSchDetail**：点击学校标记、边界、详情等的回调。
- **onPress**：地图长按事件，获取当前位置的 home school。
- **closeModal/fnClose/closePanelAndSearch**：关闭弹窗、面板等。
- **unmount**：组件卸载时清理事件。

---

## 4. 组件渲染与执行顺序

1. **构造函数**：初始化状态、注册事件、设置筛选配置。
2. **地图区域变化**：`regionChanged` 触发，调用 `doSearchSchools`，请求并渲染学校数据。
3. **渲染地图元素**：`renderOnMap` 渲染所有学校标记、边界。
4. **用户交互**：点击标记/边界/筛选，触发对应回调，更新 state 并弹出详情或面板。
5. **弹窗与面板**：`renderModal` 根据 state 渲染详情弹窗、边界选择、学校列表等。
6. **筛选与切换**：顶部筛选面板切换主项/子项，动态刷新数据。
7. **组件卸载**：`unmount` 清理所有事件监听。

---

## 5. 依赖与扩展

- **依赖组件**：
  - `MapFeature`：地图功能基类。
  - `Marker`、`Polygon`、`FlatList`、`BottomPaneWhite`、`ListDragPan` 等 UI 组件。
- **工具函数**：如 `filterSchoolName`、`clearSchoolName`、`getCoordinates`、`deepAssign`、`distance4display` 等。
- **后端接口**：如 `findMapSchools`、`findSchools`、`findPrivateSchools`、`getSchBnd`、`getHomeSchools` 等。

---

## 6. 总结

该文件是学校图层的核心，负责学校数据的获取、筛选、地图渲染、详情弹窗、边界选择等全流程。支持多类型学校和丰富的筛选交互，设计高度模块化，便于扩展和维护。

如需了解具体 UI 细节或接口参数，请查阅对应组件和后端文档。 