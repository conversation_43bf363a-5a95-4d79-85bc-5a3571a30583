import Constants from '../config/constants'
import storageIns from './storage'
import { isInChinaByTimeZone } from './common'
import env from '../config/env'

class ServerDomain {
  constructor() {
    // 根据用户时区选择默认域名
    this.serverDomain = isInChinaByTimeZone() ? 'https://ch.realmaster.cn' : env.apiBaseUrl || 'https://realmaster.com'
  }
  
  setDomain(domain) {
    if (domain === 'app.test') {
      domain = 'app.test:8888';  // 添加默认端口
    }
    if (domain !== this.serverDomain) {
      
      this.serverDomain = domain
    }
  }
  async asyncGetDomain() {
    const LastLoadDomain = Constants.CacheKey.LastLoadDomain
    let domain = storageIns.getCacheItem(LastLoadDomain)
    if (!domain) {
      domain = await storageIns.getItem(LastLoadDomain)
    }

    if (domain) {
      this.setDomain(domain)
    }

    return this.serverDomain
  }

  getDomain() {
    return this.serverDomain
  }

  getFullUrl(url) {
    return this.serverDomain + url
  }
  getProtocol() {
    return this.serverDomain.split(':')[0]
  }
}

const instance = new ServerDomain()

export default instance
