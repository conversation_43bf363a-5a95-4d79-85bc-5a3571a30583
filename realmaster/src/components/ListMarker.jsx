import React from 'react';
import PropTypes from 'prop-types';

import {
  StyleSheet,
  View,
  Text,
  Platform
} from 'react-native';

const propTypes = {
  amount: PropTypes.number,
  fontSize: PropTypes.number,
};

const defaultProps = {
  fontSize: 13,
};

class PriceMarker extends React.Component {
  render() {
    const { fontSize, amount, isTopUp, isFromSoldLayer, isSelected } = this.props;
    if(isSelected){
      background = '#5c6972';
      border = '#484e53';
    } else if (isTopUp) {
      background = '#ddddd';
      border = '#eeee';
    } else if (isFromSoldLayer){
      background = '#7269cb'; //purple
      border = '#776DD0';
    } else {
      background = '#E03131'//'#FF5A5F'
      border = '#f1f1f1'//'#D23F44'
    }
    return (
      <View style={styles.container}>
        <View style={[styles.bubble,{
            backgroundColor: background,
            borderColor: border}]}>
          {/*<Text style={styles.dollar}>$</Text>*/}
          <Text style={[styles.amount, { fontSize }]}>{amount||''}</Text>
        </View>
        {/*
          <View style={styles.arrowBorder} />
          <View style={styles.arrow} />
        */}
      </View>
    );
  }
}

PriceMarker.propTypes = propTypes;
PriceMarker.defaultProps = defaultProps;
var marginTop = 0;
if(Platform.OS !== 'ios'){
  marginTop = -2
}
const styles = StyleSheet.create({
  container: {
    // zIndex:10,
    flexDirection: 'column',
    alignSelf: 'flex-start',
    // backgroundColor:'blue',
    // overflow:'hidden',
    // width:20,
    // height:20
  },
  bubble: {
    flex: 0,
    flexDirection: 'row',
    alignSelf: 'flex-start',
    backgroundColor: 'transparent',
    padding: 0,
    width:22,
    height:22,
    marginTop:-0.5,
    alignItems:'center',
    justifyContent: 'center',
    borderRadius: 20,
    borderColor: 'transparent',
    borderWidth: 2,
  },
  dollar: {
    color: '#FFFFFF',
    fontSize: 10,
  },
  amount: {
    color: '#FFFFFF',
    fontSize: 13,
    marginTop:marginTop
  },
  arrow: {
    backgroundColor: 'transparent',
    borderWidth: 4,
    borderColor: 'transparent',
    borderTopColor: '#FF5A5F',
    alignSelf: 'center',
    marginTop: -9,
  },
  arrowBorder: {
    backgroundColor: 'transparent',
    borderWidth: 4,
    borderColor: 'transparent',
    borderTopColor: '#D23F44',
    alignSelf: 'center',
    marginTop: -0.5,
  },
});

export default PriceMarker;