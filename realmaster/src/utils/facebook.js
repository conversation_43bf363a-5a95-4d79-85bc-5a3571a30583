
import {Setting<PERSON>, <PERSON><PERSON>Button, AccessToken, AuthenticationToken, ShareDialog, LoginManager} from 'react-native-fbsdk-next';

// Setting the facebook app id using setAppID
// Remember to set CFBundleURLSchemes in Info.plist on iOS if needed
Settings.setAppID('357776481094717');

// ...

// Build up a shareable link.
// const shareLinkContent = {
//   contentType: 'link',
//   contentUrl: "https://facebook.com",
//   contentDescription: 'Wow, check out this great site!',
// };

// ...

// Share the link using the share dialog.
// LinkWithShareDialog
function FBLogin(opt,cb){
  let permissions = ['public_profile','email'];
  if(opt && opt.permissions){
    permissions = opt.permissions;
  }
  // Attempt a login using the Facebook login dialog asking for default permissions.
  // LoginManager.setLoginBehavior(Platform.OS === 'ios' ? 'native' : 'NATIVE_ONLY');
  LoginManager.logInWithPermissions(permissions).then(
    function(result) {
      if (result.isCancelled) {
        // console.log("Login cancelled");
        cb({cancelled:true})
      } else {
        // if (Platform.OS === 'ios') {
        //   AuthenticationToken.getAuthenticationTokenIOS().then((data) => {
        //     console.log(data?.authenticationToken);
        //   }).catch(error => {
        //     // console.log(error)
        //     cb({err:error})
        //   });;
        // } else {
        AccessToken.getCurrentAccessToken().then((data) => {
          // console.log(data)
          data.ok = 1;
          cb(data)
        }).catch(error => {
          // console.log(error)
          cb({err:error})
        });
        // }
      }
    },
    function(error) {
      console.error("Login fail with error: " + error);
      cb({err:error})
    }
  );
}
function FBLogout(opt,cb){
  let result = LoginManager.logOut(opt)
  // console.log('++++++',result)
  return cb(result)
  // LoginManager.logOut(opt).then(
  //   function(result) {
  //     cb(result)
  //   },
  //   function(error) {
  //     console.error("Login fail with error: " + error);
  //     cb({err:error})
  //   }
  // );
}
function FBshare(content,cb) {
  // console.log('++++++',content);
  // ShareApi.canShare(content).then(
  //   function(canShare) {
  //     if (canShare) {
  //       return ShareApi.share(content, '/me', 'Some message.');
  //     }
  //   }
  // ).then(
  //   function(result) {

  //     // console.log('+++++Result',result)
  //     if (result.isCancelled) {
  //       cb({cancelled:true})
  //       //alert('Share cancelled');
  //     } else {
  //       cb({ok:result.postId})
  //       // alert('Share success with postId: '
  //       //   + result.postId);
  //     }
  //   },
  //   function(error) {
  //     console.log('Share with ShareApi failed with error: ' + error);
  //     cb({err:error})
  //   }
  // );
  // try {
  //   return ShareDialog.show(content);
  // } catch (error) {
  //  console.log('@@@@@',error)
  // }
  // return;
  ShareDialog.canShow(content).then(
    function(canShow) {
      // console.log('______camShow',canShow)
      if (canShow) {
        return ShareDialog.show(content);
      }
    }
  ).then(
    function(result) {
      // console.log('+++++Result',result)
      if (result.isCancelled) {
        cb({cancelled:true})
        //alert('Share cancelled');
      } else {
        cb({ok:result.postId})
        // alert('Share success with postId: '
        //   + result.postId);
      }
    },
    function(error) {
      // console.log('++++++error',error)
      cb({err:error})
    }
  );
}

// TODO: sharePhoto?
export {FBLogin,FBLogout,FBshare}
