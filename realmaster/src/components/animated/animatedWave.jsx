// https://github.com/daihieptn97/react-native-animated-wave/blob/master/index.js
/**
 *
 * https://github.com/facebook/react-native
 *
 * <AUTHOR> Tr<PERSON><PERSON> hi<PERSON>
 * @link : https://github.com/daihieptn97/react-native-animated-wave
 */

import React, {Component} from 'react';
import {
    SafeAreaView,
    StyleSheet,
    ScrollView,
    View,
    Text,
    StatusBar,
    TouchableOpacity,
    Animated,
    Image,
    Easing
} from 'react-native';
// import Animated from 'react-native-reanimated';

const DEFAULT_NUMBER_LAYER = 1;
let animWaveTimes = [];
let animWaveTimeOPs = [];
const DEFAULT_SIZE_OVAN = 150;
const DEFAULT_COLOR_OVAN = 'rgba(33,151,56,0.34)';
const DEFAULT_SIZE_ZOOM = 2;
const DEFAULT_SOUCE_IMG = null;
const DEFAULT_ICON = null;
const DEFAULT_STYLE = {};

class AnimatedWave extends Component {
    constructor(props) {
        super();
        this.waveTime = [];
        this.waveTimeOP = [];
        this.init();
        this.setValueDefault();
        this.state = {
            run: true
        }
        // console.log(props)
    }

    getNumberLayer() {
        const {numberlayer} = this.props;
        return numberlayer;
    }

    getSizeOvan() {
        const {sizeOvan} = this.props;
        return sizeOvan;
    }
    getSizeWave() {
        const {sizeOvan,sizeWave} = this.props;
        return sizeWave || sizeOvan;
    }
    getColor() {
        const {colorOvan} = this.props;
        return colorOvan;
    }

    getSizeZoom() {
        const {zoom} = this.props;
        return zoom;
    }

    getSourceIMG() {
        const {source} = this.props;
        return source;
    }


    init() {
        for (let i = 0; i < DEFAULT_NUMBER_LAYER; i++) {
            this.waveTime[i] = new Animated.Value(0.3);
            this.waveTimeOP[i] = new Animated.Value(0.5);
        }
    }

    setValueDefault() {
        for (let i = 0; i < DEFAULT_NUMBER_LAYER; i++) {
            this.waveTime[i].setValue(0.3);
            this.waveTimeOP[i].setValue(0.5);
        }
    }

    componentDidMount() {
        let duration1 = 2200;
        let duration2 = 2200;
        for (let i = 0; i < DEFAULT_NUMBER_LAYER; i++) {
            animWaveTimes.push(
                Animated.timing(
                    this.waveTime[i],
                    {
                        toValue: this.getSizeZoom(),
                        duration: i === 0 ? duration1 : duration2,
                        easing: Easing.in(),
                        friction: 10,
                        useNativeDriver: false
                    }
                )
            );

            animWaveTimeOPs.push(
                Animated.timing(
                    this.waveTimeOP[i],
                    {
                        toValue: 0,
                        duration: i === 0 ? duration1 : duration2,
                        useNativeDriver: false
                    }
                )
            );
        }
        // this.itv = setInterval(()=>{this.runAnimation()},5000)
        this.runAnimation()
    }

    runAnimation() {
        let itr = -1;
        let stagg = 600
        if(DEFAULT_NUMBER_LAYER<=1){
            stagg = 0
        }
        let a = Animated.loop(
            Animated.parallel([
                Animated.stagger(stagg, animWaveTimes),
                Animated.stagger(stagg+200, animWaveTimeOPs),
            ],{iterations:itr})
        );
        this.setState({
            funcLoop: a
        });
        a.start();
        // console.log('animation started')
        // setTimeout(() => {
        //     this.state.funcLoop.reset();
        // }, 10000);
    }

    componentWillUnmount() {
        // console.log('animation stopped')
        this.state.funcLoop.stop();
    }

    renderWave = () => {
        const arr = [];
        for (let i = 0; i < DEFAULT_NUMBER_LAYER; i++) {
            arr.push(
                <Animated.View key={'animatedWaveItem_'+i}
                               style={[styles.wave, {
                                   transform: [{scale: this.waveTime[i]}],
                                   zIndex: 10,
                                   opacity: this.waveTimeOP[i],
                                   width: this.getSizeWave(),
                                   height: this.getSizeWave(),
                                   backgroundColor: this.getColor(),
                                   borderColor:'transparent',
                                   borderWidth:2,
                               }]}/>
            )
        }
        // console.log('arr=',arr)
        return arr;
    };

    hanlderClickRun = () => {

        try {
            this.props.onPress();
        } catch (e) {
            console.log(e);
        }

        // if (this.state.run) {
        //     this.setState({
        //         run: false
        //     });
        //     this.runAnimation();
        // } else {
        //     this.state.funcLoop.stop();
        //     this.setValueDefault();
        //     this.setState({
        //         run: true
        //     });
        // }
    }

    getStyleContainer() {
        const {style} = this.props;
        return style;
    }

    render() {
        // console.log('+++++',this.getSourceIMG())
        return (
            <Animated.View style={[styles.container, this.getStyleContainer()]}>
                {/* <TouchableOpacity
                    onPress={this.hanlderClickRun}
                > */}
                    {this.getSourceIMG() != null && <Animated.Image
                        style={{
                            width: this.getSizeOvan(),
                            height: this.getSizeOvan(),
                            zIndex: 11,
                            borderRadius: this.getSizeOvan()
                        }}
                        source={this.getSourceIMG()}
                    />}
                    {this.getSourceIMG() == null && <View
                        style={
                            {
                                width: this.getSizeOvan(),
                                height: this.getSizeOvan(),
                                borderRadius: this.getSizeOvan(),
                                backgroundColor: this.getColor(),
                                justifyContent: "center",
                                alignItems: "center"
                            }
                        }>
                        {this.props.icon}
                    </View>}
                    {this.renderWave()}
                {/* </TouchableOpacity> */}
            </Animated.View>
        );
    }
}


const styles = StyleSheet.create({
    wave: {

        position: 'absolute',
        // zIndex: 1112,
        borderRadius: 100
    },
    container: {
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
        // zIndex:1111,
    },
});

AnimatedWave.defaultProps = {
    numberlayer: DEFAULT_NUMBER_LAYER,
    sizeOvan: DEFAULT_SIZE_OVAN,
    colorOvan: DEFAULT_COLOR_OVAN,
    zoom: DEFAULT_SIZE_ZOOM,
    source: DEFAULT_SOUCE_IMG,
    icon: DEFAULT_ICON,
    styleContainer: DEFAULT_STYLE,
    onPress: () => {
    }
}

export default AnimatedWave;