

import React, {Component} from 'react';
import {StyleSheet, View,Switch,TouchableHighlight,FlatList,Text} from 'react-native';
import RmIcon from '../RmIcon';
// import {RMStorage} from '../../RMStorage';
// import {SYSTEM,getAppConfig} from '../../RMSystem';

import {l10n} from '../../utils/i18n';
// import { RMPost,RMGet } from '../../RMNetwork';
import { mainRequest } from '../../utils/request';

// AIzaSyClYqxKKg2WdhFE9UDKA9ekFYHDIMLfdLA || AIzaSyDg3M9-IWfpswsto_nXAIX9ESHt987-SvY
// const ADDR_AUTOCOMPLETE_API = 'https://maps.googleapis.com/maps/api/place/autocomplete/json?key=AIzaSyClYqxKKg2WdhFE9UDKA9ekFYHDIMLfdLA&language=en&components=country:ca';
const ADDR_AUTOCOMPLETE_API = '/1.5/props/streetautocompletemapbox'
export default class AddressList extends Component {
  constructor(props) {
    super(props);1
    this.state = {
      addrs:[],
      searchTxt:props.searchTxt
    }
    // this.toggle = this.toggle.bind(this);
  }
  componentDidMount() {
    this.getData();
  }
  getData = async () => {
    try {

    } catch(e) {
      // error reading value
    }
  }
  componentWillUnmount(){
    // console.log('-------addr unmouted')
    clearTimeout(this.timer);
  }
  componentDidUpdate(prevProps, prevState) {

    const {searchTxt} = this.props;
    // console.log('+++++did update',searchTxt,prevProps.searchTxt)
    if(prevProps.searchTxt !== searchTxt){
      if (this.timer) {
        clearTimeout(this.timer);
      }
      this.timer = setTimeout(()=>{
        if (shwGglAddr == 'on') {
          this.getAddress(searchTxt);
        }
      },1500);
      this.setState({searchTxt:searchTxt});
    }
  }
  getAddress(s) {
    // TODO: use rm geoaddr lib?
    // console.log('++++++do get address',s)
    // RMGet(`${ADDR_AUTOCOMPLETE_API}&input=${s}`,{},(err,responseJson={})=>{
      mainRequest({
        url: ADDR_AUTOCOMPLETE_API,
        method: 'post',
        data: {input:s},
      }).then(responseJson => {
        if(responseJson.status == 'OK') {
          let addrs = [];
          const ps = responseJson.predictions
          for(let i in ps) {
            let addr = ps[i];
            addrs.push({
              _id:addr.place_id,
              addr:addr.structured_formatting.main_text,
              city:addr.structured_formatting.secondary_text,
            })
          }
          this.setState({addrs});
        } else {
          console.error(responseJson.e||responseJson.error_message);
        }
      }).catch(err => {
        console.error(err.toString());
      })
  }
  render() {
    let {shwGglAddr, addrs} = this.state;
    return (
      <View>
        <View style={styles.header}>
          <Text>{l10n('Address Results')}</Text>
          <Switch
            onValueChange={()=>this.toggle()}
            value={(shwGglAddr=='on'?true:false)}
          />
        </View>
        <FlatList
          data={addrs}
          keyExtractor={(item) => item._id}
          renderItem={({item}) =>
            <TouchableHighlight onPress={()=>{this.props.search('addr',item)}}>
              <View style={styles.addr}>
                <RmIcon style={styles.icon} color={'rgb(130,130,130)'} name={'map-marker'} size={20}/>
                <View>
                  <Text>{item.addr}</Text>
                  <Text>{item.city}</Text>
                </View>
              </View>
            </TouchableHighlight>
          }
        />
      </View>
    )
  }
}
const styles = StyleSheet.create({
  header:{
    padding:10,
    alignItems:'center',
    flexDirection:'row',
    justifyContent:'space-between'
  },
  addr:{
    padding:10,
    backgroundColor:'#fff',
    flexDirection:'row',
    alignItems:'center'
  },
  icon:{
    marginRight:10
  }
})
