v6.6.0上线ToDo

bug
  - [x] ios移动中map的定位可能需要点两次才生效：lib问题，基本都是timeout，更换lib后有google获取精度定位的弹窗需要确认
  - [x] 通知或者外部链接到开app，如果app从后台回到前台且满足显示splash的条件，应该不显示splash
  - [x] 关闭系统定位权限，点击定位没有显示alert弹窗：Android系统定位权限判断不准确
  - [x] Android：autocomplete中点needLogin，或者输入特殊指令))9，返回服务器error页面：callback -> async造成方法未调用
  - [x] Android：map底部抽屉样式bug，离底部太近: 手机匹配问题，未发现
  - [x] map 顶部不应该显示 列表 按钮: 未复现，是否因为进了测死服
  - [x] map在强制web时不起作用：test ok, 需要等生效弹窗后再跳出测试
  - [x] WIFI+4g/5g，关闭WIFI红屏且不能自动连接；关闭vpn也无法自动连接；点retry后一直显示连接中: 做了对于vpn连接的优化，快速切换代码bug, baseUrl
  - [x] Android：市场的系统栏颜色未更改: 需要统一domain baseUrl

优化
  - [ ] 网络信号不好的状态能否获取并处理？目前是有timeout后跳转连接页面？网络从差恢复到正常时没有加载（页面一直在转圈）
  - [ ] 网速慢时，总体感觉卡顿不流畅（navigate时打开和返回都很明显？页面 左上角返回点击没有反应）
  - [ ] 首页偶尔白屏时间长,第一次安装白屏
  - [ ] 通知收到后（新闻），到页面跳转之间等待时间长
  - [ ] 未登录时，从高级检索点保存的搜索或其它需要登录功能，跳转登录页面，页面打开耗时长
  - [ ] 注销点击多次不能注销成功


  bug / ios / 12.24 - allen

- [x] 1. 搜索 成交快捷 -> 有时候跳转不到
- [x] 2. notification测试，发送成功了，显示不成功 / crash / 清空
- [x] 3. map点击，等待时间长，已回退
- [x] 4. retry button padding
- [x] 5. map 图层铺盖，卫星地图，语言切换
- [x] 6. map画图，每个点要点两第二次才能选中
- [x] 7. map学校图层打开
- [x] 8. vpn map不显示Google地图
- [x] 9. apple map卫星地图


bug / ios / 12.30
- [x] 1. apple 地图不加载、vpn map自动切Google
- [x] 2. notification崩溃、消息清空（aps）
- [x] 3. webview url不同步更新（navigation.isFocused）
- [x] 4. 缓存同步callback修改
- [x] 5. Review: zhangman liurui coderabbitai
- [x] 6. startup.drawio ai
- [-] 7. config webapp
- [x] 8. feedback allen, test ok

 0106
- fix and debug on v6.6.0
  - [x] 1. /online 并发请求, map层级多次加载, 增加universallink配置 - fixed
  - [x] 2. /cpm 请求、微信图片不展示 - debuged
  - [x] 3. 下载图片权限 - fixed
- 项目
  - [x] 1. mpa demo提交一版本，第二版优化中
  - [x] 2. appweb环境配置完成，还未跑起来
- 需求以及review
  - [x] 1. review 2 branches
- conference
  - [x] 1. allen同步安卓问题，讨论网络和地图服务
  - [x] 2. share mpa架构
  - [x] 3. 官网需求实现nodejs + mustache（tml/template、Jet、Pongo2、Raymond）

  

  0113
  - fix and debug on v6.6.0
    - [x] 1. 分享微信图片debug，完善权限请求
    - [x] 2. 地图初始化和条件筛选顺序请求时序
    - [x] 3. web多层index导致map销毁后无法打开
    - [x] 4. web多层，返回map入口预留
    - [x] 5. debug地图服务，houseSigma, react-native-maps
    - [x] 6. 同步修改到android，build apk to allen

  - 项目
    - [x] 1. 比较了go中的模版引擎，最终还是选择mustache保证一致性
    - [x] 2. 比较了daisy ui和shadcn ui。设计给了反馈，前端待实验，补充文档

