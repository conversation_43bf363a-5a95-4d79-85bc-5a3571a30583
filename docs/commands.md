## Android
### abd操作
1. 安装adb
```bash
brew install android-platform-tools
```

2. abd查看连接设备列表
```
adb devices -l
```

3. abd安装app
```bash
adb -s QSIZJN4D9HYLIVAE install ./app/build/outputs/apk/debug/app-debug.apk
```

### 打包
1. debug打包apk
```bash
npx react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/src/main/res
```
```bash
cd android
```
```bash
./gradlew assembleDebug
```
获取到在以下目录的`app-debug.apk`
```bash
realmaster/android/app/build/outputs/apk/debug/app-debug.apk
```

### applink跳转app页面测试
> 小米手机自带浏览器打开地址https://www.realmaster.cn/s/5roUsR24nk?lang=zh-cn，无法跳转app，使用命令行进行测试
1. 连接手机活着模拟器
2. 终端输入
```bash
adb shell am start -a android.intent.action.VIEW -d "https://www.realmaster.cn/s/5roUsR24nk?lang=zh-cn" com.realmaster
```
可以启动并进入app对应页面表示成功
3. 相关文件
https://realmaster.com/.well-known/assetlinks.json

realmaster/android/app/src/main/AndroidManifest.xml
