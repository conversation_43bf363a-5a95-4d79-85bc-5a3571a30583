import React from "react";
import { Pressable, StyleSheet, View, Text, ImageBackground } from "react-native";

const SplashBackground = ({ children, onSplashPress, splashImage }) => {
    return (
        <View style={styles.container}>
            <Pressable onPress={onSplashPress} style={{ flex: 1 }}>
                <ImageBackground
                    style={styles.backgroundImage}
                    source={splashImage}
                >
                    {children}
                </ImageBackground>
            </Pressable>

        </View>
    )
}

//backgroundColor: 'transparent'，背景颜色设置为透明，因为组件渲染后，设置图片有个时差，所以背景颜色设置为透明，可以避免闪烁
const styles = StyleSheet.create({
    container: {
        position: 'absolute',
        height: '100%',
        width: '100%',
        top: 0,
        left: 0,
        backgroundColor: 'transparent',
        zIndex: 1
    },
    backgroundImage: {
        flex: 1,
        width: '100%',
        height: '100%',
        flexDirection: 'row-reverse',
    }
});

export default SplashBackground;
