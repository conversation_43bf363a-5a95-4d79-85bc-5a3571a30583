import React, {Component} from 'react';
import {Platform, StyleSheet, Text, View,ImageBackground,TouchableOpacity} from 'react-native';
import RmIcon from '../RmIcon'

export default HistRight = (props) =>{
  const {item} = props;
  return(
    <View style={styles.histRight}>
      {/* <Text style={styles.histTs}>{item.histTs?item.histTs.substr(0,10):''}</Text> */}
      <TouchableOpacity style={styles.onPress} onPress={()=>props.removeHist(item._id)}>
        <RmIcon size={16} style={styles.histClose} name={'rmclose'} color={'#dbdbdb'}/>
      </TouchableOpacity>
    </View>
    // <View style={styles.hist}>
    //   <View style={styles.histLeft}>
    //     <RmIcon size={24} style={styles.histIcon} name={item.tp} color={'rgb(183,183,183)'}/>
    //     <Text>{item.search}</Text>
    //   </View>
      
    // </View>
  );  
}
const styles = StyleSheet.create({
  histRight:{
    flexDirection:'row',
    alignItems:'center'
  },
  // histTs:{
  //   color:'#777',
  //   marginRight:3,
  //   fontSize:10
  // },
  onPress:{
    padding:10
  },
  histIcon:{
    marginRight:10
  },
})