# 学校Marker点击到底部弹出信息卡片的函数调用流程分析

## 概述

本文档分析了React Native地图应用中，用户点击学校marker后底部弹出学校信息卡片的完整函数调用流程。该流程涉及事件处理、状态管理、异步数据获取和UI渲染等多个环节。

进入地图doSearchNew -> findMapSchools -> getPublicSchools
点击mark 公校getSchBnd -> getPublicSchoolById
点击房源mark，底部房源信息页中点击学校图标 -> getHomeSchools

## 流程图

```mermaid
flowchart TD
    A["用户点击地图上的学校Marker"] --> B["Marker组件触发onPress事件"]
    B --> C["调用 this.fnSelSch(sch) 返回的函数"]
    C --> D["执行 fnSelSch 内部函数逻辑"]
    D --> E["调用 this.closeOtherModals()"]
    E --> F["设置基础状态<br/>state = {sch: sch, bn: null, tracksViewChanges: true, <br/>loc: null, searchDialog: null, schList: null}"]
    F --> G{"判断学校类型"}
    
    G --> |"私立学校<br/>(sch.private)"| H["设置 state.bns = null<br/>state.showPane = true"]
    H --> I["调用 getPrivateSchoolDetail(sch)"]
    I --> J["发起网络请求 requestStdFn('getPrivateSchool', {schId: sch._id})"]
    J --> K["请求成功后调用 gotPrivateSchoolDetail(ret)"]
    K --> L["处理私立学校数据<br/>设置 sch.private = true<br/>setState({sch: sch, showPane: true, bns: null, bn: null})"]
    
    G --> |"公立学校且有边界数据<br/>(sch.bns && sch.bns.length > 0)"| M{"检查边界数量"}
    M --> |"多个边界<br/>(sch.bns.length > 1)"| N["设置 state.bns = sch.bns<br/>state.showPane = false<br/>显示边界选择列表"]
    M --> |"单个边界<br/>(sch.bns.length = 1)"| O["设置 state.bns = null<br/>state.showPane = true"]
    O --> P["调用 getBound(sch, 0)"]
    
    G --> |"公立学校但无边界数据"| Q["设置 state.bns = null<br/>state.showPane = true"]
    Q --> R["调用 getBound(sch, -1)"]
    
    P --> S["发起网络请求 requestStdFn('getSchBnd', {schId: sch._id, bn: bn})"]
    R --> S
    S --> T["请求成功后调用 gotBound(ret, bn)"]
    T --> U["处理学校边界数据<br/>设置学校简化名称<br/>setState({sch: sch, showPane: true, bns: null, bn: bn})"]
    
    L --> V["最终调用 this.setState(state)<br/>触发组件重新渲染"]
    N --> V
    U --> V
    
    V --> W["组件重新渲染时调用 render() 方法"]
    W --> X["render() 中调用 this.allFeatures.map(f => f.renderModal())"]
    X --> Y["MapSchool.renderModal() 方法被调用"]
    Y --> Z{"检查状态条件"}
    
    Z --> |"this.state.showPane = true"| AA["渲染学校详情模态框"]
    Z --> |"this.state.schList 存在"| BB["渲染学校列表模态框"]
    Z --> |"this.state.bns 存在"| CC["渲染边界选择模态框"]
    Z --> |"其他条件"| DD["返回 null，不渲染任何模态框"]
    
    AA --> EE["返回 BottomPaneWhite 组件"]
    EE --> FF["设置学校信息：<br/>- title: sch.nm (学校名称)<br/>- title1b: 学校地址<br/>- height: 动态高度<br/>- onPress: fnSelSchDetail(sch)<br/>- cbClose: fnClose()"]
    FF --> GG["渲染学校详细信息：<br/>- 学校类型标签 (renderSchoolTypes)<br/>- 关键信息 (keyFacts)<br/>- 评分等级信息<br/>- 边界信息等"]
    
    GG --> HH["BottomPaneWhite 组件在屏幕底部显示<br/>用户看到学校信息卡片"]
    
    style A fill:#e1f5fe
    style HH fill:#c8e6c9
    style EE fill:#fff3e0
    style V fill:#f3e5f5
```

## 详细流程分析

### 1. 事件触发阶段

#### 1.1 Marker渲染
在 `MapSchool.jsx` 的 `renderMarker()` 方法中（第886行），创建学校marker（renderOnMap里调用renderMarker）：

```javascript
// Marker react-native-maps自带的
<Marker
  stopPropagation={true}
  tracksViewChanges={this.state.tracksViewChanges}
  style={{zIndex}}
  key={'' + sch._id + sch.nm}
  onPress={this.fnSelSch(sch)}    // 绑定点击事件
  coordinate={loc}
>
  {/* marker内容 */}
</Marker>
```

#### 1.2 事件绑定
- **事件属性**: `onPress={this.fnSelSch(sch)}`
- **返回值**: `fnSelSch(sch)` 返回一个事件处理函数
- **触发时机**: 用户点击marker时

### 2. 事件处理阶段

#### 2.1 核心处理函数: `fnSelSch(sch)`
位置：`MapSchool.jsx` 第1753行

```javascript
fnSelSch(sch) {
  return e => {
    if (e.persist) {
      e.persist(); // 避免事件池相关警告
    }
    var self = this;
    if (!sch) return false;
    
    // 关闭其他模态框
    this.closeOtherModals();
    
    // 设置基础状态
    let state = {
      sch: sch, 
      bn: null, 
      tracksViewChanges: true, 
      loc: null, 
      searchDialog: null, 
      schList: null
    };
    
    // 根据学校类型分别处理...
    // 如果是私校，getPrivateSchool 根据id查询私校数据
    // 如果sch.bns有值，大于1个值，showPane = false；否则 getBound
    // 否则 getBound
  };
}
```

#### 2.2 学校类型判断逻辑

**私立学校处理**:
```javascript
if (sch.private) {
  state.bns = null;
  state.showPane = true;
  this.getPrivateSchoolDetail(sch);
}
```

**公立学校有边界数据**:
```javascript
else if (sch.bns && sch.bns.length > 0) {
  if (sch.bns.length > 1) {
    // 多个边界：显示边界选择列表
    state.bns = sch.bns;
    state.showPane = false;
  } else {
    // 单个边界：直接显示学校详情
    state.bns = null;
    state.showPane = true;
    this.getBound(sch, 0);
  }
}
```

**公立学校无边界数据**:
```javascript
else {
  state.bns = null;
  state.showPane = true;
  this.getBound(sch, -1);
}
```

### 3. 数据获取阶段

#### 3.1 私立学校数据获取
**方法**: `getPrivateSchoolDetail(sch)` (第1789行)

```javascript
getPrivateSchoolDetail(sch) {
  if (sch) {
    requestStdFn('getPrivateSchool', {schId: sch._id})
      .then(ret => {
        this.gotPrivateSchoolDetail(ret);
      })
      .catch(err => {
        console.log(err);
        return;
      });
  }
}
```

**数据处理**: `gotPrivateSchoolDetail(sch)` (第1802行)

```javascript
gotPrivateSchoolDetail(sch) {
  if (sch.sch) sch = sch.sch;
  sch.private = true;
  
  // 处理特殊字段
  for (k of ['boarding', 'admIntvw', 'admSSAT']) {
    if (sch[k] && sch[k] != 'Yes') {
      delete sch[k];
    }
  }
  
  this.setState({
    sch: sch, 
    showPane: true, 
    bns: null, 
    bn: null, 
    tracksViewChanges: true
  });
  this.trackOff();
}
```

#### 3.2 公立学校边界数据获取
**方法**: `getBound(sch, bnIndex)` (第1813行)

```javascript
getBound(sch, bnIndex) {
  if (sch && sch._id && !(sch.university || sch.college)) {
    let bn = null;
    if (sch.bns && sch.bns[bnIndex]) {
      bn = sch.bns[bnIndex];
    }
    
    requestStdFn('getSchBnd', {schId: sch._id, bn: bn})
      .then(ret => {
        this.gotBound(ret, bn);
      })
      .catch(err => {
        console.log(err);
        return;
      });
  }
}
```

**数据处理**: `gotBound(sch, bn)` (第1837行)

```javascript
gotBound(sch, bn) {
  sch._id = '' + sch._id;
  sch.snm = filterSchoolName(sch);
  sch.ssnm = clearSchoolName(sch);
  
  this.setState({
    sch: sch, 
    showPane: true, 
    bns: null, 
    bn: bn, 
    tracksViewChanges: true
  });
  this.trackOff();
}
```

### 4. 渲染阶段

#### 4.1 主组件渲染入口
在 `RMMapSearchNative.jsx` 的 `render()` 方法中（第1393行）：

```javascript
// 获取底部模态框内容
bottomModel = this.allFeatures.map((f) => { 
  return f.renderModal(); 
});
```

#### 4.2 学校模态框渲染
**方法**: `MapSchool.renderModal()` (第1310行)

**状态检查**:
```javascript
renderModal() {
  if (!this.featureOn) return null;
  
  // 显示学校详情面板
  if (this.state.showPane) {
    // 渲染学校详情...
  }
  
  // 显示学校列表
  if (this.state.schList) {
    // 渲染学校列表...
  }
  
  // 显示边界选择
  if (this.state.bns) {
    // 渲染边界选择...
  }
  
  return null;
}
```

#### 4.3 学校详情卡片渲染
当 `this.state.showPane = true` 时，渲染学校详情（第1412-1509行）：

```javascript
return (
  <BottomPaneWhite
    key={'schoolDetailModal'}
    height={height}
    title={sch.nm}                    // 学校名称
    title1b={(sch.addr || 'No addr') + ', ' + (sch.city || '')}  // 学校地址
    onPress={this.fnSelSchDetail(sch)}  // 点击查看详情
    cbClose={this.fnClose()}>           // 关闭回调
    
    {/* 学校详细信息内容 */}
    <View key={'schDetail'} style={{marginTop: 10}}>
      {/* 学校类型标签 */}
      <View key={'schGrades'} style={{flexDirection: 'row', paddingLeft: 10, width: '100%'}}>
        {grades}
      </View>
      
      {/* 关键信息区域 */}
      <TouchableOpacity onPress={this.fnSelSchDetail(sch)} key={'schKeyFacts'}>
        {!(sch.university || sch.college) && (
          <View style={{borderTopColor: '#f1f1f1', borderTopWidth: 1, marginTop: 15}} key={'kfWrapper'}>
            {/* 关键信息标题 */}
            <View style={styles.schoolNameWrapper} key={'kf'}>
              <Text key={'kf'} style={styles.schoolName}>
                {keyFactLabel}
              </Text>
            </View>
            
            {/* 关键信息内容 */}
            <View style={{marginTop: 15, flexDirection: 'row', justifyContent: 'space-around'}} key={'kfView'}>
              {keyFacts}
            </View>
          </View>
        )}
      </TouchableOpacity>
    </View>
  </BottomPaneWhite>
);
```

### 5. 关键信息构建

#### 5.1 学校类型标签
**方法**: `renderSchoolTypes(sch, grades)` (第1026行)

处理学校标签信息，如：
- 学校级别（Elementary, Middle, Secondary）
- 语言类型（English, French Immersion）
- 学校性质（Catholic, Public）
- 特殊项目（IB, AP, Gifted等）

#### 5.2 关键事实信息
**构建逻辑**（第1351-1404行）：

```javascript
let keyFacts = [];

if (sch.private) {
  // 私立学校关键信息
  if (sch.sex) keyFacts.push(this.keyFact('Gender', l10n('Gender'), sch.sex));
  if (sch.tuitn) keyFacts.push(this.keyFact('Tuition', l10n('Tuition'), sch.tuitn));
  if (sch.fndd) keyFacts.push(this.keyFact('Founded', l10n('Founded'), sch.fndd));
}

if (sch.firate) {
  // Fraser Institute评分信息
  keyFacts.push(this.keyFact('firate', l10n('Rating'), sch.firate));
  if (sch.firank) keyFacts.push(this.keyFact('firank', l10n('Fraser') + '/' + sch.fitotal, sch.firank));
}

if (this.state.bn) {
  // 当前边界信息
  keyFacts.push(this.keyFact('curbnd', l10n('Current Boundary'), 
    gfConvert(this.state.bn.gf) + '-' + this.state.bn.gt + ' ' + fiConvert(this.state.bn.fi)));
}
```

### 6. 相关工具函数

#### 6.1 学校名称处理
```javascript
function filterSchoolName(sch) {
  return sch.nm
    .toUpperCase()
    .replace(' Junior and Senior Public School', '')
    .replace('Junior Public School', 'JP')
    .replace('Senior Public School', 'SP')
    .replace('Public School', 'PS')
    .replace('Catholic School', 'CS')
    .replace(' Community School', '');
}

function clearSchoolName(sch) {
  return sch.nm.replace(/\s(Public|School|Elementory|Middle|Secondary|Catholic)/gi, '');
}
```

#### 6.2 关闭回调函数
```javascript
fnClose() {
  let self = this;
  return () => {
    self.setState({
      bns: null, 
      showPane: null, 
      tracksViewChanges: true
    });
    this.trackOff();
  };
}
```

## 核心文件和类

### 主要文件
- **`RMMapSearchNative.jsx`**: 主地图组件，负责整体渲染和feature管理
- **`MapSchool.jsx`**: 学校功能组件，处理学校相关的所有逻辑
- **`BottomPaneWhite.jsx`**: 底部白色面板UI组件

### 核心类和方法
- **MapSchool类**: 继承自MapFeature，专门处理学校相关功能
- **关键方法**:
  - `renderMarker()`: 渲染学校marker
  - `fnSelSch()`: 处理marker点击事件
  - `renderModal()`: 渲染模态框
  - `getPrivateSchoolDetail()`: 获取私立学校详情
  - `getBound()`: 获取学校边界信息

## 状态管理

### 关键状态字段
```javascript
state = {
  sch: null,           // 当前选中的学校对象
  showPane: false,     // 是否显示学校详情面板
  showPanel: false,    // 是否显示学校筛选面板
  bns: null,          // 边界数据数组
  bn: null,           // 当前选中的边界对象
  schs: {},           // 所有学校数据
  schList: null,      // 学校列表数据
  tracksViewChanges: true  // 性能优化标志
}
```

### 状态流转
1. **初始状态**: 所有相关状态为null/false
2. **点击marker**: 设置sch对象，根据学校类型设置showPane或bns
3. **数据获取完成**: 更新sch数据，设置showPane=true
4. **渲染阶段**: 基于showPane状态决定是否渲染底部面板
5. **关闭面板**: 重置相关状态为null/false

## 网络请求

### API调用
- **私立学校详情**: `requestStdFn('getPrivateSchool', {schId: sch._id})`
- **学校边界信息**: `requestStdFn('getSchBnd', {schId: sch._id, bn: bn})`

### 错误处理
所有网络请求都包含catch块进行错误处理，失败时记录错误日志但不阻断用户操作。

## 性能优化

### 渲染优化
- **tracksViewChanges**: 控制marker是否跟踪视图变化，减少不必要的重渲染
- **trackOff()**: 延时关闭视图变化跟踪的方法

### 内存管理
- **closeOtherModals()**: 确保同时只有一个模态框显示
- **状态清理**: 在打开新面板前清理之前的状态

## 总结

这个流程展现了一个完整的React Native应用中从用户交互到UI渲染的数据流，涉及了：

1. **事件处理**: 从用户点击到事件响应
2. **状态管理**: 复杂的状态更新和流转
3. **异步数据**: 网络请求和数据处理
4. **条件渲染**: 基于状态的UI渲染逻辑
5. **性能优化**: 减少不必要的渲染和计算

整个流程设计体现了良好的组件化思想和数据驱动的开发模式，通过清晰的状态管理实现了复杂的交互逻辑。 