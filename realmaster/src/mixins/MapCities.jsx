/*
*/
import React, {Component} from 'react';
import {
  StyleSheet,
  View,
  Text,
  Dimensions,
  // TouchableHighlight,
  TouchableOpacity,
  FlatList,
//   Image,
//   ScrollView,
} from 'react-native';
import MapFeature from './MapFeature';
// import { Marker } from 'react-native-maps';
//import PriceMarker from '../views/PriceMarker';
// import IconText from '../views/IconText';
import BottomPane from '../components/BottomPane';
// import {RMPost,getServerDomain} from '../RMNetwork';
import serverDomainIns from '../utils/serverDomain';
import { mainRequest } from '../utils/request';
import {l10n} from '../utils/i18n';
import { Icon, eventEmitter } from '../utils/common';

const { width, height } = Dimensions.get('window');
var modalHeight = height-44;
class MapCities extends MapFeature{
  constructor(map,props){
    super(map,"MapCities",props);
    this.state = {
      tracksViewChanges:false,
      paddingRight:0,
      ptype:'Residential',
      projCitySelect:false,
      cities:[],
    }
    this.featureOn = true;
    // console.log('xxxxx',map.data)
    // if (map.data.stig) {
    this.state.paddingRight = 10;
    // }
    // this.getProjCities();
    eventEmitter.on("map.props.ptype",this.setPtype)
  }
  getProjCities(p){
    if (this.state.cities.length) {
      return;
    }
    var self = this;
    mainRequest({
      url: "/1.5/prop/projects/projCities",
      method: 'post',
      data: {}
    }).then(ret=> {
      if (ret.err || ret.e) {
        return this.flashMessage(ret.err || ret.e);
      }
      let cities = []
      if (ret.ok) {
        cities = self.parseCityList(ret.l);
      }
      this.setState({cities},()=>{
        // console.log(this.state.dispVar);
      });
    }).catch(err=> {
      this.flashMessage(err,ret)
      return;
    })
  }
  parseCityList(l){
    var ret = [];
    var map = {};
    for (let i of l) {
      if (!map[i.prov]) {
        map[i.prov] = [];
      }
      map[i.prov].push(i);
    }
    // var keys = Object.keys(map);
    for (let key in map) {
      ret.push({isProv:true, prov:key, p:map[key][0].p});
      for (let i of map[key]) {
        ret.push(i)
      }
    }
    // console.log(ret);
    return ret;
  }
  regionChanged(event,map){
    // console.log("regionChanged zoom:" + event.zoom);
    super.regionChanged(event,map);
  }
  setPtype=(ptype)=>{
    this.setState({ptype})
  }
  renderOnMap(){
    return null;
  }
  selectCity(c){
    if (c) {
      this.setState({projCitySelect:false})
      // window.bus.$emit('set-project-city', c);
      eventEmitter.emit("map.props.city",c)
    } else {
      let projCitySelect = this.state.projCitySelect;
      // this.setState({projCitySelect:!projCitySelect})
      // this.showCities = !this.showCities;
    }
  }
  renderCityRow(c){
    function getCityName(c){
      if(c.isProv){
        return c.p
      }
      if (c.o != c.n) {
        return `${c.o} ${c.n}`;
      } else {
        return c.o;
      }
    }
    let split = {}
    if(c.isProv){
      split = {
        borderTopWidth: 10,
        borderTopColor: '#f1f1f1'
      }
    }
    return (<TouchableOpacity
      onPress={()=>{this.selectCity(c)}}
      style={{}}
      key={'cityList'+getCityName(c)}
      >
      <View style={[{flexDirection:'row',padding:10},split]}>
        <Text style={styles.title}>{getCityName(c)}</Text>
      </View>
    </TouchableOpacity>)
  }
  // TODO: map project city select
  renderFullScreenModal(){
    // div#citySelect.modal(:class="{active:showCities}")
    // header.bar.bar-nav
    //   a.icon.icon-close.pull-right(@click="selectCity()")
    //   h1.title {{_('Select City')}}
    // div.content
    //   ul.table-view
    //     li.table-view-cell.split(@click="selectCity({})") {{_('All')}}
    //     li.table-view-cell(v-for="c in cities", @click="selectCity(c)", :class="{split:c.isProv}")
    //       span(v-show="c.isProv") {{c.p}}
    //       span(v-show="!c.isProv") {{displayCity(c)}}
    if (!this.featureOn) return null;
    const allCitySelect = this.state.cities.length?(
      <TouchableOpacity
        onPress={()=>{this.selectCity({})}}
        style={{}}
      >
        <View style={{flexDirection:'row',padding:10}}>
          <Text style={styles.title}>All</Text>
        </View>
      </TouchableOpacity>
      ):null;
    if (this.state.projCitySelect){
      // console.log('render school list');
      // return;
      return (
        <BottomPane key={'citiesSelect'}
          statusBar={{hidden:true}}
          height={modalHeight}
          title={l10n('Select City')}
          cbClose={()=>{this.setState({projCitySelect:false});}}>
          <FlatList
            // ref={(ref) => { this.flatListRef = ref; }}
            // style={styles.mainSelector}
            ListHeaderComponent={allCitySelect}
            contentContainerStyle={{}}
            bounces={true}
            showsVerticalScrollIndicator={false}
            showsHorizontalScrollIndicator={false}
            data={this.state.cities}
            contentInset={{ right: 0, top: 0, left: 0, bottom: 3 }}
            horizontal={false}
            renderItem={({item,index,separators})=>{return this.renderCityRow(item)}}
            keyExtractor={(c) => {return c.o}}
          ></FlatList>
        </BottomPane>);
    }
  }
  renderModal(){
    return null;
  }
  unmount(){
    super.unmount();
    eventEmitter.removeListener("map.props.ptype",this.setPtype);
  }
  setCenter(loc){
    // console.log('set center',loc);
    // var latitudeDelta = 0.095;
    // var region = {
    //   latitudeDelta: latitudeDelta,
    //   longitudeDelta: latitudeDelta*ASPECT_RATIO,
    //   latitude:  loc.lat,
    //   longitude: loc.lng,
    // }
    // this.map.animateToRegion(region, 400);
    this.map.setCenterAndZoom(loc,{zoom:13})
  }
  showCitySelect(opts={}){
    // console.log('show city select in new page');
    // TODO:1 ptype, 2 open-project-city-modal
    var optsStr = opts.doSearch?'search=1':'';
    var url = serverDomainIns.getFullUrl(`/1.5/city/select?${optsStr}`);
    var opt = {
      hide:false,
      sel:'#callBackString',
      tp:'pageContent',
      // toolbar:false,
      url:url
    }
    opt.title = l10n('Select City')
    var self = this;
    var isProject = this.state.ptype == 'Project';
    if (isProject) {
      // window.bus.$emit('open-project-city-modal');
      // console.log('here!!')
      // this.setState({projCitySelect:true})
      opt.url = serverDomainIns.getFullUrl('/1.5/prop/projects/cities');
      // RMSrv.getPageContent(url,'#callBackString',{title:this._('Select City')},function(val) {
      //   if (val == ':cancel') {
      //     return;
      //   }
      //   try {
      //     var c = JSON.parse(val);
      //   } catch (err) {
      //     var c = {};
      //   }
      //   window.bus.$emit('set-project-city', c);
      // });
      // return;
    }
    // console.log('++++',this.state.ptype,opt)
    // this.clearModals();
    var cb = (val)=>{
      // console.log('MapSearch cb val: '+val);
      if (val == ':cancel') {
        // console.log('canceled');
        return;
      }
      try {
        var city = JSON.parse(val);
        // alert(JSON.stringify(city));
        // console.log('city is: ',city);
        eventEmitter.emit('map.clearModals',{src:'mapSearch',backdrop:false})
        // eventEmitter.emit(SYSTEM.EVENT_CLEAR_FEATURE_PANEL,{});

        if(isProject){
          self.selectCity(city)
          return
        }
        city = city.city
        // TODO: unset vals
        this.setCenter({lat:city.lat,lng:city.lng})
        // window.bus.$emit('set-city',city);
      } catch (e) {
        // console.error(e);
      }
    }
    eventEmitter.emit("app.message",{msg:JSON.stringify(opt),cb:cb});
  }
  renderButton(id){
    // console.log('here');
    // return null;
    return (
      <TouchableOpacity key={'citySelectBtn'} style={[styles.btnListView,styles.navCityBtn,{
          paddingRight:this.state.paddingRight,
          // backgroundColor:'yellow',
        }]}
        onPress={(e)=>{this.showCitySelect(e)}}>
        <View>
          <Text style={{color: 'white', fontSize:16}}>{l10n('CITY')}</Text>
        </View>
      </TouchableOpacity>
    )
  }
  renderButton2(id){
    return null
    return (
      <TouchableOpacity key={id} style={[styles.btnListView,styles.navCityBtn,{paddingRight:this.state.paddingRight}]} onPress={(e)=>{this.showCitySelect(e)}}>
        <View style={{borderRightColor:'#f5f5f5',borderRightWidth:0.5,flexDirection:'row'}}>
          <Text style={{marginLeft:12, color: '#777', fontSize:16}}>{l10n('City')}</Text>
          <Icon name="caret-down"
            size={14}
            color="#777"
            style={{marginLeft:7,marginRight:14,marginTop:1,height:16,alignSelf:'center'}}
            // hitSlop={{top: 10, bottom: 10, left: 0, right: 120}}
          />
        </View>
      </TouchableOpacity>
    )
  }
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'column',
    alignSelf: 'flex-start',
  },
  btnListView:{
    // paddingRight:paddingRight
    // position: 'absolute',
    // top: 10,
    // left:45,
    // width:44
  },
  navCityBtn:{
    flexDirection:'row',
    alignItems: 'center',
    justifyContent:'center',
  }
});

export default MapCities;
