package com.realmaster;

import android.app.Application;
import com.facebook.react.PackageList;
import com.facebook.react.ReactApplication;
import com.facebook.react.ReactNativeHost;
import com.facebook.react.ReactPackage;
import com.facebook.react.defaults.DefaultNewArchitectureEntryPoint;
import com.facebook.react.defaults.DefaultReactNativeHost;
import com.facebook.soloader.SoLoader;
import java.util.List;
import com.wix.reactnativenotifications.RNNotificationsPackage;
import com.wechatlib.WeChatLibPackage; // Add this line

// https://github.com/wonday/react-native-orientation-locker
import org.wonday.orientation.OrientationActivityLifecycle;
// import com.theweflex.react.WeChatPackage; // Add this line

import android.util.Log;

public class MainApplication extends Application implements ReactApplication {

  private final ReactNativeHost mReactNativeHost =
      new DefaultReactNativeHost(this) {
        @Override
        public boolean getUseDeveloperSupport() {
          return BuildConfig.DEBUG;
        }

        @Override
        protected List<ReactPackage> getPackages() {
          @SuppressWarnings("UnnecessaryLocalVariable")
          List<ReactPackage> packages = new PackageList(this).getPackages();
          // Packages that cannot be autolinked yet can be added manually here, for example:
          // packages.add(new MyReactNativePackage());
          // packages.add(new WeChatLibPackage()); // Add this line
          return packages;
        }

        @Override
        protected String getJSMainModuleName() {
          return "index";
        }

        @Override
        protected boolean isNewArchEnabled() {
          return BuildConfig.IS_NEW_ARCHITECTURE_ENABLED;
        }

        @Override
        protected Boolean isHermesEnabled() {
          return BuildConfig.IS_HERMES_ENABLED;
        }
      };

  @Override
  public ReactNativeHost getReactNativeHost() {
    return mReactNativeHost;
  }

  @Override
  public void onCreate() {
    // https://stackoverflow.com/questions/57709742/unable-to-instantiate-fragment-com-swmansion-rnscreens-screen
    super.onCreate();
    SoLoader.init(this, /* native exopackage */ false);
    if (BuildConfig.IS_NEW_ARCHITECTURE_ENABLED) {
      // If you opted-in for the New Architecture, we load the native entry point for this app.
      DefaultNewArchitectureEntryPoint.load();
    }
    /**
   * Stub class that does nothing to ease the migration out of Flipper. Users should stop calling
   * `ReactNativeFlipper.initializeFlipper` in their `MainApplication.java` as this class will be
   * removed in React Native 0.75 or future versions.
   */
    //https://github.com/facebook/fresco/issues/2751
    // ReactNativeFlipper.initializeFlipper(this, getReactNativeHost().getReactInstanceManager());

    // 方式1：通过Resources获取
    String mapsApiKey = getResources().getString(getResources().getIdentifier("MAPS_API_KEY", "string", getPackageName()));
    Log.d("RealMaster", "Maps API Key from Resources: " + mapsApiKey);

    registerActivityLifecycleCallbacks(OrientationActivityLifecycle.getInstance());
  }
}
