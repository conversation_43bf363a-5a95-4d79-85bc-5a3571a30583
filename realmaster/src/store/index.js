import { create } from 'zustand';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { persist, createJSONStorage } from 'zustand/middleware';

const useStore = create(persist(
    (set, get) => ({
        //设备信息
        deviceInfo: {
            version: '',
            buildId: '',
            totalMemory: 0,
            usedMemory: 0,
            maxMemory: 0
        },
        setDeviceInfo: (payload) => set({ deviceInfo: { ...get().deviceInfo, ...payload } }),
        //应用信息
        appInfo: {
            lastLoadDomain: '',
            lastLoadUrl: '',
            cookieDomains: {},
        },
        setAppInfo: (payload) => set({ appInfo: { ...get().appInfo, ...payload } }),
    }),
    {
        name: 'RMStore',
        storage: createJSONStorage(() => AsyncStorage),
    }
))


export default useStore;

