import NetInfo from '@react-native-community/netinfo';
import serverDomainIns from './serverDomain';
import { mainRequest } from './request';
import { eventEmitter } from './common'
import { Platform } from 'react-native';

class Network {
  constructor() {
    if (!Network.instance) {
      this.netInfoSub = null;
      this.networkConnected = true;
      this.networkConnectionTimeout = null;
      this._lastCheckTime = null;
      Network.instance = this;
    }
    return Network.instance;
  }

  doCheckNetwork() {
    const now = Date.now();
    if (this._lastCheckTime && (now - this._lastCheckTime < 2000)) {
      return;
    }
    this._lastCheckTime = now;

    let handle;
    const prefix = serverDomainIns.getDomain();
    const url = prefix + '/online';

    const alertOffline = () => {
      handle = null;
      this.networkConnected = false;
      eventEmitter.emit('network.offline', 'offline');
    };

    handle = setTimeout(() => {
      alertOffline();
    }, 6000);
    
    mainRequest({url}).then(res => {
      clearTimeout(handle);
      handle = null;
      eventEmitter.emit('network.online', 'online');
    }).catch(err => {
        if (/Network Error/.test(err.toString())) {
          if (handle) {
            clearTimeout(handle);
            alertOffline();
          }
          return;
        }
      });
  }
  handleConnectivityChange(state) {
    // global.rmLog(`network.js:55~~~handleConnectivityChange`, state);
    //TODO:ios在VPN连接情况下，有时不能检测到state变化，且使用isInternetReachable在iOS上返回值不准确
    // Alert.alert('handleConnectivityChange', JSON.stringify(state))
    let isConnected = state.isConnected;
    if (Platform.OS === 'android' && state.type === 'vpn') {
      isConnected = false;
    }
    
    // NOTE: observation: sometimes offline->online this triggers twice in short timeperiod
    clearTimeout(this.networkConnectionTimeout);
    this.networkConnectionTimeout = setTimeout(() => {
      if (this.networkConnected !== isConnected) {
        this.networkConnected = isConnected;
        if (isConnected) {
          setTimeout(() => {
            if (this.networkConnected) {
              this.doCheckNetwork();
            }
          }, 300);
        } else {
          this.doCheckNetwork();
        }
      }
    }, 500);
  }
  subNetworkStateChange() {
    this.netInfoSub = NetInfo.addEventListener(this.handleConnectivityChange.bind(this))
  }
  unsubNetworkStateChange() {
    if (this.netInfoSub) {
      this.netInfoSub()
    }
  }
  checkIsConnected() {
    NetInfo.fetch().then(state => {
      if (!state.isConnected) {
        this.doCheckNetwork();
      }
    });
  }
  isConnected() {
    return this.networkConnected;
  }

}

const instance = new Network();

export default instance;
