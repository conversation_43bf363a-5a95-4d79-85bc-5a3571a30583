// Top-level build file where you can add configuration options common to all sub-projects/modules.
def REACT_NATIVE_VERSION = new File(['node', '--print',"JSON.parse(require('fs').readFileSync(require.resolve('react-native/package.json'), 'utf-8')).version"].execute(null, rootDir).text.trim())


buildscript {
  ext {
    buildToolsVersion = "34.0.0"
    minSdkVersion = 23
    compileSdkVersion = 34
    targetSdkVersion = 34

    // We use NDK 23 which has both M1 support and is the side-by-side NDK version from AGP.
    ndkVersion = "23.1.7779620"
    googlePlayServicesAuthVersion = "20.7.0"
    playServicesLocationVersion = "21.0.1"

//    kotlinVersion = "1.6.10"
    kotlin_version = '1.6.10' // <- react-native-camera-kit
  }
  repositories {
    google()
    mavenCentral()
  }
  dependencies {
    classpath("com.android.tools.build:gradle")
    classpath("com.facebook.react:react-native-gradle-plugin")
    classpath("com.google.gms:google-services:4.4.1")
    classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version") // <- add this line

  }
}
//     }
// }

// allprojects {
//     configurations.all {
//         resolutionStrategy {
//             force "com.facebook.react:react-native:" + REACT_NATIVE_VERSION
//         }
//     }
// //    tasks.withType(Javadoc).all { enabled = false }
//     repositories {
//         mavenLocal()
//         maven {
//             // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm
//             url("$rootDir/../node_modules/react-native/android")
//         }
//         maven {
//             // Android JSC is installed from npm
//             url("$rootDir/../node_modules/jsc-android/dist")
//         }
//         maven { url "https://jitpack.io" }
//         maven { url "https://maven.google.com" }
//         google()
//         jcenter()
