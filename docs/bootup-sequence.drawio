<mxfile host="Electron" modified="2024-02-02T12:35:58.715Z" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/22.1.16 Chrome/120.0.6099.109 Electron/28.1.0 Safari/537.36" etag="NTeiI9LfGPzIFSvPbYAv" version="22.1.16" type="device" pages="2">
  <diagram name="第 1 页" id="clhQmhijyL6iCD1ZZmiI">
    <mxGraphModel dx="-998" dy="1404" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="3300" pageHeight="2339" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="PVnm7d742OGf18d8E8Ka-55" value="Rmweb" style="shape=umlLifeline;perimeter=lifelinePerimeter;whiteSpace=wrap;html=1;container=1;collapsible=0;recursiveResize=0;outlineConnect=0;rounded=1;shadow=0;comic=0;labelBackgroundColor=none;strokeWidth=1;fontFamily=Verdana;fontSize=12;align=center;" parent="1" vertex="1">
          <mxGeometry x="4298" y="619" width="139" height="1361" as="geometry" />
        </mxCell>
        <mxCell id="K4cVLSxSKiJ1bfH9_nxf-3" value="" style="endArrow=open;startArrow=none;endFill=0;startFill=0;endSize=8;startSize=10;html=1;rounded=0;" parent="PVnm7d742OGf18d8E8Ka-55" target="PVnm7d742OGf18d8E8Ka-72" edge="1">
          <mxGeometry width="160" relative="1" as="geometry">
            <mxPoint x="72" y="1251" as="sourcePoint" />
            <mxPoint x="232" y="1251" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="K4cVLSxSKiJ1bfH9_nxf-11" value="" style="endArrow=open;startArrow=none;endFill=0;startFill=0;endSize=8;startSize=10;html=1;rounded=0;" parent="PVnm7d742OGf18d8E8Ka-55" target="PVnm7d742OGf18d8E8Ka-55" edge="1">
          <mxGeometry width="160" relative="1" as="geometry">
            <mxPoint x="-107.25" y="691" as="sourcePoint" />
            <mxPoint x="52.75" y="691" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="PVnm7d742OGf18d8E8Ka-60" value="App" style="shape=umlLifeline;perimeter=lifelinePerimeter;whiteSpace=wrap;html=1;container=1;collapsible=0;recursiveResize=0;outlineConnect=0;rounded=1;shadow=0;comic=0;labelBackgroundColor=none;strokeWidth=1;fontFamily=Verdana;fontSize=12;align=center;" parent="1" vertex="1">
          <mxGeometry x="4137" y="618" width="100" height="1372" as="geometry" />
        </mxCell>
        <mxCell id="PVnm7d742OGf18d8E8Ka-135" value="" style="endArrow=open;startArrow=none;endFill=0;startFill=0;endSize=8;startSize=10;html=1;rounded=0;" parent="PVnm7d742OGf18d8E8Ka-60" target="PVnm7d742OGf18d8E8Ka-55" edge="1">
          <mxGeometry width="160" relative="1" as="geometry">
            <mxPoint x="53" y="382" as="sourcePoint" />
            <mxPoint x="213" y="382" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="PVnm7d742OGf18d8E8Ka-138" value="navigation to webview&lt;br&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="PVnm7d742OGf18d8E8Ka-135" vertex="1" connectable="0">
          <mxGeometry x="-0.8344" y="2" relative="1" as="geometry">
            <mxPoint x="66" y="-13" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="PVnm7d742OGf18d8E8Ka-61" value="" style="html=1;points=[[0,0,0,0,5],[0,1,0,0,-5],[1,0,0,0,5],[1,1,0,0,-5]];perimeter=orthogonalPerimeter;outlineConnect=0;targetShapes=umlLifeline;portConstraint=eastwest;newEdgeStyle={&quot;curved&quot;:0,&quot;rounded&quot;:0};" parent="PVnm7d742OGf18d8E8Ka-60" vertex="1">
          <mxGeometry x="45" y="60" width="10" height="1230" as="geometry" />
        </mxCell>
        <mxCell id="bYksRBHXHsnR9eDWDTpC-7" value="" style="endArrow=open;startArrow=none;endFill=0;startFill=0;endSize=8;startSize=10;html=1;rounded=0;" parent="PVnm7d742OGf18d8E8Ka-60" edge="1">
          <mxGeometry width="160" relative="1" as="geometry">
            <mxPoint x="53" y="893" as="sourcePoint" />
            <mxPoint x="552.5000000000009" y="893" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="PVnm7d742OGf18d8E8Ka-113" value="" style="endArrow=none;html=1;rounded=0;endFill=0;shape=link;" parent="PVnm7d742OGf18d8E8Ka-60" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="-147" y="1152" as="sourcePoint" />
            <mxPoint x="634.25" y="1152" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="PVnm7d742OGf18d8E8Ka-114" value="webview处理网络检查失败" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;" parent="PVnm7d742OGf18d8E8Ka-113" connectable="0" vertex="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="3" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="PVnm7d742OGf18d8E8Ka-68" value="SplashScreen" style="shape=umlLifeline;perimeter=lifelinePerimeter;whiteSpace=wrap;html=1;container=1;collapsible=0;recursiveResize=0;outlineConnect=0;rounded=1;shadow=0;comic=0;labelBackgroundColor=none;strokeWidth=1;fontFamily=Verdana;fontSize=12;align=center;" parent="1" vertex="1">
          <mxGeometry x="4469.25" y="619" width="100" height="1361" as="geometry" />
        </mxCell>
        <mxCell id="PVnm7d742OGf18d8E8Ka-117" value="" style="endArrow=none;html=1;rounded=0;endFill=0;shape=link;" parent="PVnm7d742OGf18d8E8Ka-68" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="-449.25" y="416" as="sourcePoint" />
            <mxPoint x="332" y="416" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="PVnm7d742OGf18d8E8Ka-118" value="用户启动应用程序（正常启动）" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;" parent="PVnm7d742OGf18d8E8Ka-117" connectable="0" vertex="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="3" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="PVnm7d742OGf18d8E8Ka-69" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;dashed=1;startArrow=none;startFill=0;endArrow=none;endFill=0;" parent="1" target="PVnm7d742OGf18d8E8Ka-71" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="4035" y="680" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="PVnm7d742OGf18d8E8Ka-70" value="User" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;" parent="1" vertex="1">
          <mxGeometry x="4020" y="600" width="30" height="60" as="geometry" />
        </mxCell>
        <mxCell id="PVnm7d742OGf18d8E8Ka-71" value="User&lt;br&gt;" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;" parent="1" vertex="1">
          <mxGeometry x="4020" y="1930" width="30" height="60" as="geometry" />
        </mxCell>
        <mxCell id="PVnm7d742OGf18d8E8Ka-72" value="netWorkError" style="shape=umlLifeline;perimeter=lifelinePerimeter;whiteSpace=wrap;html=1;container=1;collapsible=0;recursiveResize=0;outlineConnect=0;rounded=1;shadow=0;comic=0;labelBackgroundColor=none;strokeWidth=1;fontFamily=Verdana;fontSize=12;align=center;" parent="1" vertex="1">
          <mxGeometry x="4640" y="620" width="100" height="1360" as="geometry" />
        </mxCell>
        <mxCell id="PVnm7d742OGf18d8E8Ka-106" value="" style="endArrow=none;html=1;rounded=0;endFill=0;shape=link;" parent="1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="4020" y="720" as="sourcePoint" />
            <mxPoint x="4801.25" y="720" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="PVnm7d742OGf18d8E8Ka-107" value="用户启动应用程序（通知启动）" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;" parent="PVnm7d742OGf18d8E8Ka-106" connectable="0" vertex="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="3" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="PVnm7d742OGf18d8E8Ka-119" value="par" style="shape=umlFrame;whiteSpace=wrap;html=1;pointerEvents=0;" parent="1" vertex="1">
          <mxGeometry x="4110" y="780" width="300" height="180" as="geometry" />
        </mxCell>
        <mxCell id="PVnm7d742OGf18d8E8Ka-128" value="" style="html=1;points=[[0,0,0,0,5],[0,1,0,0,-5],[1,0,0,0,5],[1,1,0,0,-5]];perimeter=orthogonalPerimeter;outlineConnect=0;targetShapes=umlLifeline;portConstraint=eastwest;newEdgeStyle={&quot;curved&quot;:0,&quot;rounded&quot;:0};" parent="1" vertex="1">
          <mxGeometry x="4187.9" y="833.11" width="10" height="40" as="geometry" />
        </mxCell>
        <mxCell id="PVnm7d742OGf18d8E8Ka-129" value="readCache/&lt;br style=&quot;border-color: var(--border-color);&quot;&gt;registerNotificationEvents" style="html=1;align=left;spacingLeft=2;endArrow=block;rounded=0;edgeStyle=orthogonalEdgeStyle;curved=0;rounded=0;" parent="1" target="PVnm7d742OGf18d8E8Ka-128" edge="1">
          <mxGeometry x="0.0026" relative="1" as="geometry">
            <mxPoint x="4192.9" y="813.11" as="sourcePoint" />
            <Array as="points">
              <mxPoint x="4222.9" y="843.11" />
            </Array>
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="PVnm7d742OGf18d8E8Ka-130" value="" style="html=1;points=[[0,0,0,0,5],[0,1,0,0,-5],[1,0,0,0,5],[1,1,0,0,-5]];perimeter=orthogonalPerimeter;outlineConnect=0;targetShapes=umlLifeline;portConstraint=eastwest;newEdgeStyle={&quot;curved&quot;:0,&quot;rounded&quot;:0};" parent="1" vertex="1">
          <mxGeometry x="4187.9" y="913.11" width="10" height="40" as="geometry" />
        </mxCell>
        <mxCell id="PVnm7d742OGf18d8E8Ka-131" value="checkNetwork" style="html=1;align=left;spacingLeft=2;endArrow=block;rounded=0;edgeStyle=orthogonalEdgeStyle;curved=0;rounded=0;exitX=0;exitY=1;exitDx=0;exitDy=-5;exitPerimeter=0;" parent="1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="4191.9" y="813.11" as="sourcePoint" />
            <Array as="points">
              <mxPoint x="4244.9" y="813.11" />
              <mxPoint x="4244.9" y="932.11" />
            </Array>
            <mxPoint x="4200" y="932" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="PVnm7d742OGf18d8E8Ka-133" value="notification启动应用" style="html=1;verticalAlign=bottom;endArrow=block;curved=0;rounded=0;" parent="1" edge="1">
          <mxGeometry x="0.0045" y="5" width="80" relative="1" as="geometry">
            <mxPoint x="4031.3999999999996" y="755" as="sourcePoint" />
            <mxPoint x="4187.900000000001" y="755" as="targetPoint" />
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="PVnm7d742OGf18d8E8Ka-115" value="" style="endArrow=none;html=1;rounded=0;endFill=0;shape=link;" parent="1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="3990" y="1461.37" as="sourcePoint" />
            <mxPoint x="4771.25" y="1461.37" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="PVnm7d742OGf18d8E8Ka-116" value="app.tsx处理网络检查失败" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;" parent="PVnm7d742OGf18d8E8Ka-115" connectable="0" vertex="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="3" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="PVnm7d742OGf18d8E8Ka-145" value="启动应用" style="html=1;verticalAlign=bottom;endArrow=block;curved=0;rounded=0;" parent="1" edge="1">
          <mxGeometry x="0.0045" y="5" width="80" relative="1" as="geometry">
            <mxPoint x="4031.3999999999996" y="1074.38" as="sourcePoint" />
            <mxPoint x="4187.900000000001" y="1074.38" as="targetPoint" />
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="OfFRMqVN4EO7qHBqwof0-1" value="par" style="shape=umlFrame;whiteSpace=wrap;html=1;pointerEvents=0;" parent="1" vertex="1">
          <mxGeometry x="4113.75" y="1098" width="300" height="180" as="geometry" />
        </mxCell>
        <mxCell id="OfFRMqVN4EO7qHBqwof0-2" value="" style="html=1;points=[[0,0,0,0,5],[0,1,0,0,-5],[1,0,0,0,5],[1,1,0,0,-5]];perimeter=orthogonalPerimeter;outlineConnect=0;targetShapes=umlLifeline;portConstraint=eastwest;newEdgeStyle={&quot;curved&quot;:0,&quot;rounded&quot;:0};" parent="1" vertex="1">
          <mxGeometry x="4186.65" y="1137.1100000000001" width="10" height="40" as="geometry" />
        </mxCell>
        <mxCell id="OfFRMqVN4EO7qHBqwof0-3" value="readCache" style="html=1;align=left;spacingLeft=2;endArrow=block;rounded=0;edgeStyle=orthogonalEdgeStyle;curved=0;rounded=0;" parent="1" target="OfFRMqVN4EO7qHBqwof0-2" edge="1">
          <mxGeometry x="0.0026" relative="1" as="geometry">
            <mxPoint x="4191.65" y="1117.1100000000001" as="sourcePoint" />
            <Array as="points">
              <mxPoint x="4221.65" y="1147.1100000000001" />
            </Array>
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="OfFRMqVN4EO7qHBqwof0-4" value="" style="html=1;points=[[0,0,0,0,5],[0,1,0,0,-5],[1,0,0,0,5],[1,1,0,0,-5]];perimeter=orthogonalPerimeter;outlineConnect=0;targetShapes=umlLifeline;portConstraint=eastwest;newEdgeStyle={&quot;curved&quot;:0,&quot;rounded&quot;:0};" parent="1" vertex="1">
          <mxGeometry x="4186.65" y="1217.1100000000001" width="10" height="40" as="geometry" />
        </mxCell>
        <mxCell id="OfFRMqVN4EO7qHBqwof0-5" value="checkNetwork" style="html=1;align=left;spacingLeft=2;endArrow=block;rounded=0;edgeStyle=orthogonalEdgeStyle;curved=0;rounded=0;exitX=0;exitY=1;exitDx=0;exitDy=-5;exitPerimeter=0;" parent="1" edge="1">
          <mxGeometry x="-0.0003" relative="1" as="geometry">
            <mxPoint x="4190.65" y="1117.1100000000001" as="sourcePoint" />
            <Array as="points">
              <mxPoint x="4243.65" y="1117.1100000000001" />
              <mxPoint x="4243.65" y="1236.1100000000001" />
            </Array>
            <mxPoint x="4198.75" y="1236" as="targetPoint" />
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="OfFRMqVN4EO7qHBqwof0-9" value="alt" style="shape=umlFrame;whiteSpace=wrap;html=1;pointerEvents=0;" parent="1" vertex="1">
          <mxGeometry x="4110" y="1336" width="480" height="100" as="geometry" />
        </mxCell>
        <mxCell id="OfFRMqVN4EO7qHBqwof0-11" value="显示splash" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
          <mxGeometry x="4183" y="1334" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="OfFRMqVN4EO7qHBqwof0-15" value="" style="endArrow=open;startArrow=none;endFill=0;startFill=0;endSize=8;startSize=10;html=1;rounded=0;" parent="1" edge="1">
          <mxGeometry width="160" relative="1" as="geometry">
            <mxPoint x="4190" y="1386" as="sourcePoint" />
            <mxPoint x="4518.749999999999" y="1386" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="OfFRMqVN4EO7qHBqwof0-16" value="navigation to splash" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
          <mxGeometry x="4275" y="1354" width="130" height="30" as="geometry" />
        </mxCell>
        <mxCell id="bYksRBHXHsnR9eDWDTpC-1" value="par" style="shape=umlFrame;whiteSpace=wrap;html=1;pointerEvents=0;" parent="1" vertex="1">
          <mxGeometry x="4113.75" y="1554" width="300" height="180" as="geometry" />
        </mxCell>
        <mxCell id="bYksRBHXHsnR9eDWDTpC-2" value="" style="html=1;points=[[0,0,0,0,5],[0,1,0,0,-5],[1,0,0,0,5],[1,1,0,0,-5]];perimeter=orthogonalPerimeter;outlineConnect=0;targetShapes=umlLifeline;portConstraint=eastwest;newEdgeStyle={&quot;curved&quot;:0,&quot;rounded&quot;:0};" parent="1" vertex="1">
          <mxGeometry x="4186.65" y="1599.1100000000001" width="10" height="40" as="geometry" />
        </mxCell>
        <mxCell id="bYksRBHXHsnR9eDWDTpC-3" value="readCache" style="html=1;align=left;spacingLeft=2;endArrow=block;rounded=0;edgeStyle=orthogonalEdgeStyle;curved=0;rounded=0;" parent="1" target="bYksRBHXHsnR9eDWDTpC-2" edge="1">
          <mxGeometry x="0.0126" relative="1" as="geometry">
            <mxPoint x="4191.65" y="1579.1100000000001" as="sourcePoint" />
            <Array as="points">
              <mxPoint x="4221.65" y="1609.1100000000001" />
            </Array>
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="bYksRBHXHsnR9eDWDTpC-4" value="" style="html=1;points=[[0,0,0,0,5],[0,1,0,0,-5],[1,0,0,0,5],[1,1,0,0,-5]];perimeter=orthogonalPerimeter;outlineConnect=0;targetShapes=umlLifeline;portConstraint=eastwest;newEdgeStyle={&quot;curved&quot;:0,&quot;rounded&quot;:0};" parent="1" vertex="1">
          <mxGeometry x="4186.65" y="1679.1100000000001" width="10" height="40" as="geometry" />
        </mxCell>
        <mxCell id="bYksRBHXHsnR9eDWDTpC-5" value="checkNetwork" style="html=1;align=left;spacingLeft=2;endArrow=block;rounded=0;edgeStyle=orthogonalEdgeStyle;curved=0;rounded=0;exitX=0;exitY=1;exitDx=0;exitDy=-5;exitPerimeter=0;" parent="1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="4190.65" y="1579.1100000000001" as="sourcePoint" />
            <Array as="points">
              <mxPoint x="4243.65" y="1579.1100000000001" />
              <mxPoint x="4243.65" y="1698.1100000000001" />
            </Array>
            <mxPoint x="4198.75" y="1698" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="bYksRBHXHsnR9eDWDTpC-8" value="显示 netWorkError" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
          <mxGeometry x="4237" y="1486" width="120" height="30" as="geometry" />
        </mxCell>
        <mxCell id="K4cVLSxSKiJ1bfH9_nxf-1" value="alt" style="shape=umlFrame;whiteSpace=wrap;html=1;pointerEvents=0;" parent="1" vertex="1">
          <mxGeometry x="4060" y="1522.11" width="380" height="225.89" as="geometry" />
        </mxCell>
        <mxCell id="K4cVLSxSKiJ1bfH9_nxf-2" value="重新初始化" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
          <mxGeometry x="4114" y="1522" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="K4cVLSxSKiJ1bfH9_nxf-4" value="显示netWorkError" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
          <mxGeometry x="4450" y="1848" width="120" height="30" as="geometry" />
        </mxCell>
        <mxCell id="K4cVLSxSKiJ1bfH9_nxf-6" value="初始化" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
          <mxGeometry x="4183" y="777" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="K4cVLSxSKiJ1bfH9_nxf-8" value="初始化" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
          <mxGeometry x="4181" y="1092" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="K4cVLSxSKiJ1bfH9_nxf-12" value="&lt;span style=&quot;color: rgb(0, 0, 0); font-family: Helvetica; font-size: 11px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: center; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; float: none; display: inline !important;&quot;&gt;;&lt;/span&gt;" style="text;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="4227" y="1284" width="140" height="40" as="geometry" />
        </mxCell>
        <mxCell id="AjkhVIFJN43apjB6ultQ-49" value="" style="rounded=0;html=1;jettySize=auto;orthogonalLoop=1;fontSize=11;endArrow=classic;endFill=1;endSize=8;strokeWidth=1;shadow=0;labelBackgroundColor=none;edgeStyle=orthogonalEdgeStyle;" parent="1" source="AjkhVIFJN43apjB6ultQ-50" target="AjkhVIFJN43apjB6ultQ-53" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="AjkhVIFJN43apjB6ultQ-50" value="初始化存储(readCache)" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;" parent="1" vertex="1">
          <mxGeometry x="5290" y="490" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="AjkhVIFJN43apjB6ultQ-51" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;jettySize=auto;orthogonalLoop=1;fontSize=11;endArrow=classic;endFill=1;strokeWidth=1;shadow=0;labelBackgroundColor=none;" parent="1" source="AjkhVIFJN43apjB6ultQ-53" target="AjkhVIFJN43apjB6ultQ-55" edge="1">
          <mxGeometry x="-0.7778" y="10" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="AjkhVIFJN43apjB6ultQ-52" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="AjkhVIFJN43apjB6ultQ-53" target="AjkhVIFJN43apjB6ultQ-57" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="AjkhVIFJN43apjB6ultQ-53" value="检查网络&lt;span style=&quot;font-variant-numeric: normal; font-variant-east-asian: normal; font-variant-alternates: normal; font-kerning: auto; font-optical-sizing: auto; font-feature-settings: normal; font-variation-settings: normal; font-stretch: normal; line-height: normal; font-family: &amp;quot;.Apple Color Emoji UI&amp;quot;;&quot; class=&quot;s2&quot;&gt;🛜&lt;/span&gt;&lt;span style=&quot;font-variant-numeric: normal; font-variant-east-asian: normal; font-variant-alternates: normal; font-kerning: auto; font-optical-sizing: auto; font-feature-settings: normal; font-variation-settings: normal; font-stretch: normal; line-height: normal; font-family: &amp;quot;PingFang SC&amp;quot;;&quot; class=&quot;s1&quot;&gt;连接状态&lt;/span&gt;(checkIsConnected)" style="rhombus;whiteSpace=wrap;html=1;shadow=0;fontFamily=Helvetica;fontSize=12;align=center;strokeWidth=1;spacing=6;spacingTop=-4;" parent="1" vertex="1">
          <mxGeometry x="5250" y="620" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="AjkhVIFJN43apjB6ultQ-54" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="AjkhVIFJN43apjB6ultQ-55" target="AjkhVIFJN43apjB6ultQ-50" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="AjkhVIFJN43apjB6ultQ-55" value="断网弹窗" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;" parent="1" vertex="1">
          <mxGeometry x="5690" y="640" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="AjkhVIFJN43apjB6ultQ-56" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="AjkhVIFJN43apjB6ultQ-57" target="AjkhVIFJN43apjB6ultQ-61" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="AjkhVIFJN43apjB6ultQ-57" value="初始化本地化(initL10n)" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;" parent="1" vertex="1">
          <mxGeometry x="5290" y="820" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="AjkhVIFJN43apjB6ultQ-58" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="AjkhVIFJN43apjB6ultQ-59" target="AjkhVIFJN43apjB6ultQ-67" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="AjkhVIFJN43apjB6ultQ-59" value="取消之前的所有请求" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;" parent="1" vertex="1">
          <mxGeometry x="5290" y="980" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="AjkhVIFJN43apjB6ultQ-60" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="AjkhVIFJN43apjB6ultQ-61" target="AjkhVIFJN43apjB6ultQ-59" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="AjkhVIFJN43apjB6ultQ-61" value="获取启动屏幕配置(getSplashJson)" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;" parent="1" vertex="1">
          <mxGeometry x="5290" y="900" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="AjkhVIFJN43apjB6ultQ-62" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="AjkhVIFJN43apjB6ultQ-63" target="AjkhVIFJN43apjB6ultQ-69" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="AjkhVIFJN43apjB6ultQ-63" value="添加到请求数组" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;" parent="1" vertex="1">
          <mxGeometry x="5290" y="1220" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="AjkhVIFJN43apjB6ultQ-64" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="AjkhVIFJN43apjB6ultQ-65" target="AjkhVIFJN43apjB6ultQ-63" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="AjkhVIFJN43apjB6ultQ-65" value="生成请求对象" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;" parent="1" vertex="1">
          <mxGeometry x="5290" y="1140" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="AjkhVIFJN43apjB6ultQ-66" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="AjkhVIFJN43apjB6ultQ-67" target="AjkhVIFJN43apjB6ultQ-65" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="AjkhVIFJN43apjB6ultQ-67" value="遍历请求域名数组" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;" parent="1" vertex="1">
          <mxGeometry x="5290" y="1060" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="AjkhVIFJN43apjB6ultQ-68" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="AjkhVIFJN43apjB6ultQ-69" target="AjkhVIFJN43apjB6ultQ-71" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="AjkhVIFJN43apjB6ultQ-69" value="执行fetchUrl&lt;span style=&quot;font-variant-numeric: normal; font-variant-east-asian: normal; font-variant-alternates: normal; font-kerning: auto; font-optical-sizing: auto; font-feature-settings: normal; font-variation-settings: normal; font-stretch: normal; line-height: normal; font-family: &amp;quot;PingFang SC&amp;quot;;&quot; class=&quot;s1&quot;&gt;函数&lt;/span&gt;" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;" parent="1" vertex="1">
          <mxGeometry x="5290" y="1300" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="AjkhVIFJN43apjB6ultQ-70" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="AjkhVIFJN43apjB6ultQ-71" target="AjkhVIFJN43apjB6ultQ-74" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="AjkhVIFJN43apjB6ultQ-71" value="处理结果(gotResult)" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;" parent="1" vertex="1">
          <mxGeometry x="5290" y="1380" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="AjkhVIFJN43apjB6ultQ-72" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="AjkhVIFJN43apjB6ultQ-74" target="AjkhVIFJN43apjB6ultQ-78" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="AjkhVIFJN43apjB6ultQ-73" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="AjkhVIFJN43apjB6ultQ-74" target="AjkhVIFJN43apjB6ultQ-76" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="AjkhVIFJN43apjB6ultQ-74" value="有错误?" style="rhombus;whiteSpace=wrap;html=1;rounded=1;glass=0;strokeWidth=1;shadow=0;" parent="1" vertex="1">
          <mxGeometry x="5248.75" y="1485" width="201.25" height="80" as="geometry" />
        </mxCell>
        <mxCell id="AjkhVIFJN43apjB6ultQ-75" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="AjkhVIFJN43apjB6ultQ-76" target="AjkhVIFJN43apjB6ultQ-85" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="AjkhVIFJN43apjB6ultQ-76" value="设置错误信息" style="whiteSpace=wrap;html=1;rounded=1;glass=0;strokeWidth=1;shadow=0;" parent="1" vertex="1">
          <mxGeometry x="5130" y="1620" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="AjkhVIFJN43apjB6ultQ-77" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="AjkhVIFJN43apjB6ultQ-78" target="AjkhVIFJN43apjB6ultQ-81" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="AjkhVIFJN43apjB6ultQ-78" value="处理成功结果" style="whiteSpace=wrap;html=1;rounded=1;glass=0;strokeWidth=1;shadow=0;" parent="1" vertex="1">
          <mxGeometry x="5485" y="1620" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="AjkhVIFJN43apjB6ultQ-79" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="AjkhVIFJN43apjB6ultQ-81" target="AjkhVIFJN43apjB6ultQ-85" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="5430" y="1780" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="AjkhVIFJN43apjB6ultQ-80" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="AjkhVIFJN43apjB6ultQ-81" target="AjkhVIFJN43apjB6ultQ-83" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="AjkhVIFJN43apjB6ultQ-81" value="需要跳转" style="rhombus;whiteSpace=wrap;html=1;rounded=1;glass=0;strokeWidth=1;shadow=0;" parent="1" vertex="1">
          <mxGeometry x="5505" y="1740" width="80" height="80" as="geometry" />
        </mxCell>
        <mxCell id="AjkhVIFJN43apjB6ultQ-82" value="YES" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
          <mxGeometry x="5445" y="1758" width="50" height="30" as="geometry" />
        </mxCell>
        <mxCell id="AjkhVIFJN43apjB6ultQ-83" value="执行跳转(jump)" style="whiteSpace=wrap;html=1;rounded=1;glass=0;strokeWidth=1;shadow=0;" parent="1" vertex="1">
          <mxGeometry x="5585" y="1880" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="AjkhVIFJN43apjB6ultQ-84" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="AjkhVIFJN43apjB6ultQ-85" target="AjkhVIFJN43apjB6ultQ-91" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="AjkhVIFJN43apjB6ultQ-85" value="选择下一个请求" style="rhombus;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="5190" y="1740" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="AjkhVIFJN43apjB6ultQ-86" value="YES" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
          <mxGeometry x="5250" y="1950" width="50" height="30" as="geometry" />
        </mxCell>
        <mxCell id="AjkhVIFJN43apjB6ultQ-87" value="NO" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
          <mxGeometry x="5560" y="1820" width="40" height="30" as="geometry" />
        </mxCell>
        <mxCell id="AjkhVIFJN43apjB6ultQ-88" value="NO" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
          <mxGeometry x="5300" y="1870" width="40" height="30" as="geometry" />
        </mxCell>
        <mxCell id="AjkhVIFJN43apjB6ultQ-89" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" parent="1" source="AjkhVIFJN43apjB6ultQ-91" target="AjkhVIFJN43apjB6ultQ-55" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="5250" y="2020" />
              <mxPoint x="5750" y="2020" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="AjkhVIFJN43apjB6ultQ-90" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="AjkhVIFJN43apjB6ultQ-91" target="AjkhVIFJN43apjB6ultQ-83" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="AjkhVIFJN43apjB6ultQ-91" value="有错误" style="rhombus;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="5205" y="1860" width="90" height="80" as="geometry" />
        </mxCell>
        <mxCell id="AjkhVIFJN43apjB6ultQ-92" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="AjkhVIFJN43apjB6ultQ-76" target="AjkhVIFJN43apjB6ultQ-76" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="AjkhVIFJN43apjB6ultQ-93" value="YES" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
          <mxGeometry x="5340" y="700" width="50" height="30" as="geometry" />
        </mxCell>
        <mxCell id="AjkhVIFJN43apjB6ultQ-94" value="NO" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
          <mxGeometry x="5445" y="640" width="40" height="30" as="geometry" />
        </mxCell>
        <mxCell id="AjkhVIFJN43apjB6ultQ-95" value="YES" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
          <mxGeometry x="5280" y="1560" width="50" height="30" as="geometry" />
        </mxCell>
        <mxCell id="AjkhVIFJN43apjB6ultQ-96" value="NO" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
          <mxGeometry x="5455" y="1500" width="40" height="30" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
  <diagram id="TUvyzDjpAwJ0mQO6w97R" name="第 2 页">
    <mxGraphModel dx="1026" dy="772" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="3300" pageHeight="2339" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-1" value="" style="edgeStyle=orthogonalEdgeStyle;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;curved=1;" parent="1" source="nr_3PgkoyfCLtJVM_fCE-2" target="nr_3PgkoyfCLtJVM_fCE-5" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="655" y="-70" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-2" value="" style="ellipse;html=1;shape=endState;fillColor=#000000;strokeColor=#DAE8FC;" parent="1" vertex="1">
          <mxGeometry x="535" y="130" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-3" value="" style="edgeStyle=orthogonalEdgeStyle;orthogonalLoop=1;jettySize=auto;html=1;curved=1;" parent="1" source="nr_3PgkoyfCLtJVM_fCE-5" target="nr_3PgkoyfCLtJVM_fCE-10" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="820" y="285" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-4" style="edgeStyle=orthogonalEdgeStyle;orthogonalLoop=1;jettySize=auto;html=1;curved=1;" parent="1" source="nr_3PgkoyfCLtJVM_fCE-5" target="nr_3PgkoyfCLtJVM_fCE-19" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="510" y="1020" as="targetPoint" />
            <Array as="points">
              <mxPoint x="400" y="285" />
              <mxPoint x="400" y="950" />
              <mxPoint x="510" y="950" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-5" value="&lt;p style=&quot;margin:0px;margin-top:6px;text-align:center;&quot;&gt;&lt;b&gt;CheckNetWork&lt;/b&gt;&lt;/p&gt;&lt;hr&gt;&lt;p style=&quot;margin:0px;margin-left:8px;&quot;&gt;&lt;br&gt;&lt;/p&gt;" style="align=left;overflow=fill;html=1;dropTarget=0;whiteSpace=wrap;" parent="1" vertex="1">
          <mxGeometry x="460" y="260" width="180" height="50" as="geometry" />
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-6" value="" style="shape=component;jettyWidth=8;jettyHeight=4;" parent="nr_3PgkoyfCLtJVM_fCE-5" vertex="1">
          <mxGeometry x="1" width="20" height="20" relative="1" as="geometry">
            <mxPoint x="-24" y="4" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-7" value="par" style="shape=umlFrame;whiteSpace=wrap;html=1;pointerEvents=0;" parent="1" vertex="1">
          <mxGeometry x="2606" y="345" width="634" height="345" as="geometry" />
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-8" value="" style="edgeStyle=orthogonalEdgeStyle;orthogonalLoop=1;jettySize=auto;html=1;curved=1;" parent="1" source="nr_3PgkoyfCLtJVM_fCE-10" target="nr_3PgkoyfCLtJVM_fCE-13" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-9" value="" style="edgeStyle=orthogonalEdgeStyle;orthogonalLoop=1;jettySize=auto;html=1;curved=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="nr_3PgkoyfCLtJVM_fCE-10" target="nr_3PgkoyfCLtJVM_fCE-16" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1026" y="729" as="targetPoint" />
            <Array as="points">
              <mxPoint x="810" y="470" />
              <mxPoint x="1010" y="470" />
              <mxPoint x="1010" y="1095" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-10" value="&lt;p style=&quot;margin:0px;margin-top:6px;text-align:center;&quot;&gt;&lt;b&gt;SplasbScreen&lt;/b&gt;&lt;/p&gt;&lt;hr&gt;&lt;p style=&quot;margin:0px;margin-left:8px;&quot;&gt;&lt;br&gt;&lt;/p&gt;" style="align=left;overflow=fill;html=1;dropTarget=0;whiteSpace=wrap;" parent="1" vertex="1">
          <mxGeometry x="720" y="380" width="180" height="50" as="geometry" />
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-11" value="" style="shape=component;jettyWidth=8;jettyHeight=4;" parent="nr_3PgkoyfCLtJVM_fCE-10" vertex="1">
          <mxGeometry x="1" width="20" height="20" relative="1" as="geometry">
            <mxPoint x="-24" y="4" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-12" value="" style="edgeStyle=orthogonalEdgeStyle;orthogonalLoop=1;jettySize=auto;html=1;curved=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="nr_3PgkoyfCLtJVM_fCE-13" target="nr_3PgkoyfCLtJVM_fCE-16" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="940" y="710" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-13" value="&lt;p style=&quot;margin:0px;margin-top:6px;text-align:center;&quot;&gt;&lt;b&gt;AdScreen&lt;/b&gt;&lt;/p&gt;&lt;hr&gt;&lt;p style=&quot;margin:0px;margin-left:8px;&quot;&gt;&lt;br&gt;&lt;/p&gt;" style="align=left;overflow=fill;html=1;dropTarget=0;whiteSpace=wrap;" parent="1" vertex="1">
          <mxGeometry x="640" y="550" width="180" height="50" as="geometry" />
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-14" value="" style="shape=component;jettyWidth=8;jettyHeight=4;" parent="nr_3PgkoyfCLtJVM_fCE-13" vertex="1">
          <mxGeometry x="1" width="20" height="20" relative="1" as="geometry">
            <mxPoint x="-24" y="4" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-15" value="" style="edgeStyle=orthogonalEdgeStyle;orthogonalLoop=1;jettySize=auto;html=1;curved=1;" parent="1" source="nr_3PgkoyfCLtJVM_fCE-16" target="nr_3PgkoyfCLtJVM_fCE-19" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-16" value="&lt;p style=&quot;margin:0px;margin-top:6px;text-align:center;&quot;&gt;&lt;b&gt;RmWeb&lt;/b&gt;&lt;/p&gt;&lt;hr&gt;&lt;p style=&quot;margin:0px;margin-left:8px;&quot;&gt;webView&lt;/p&gt;" style="align=left;overflow=fill;html=1;dropTarget=0;whiteSpace=wrap;" parent="1" vertex="1">
          <mxGeometry x="810" y="1070" width="180" height="50" as="geometry" />
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-17" value="" style="shape=component;jettyWidth=8;jettyHeight=4;" parent="nr_3PgkoyfCLtJVM_fCE-16" vertex="1">
          <mxGeometry x="1" width="20" height="20" relative="1" as="geometry">
            <mxPoint x="-24" y="4" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-18" value="" style="orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;rounded=0;" parent="1" source="nr_3PgkoyfCLtJVM_fCE-19" target="nr_3PgkoyfCLtJVM_fCE-28" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="650" y="1385" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-19" value="&lt;p style=&quot;margin:0px;margin-top:6px;text-align:center;&quot;&gt;&lt;b&gt;NetWorkError&lt;/b&gt;&lt;/p&gt;&lt;hr&gt;&lt;p style=&quot;margin:0px;margin-left:8px;&quot;&gt;&lt;br&gt;&lt;/p&gt;" style="align=left;overflow=fill;html=1;dropTarget=0;whiteSpace=wrap;" parent="1" vertex="1">
          <mxGeometry x="580" y="1240" width="180" height="50" as="geometry" />
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-20" value="" style="shape=component;jettyWidth=8;jettyHeight=4;" parent="nr_3PgkoyfCLtJVM_fCE-19" vertex="1">
          <mxGeometry x="1" width="20" height="20" relative="1" as="geometry">
            <mxPoint x="-24" y="4" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-21" value="启动应用" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="550" y="190" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-22" value="网络连接" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="760" y="280" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-23" value="有广告" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="730" y="450" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-24" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1080" y="580" as="sourcePoint" />
            <mxPoint x="1080" y="580" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-25" value="&lt;div style=&quot;text-align: left;&quot;&gt;&lt;span style=&quot;background-color: initial;&quot;&gt;广告倒计时结束/&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left;&quot;&gt;&lt;span style=&quot;background-color: initial;&quot;&gt;手动跳过/&lt;/span&gt;&lt;/div&gt;两个小时内显示跳过" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="730" y="650" width="135" height="30" as="geometry" />
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-26" value="网络断开" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="400" y="620" width="90" height="30" as="geometry" />
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-27" value="网络错误" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="680" y="1130" width="90" height="30" as="geometry" />
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-28" value="" style="ellipse;html=1;shape=endState;fillColor=#000000;strokeColor=#DAE8FC;" parent="1" vertex="1">
          <mxGeometry x="655" y="1380" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-29" value="无广告" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="970" y="470" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-30" value="Rmweb" style="shape=umlLifeline;perimeter=lifelinePerimeter;whiteSpace=wrap;html=1;container=1;collapsible=0;recursiveResize=0;outlineConnect=0;rounded=1;shadow=0;comic=0;labelBackgroundColor=none;strokeWidth=1;fontFamily=Verdana;fontSize=12;align=center;" parent="1" vertex="1">
          <mxGeometry x="1810" y="225" width="100" height="580" as="geometry" />
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-31" value="" style="html=1;points=[[0,0,0,0,5],[0,1,0,0,-5],[1,0,0,0,5],[1,1,0,0,-5]];perimeter=orthogonalPerimeter;outlineConnect=0;targetShapes=umlLifeline;portConstraint=eastwest;newEdgeStyle={&quot;curved&quot;:0,&quot;rounded&quot;:0};" parent="nr_3PgkoyfCLtJVM_fCE-30" vertex="1">
          <mxGeometry x="45" y="107" width="10" height="153" as="geometry" />
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-32" value="" style="html=1;points=[[0,0,0,0,5],[0,1,0,0,-5],[1,0,0,0,5],[1,1,0,0,-5]];perimeter=orthogonalPerimeter;outlineConnect=0;targetShapes=umlLifeline;portConstraint=eastwest;newEdgeStyle={&quot;curved&quot;:0,&quot;rounded&quot;:0};" parent="nr_3PgkoyfCLtJVM_fCE-30" vertex="1">
          <mxGeometry x="45" y="425" width="10" height="80" as="geometry" />
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-33" value="App" style="shape=umlLifeline;perimeter=lifelinePerimeter;whiteSpace=wrap;html=1;container=1;collapsible=0;recursiveResize=0;outlineConnect=0;rounded=1;shadow=0;comic=0;labelBackgroundColor=none;strokeWidth=1;fontFamily=Verdana;fontSize=12;align=center;" parent="1" vertex="1">
          <mxGeometry x="1630" y="225" width="100" height="580" as="geometry" />
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-34" value="" style="html=1;points=[[0,0,0,0,5],[0,1,0,0,-5],[1,0,0,0,5],[1,1,0,0,-5]];perimeter=orthogonalPerimeter;outlineConnect=0;targetShapes=umlLifeline;portConstraint=eastwest;newEdgeStyle={&quot;curved&quot;:0,&quot;rounded&quot;:0};" parent="nr_3PgkoyfCLtJVM_fCE-33" vertex="1">
          <mxGeometry x="45" y="85" width="10" height="205" as="geometry" />
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-35" value="初始化url" style="html=1;verticalAlign=bottom;endArrow=block;curved=0;rounded=0;" parent="nr_3PgkoyfCLtJVM_fCE-33" edge="1">
          <mxGeometry x="-0.0017" width="80" relative="1" as="geometry">
            <mxPoint x="50" y="144" as="sourcePoint" />
            <mxPoint x="229.5" y="144" as="targetPoint" />
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-36" value="" style="html=1;points=[[0,0,0,0,5],[0,1,0,0,-5],[1,0,0,0,5],[1,1,0,0,-5]];perimeter=orthogonalPerimeter;outlineConnect=0;targetShapes=umlLifeline;portConstraint=eastwest;newEdgeStyle={&quot;curved&quot;:0,&quot;rounded&quot;:0};" parent="nr_3PgkoyfCLtJVM_fCE-33" vertex="1">
          <mxGeometry x="45" y="392" width="10" height="88" as="geometry" />
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-37" value="" style="html=1;points=[[0,0,0,0,5],[0,1,0,0,-5],[1,0,0,0,5],[1,1,0,0,-5]];perimeter=orthogonalPerimeter;outlineConnect=0;targetShapes=umlLifeline;portConstraint=eastwest;newEdgeStyle={&quot;curved&quot;:0,&quot;rounded&quot;:0};" parent="nr_3PgkoyfCLtJVM_fCE-33" vertex="1">
          <mxGeometry x="51" y="115" width="10" height="40" as="geometry" />
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-38" value="reacCache/&lt;br&gt;registerNotificationEvents" style="html=1;align=left;spacingLeft=2;endArrow=block;rounded=0;edgeStyle=orthogonalEdgeStyle;curved=0;rounded=0;" parent="nr_3PgkoyfCLtJVM_fCE-33" target="nr_3PgkoyfCLtJVM_fCE-37" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="56" y="95" as="sourcePoint" />
            <Array as="points">
              <mxPoint x="86" y="125" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-39" value="" style="html=1;points=[[0,0,0,0,5],[0,1,0,0,-5],[1,0,0,0,5],[1,1,0,0,-5]];perimeter=orthogonalPerimeter;outlineConnect=0;targetShapes=umlLifeline;portConstraint=eastwest;newEdgeStyle={&quot;curved&quot;:0,&quot;rounded&quot;:0};" parent="nr_3PgkoyfCLtJVM_fCE-33" vertex="1">
          <mxGeometry x="51" y="430" width="10" height="40" as="geometry" />
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-40" value="reacCache/&lt;br&gt;registerNotificationEvents" style="html=1;align=left;spacingLeft=2;endArrow=block;rounded=0;edgeStyle=orthogonalEdgeStyle;curved=0;rounded=0;" parent="nr_3PgkoyfCLtJVM_fCE-33" target="nr_3PgkoyfCLtJVM_fCE-39" edge="1">
          <mxGeometry x="0.0118" relative="1" as="geometry">
            <mxPoint x="56" y="410" as="sourcePoint" />
            <Array as="points">
              <mxPoint x="86" y="440" />
            </Array>
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-41" value="初始化url" style="html=1;verticalAlign=bottom;endArrow=block;curved=0;rounded=0;" parent="nr_3PgkoyfCLtJVM_fCE-33" target="nr_3PgkoyfCLtJVM_fCE-30" edge="1">
          <mxGeometry x="0.0029" width="80" relative="1" as="geometry">
            <mxPoint x="60" y="455" as="sourcePoint" />
            <mxPoint x="140" y="455" as="targetPoint" />
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-42" value="SplashScreen" style="shape=umlLifeline;perimeter=lifelinePerimeter;whiteSpace=wrap;html=1;container=1;collapsible=0;recursiveResize=0;outlineConnect=0;rounded=1;shadow=0;comic=0;labelBackgroundColor=none;strokeWidth=1;fontFamily=Verdana;fontSize=12;align=center;" parent="1" vertex="1">
          <mxGeometry x="1989.25" y="225" width="100" height="580" as="geometry" />
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-43" value="" style="html=1;points=[[0,0,0,0,5],[0,1,0,0,-5],[1,0,0,0,5],[1,1,0,0,-5]];perimeter=orthogonalPerimeter;outlineConnect=0;targetShapes=umlLifeline;portConstraint=eastwest;newEdgeStyle={&quot;curved&quot;:0,&quot;rounded&quot;:0};" parent="nr_3PgkoyfCLtJVM_fCE-42" vertex="1">
          <mxGeometry x="45.75" y="130" width="10" height="160" as="geometry" />
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-44" value="检测到网络错误" style="html=1;verticalAlign=bottom;endArrow=block;curved=0;rounded=0;" parent="nr_3PgkoyfCLtJVM_fCE-42" edge="1">
          <mxGeometry x="-0.0038" width="80" relative="1" as="geometry">
            <mxPoint x="-129.75" y="201" as="sourcePoint" />
            <mxPoint x="230.25" y="201" as="targetPoint" />
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-45" value="检测到网络错误" style="html=1;verticalAlign=bottom;endArrow=block;curved=0;rounded=0;" parent="nr_3PgkoyfCLtJVM_fCE-42" source="nr_3PgkoyfCLtJVM_fCE-32" edge="1">
          <mxGeometry width="80" relative="1" as="geometry">
            <mxPoint x="60.75" y="462" as="sourcePoint" />
            <mxPoint x="230.25000000000023" y="461.9999999999998" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-46" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;dashed=1;startArrow=none;startFill=0;endArrow=none;endFill=0;" parent="1" target="nr_3PgkoyfCLtJVM_fCE-48" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1565" y="280" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-47" value="User" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;" parent="1" vertex="1">
          <mxGeometry x="1550" y="200" width="30" height="60" as="geometry" />
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-48" value="User&lt;br&gt;" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;" parent="1" vertex="1">
          <mxGeometry x="1550" y="820" width="30" height="60" as="geometry" />
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-49" value="netWorkError" style="shape=umlLifeline;perimeter=lifelinePerimeter;whiteSpace=wrap;html=1;container=1;collapsible=0;recursiveResize=0;outlineConnect=0;rounded=1;shadow=0;comic=0;labelBackgroundColor=none;strokeWidth=1;fontFamily=Verdana;fontSize=12;align=center;" parent="1" vertex="1">
          <mxGeometry x="2170" y="225" width="100" height="580" as="geometry" />
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-50" value="" style="html=1;points=[[0,0,0,0,5],[0,1,0,0,-5],[1,0,0,0,5],[1,1,0,0,-5]];perimeter=orthogonalPerimeter;outlineConnect=0;targetShapes=umlLifeline;portConstraint=eastwest;newEdgeStyle={&quot;curved&quot;:0,&quot;rounded&quot;:0};" parent="nr_3PgkoyfCLtJVM_fCE-49" vertex="1">
          <mxGeometry x="45" y="175" width="10" height="80" as="geometry" />
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-51" value="" style="html=1;points=[[0,0,0,0,5],[0,1,0,0,-5],[1,0,0,0,5],[1,1,0,0,-5]];perimeter=orthogonalPerimeter;outlineConnect=0;targetShapes=umlLifeline;portConstraint=eastwest;newEdgeStyle={&quot;curved&quot;:0,&quot;rounded&quot;:0};" parent="nr_3PgkoyfCLtJVM_fCE-49" vertex="1">
          <mxGeometry x="45" y="437" width="10" height="80" as="geometry" />
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-52" value="启动应用" style="html=1;verticalAlign=bottom;endArrow=block;curved=0;rounded=0;" parent="1" target="nr_3PgkoyfCLtJVM_fCE-33" edge="1">
          <mxGeometry width="80" relative="1" as="geometry">
            <mxPoint x="1560" y="320" as="sourcePoint" />
            <mxPoint x="1640" y="320" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-53" value="返回Rmweb" style="html=1;verticalAlign=bottom;endArrow=open;dashed=1;endSize=8;curved=0;rounded=0;" parent="1" edge="1">
          <mxGeometry x="0.0029" relative="1" as="geometry">
            <mxPoint x="2210" y="463" as="sourcePoint" />
            <mxPoint x="1865" y="463" as="targetPoint" />
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-54" value="通知启动" style="html=1;verticalAlign=bottom;endArrow=block;curved=0;rounded=0;" parent="1" target="nr_3PgkoyfCLtJVM_fCE-36" edge="1">
          <mxGeometry x="-0.0061" width="80" relative="1" as="geometry">
            <mxPoint x="1560" y="647" as="sourcePoint" />
            <mxPoint x="1640" y="647" as="targetPoint" />
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-55" value="返回Rmweb" style="html=1;verticalAlign=bottom;endArrow=open;dashed=1;endSize=8;curved=0;rounded=0;" parent="1" target="nr_3PgkoyfCLtJVM_fCE-30" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="2210" y="717" as="sourcePoint" />
            <mxPoint x="2038.5735294117646" y="716.9999999999998" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-56" value="是否显示Splash" style="html=1;verticalAlign=bottom;endArrow=block;curved=0;rounded=0;" parent="1" edge="1">
          <mxGeometry width="80" relative="1" as="geometry">
            <mxPoint x="1860" y="391" as="sourcePoint" />
            <mxPoint x="2038.75" y="391" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-57" value="Rmweb" style="shape=umlLifeline;perimeter=lifelinePerimeter;whiteSpace=wrap;html=1;container=1;collapsible=0;recursiveResize=0;outlineConnect=0;rounded=1;shadow=0;comic=0;labelBackgroundColor=none;strokeWidth=1;fontFamily=Verdana;fontSize=12;align=center;" parent="1" vertex="1">
          <mxGeometry x="2788" y="289" width="422" height="761" as="geometry" />
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-58" value="App" style="shape=umlLifeline;perimeter=lifelinePerimeter;whiteSpace=wrap;html=1;container=1;collapsible=0;recursiveResize=0;outlineConnect=0;rounded=1;shadow=0;comic=0;labelBackgroundColor=none;strokeWidth=1;fontFamily=Verdana;fontSize=12;align=center;" parent="1" vertex="1">
          <mxGeometry x="2627" y="288" width="593" height="762" as="geometry" />
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-59" value="" style="html=1;points=[[0,0,0,0,5],[0,1,0,0,-5],[1,0,0,0,5],[1,1,0,0,-5]];perimeter=orthogonalPerimeter;outlineConnect=0;targetShapes=umlLifeline;portConstraint=eastwest;newEdgeStyle={&quot;curved&quot;:0,&quot;rounded&quot;:0};" parent="nr_3PgkoyfCLtJVM_fCE-58" vertex="1">
          <mxGeometry x="45" y="60" width="10" height="640" as="geometry" />
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-60" value="" style="html=1;points=[[0,0,0,0,5],[0,1,0,0,-5],[1,0,0,0,5],[1,1,0,0,-5]];perimeter=orthogonalPerimeter;outlineConnect=0;targetShapes=umlLifeline;portConstraint=eastwest;newEdgeStyle={&quot;curved&quot;:0,&quot;rounded&quot;:0};" parent="nr_3PgkoyfCLtJVM_fCE-58" vertex="1">
          <mxGeometry x="50" y="122" width="10" height="40" as="geometry" />
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-61" value="iniitial app" style="html=1;align=left;spacingLeft=2;endArrow=block;rounded=0;edgeStyle=orthogonalEdgeStyle;curved=0;rounded=0;" parent="nr_3PgkoyfCLtJVM_fCE-58" target="nr_3PgkoyfCLtJVM_fCE-60" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="55" y="102" as="sourcePoint" />
            <Array as="points">
              <mxPoint x="85" y="132" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-62" value="" style="html=1;points=[[0,0,0,0,5],[0,1,0,0,-5],[1,0,0,0,5],[1,1,0,0,-5]];perimeter=orthogonalPerimeter;outlineConnect=0;targetShapes=umlLifeline;portConstraint=eastwest;newEdgeStyle={&quot;curved&quot;:0,&quot;rounded&quot;:0};" parent="nr_3PgkoyfCLtJVM_fCE-58" vertex="1">
          <mxGeometry x="50" y="202" width="10" height="40" as="geometry" />
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-63" value="checkNetwork" style="html=1;align=left;spacingLeft=2;endArrow=block;rounded=0;edgeStyle=orthogonalEdgeStyle;curved=0;rounded=0;" parent="nr_3PgkoyfCLtJVM_fCE-58" target="nr_3PgkoyfCLtJVM_fCE-62" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="55" y="182" as="sourcePoint" />
            <Array as="points">
              <mxPoint x="85" y="212" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-64" value="" style="group;fillColor=none;" parent="nr_3PgkoyfCLtJVM_fCE-58" vertex="1" connectable="0">
          <mxGeometry x="-7" y="272" width="600" height="120" as="geometry" />
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-65" value="" style="endArrow=none;dashed=1;endFill=0;endSize=12;html=1;rounded=0;entryX=0.998;entryY=0.591;entryDx=0;entryDy=0;entryPerimeter=0;" parent="nr_3PgkoyfCLtJVM_fCE-64" target="nr_3PgkoyfCLtJVM_fCE-69" edge="1">
          <mxGeometry width="160" relative="1" as="geometry">
            <mxPoint y="70" as="sourcePoint" />
            <mxPoint x="198.7577639751553" y="70" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-66" value="默认splashScreen" style="html=1;verticalAlign=bottom;endArrow=block;curved=0;rounded=0;" parent="nr_3PgkoyfCLtJVM_fCE-64" target="nr_3PgkoyfCLtJVM_fCE-72" edge="1">
          <mxGeometry width="80" relative="1" as="geometry">
            <mxPoint x="71" y="50" as="sourcePoint" />
            <mxPoint x="151" y="50" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-67" value="显示NetWorkError" style="html=1;verticalAlign=bottom;endArrow=block;curved=0;rounded=0;" parent="nr_3PgkoyfCLtJVM_fCE-64" target="nr_3PgkoyfCLtJVM_fCE-76" edge="1">
          <mxGeometry width="80" relative="1" as="geometry">
            <mxPoint x="71" y="100" as="sourcePoint" />
            <mxPoint x="151" y="100" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-68" value="" style="group" parent="nr_3PgkoyfCLtJVM_fCE-64" vertex="1" connectable="0">
          <mxGeometry width="600" height="120" as="geometry" />
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-69" value="alt" style="shape=umlFrame;whiteSpace=wrap;html=1;pointerEvents=0;" parent="nr_3PgkoyfCLtJVM_fCE-68" vertex="1">
          <mxGeometry width="600" height="120" as="geometry" />
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-70" value="网络连接" style="text;align=center;fontStyle=1;verticalAlign=middle;spacingLeft=3;spacingRight=3;strokeColor=none;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;html=1;" parent="nr_3PgkoyfCLtJVM_fCE-68" vertex="1">
          <mxGeometry x="51" width="80" height="26" as="geometry" />
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-71" value="网络断开" style="text;align=center;fontStyle=1;verticalAlign=middle;spacingLeft=3;spacingRight=3;strokeColor=none;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;html=1;" parent="nr_3PgkoyfCLtJVM_fCE-68" vertex="1">
          <mxGeometry y="70" width="80" height="26" as="geometry" />
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-72" value="SplashScreen" style="shape=umlLifeline;perimeter=lifelinePerimeter;whiteSpace=wrap;html=1;container=1;collapsible=0;recursiveResize=0;outlineConnect=0;rounded=1;shadow=0;comic=0;labelBackgroundColor=none;strokeWidth=1;fontFamily=Verdana;fontSize=12;align=center;" parent="1" vertex="1">
          <mxGeometry x="2959.25" y="289" width="100" height="761" as="geometry" />
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-73" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;dashed=1;startArrow=none;startFill=0;endArrow=none;endFill=0;" parent="1" target="nr_3PgkoyfCLtJVM_fCE-75" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="2525" y="350" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-74" value="User" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;" parent="1" vertex="1">
          <mxGeometry x="2510" y="270" width="30" height="60" as="geometry" />
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-75" value="User&lt;br&gt;" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;" parent="1" vertex="1">
          <mxGeometry x="2510" y="990" width="30" height="60" as="geometry" />
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-76" value="netWorkError" style="shape=umlLifeline;perimeter=lifelinePerimeter;whiteSpace=wrap;html=1;container=1;collapsible=0;recursiveResize=0;outlineConnect=0;rounded=1;shadow=0;comic=0;labelBackgroundColor=none;strokeWidth=1;fontFamily=Verdana;fontSize=12;align=center;" parent="1" vertex="1">
          <mxGeometry x="3130" y="290" width="100" height="760" as="geometry" />
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-77" value="启动应用" style="html=1;verticalAlign=bottom;endArrow=block;curved=0;rounded=0;" parent="1" target="nr_3PgkoyfCLtJVM_fCE-58" edge="1">
          <mxGeometry x="-0.361" y="10" width="80" relative="1" as="geometry">
            <mxPoint x="2520" y="390" as="sourcePoint" />
            <mxPoint x="2600" y="390" as="targetPoint" />
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-78" value="" style="group" parent="1" vertex="1" connectable="0">
          <mxGeometry x="2606" y="770" width="670" height="200" as="geometry" />
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-79" value="加载广告" style="text;align=center;fontStyle=1;verticalAlign=middle;spacingLeft=3;spacingRight=3;strokeColor=none;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;html=1;" parent="nr_3PgkoyfCLtJVM_fCE-78" vertex="1">
          <mxGeometry x="67" width="80" height="26" as="geometry" />
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-80" value="" style="group" parent="nr_3PgkoyfCLtJVM_fCE-78" vertex="1" connectable="0">
          <mxGeometry width="670" height="200" as="geometry" />
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-81" value="alt" style="shape=umlFrame;whiteSpace=wrap;html=1;pointerEvents=0;" parent="nr_3PgkoyfCLtJVM_fCE-80" vertex="1">
          <mxGeometry width="670" height="200" as="geometry" />
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-82" value="" style="endArrow=none;endFill=0;html=1;edgeStyle=orthogonalEdgeStyle;align=left;verticalAlign=top;rounded=0;dashed=1;" parent="nr_3PgkoyfCLtJVM_fCE-80" edge="1">
          <mxGeometry x="-1" relative="1" as="geometry">
            <mxPoint y="100" as="sourcePoint" />
            <mxPoint x="670" y="100" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="nr_3PgkoyfCLtJVM_fCE-83" value="不加载广告" style="text;align=center;fontStyle=1;verticalAlign=middle;spacingLeft=3;spacingRight=3;strokeColor=none;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;html=1;" parent="nr_3PgkoyfCLtJVM_fCE-80" vertex="1">
          <mxGeometry y="100" width="80" height="26" as="geometry" />
        </mxCell>
        <mxCell id="TXLZZ7lhY2o2Zn4f4jsf-1" value="" style="endArrow=open;startArrow=cross;endFill=0;startFill=0;endSize=8;startSize=10;html=1;rounded=0;" edge="1" parent="1">
          <mxGeometry width="160" relative="1" as="geometry">
            <mxPoint x="4820" y="670" as="sourcePoint" />
            <mxPoint x="4980" y="670" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="TXLZZ7lhY2o2Zn4f4jsf-2" value="navigation to webview&lt;br&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="TXLZZ7lhY2o2Zn4f4jsf-1">
          <mxGeometry x="-0.8344" y="2" relative="1" as="geometry">
            <mxPoint x="66" y="-13" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="TXLZZ7lhY2o2Zn4f4jsf-3" value="par" style="shape=umlFrame;whiteSpace=wrap;html=1;pointerEvents=0;" vertex="1" parent="1">
          <mxGeometry x="4743.75" y="776" width="300" height="180" as="geometry" />
        </mxCell>
        <mxCell id="TXLZZ7lhY2o2Zn4f4jsf-4" value="" style="html=1;points=[[0,0,0,0,5],[0,1,0,0,-5],[1,0,0,0,5],[1,1,0,0,-5]];perimeter=orthogonalPerimeter;outlineConnect=0;targetShapes=umlLifeline;portConstraint=eastwest;newEdgeStyle={&quot;curved&quot;:0,&quot;rounded&quot;:0};" vertex="1" parent="1">
          <mxGeometry x="4816.65" y="815.1100000000001" width="10" height="40" as="geometry" />
        </mxCell>
        <mxCell id="TXLZZ7lhY2o2Zn4f4jsf-5" value="reacCache/&lt;br style=&quot;border-color: var(--border-color);&quot;&gt;registerNotificationEvents" style="html=1;align=left;spacingLeft=2;endArrow=block;rounded=0;edgeStyle=orthogonalEdgeStyle;curved=0;rounded=0;" edge="1" parent="1" target="TXLZZ7lhY2o2Zn4f4jsf-4">
          <mxGeometry x="0.0026" relative="1" as="geometry">
            <mxPoint x="4821.65" y="795.1100000000001" as="sourcePoint" />
            <Array as="points">
              <mxPoint x="4851.65" y="825.1100000000001" />
            </Array>
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="TXLZZ7lhY2o2Zn4f4jsf-6" value="" style="html=1;points=[[0,0,0,0,5],[0,1,0,0,-5],[1,0,0,0,5],[1,1,0,0,-5]];perimeter=orthogonalPerimeter;outlineConnect=0;targetShapes=umlLifeline;portConstraint=eastwest;newEdgeStyle={&quot;curved&quot;:0,&quot;rounded&quot;:0};" vertex="1" parent="1">
          <mxGeometry x="4816.65" y="895.1100000000001" width="10" height="40" as="geometry" />
        </mxCell>
        <mxCell id="TXLZZ7lhY2o2Zn4f4jsf-7" value="checkNetwork" style="html=1;align=left;spacingLeft=2;endArrow=block;rounded=0;edgeStyle=orthogonalEdgeStyle;curved=0;rounded=0;exitX=0;exitY=1;exitDx=0;exitDy=-5;exitPerimeter=0;" edge="1" parent="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="4820.65" y="795.1100000000001" as="sourcePoint" />
            <Array as="points">
              <mxPoint x="4873.65" y="795.1100000000001" />
              <mxPoint x="4873.65" y="914.1100000000001" />
            </Array>
            <mxPoint x="4828.75" y="914" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="TXLZZ7lhY2o2Zn4f4jsf-8" value="用户启动应用程序（正常启动）" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;" connectable="0" vertex="1" parent="1">
          <mxGeometry x="5044.25" y="705" as="geometry" />
        </mxCell>
        <mxCell id="TXLZZ7lhY2o2Zn4f4jsf-9" value="par" style="shape=umlFrame;whiteSpace=wrap;html=1;pointerEvents=0;" vertex="1" parent="1">
          <mxGeometry x="4740" y="450" width="300" height="180" as="geometry" />
        </mxCell>
        <mxCell id="TXLZZ7lhY2o2Zn4f4jsf-10" value="" style="html=1;points=[[0,0,0,0,5],[0,1,0,0,-5],[1,0,0,0,5],[1,1,0,0,-5]];perimeter=orthogonalPerimeter;outlineConnect=0;targetShapes=umlLifeline;portConstraint=eastwest;newEdgeStyle={&quot;curved&quot;:0,&quot;rounded&quot;:0};" vertex="1" parent="1">
          <mxGeometry x="4817.9" y="489.11" width="10" height="40" as="geometry" />
        </mxCell>
        <mxCell id="TXLZZ7lhY2o2Zn4f4jsf-11" value="reacCache/&lt;br style=&quot;border-color: var(--border-color);&quot;&gt;registerNotificationEvents" style="html=1;align=left;spacingLeft=2;endArrow=block;rounded=0;edgeStyle=orthogonalEdgeStyle;curved=0;rounded=0;" edge="1" parent="1" target="TXLZZ7lhY2o2Zn4f4jsf-10">
          <mxGeometry x="0.0026" relative="1" as="geometry">
            <mxPoint x="4822.9" y="469.11" as="sourcePoint" />
            <Array as="points">
              <mxPoint x="4852.9" y="499.11" />
            </Array>
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="TXLZZ7lhY2o2Zn4f4jsf-12" value="" style="html=1;points=[[0,0,0,0,5],[0,1,0,0,-5],[1,0,0,0,5],[1,1,0,0,-5]];perimeter=orthogonalPerimeter;outlineConnect=0;targetShapes=umlLifeline;portConstraint=eastwest;newEdgeStyle={&quot;curved&quot;:0,&quot;rounded&quot;:0};" vertex="1" parent="1">
          <mxGeometry x="4817.9" y="569.11" width="10" height="40" as="geometry" />
        </mxCell>
        <mxCell id="TXLZZ7lhY2o2Zn4f4jsf-13" value="checkNetwork" style="html=1;align=left;spacingLeft=2;endArrow=block;rounded=0;edgeStyle=orthogonalEdgeStyle;curved=0;rounded=0;exitX=0;exitY=1;exitDx=0;exitDy=-5;exitPerimeter=0;" edge="1" parent="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="4821.9" y="469.11" as="sourcePoint" />
            <Array as="points">
              <mxPoint x="4874.9" y="469.11" />
              <mxPoint x="4874.9" y="588.11" />
            </Array>
            <mxPoint x="4830" y="588" as="targetPoint" />
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
