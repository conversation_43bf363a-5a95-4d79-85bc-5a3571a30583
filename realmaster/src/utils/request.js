import axios from 'axios';
import Constants from '../config/constants';
import cookiesIns from './cookies';
import {getDomainByUrl} from './common';
import {getUserAgent, urlFix} from './business';
import appConfigIns from '../config/appConfig';

// let initalRequest = true;

const formatConfig = async config => {
  // 格式化 URL
  config.url = urlFix(config.url);

  // 获取完整url的domain
  const domain = getDomainByUrl(config.url);
  const cookie = cookiesIns.getCookie(domain);

  // const cookie = "_ga=GA1.1.57057434.1724311099; _ga_V97MHW8Y54=GS1.1.1724376175.3.1.1724376915.60.0.0; cmate.sid=HfWnaT1KAHxNWxo99dlaQQ0OU3mrNE1VSrykXvt8LpotiXlEjcdShiIZm0615l3p; uuid=T1jh6cZe4h1a; appmode=mls; apsv=6.5.0; k=kihOsZoAxL_qleK_2bIW2; locale=zh-cn"
  const deviceUserAgent = await getUserAgent();

  const headers = {
    'Content-Type': 'application/json;charset=UTF-8',
    Accept: 'application/json',
    Cache: 'no-cache',
    'User-Agent': deviceUserAgent,
    Cookie: `;${cookie}`,
  };

  config.headers = {
    ...headers,
    ...config.headers,
  };

  config.method = config.method || 'get';

  //自定义请求头
  return config;
};

const request = config => {
  //创建请求实例
  const instance = axios.create({
    baseURL: '',
    timeout: 10000,
    withCredentials: true,
  });
  //请求拦截
  instance.interceptors.request.use(
    config => {
      return formatConfig(config);
    },

    error => {
      //错误请求
      return Promise.reject(error);
    },
  );

  //响应拦截
  instance.interceptors.response.use(
    response => {
    return response.data;
    },
    error => {
      //网络错误
      return Promise.reject(error);
    },
  );

  return instance(config);
};

export const fetchSystemVals = (opt = {}, cb) => {
  // console.log('fetchSystemVals called!!',opt);
  var datas = Constants.Datas;
  if (opt.datas) {
    datas = opt.datas;
    delete opt.datas;
  }
  var dataObj = {datas: datas};
  var postObj = Object.assign(dataObj, opt);
  request({
    method: 'post',
    url: '/1.5/pageData',
    data: postObj,
  })
    .then(ret => {
      if (ret.datas) {
        appConfigIns.setAppConfig(ret.datas);
        cb && cb(ret.datas);
      }
    })
    .catch(err => {
      console.error(err);
    });
};

const requestStdFn = async (fnName, param) => {
  return new Promise((resolve, reject) => {
    const data = {
      q: [
        {
          fn: fnName,
          p: param,
        },
      ],
    };



    request({
      url: '/1.5/stdfun',
      method: 'post',
      data,
    })
      .then(res => {
        const {ret, err} = res.r[0];
        if (err) {
          reject(err);
        } else {
          resolve(ret);
        }
      })
      .catch(err => {
        reject(err);
      });
  });
};

const mainRequest = config => {
  // 确保 signal 能正确传递给 axios
  const finalConfig = {
    ...config,
    // 如果调用时传入了 signal，就使用传入的 signal
    signal: config.signal
  };
  return request(finalConfig);
};

export {mainRequest, requestStdFn};
