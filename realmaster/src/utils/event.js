class RMEvent {
  constructor(){
    this._handlers = {};
  }
  addListener(event,listener){
    if (!this._handlers[event]){
      this._handlers[event] = [];
    }
    handlers = this._handlers;
    if (handlers.includes(listener)) return false;
    handlers.push(listener);
    return true;
  }
  removeListener(event,listener){
    if (handlers = this._handlers[event]){
      if ((i = handlers.indexOf(listener)) >= 0){
        handlers.splice(i,1);
        return true;
      }
    }
    return false;
  }
  fireEvent(event,paramObject,extra1,extra2){
    if (handlers = this._handlers[event]){
      handlers.forEach(h => {
        h(paramObject,extra1,extra2);
      });
      return true;
    }
    return false;
  }
};
const ghandler = new RMEvent();

export default ghandler
