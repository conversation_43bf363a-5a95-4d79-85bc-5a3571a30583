# 学校预览功能需求文档

## 1. 功能概述

在房源地图页，用户可预览地图上各学校的关键信息（Key Facts），包括学校类型、评分、排名、年级范围等。不同类型学校（公立、私立、省外公校）展示内容和样式略有不同。  
当无数据时，需显示 N/A。Fraser 显示的 Key Facts 必须为最近年份的数据，且列表排序需正确。

---

## 2. 详细需求

### 2.1 地图与学校卡片

- 地图上展示房源及学校位置。
- 点击学校图标，底部弹出学校信息卡片。
- 学校卡片内容包括：
  - 学校名称、地址
  - 学校类型标签（如 Elementary, English）
  - Key Facts 区块

---

### 2.2 Key Facts 展示规则

#### 2.2.1 安省公立学校

- 展示内容：
  - 学校名名称
  - 学校地址，距离
  - 学校标签
  - Key Facts
    - AI Ranking（排名/总数，G3 或 G6）优先级G9，G6，G3？？？？
    - AI Rating（评分）
    - Grade（年级范围）
- 样式要求：
  - Key Facts 各部分宽度分别为 43%、43%、14%，左对齐
  - AI Rating 和 AI Ranking 需可点击，弹出 Alert 提示（样式参考房源 detail 页类似部分）这两个点开是同一个提示内容？？？
- Alert 标题及内容（中英文）：
  - 标题：AI Rating & Ranking
  - 英文：RealMaster uses AI to estimate a school's rating and ranking. This is a reference point only. Contact an agent for better insight into a full report of the school.
  - 中文：RealMaster 使用 AI 来估计学校的评级和排名，这只是一个参考点。联系代理，以便更好地了解学校的完整报告。

#### 2.2.2 非安省公校

- 展示内容：
  - 学校名名称
  - 学校地址，距离
  - 学校标签
  - Key Facts
    - Rating（评分）
    - Fraser Ranking（排名/总数）
    - Grade（年级范围）
- 样式要求：
  - Key Facts 各部分宽度为 33.3%，左对齐


#### 2.2.3 私立学校

- 展示内容：
  - 学校名名称
  - 学校地址
  - 学校标签
  - Key Facts
    - AI Ranking（排名）
    - AI Rating（评分）
    - Grade（年级范围）
- 样式要求：
  - Key Facts 各部分宽度分别为 40%、40%、20%，左对齐
  - AI Rating 和 AI Ranking 需可点击，弹出 Alert 提示（样式参考房源 detail 页类似部分）

#### 2.2.4 无数据情况

- 若无相关数据，所有字段显示为 N/A

#### 2.2.5 Fraser Key Facts

- 必须显示最近年份的数据
- 包含列表的排序也需检查正确

---

### 2.3 边界信息

- 公校有边界时，显示：Current Boundary（当前边界）
- 公校无边界时，显示：Grade
- 私校仅显示：Grade

---

## 3. 交互说明

- 点击 AI Rating 或 AI Ranking，弹出 Alert，内容如上。
- Key Facts 区块需根据学校类型动态调整内容和样式。

---

## 4. UI 规范

- 采用左对齐，宽度分配如上。
- 保持整体风格与房源详情页一致。
- 保证响应式设计，适配不同屏幕尺寸。

---

## 5. 其他

- 代码需遵循项目命名规范与结构要求。
- 数据展示需确保准确、及时，避免展示过期或错误信息。

---

1. 大学不变的意思是保持现在的展示方式，不需要改动
