/*
-. topMenuBar
-. Support PrimaryFeature & NormalFeature
-. Support on/off features by side menu

-. Support multiple notification message rotation.
*/

import {
  View,
  Alert,
  StyleSheet,
  TouchableOpacity,
  Text,
  Image,
  TextInput,
  Platform,
  Modal,
  FlatList,
  TouchableHighlight,
  Button,
  Animated,
  Switch,
  Dimensions,
  PixelRatio} from 'react-native';
import React, { Component } from 'react';
import MapView, { <PERSON>er, PROVIDER_GOOGLE , PROVIDER_DEFAULT, MAP_TYPES} from 'react-native-maps';
// import {RMPost,getServerDomain} from './RMNetwork';
// import Icon from 'react-native-vector-icons/Ionicons';
// import { createIconSetFromIcoMoon } from 'react-native-vector-icons';
// import { appConfig,getAppConfig,SYSTEM } from './RMSystem';
import appConfigIns from '../config/appConfig'
import Constants from '../config/constants';
import { Icon, eventEmitter } from '../utils/common';
import {setAppLang,l10n} from '../utils/i18n';
// import PriceMarker from '../components/PriceMarker';
// import ListMarker from '../components/ListMarker';
// import {RMStorage} from './RMStorage';
import storageIns from '../utils/storage'
import {propPrice,currency} from '../mixins/filters';
import {serializeData,urlParamToObject,getIconChar,convert_rm_imgs,listingPicUrls, isValidPolygonBnds} from '../mixins/mapSearch';
import {RMStatusBar,RMBottomBar,getBottomBarHeight, getStatusBarHeight} from '../components/RMStatusBar';
var bottomBarHeight = getBottomBarHeight()

import MapSchool from '../mixins/MapSchool';
import MapProps from '../mixins/MapProps';
import MapStigmatized from '../mixins/MapStigmatized';
import MapDistance from '../mixins/MapDistance';
import MapCoop from '../mixins/MapCoop';
import MapDummyLayer from '../mixins/MapDummyLayer'
import MapCities from '../mixins/MapCities';
import MapAutocomplete from '../mixins/MapAutocomplete';
import ZoomControl from '../components/ZoomControl';
import FlashMessage from '../components/FlashMessage'
import BottomPane from '../components/BottomPane';
import MapMeasure from '../mixins/MapMeasure';
import MapTransit from '../mixins/MapTransit';
import { getColor } from '../utils/RMstyle';
// import MapCommunity from '../mixins/MapCommunity';
// import {getCookieValueByKey} from '..//cookies'
import cookiesIns from '../utils/cookies'
import BottomPaneWhite from '../components/BottomPaneWhite';
// import {setAppConfig} from './RMSystem'
import AnimatedWave from '../components/animated/animatedWave'

const AnimatedIcon = Animated.createAnimatedComponent(Icon);

// red house marker
const hMarker = require('../assets/images/props/hmarker2.png');
// red marker
const cMarker = require('../assets/images/props/none-selected.png');
// blue marker
const uMarker = require('../assets/images/props/umarker.png');

const markerIcons = {
  hMarker:hMarker,
  cMarker:cMarker,
  uMarker:uMarker,
}
const mapStyles = StyleSheet.create({
  container: {
    ...StyleSheet.absoluteFillObject,
    //height: 400,
    //width: 400,
    //justifyContent: 'flex-end',
    //alignItems: 'center',
   //  position:'absolute',
    flexDirection: 'column',
    flex: 1,
  },
  map: {
    // ...StyleSheet.absoluteFillObject,
    flex: 1,
    position:'relative',
    zIndex:1
  },
});
const styles = StyleSheet.create({
  backdrop:{
    zIndex:16,
    // elevation:5,
    position: 'absolute',
    // bottom: 0,
    top: 0,
    backgroundColor: 'rgba(0,0,0,0.7)',
    left: 0,
    right: 0,
    bottom:0,
  },
  tipText:{
    height:50,
    // flex:1,
    padding:5,
    position:'relative',
    backgroundColor:'white',
    bottom:0
  },
  navBarButtonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    backgroundColor:'#e03131',
    zIndex:17
    // paddingLeft:10,
    // paddingRight:3,
    // alignItems: 'stretch',
  },
  navBarButtonContainer2:{
    flexDirection:'row',
    backgroundColor:'white',
    color:'#ddd',
    height: 44,
    zIndex:17,
  },
  filterBarButton:{
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    //margin: 10,
    // width: 20,
    // height: 20,
    padding: 5,
    // paddingLeft:15,
    // paddingRight:15,
    // paddingTop:10,
    // paddingBottom:15,
    fontSize: 17,
  },
  navBarButton: {
    // flexDirection: 'row',
    // justifyContent: 'center',
    // alignItems: 'center',
    // alignSelf:'center',
    //margin: 10,
    width: 44,
    height: 44,
    // padding: 10,
    paddingLeft:8,
    paddingRight:10,
    paddingTop:12,
    // backgroundColor:'blue',
    // paddingBottom:15,
    fontSize: 20,
  },
  filterBar: {
    flexDirection: 'row',
    // marginVertical: 20,
    height:30,
    backgroundColor: 'white',
  },
  filterBarLink:{
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    width:'25%',
  },
  modalStyle:{
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center'
  },
  modalContent:{
    height: 300
  },
  quickFillterWrapper:{
    position: 'absolute',
    // bottom: 0,
    top: 74,
    backgroundColor: 'white',
    left: 0,
    right: 0,
    zIndex: 15,
    height: 240,
  },
  buttonStyle:{
    // paddingRight: 10,
    // paddingLeft: 10,
    // paddingTop:4,
    // paddingBottom:4,
    // borderRadius: 0,
    alignItems:'center',
    justifyContent:'center',
    backgroundColor: '#5cb85c',
    height:50,
  },
  bottomBar:{
    flexDirection: 'row',
    // marginVertical: 20,
    zIndex:15,
    height:50,
    backgroundColor: 'white',
  },
  overlayButton:{
    width: 40,
    height: 40,
    backgroundColor:'transparent',
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'column',
  },
  overlayButtonFocus:{
    // borderWidth: 1,
    // borderColor: "#20232a",
    color: "#e33",
  },
  overlayButtonNotFocus:{
    backgroundColor:"#777777",
  },
  overlayButtonOn:{
    //backgroundColor:'#ffddee',
    color: 'black',
  },
  overlayButtonText:{
    color: '#999999',
    fontSize: 6,
    justifyContent: 'center',
    alignItems: 'stretch',
    flexDirection: 'row',
    flex:1,
  },
  overlayButtonImage:{
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
  },
  overLayShadow:{
    // elevation is for android shadow
    elevation:2,
    // zIndex:15,
    // overflow: 'visible',
    shadowColor:'#d2d2d2',//'#d2d2d2',
    shadowOffset:{width:1,height:3},
    shadowRadius:3,
    shadowOpacity:0.8,
    // borderRadius:undefined,
    // backgroundColor: 'white',
  },
  overlayOnOffMenuWrapper:{
    position: 'absolute',
    right:8,
    // top:380,//change this if +/- removed
    zIndex:15,
  },
  overlayOnOffMenu:{
    backgroundColor:'white',
    borderRadius:20,
    width: 40,
    height: 88,//change this if have more to disp
    // overflow: 'hidden',
    flexDirection:'column',
    //justifyContent:'space-between',
  },
});
const { width, height } = Dimensions.get('window');
var ASPECT_RATIO = width / (height-44-50);
let initialRegion;
// var COOKIE = '';
function allAreNull(arr) {
  return arr.every((ele) => {
    return ele === null || ele === undefined
  });
}

// 添加新的辅助函数来检测 Google Maps 服务可用性
const checkGoogleMapsAvailability = async () => {
  try {
    const testUrls = [
      'https://www.googleapis.com/generate_204',
      'https://clients2.google.com/generate_204',
      'https://clients3.google.com/generate_204'
    ];

    // 添加时间戳参数避免缓存
    const promises = testUrls.map(url => {
      const timestampedUrl = `${url}${url.includes('?') ? '&' : '?'}_=${Date.now()}`;
      return fetch(timestampedUrl, {
        method: 'HEAD',
        timeout: 3000,
        // 添加更多缓存控制头
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      });
    });

    // 任何一个请求成功即认为可访问
    const results = await Promise.any(promises);
    return results.ok;

  } catch (error) {
    console.log('Google 服务检测失败:', error); 
    return false;
  }
}

class RMMapSearchNative extends Component {
  constructor(props) {
    super(props);
    this.allFeatures = []; // all features array
    // mainFeatures and featurs exclusive, can only be one
    this.features = {}; // normal features
    this.mainFeatures = {}; // main features
    this.featuresOnPress = {}; // features support onPress
    this.featuresOnPressMenu = []; // feature onPress menu
    this.featureFocused = null;
    bottomBarHeight = getBottomBarHeight()
    ASPECT_RATIO = width / (height-44-50-bottomBarHeight);
    this.activeFeatureNameMapping = {
      'MapStigmatized':l10n('Stigmatized'),
      'Schools':l10n('School'),
      'MapCoop':l10n('Co-op housing'),
      'MapTransit':l10n('Mass Transit'),
      'MapDistance':l10n('Scale'),
      'MapDummyLayer':l10n('Map')
    };
    // this.pgNum = 0
    this.screenWidth = width;
    this.startedRender = false;
    // this.doSearchTimeout = null,
    // this.viewMode = 'map',//list
    // this.appMapMode = 'map',//fav
    // this.previousRequestTs = null,
    this.thisProps = Object.assign({},this.props);
    // this.isOnTop = this.thisProps.route.params.isOnTop;
    // NOTE: initialRegion and moveTobbox not exact match!
    if(Platform.OS !== 'ios'){
      initialRegion = {
        latitude: 43.6448,
        longitude: -79.3958,
        latitudeDelta: 0.015,
        longitudeDelta: 0.015 * ASPECT_RATIO,
      }
      var coords;
      if(this.thisProps && this.thisProps.navigation && this.thisProps.route.params){
        coords = this.thisProps.route.params.coords;
      }
      if (coords && coords.latitude) {
        initialRegion.latitude = parseFloat(coords.latitude) || 43.6448;
        initialRegion.longitude = parseFloat(coords.longitude) || -79.3958;
        initialRegion.latitudeDelta = parseFloat(coords.delta || this.thisProps.delta) || 0.015;
        if (Array.isArray(this.thisProps.route.params.bbox)) {
          let bbox = this.thisProps.route.params.bbox
          longitudeDelta = Math.abs(bbox[2]-bbox[0])
        }
      }
    }
    var stateObj = {
      appmode: 'mls',
      animateValue:new Animated.Value(0),
      // curSch:{},
      uMarker:{},
      hMarker:{},
      cMarker:{},
      activeFeatureName:'house',
      prevActiveIcon:'rm-school',
      prevActiveSize: 24,
      mapReady:true,
      prevActiveStyle: null,
      tracksViewChanges:false,
      //stores displayed values
      mapTypeId:this.thisProps.mapTypeId|| MAP_TYPES.STANDARD,
      // schools:[],
      // mapProps:{},//NOTE: for map markers, diff from prop
      // props:[],//NOTE: list mode props list
      // domFilterVals:[],
      // ptype2s:[{k:'House',v:'House'},{k:'Detached',v:'Detached'}],
      // propHalfDetail:false,
      // initialRegion,
      showLayerSelect:false,
      showBackdrop:false,
      mapPadding:{
        top: 0,
        right: 0,
        left: 0,
        bottom: 0,
      },
      isShowDrawPolygon:true,
      isDrawActive:false,
      canAccessGoogle: null
    };

    this.state = stateObj;
    this.loadMapInitialRegion()
  }
  componentWillUnmount(){
    // console.log('unmount stuff and clear timeout')
    clearTimeout(this._trackOffTimer);
    clearTimeout(this._locateMe);
    // NOTE: no use, still setstate when unmounted
    // this.locateMecb = ()=>{};
    // maybe cancel inside geopostion
    eventEmitter.removeListener("map.locateMe", this.locateMe.bind(this));
    eventEmitter.removeListener("map.showStigma", this.showStigma.bind(this));
    eventEmitter.removeListener("map.regionChange", this.onRegionChangeComplete.bind(this));
    eventEmitter.removeListener('map.clearModals',this.clearModals.bind(this))
    eventEmitter.removeListener(Constants.ChangeAppMode,this.changeAppMode.bind(this));
    // eventEmitter.removeListener("map.setMapPadding", this.setMapPadding);
    this.allFeatures.forEach((f)=>{
      f.unmount();
    });
  }
  toggleAnimation(){
    this.state.animateValue.setValue(0);
    Animated.timing(this.state.animateValue, {
      toValue: 1,
      duration: 800,
      useNativeDriver: true
    }).start();
    // this.setState({ full: !this.state.full });
  }
  trackOff(){
    clearTimeout(this._trackOffTimer);
    this._trackOffTimer = setTimeout(()=>{
      this.setState({tracksViewChanges:false});
    },1000);
  }
  // given array of points, where last point equals first, return center and delta of array of points
  // where center is (maxLat-minLat)/2, (maxLng-minLng)/2
  // @input bnds = [lat,lng,lat,lng....]
  // @return {center:{lat,lng},delta}
  calcCenterAndDelta(bnds){
    var maxLat = -90;
    var minLat = 90;
    var maxLng = -180;
    var minLng = 180;
    bnds = bnds.map((ele)=>{ return parseFloat(ele) })
    // console.log('+++++bnds2',bnds)
    for (let i=0;i<bnds.length;i+=2) {
      if(i==bnds.length-1){
        continue
      }
      if(bnds[i]>maxLat){
        maxLat = bnds[i]
      }
      if(bnds[i]<minLat){
        minLat = bnds[i]
      }
      if(bnds[i+1]>maxLng){
        maxLng = bnds[i+1]
      }
      if(bnds[i+1]<minLng){
        minLng = bnds[i+1]
      }
    }
    var center = {
      lat:(maxLat+minLat)/2,
      lng:(maxLng+minLng)/2
    }
    var delta = Math.max(maxLat-minLat,maxLng-minLng)*1.6
    // console.log('+++++center,delta',center,delta)
    return {center,delta}

  }
  loadMapInitialRegion(){
    // initial region -> move map to this location
    // return
    var latitudeDelta = 0.015;
    var longitudeDelta;
    var LATITUDE;
    var LONGITUDE;
    var bbox = [];
    var lastMapPosition = storageIns.getCacheItem(Constants.LastMapPosition)
    try {
      lastMapPosition = lastMapPosition && JSON.parse(lastMapPosition)
    } catch (error) {
      console.error('Error loadMapInitialRegion: ',error)
      lastMapPosition = null;
    }
    // console.log('this.props: ',this.thisProps.navigation, this.props.route.params)
    var coords = {};
    var bnds = [];
    if(this.thisProps && this.thisProps.navigation && this.thisProps.route.params){
      // console.log('setting prop!!',this.thisProps.route.params)
      coords = this.thisProps.route.params.coords;
    }
    if(this.thisProps && this.thisProps.route && this.thisProps.route.params){
      // console.log('xxxxx',this.thisProps.route.params)
      bnds = this.thisProps.route.params.bnds || [];
    }
    // console.log('+++++ coords',coords,this.thisProps.delta,' bnds->',bnds)
    if (isValidPolygonBnds(bnds)){
      // fit map to this bnds, calc center and delta, bnds is array of points
      // console.log('+++++bnds',bnds)
      // this.map.fitToCoordinates(coordsAll)
      let {center,delta} = this.calcCenterAndDelta(bnds)
      LATITUDE = center.lat;
      LONGITUDE = center.lng;
      latitudeDelta = delta;
    } else if (coords && coords.latitude) {
      // console.log('props has coords!!!',this.thisProps.route.params.bbox,coords)
      LATITUDE = parseFloat(coords.latitude) || 43.6448;
      LONGITUDE = parseFloat(coords.longitude) || -79.3958;
      latitudeDelta = parseFloat(coords.delta || this.thisProps.delta) || 0.015;
      if (Array.isArray(this.thisProps.route.params.bbox)) {
        let tmpBbox = this.thisProps.route.params.bbox
        for (const i of [0,1,2,3]) {
          bbox.push(parseFloat(tmpBbox[i]))
        }
        // console.log('!!!!bbox',bbox)
        longitudeDelta = Math.abs(bbox[2]-bbox[0])
        // console.log('+++++++longitudeDelta',longitudeDelta)
      }
      // this.props.cMarker = true;
    } else if (lastMapPosition && lastMapPosition.lat!=null) {
      // console.log('has lastMap pos!!', lastMapPosition)
      LATITUDE = lastMapPosition.lat;
      LONGITUDE = lastMapPosition.lng;
      latitudeDelta = lastMapPosition.delta;
      // this.props.uMarker = true;
    } else {
      LATITUDE = 43.6448;
      LONGITUDE = -79.3958;
    }
    initialRegion = {
      latitude:LATITUDE,
      longitude:LONGITUDE,
      latitudeDelta: latitudeDelta,
      longitudeDelta: longitudeDelta?longitudeDelta:latitudeDelta * ASPECT_RATIO,
    }

    // console.log('lat lng finally=  ',this.state.initialRegion)
    // console.log('+++',this.state.initialRegion)
    // NOTE: this initial region is not working for this lib new version
    // https://github.com/react-native-maps/react-native-maps/issues/4244
    // this.setState(state);
    if(bbox.length){
      this.fitToInitialBbox(bbox)
    } else {
      this.animateToInitialRegion({initialRegion},lastMapPosition)
    }
  }

  fitToInitialBbox(bbox=[]){
    if(!this.startedRender){
      setTimeout(() => {
        this.fitToInitialBbox(bbox)
      }, 200);
      return
    }
    // console.log('render already started, move to bbox',bbox)
    setTimeout(() => {
      this.fitToBBox(bbox)
    }, 100);
  }

  animateToInitialRegion({initialRegion},lastMapPosition){
    if(!this.startedRender){
      setTimeout(() => {
        this.animateToInitialRegion({initialRegion},lastMapPosition)
      }, 100);
      return
    }
    // console.log('render already started, move to position',region)
    this.map.animateToRegion(initialRegion, 0);
    if(lastMapPosition){
      return
    }
    // setTimeout(() => {
    //   this.checkIfMapMoved(region);
    // }, 1800);
  }

  calcDiffBetweenRegions(prev,cur){
    var diff = {};
    if(prev.latitude && cur.latitude){
      diff.latitude = cur.latitude - prev.latitude;
    }
    if(prev.longitude && cur.longitude){
      diff.longitude = cur.longitude - prev.longitude;
    }
    if(prev.latitudeDelta && cur.latitudeDelta){
      diff.latitudeDelta = cur.latitudeDelta - prev.latitudeDelta;
    }
    if(prev.longitudeDelta && cur.longitudeDelta){
      diff.longitudeDelta = cur.longitudeDelta - prev.longitudeDelta;
    }
    return diff;
  }
  async checkIfMapMoved(region){
    // do nothing if has locate me
    if(this.thisProps.gps){
      return
    }
    var bnds = await this.map.getMapBoundaries()
    var bbox, mapRegion;
    if (bnds) {
      var ne = bnds.northEast;
      var sw = bnds.southWest;
      bbox = [
        sw.longitude, sw.latitude,
        ne.longitude, ne.latitude
      ]
      var delta = bbox[3]-bbox[1];
      var lat = (bbox[1]+bbox[3])/2;
      var lng = (bbox[0]+bbox[2])/2;
      // zoom = self.calcZoomLevel((delta)/2)
      mapRegion = {
        latitude: lat,
        longitude: lng,
        latitudeDelta: delta,
        longitudeDelta: delta * ASPECT_RATIO,
      }
    }
    // console.log('map region=',mapRegion)
    var diff = this.calcDiffBetweenRegions(mapRegion,region);
    if(diff.latitude!=0 || diff.longitude!=0 || diff.latitudeDelta!=0 || diff.longitudeDelta!=0){
      // console.log('diff= ',diff)
      if(Math.abs(diff.latitudeDelta) < 0.1 && Math.abs(diff.longitudeDelta) < 0.1){
        // do nothing
        // console.log('+++++correct map postion diff=, region=',diff,region)
        // var bnds = await this.map.getMapBoundaries()
        // console.log(bnds)
      } else {
        // console.log('xxxxxx incorrect map postion')
        this.animateToInitialRegion({initialRegion:region})
      }
    }
  }
  // async may cause render before didmount;
  async componentDidMount(){
    // TODO: this causes warning: Expected Instance Props to Match Memoized Props
    // FIX: use react-navigation ~5.0.0,
    this.thisProps = {
      ...this.props,
      ...this.props.route.params
    }
    this.data = {
      // pubSchOnly:this.thisProps.pubSchOnly,
      stig:this.thisProps.stig,
      transit:this.thisProps.transit,
      coop:this.thisProps.coop,
      measure:this.thisProps.measure
    };
    this.data.closePopup = this.thisProps.closePopup;
    // if (!this.props.coords) {
    //   this.props.coords = {
    //     latitude:null,
    //     longitude:null
    //   }
    // }
    // await this.loadMapInitialRegion();

    eventEmitter.on('map.clearModals',this.clearModals.bind(this))
    eventEmitter.on('map.locateMe', this.locateMe.bind(this));
    eventEmitter.on('map.showStigma', this.showStigma.bind(this));
    eventEmitter.on('map.regionChange', this.onRegionChangeComplete.bind(this));
    eventEmitter.on(Constants.ChangeAppMode,this.changeAppMode.bind(this));
    // eventEmitter.on('map.setMapPadding', this.setMapPadding)
    // var thisProps = this.props;
    let appmode = await cookiesIns.getCookieValueByKey(null,'appmode') || 'mls';
    // console.log('+++++',getAppConfig('useTileOverlay'),getAppConfig('tileUrl'))
    let state = {
      tracksViewChanges:true,
      useTileOverlay: appConfigIns.getAppConfig('useTileOverlay'),
      tileUrl: appConfigIns.getAppConfig('tileUrl'),
      maximumZ: appConfigIns.getAppConfig('maximumZ'),
      tintColor:await getColor('mainTheme',appmode),
      appmode,
    }
    // console.log('++++++RMMapSearch: props',this.props)
    if (this.thisProps.hMarker || this.thisProps.cMarker || this.thisProps.uMarker) {
      var name = 'hMarker';
      if (this.thisProps.cMarker) {
        name = 'cMarker'
      } else if (this.thisProps.uMarker){
        name = 'uMarker'
      }
      state[name] = {coords:{
        latitude: initialRegion.latitude,
        longitude: initialRegion.longitude}
      };
      // console.log(state)
    }
    if(this.thisProps.gps){
      clearTimeout(this._locateMe);
      this._locateMe = setTimeout(() => {
        this.locateMe()
      }, 500);
    }
    // if (this.props && this.props.coords.latitude) {
    // state.coords = this.props.coords;
    // }

    // TODO: initial features based on this.props
    // NOTE: this.props somehow changed here, use thisProps
    // console.log('++++++++'+this.data.pubSchOnly)
    // TODO: layers based on config?
    // if (!this.data.pubSchOnly) {
    // this.removeFeature({name:'MapProps'})
    // }
    // TODO: feature select layer
    // NOTE: for now if/else for mapStig

    // openMap({ feature:[''] })

    // features:[prop] MapProps
    // if stig, stig is active
    // let initOnName = 'MapDummyLayer'
    let initOnName = 'Schools'
    function isFeatureOn(nm){
      return initOnName == nm
    }
    if (this.data.stig) {
      initOnName = 'MapStigmatized';
    }
    // if (this.data.measure){
    //   initOnName = 'measure';
    // }
    if (this.data.transit){
      initOnName = 'MapTransit'
    }
    if (this.data.coop){
      initOnName = 'MapCoop'
    }
    var propFeatureOn = true
    var anyStigCoopOn = false
    for(let i of ['MapCoop','MapStigmatized']){
      if(isFeatureOn(i)){
        anyStigCoopOn = true
        break;
      }
    }
    if(this.thisProps.nohouse){//anyStigCoopOn
      propFeatureOn = false
    }
    state.activeFeatureName = initOnName;
    this.setState(state);
    this.trackOff();
    // console.log(this.thisProps)
    // this.featureMapAutocomplete = new MapAutocomplete(this);
    if(!propFeatureOn){
      eventEmitter.emit('map.props.fetureOn',false)
      this.thisProps.propFeatureOn = false
    }
    this.featureMapProps  = new MapProps(this,this.thisProps,propFeatureOn,
      {
        setShowDrawPolygon:this.setShowDrawPolygon.bind(this),
        setDrawActive:this.setDrawActive.bind(this),
        ASPECT_RATIO
      }
    );
    this.featureMapCities = new MapCities(this);
    this.featureDummy = new MapDummyLayer(this,this.thisProps,isFeatureOn('MapDummyLayer'))
    this.featureMapSchool = new MapSchool(this,this.thisProps,isFeatureOn('Schools'));
    this.featureTransit = new MapTransit(this,this.thisProps,isFeatureOn('MapTransit'));
    this.featureMapCoop  = new MapCoop(this,this.thisProps,isFeatureOn('MapCoop'));
    // if(isFeatureOn('measure')){
    //   this.featureMeasure = new MapMeasure(this,this.thisProps,isFeatureOn('measure'));
    // }
    this.featureMapStigmatized  = new MapStigmatized(this,this.thisProps,isFeatureOn('MapStigmatized'));
    // this.featureMapDistance  = new MapDistance(this,this.thisProps,propFeatureOn);
    // this.featureMapCommunity  = new MapCommunity(this,this.thisProps,false);
    //this.featureMapZoomCtrl = new MapZoomCtrl(this);
    // this.thisProps = thisProps;
    setTimeout(() => {
      this.toggleAnimation()
    }, 1400);
    this.layerAnimation = setInterval(()=>{
      this.toggleAnimation()
    },8000)
    // if(Platform.OS == 'android'){
    //   this.mapReadyTimeout = setTimeout(()=>{
    //     this.setState({mapReady:false})
    //   },5000)
    // }

    // 在 componentDidMount 中进行检测
    const canAccessGoogle = await checkGoogleMapsAvailability();
    global.rmLog(`RMMapSearchNative.jsx:787~~~canAccessGoogle`, canAccessGoogle);
    this.setState({ canAccessGoogle });
  }

  // NOTE: from github
  calcZoomLevel(longitudeDelta){
    return Math.log2(360 * ((this.screenWidth/256) / longitudeDelta)) + 1
  }
  calcDeltaFromZoom(zoom){
    return ((this.screenWidth/256)) / (Math.pow(2,(zoom-1))/360)
  }
  clearSchoolMarkers(ignore){
    console.log('clearSchoolMarkers');
  }
  addFeature(feature){
    if (feature.isMainFeature()){
      this.mainFeatures[feature.name] = feature;
    }else{
      this.features[feature.name] = feature;
    }
    this.allFeatures.push(feature);
  }
  removeFeature(feature){
    let fName = feature.name;
    // console.log('xxxxxxRemove feature '+feature)
    delete this['feature'+fName];
    delete this.features[fName];
    delete this.mainFeatures[fName];
    for( var i = 0; i < this.mainFeatures.length; i++){
      if (this.allFeatures[i] === feature) {
        this.allFeatures.splice(i, 1);
        break;
      }
    }
    this.removeOnPressFeature(feature); // keep things consistant
    var state = {tracksViewChanges:true};
    this.setState(state);
    this.trackOff();
    // console.log(Object.keys(this.features))
  }
  addOnPressFeature(feature){
    this.featuresOnPress[feature.name] = feature;
    let menu = []
    Object.values(this.featuresOnPress).forEach((f)=>{
      menu.push({fname:f.name,menu:f.onPressMenu()});
    })
    this.featuresOnPressMenu = menu;
  }
  removeOnPressFeature(feature){
    delete this.featuresOnPress[feature.name];
  }
  focusOn(feature){
    this.featureFocused = feature;
  }
  focusOff(feature){
    if (this.featureFocused == feature){
      this.featureFocused = null;
    }
  }
  closeOtherModals(feature){
    this.allFeatures.forEach((f)=>{
      if (f.name != feature.name){
        f.closeModal();
      }
    });
  }
  featureSetState(feature,newFeatureState,cb){
    var self = this;
    setTimeout(
      ()=>{
        self.setState( (state,props) => {
          let changedState = {};
          // get previouse state or use new state
          changedState[feature.name] = (state[feature.name] || newFeatureState);
          // merge new state into existing one
          Object.assign(changedState[feature.name],newFeatureState);
          return changedState;
        },cb);
      },10);
  }
  mapOnPress(e){
    // console.log('map on press')
  }
  // NOTE: onRegionChangeComplete not triggered on android
  mapOnReady(e) {
    try {
      this.startedRender = true
      clearTimeout(this.mapReadyTimeout);
      this.setState({ mapReady: true })
      if (Platform.OS == 'android') {
        if (!initialRegion) {
          console.error('Initial region not set during map initialization');
          return;
        }
        this.onRegionChangeComplete(initialRegion);
      }
    } catch (error) {
      console.error('Error in mapOnReady:', error);
      this.setState({ mapReady: false });
    }

  }
  switchToWebMap(e){
    appConfigIns.setAppConfig({useWebMap:true})
    this.goBack('/1.5/mapSearch?d=/home/<USER>')
  }
  onLongPress(e){
    // console.log(e);
    var self = this;
    // polygon drag
    if(self.state.isShowDrawPolygon){
      return
    }
    if (self.featureFocused){
      return self.featureFocused.onPress(e,self);
    }
    if (self.featuresOnPress){
      let featursPressList = Object.keys(self.featuresOnPress);
      let featuresLength = featursPressList.length;
      // if only one feature support onPress, trigger onPress
      // console.log('++++++featureOnPress length == ',featursPressList,featuresLength,self.featuresOnPress)
      // TODO: clear modals
      if (featuresLength == 1){
        let key = featursPressList[0]
        // console.log('++++++featureOnPress length == 1',key)
        self.featuresOnPress[key].onPress(e,self);
        return
      // else show list and let user select
      } else if ( featuresLength > 1){
        // show popup menu
        // console.log('xxxxxxxxfeaturesOnPress',featursPressList.concat.length)
        self._onPressEvent = e;
        self.setState({popupMenu:self.featuresOnPressMenu,onPressLoc:{lat:e.coordinate.latitude,lng:e.coordinate.longitude}});
        return;
      }
      // console.log('+++++featuresOnPress',Object.keys(self.featuresOnPress))
      // Object.entries(self.features).forEach(([name, f]) => {
      //   // { target: 53,
      //   // coordinate: { latitude: 43.51938069196711, longitude: -79.66070249676704 },
      //   // position: { y: 300.33441162109375, x: 289.6678771972656 } }
      //   f.onPress(e,self);
      // });
    }
  }
  setMapPadding = ({mapPadding})=>{
    // console.log('set setMapPadding:', mapPadding.bottom)
    this.setState({mapPadding})
  }
  async changeAppMode(opt={}){
    // console.log('+++changeAppMode in mapsearch',opt)
    if(opt.val){
      this.setState({
        appmode:opt.val,
        tintColor:await getColor('mainTheme',opt.val)
      })
    }
  }

  // region = {latitude: 43.51938069196711, longitude: -79.66070249676704, latitudeDelta: 0.0922, longitudeDelta: 0.0421}
  // bbox = [sw.longitude, sw.latitude, ne.longitude, ne.latitude]
  converRegionToBbox = (region)=>{
    let lngDelta = region.longitudeDelta
    let latDelta = region.latitudeDelta
    let bbox = [
      region.longitude-lngDelta/2, region.latitude-latDelta/2,
      region.longitude+lngDelta/2, region.latitude+latDelta/2
    ]
    return bbox
  }

  async onRegionChangeComplete(region) {
    let self = this,bnds,bbox,zoom;

    if(!region){
      try {
        bnds = await self.map.getMapBoundaries();
      } catch (error) {
        console.error('getMapBoundaries error',error)
      }
      // console.log('xxxxxx',bnds)
      var ne = bnds.northEast;
      var sw = bnds.southWest;
      bbox = [
        sw.longitude, sw.latitude,
        ne.longitude, ne.latitude
      ]
      var delta = bbox[3]-bbox[1];
      var lat = (bbox[1]+bbox[3])/2;
      var lng = (bbox[0]+bbox[2])/2;
      this.lastPos = {lat:lat,lng:lng,delta}
      // RMStorage.setItemObj(SYSTEM.lastMapPosition,{lat:lat,lng:lng,delta});
      zoom = self.calcZoomLevel((delta)/2)
      // console.log('onRegionChangeComplete++ zoom');
    } else {
      bbox = this.converRegionToBbox(region);
      zoom = self.calcZoomLevel((region.latitudeDelta))
      this.lastRegion = region
      this.lastPos = {lat:region.latitude,lng:region.longitude,delta:region.latitudeDelta}
    }

    storageIns.setCacheItem(Constants.LastMapPosition, JSON.stringify(this.lastPos),true)

    if(!bbox){
      return console.error('no bbox');
    }




    // console.log('bbox is: ',bbox)
    var opts = {bbox,zoom,self}
    // if (zoom > 15) {
      // self.getSchoolsInfo(null,{bbox:bbox, mode:'list'});
    // } else {
      // var tmp = {};
      // if (self.curSch && self.curSch._id) {
      //   tmp.skip = [self.curSch._id];
      // }
      // self.clearSchoolMarkers(tmp);
    // }
    if (this.doSearchTimeout) {
      clearTimeout(this.doSearchTimeout)
    }
    this.doSearchTimeout = setTimeout(()=>{
      // console.log('region change complete')
      if (this.featureFocused){
        this.featureFocused.regionChanged(opts,this);
      } else {
        self.allFeatures.forEach((f) => {
          f.regionChanged(opts,self);
        });
      }
    },0)
    // this.setState({
    //   mapBoundaries await this.map.getMapBoundaries(),
    // });
    // var mapBoundaries = await this.map.getMapBoundaries()
    // console.log(mapBoundaries);
  }
  switchMapType(){
    var type = 'standard';
    if (this.state.mapTypeId == MAP_TYPES.STANDARD) {
      type = MAP_TYPES.HYBRID;//satellite
    }
    this.state.mapTypeId = type;
    this.setState({
      mapTypeId:type
    })
  }
  locateMecb = (val)=>{
    var coords,self = this;
    // console.log('locateMecb:',val,self.map)
    if(coords = val.coords){
      var opt = {lat:coords.latitude, lng:coords.longitude, showMarker:1, uMarker:1};
      self.setCenterAndZoom(opt,{zoom:16});
    }
  }
  locateMe(){
    var opt = {
      // hide:false,
      // sel:'#callBackString',
      tp:'geoPosition',
      // toolbar:false,
      // url:getServerDomain()+'/1.5/city/select?search=1',
    }
    eventEmitter.emit("app.message",{msg:JSON.stringify(opt),cb:this.locateMecb});
  }
  // draw points on map and render as polygon
  drawPoly(){
    // invoke mapProps draw poly
    this.setState({isDrawActive:!this.state.isDrawActive},()=>{
      // console.log(this.state)
    })
    this.featureMapProps.drawDefaultPoly()
  }
  setShowDrawPolygon(val){
    // console.log('+++++setShowDrawPolygon',val,this.state)
    // var self = this;
    this.setState({isShowDrawPolygon:val},()=>{
      // console.log(this.state)
    })
  }
  setDrawActive(val){
    this.setState({isDrawActive:val},()=>{
      // console.log(this.state)
    })
  }
  cancelPopupMenu(){
    if (this.state.popupMenu){
      this.setState({popupMenu:null,onPressLoc:null});
      this._onPressEvent = null;
    }
  }
  popupMenuSelected(item){
    if (f = this.featuresOnPress[item.fname]){
      f.onPress(this._onPressEvent,this);
    }
    this.cancelPopupMenu();
  }
  // method = renderBottomBar/render...
  _mainThenNormal(method){
    let ret = [];
    // console.log('method = ',method)
    Object.values(this.mainFeatures).forEach( feature =>{
      let r = feature[method]()
      // console.log('r==',r)
      if (r){
        ret.push(r);
      }
    });
    if (ret.length > 0) return ret;
    Object.values(this.features).forEach( feature =>{
      let r = feature[method]()
      // console.log('r==',r)
      if (r){
        ret.push(r);
      }
    });
    if (ret.length > 0) return ret;
    return null;
  }
  // still show backdrop
  clearLayerSelect(){
    // console.log('clear clearLayerSelect')
    let state = {
      showLayerSelect:false,
    }
    this.setState(state)
  }
  // hide backdrop & layer
  // NOTE: if this is slow then because sth is rendering in backend
  clearModals(opt={}){
    // TODO: ignore if this map is already hidden
    // console.log('----isOnTop',this.isOnTop())
    let state = {
      showLayerSelect:false,
      showBackdrop:false,
    }
    if(opt.backdrop){
      state.showBackdrop = true
    }
    // console.log('clear modal',opt,state)
    // NOTE: clear my modals
    this.setState(state)
    let opt2 = {msg:'clear',val:false,src:opt.src}
    this.clearFeaturesPanels(opt2)
    // eventEmitter.emit(SYSTEM.EVENT_CLEAR_FEATURE_PANEL,{msg:'clear',val:false,src:opt.src});
  }
  clearFeaturesPanels(opt){
    this.allFeatures.forEach( feature =>{
      feature.setShowPanel(opt);
    });
  }
  getBackDropVal(){
    return this.state.showBackdrop;
  }
  toggleBackDrop(onOff){
    let showBackdrop = this.state.showBackdrop;
    if(onOff){
      if(onOff=='On'){
        showBackdrop = false;
      } else {
        showBackdrop = true;
      }
    }
    let state = {
      showBackdrop:!showBackdrop,
    }
    this.setState(state)
  }
  toggleLayerSelect(){
    let showLayerSelect = this.state.showLayerSelect;
    let showBackdrop = this.state.showBackdrop;
    let state = {
      showLayerSelect:!showLayerSelect,
      showBackdrop:!showBackdrop,
    }
    // console.log('++++toggleLayerSelect',state.showLayerSelect)
    clearInterval(this.layerAnimation)
    this.setState(state,()=>{
      if(state.showLayerSelect){
        // this.clearModals()
        // TODO: hide open modal for other features
        // eventEmitter.emit(SYSTEM.EVENT_CLEAR_FEATURE_PANEL,{msg:'clear',val:false});
        this.clearFeaturesPanels({msg:'clear',val:false})
      }
    })
  }
  // reset to prop and school layer active
  showStigma(){
    this.resetMapLayer(['MapProps'],['MapStigmatized'])
  }
  //
  resetMapLayer(ignore=['MapProps'],forceOnLayer=['Schools']){
    // console.log('resetMapLayer ignore=',ignore)
    this.switchOffAllLayers(ignore,forceOnLayer)
    // TODO: switch on school layer
    this.setState({activeFeatureName:forceOnLayer[0]})
    this.clearModals()
  }
  selectDummyLayer(){
    let dummy = ['MapDummyLayer']
    // show layer select again
    if(dummy.includes(this.state.activeFeatureName)){
      this.toggleLayerSelect()
      return
    }
    this.resetMapLayer(['MapProps'],dummy)
  }
  switchOffAllLayers(ignore=['MapProps'],forceOnLayer=[]){
    this.allFeatures.forEach( feature =>{
      let onOff;
      if (onOff = feature.onOffView()){
        // console.log('ignore=',ignore,feature.name)
        if(forceOnLayer.includes(feature.name)){
          if (!onOff.on){
            onOff.toggleOnOff('on',true)
          }
        } else if(ignore.includes(feature.name)){
          // ignore this feature
        } else {
          if (onOff.on){
            onOff.toggleOnOff('off')
          }
        }
      }
    });
  }
  setMapScaleStatus(item,status){
    item.onOff.setStatus(status);
    storageIns.setCacheItem(Constants.MapScaleStatus, status,true)
  }
  // select this feature on
  selectLayer(item, opt={}){
    let activeFeatureName = this.state.activeFeatureName;
    let prevActiveIcon = item.onOff.icon;
    let prevActiveSize = item.onOff.iconSize || 23;
    let prevActiveStyle = item.onOff.iconStyle;
    if(item.feature.name == 'MapProps' || item.feature.name == 'MapDistance'){
      item.onOff.toggleOnOff();
      return
    }
    if(activeFeatureName != item.feature.name){
      this.switchOffAllLayers(['MapProps','MapDistance'])
      item.onOff.toggleOnOff();
    } else {
      console.log('same feature, only toggle select, set showPane=1, ',activeFeatureName)
      // eventEmitter.emit(SYSTEM.EVENT_CLEAR_FEATURE_PANEL,{name:activeFeatureName,msg:'clear',val:true});
      this.clearFeaturesPanels({name:activeFeatureName,msg:'clear',val:true})
    }
    if(['MapTransit','Schools'].indexOf(item.feature.name)>-1){
      this.clearLayerSelect();
      if(item.feature.name == 'MapTransit'){
        this.toggleBackDrop('off')
      }
      // NOTE: reduce map size/bbox, since feature has panel
      // if(item.feature.name=='MapTransit'){
      //   this.setMapPadding({mapPadding:{
      //     top: 0,
      //     right: 0,
      //     left: 0,
      //     bottom: 250,
      //   }})
      // }
    } else {
      this.clearModals()
    }
    this.setState({prevActiveIcon,prevActiveSize,prevActiveStyle,activeFeatureName:item.feature.name})
    // this.toggleBackDrop()
  }
  renderTriggered(){
    if(this.renderCount){
      this.renderCount++;
    } else {
      this.renderCount = 1;
    }
    clearTimeout(this.renderCountTimeout)
    this.renderCountTimeout = setTimeout(() => {
      console.log('render triggered',this.renderCount)
      this.renderCount = null;
    }, 2000);
  }
  render() {
    let self = this;

    // this.startedRender = true;
    // on map markers
    let onMapMarkers = [];
    // console.log('+++render triggered')
    // this.renderTriggered()
    for (let nMarker of ['cMarker','hMarker','uMarker']){
      if (this.state[nMarker] && this.state[nMarker].coords){
        // console.log('+++render triggered',nMarker)
        let width = 26,height=26;
        let track = this.state.tracksViewChanges;
        if(nMarker == 'hMarker'){
          width = 67;
          height = 67;
          track = true;
        }
        onMapMarkers.push(<Marker
        tracksViewChanges={ track }
        coordinate={this.state[nMarker].coords}
        key={"marker_"+nMarker}
        style={{width,height}}
        // onPress={this.markerPressed(prop)}
        // key={this.state.cMarker.key}
        // pinColor={prop.isTopUp ? '#c5a620' : '#5c6972'}
        // icon={this.state.cMarker.icon}
        >
          {nMarker === 'hMarker' &&
            <AnimatedWave
              sizeOvan={26}
              numberlayer={1}
              // sizeWave={26}
              source={markerIcons[nMarker]}
              // onPress={() => alert("Hello")}
              colorOvan={'#e03131'}
              zoom={2.6}
          />}
          {nMarker !== 'hMarker' &&
            <Image
              resizeMode='stretch'
              style={{width,height}}
              source={markerIcons[nMarker]}//reddot.png
          />}
        </Marker>);
      }
    }
    if (this.state.onPressLoc){
      onMapMarkers.push(<Marker
        tracksViewChanges={ this.state.tracksViewChanges }
        key={'OnPressMarker'}
        coordinate={{longitude:this.state.onPressLoc.lng,latitude:this.state.onPressLoc.lat}}
        >
      </Marker>);
    }
    // top buttons
    let topButtons;
    let topButtons2;
    // let topButtons3;
    let topBarStyle = {
      backgroundColor:this.state.tintColor
    };
    let activeFeatureName = this.state.activeFeatureName;

    if (this.featureFocused){
      topButtons = (<View style={{flex:8,
        justifyContent: 'center',
        alignItems:'center',
        paddingTop:6,
        paddingRight:44,
        alignContent:'center',
        flexDirection: 'row',
        backgroundColor:'blue'}}>
        {this.featureFocused.renderButton()}
      </View>);
      topBarStyle = {};
    }else{
      topButtons = this.allFeatures.map( (f) => { return f.renderButton(); });
      topButtons2 = this.allFeatures.map( (f) => { return f.renderButton2(f.name); });
      // topButtons3 = this.allFeatures.map( (f) => { return f.renderButton3(f.name); });
    }

    // bottom model
    let bottomModel;
    if (this.featureFocused){
      // focused feature
      bottomModel = this.featureFocused.renderModal();
    }else if (this.state.popupMenu && this._onPressEvent){
      // map onPress Menu
      let bottomModelMenus = this.state.popupMenu.map((item)=>{
        return (<TouchableOpacity onPress={()=>{self.popupMenuSelected(item)}} key={"popupMenu"+item.fname}>
          <View style={styles.buttonStyle}>
            <Text style={{color:'white'}}>{item.menu}</Text>
          </View>
          </TouchableOpacity>);
      })

      const menuHeight = Platform.select({
        ios: 50 * (this.state.popupMenu.length + 1) + bottomBarHeight,
        android: 50 * PixelRatio.get() * (this.state.popupMenu.length + 1) + bottomBarHeight
      });

      const maxHeight = Dimensions.get('window').height * 0.7;
      const finalHeight = Math.min(menuHeight, maxHeight);

      bottomModel = (<BottomPane key={'bottomModel'} hideTitleBar={true} cbClose={()=>{this.cancelPopupMenu();}}>
          <View style={[styles.gradesWrapper,{
            paddingLeft:0, 
            maxHeight: maxHeight,
            height: finalHeight
          }]} key={"popupMenuCancel"}>
            {bottomModelMenus}
            <TouchableOpacity onPress={()=>{this.cancelPopupMenu()}} key={"popupMenuCancel"}>
              <View style={[styles.buttonStyle,{backgroundColor:'white'}]}>
                <Text>{l10n('Cancel')}</Text>
              </View>
            </TouchableOpacity>
          </View>
        </BottomPane>);
    }else {
      bottomModel = this.allFeatures.map( (f) => { return f.renderModal(); });
    }
    let fullScreenModal = this.allFeatures.map( (f) => { return f.renderFullScreenModal(); });

    // bottomBar
    let bottomBar,bottomNavHeight=50;
    if (this.featureFocused){
      bottomBar = this.featureFocused.renderBottomBar();
    }else{
      bottomBar = this._mainThenNormal('renderBottomBar');
    }
    if(!bottomBar){
      bottomNavHeight = 0
    }

    // header menu
    let headerMenu;
    if (this.featureFocused){
      headerMenu = this.featureFocused.renderHeaderMenu();
    }else{
      headerMenu = this._mainThenNormal('renderHeaderMenu');
    }
    // this feature menu, eg. school type/transit type
    let featurePanel;
    if (this.featureFocused){
      featurePanel = this.featureFocused.renderFeaturePanel();
    }else{
      featurePanel = this._mainThenNormal('renderFeaturePanel');
    }
    // on off menu
    // let onOffMenu = [];
    // this.allFeatures.forEach( f =>{
    //   if (onOff = f.onOffView()){
    //     let style = [styles.overlayButton],estyle=null;
    //     if (onOff.on){style.push(styles.overlayButtonOn);estyle=styles.overlayButtonOn}
    //     if (this.featureFocused){
    //       if(this.featureFocused == f){
    //         style.push(styles.overlayButtonFocus)
    //       }else{
    //         style.push(styles.overlayButtonNotFocus)
    //       }
    //     }
    //     onOffMenu.push(<View style={style} key={"onOffBtn"+onOff.name}>
    //       <TouchableOpacity onPress={()=> f.onOffView().callback()}>
    //           {onOff.image && <Image source={onOff.image} style={[styles.overlayButtonImage,estyle]} />}
    //           {onOff.icon && <Icon name={onOff.icon} size={30} color={onOff.on?'#000':'#999'} style={[styles.overlayButtonImage,estyle]} />}
    //           <Text style={[styles.overlayButtonText,estyle]}>{onOff.name}</Text>
    //       </TouchableOpacity>
    //       </View>);
    //   }
    // })
    let onOffMenuData = [];
    let activeLayerItem = {onOff:{icon:'layers-off',name:l10n('Property'),on:true}};
    let activeFeatureDisp = this.activeFeatureNameMapping[activeFeatureName];
    // console.log(activeFeatureName,activeFeatureDisp)
    this.allFeatures.forEach( feature =>{
      let onOff;
      if (onOff = feature.onOffView()){
        let style = [styles.overlayButton],estyle={};
        if (onOff.on){
          style.push(styles.overlayButtonOn);
          estyle = styles.overlayButtonOn;
          // console.log('+++',feature.name,onOff)
        }
        // if not showing props on map, got room for other display
        if(onOff.toggle && feature.name=='MapProps' && !onOff.on && activeFeatureDisp){
          // console.log('+++ house is off',feature.name,onOff,topButtons)
          topButtons.unshift(this.renderActiveFeatureName(activeFeatureDisp))
          let activeFeatureExtraDisplay = this.renderActiveFeatureDisplay(activeFeatureName)
          topButtons2.push(activeFeatureExtraDisplay)
          // console.log('+++ house is off',topButtons2)
        }
        if (this.featureFocused){
          if(this.featureFocused == feature){
            style.push(styles.overlayButtonFocus)
          }else{
            style.push(styles.overlayButtonNotFocus)
          }
        }
        let featIcon = {feature,style,estyle,onOff}
        if(feature.name !== 'MapDummyLayer'){
          onOffMenuData.push(featIcon);
        }
        // console.log(onOff)
        if (onOff.on){
          activeLayerItem = featIcon;
        }
      }
    });
    let additionalStyle = {};
    if(Platform.OS == 'ios'){
      additionalStyle.paddingLeft = 10;
    }
    var activeLayerIconName = activeLayerItem.onOff.icon;
    var activeLayerIconColor = activeLayerItem.onOff.on?'#007aff':'black';
    var activeLayerIconSize = activeLayerItem.onOff.iconSize||23;
    var activeLayerIconStyle = activeLayerItem.onOff.iconStyle||{};
    if(this.state.activeFeatureName == 'MapDummyLayer'){
      activeLayerIconName = this.state.prevActiveIcon; //#'rm-school'
      activeLayerIconSize = this.state.prevActiveSize;
      activeLayerIconStyle = this.state.prevActiveStyle||{};
      activeLayerIconColor = 'black'
    }
    let defaultProvider = PROVIDER_DEFAULT;
    //ios国内用户默认苹果地图
    if(Platform.OS !=='android' && (this.state.canAccessGoogle || !appConfigIns.getAppConfig('isCip'))) {
      defaultProvider = PROVIDER_GOOGLE;
    }
    // if(Platform.OS !=='android' && !appConfigIns.getAppConfig('isCip')) {
    //   defaultProvider = PROVIDER_GOOGLE;
    // }
    if(appConfigIns.getAppConfig('useDefaultMap')){
      defaultProvider = PROVIDER_DEFAULT;
    }
    let topPxValue = 350+getStatusBarHeight();
    if(this.state.isShowDrawPolygon){
      topPxValue = 400+getStatusBarHeight()
    }
    // console.log(activeLayerIconName,activeLayerIconSize,activeLayerIconStyle)
    // console.log('+++render on map',Date.now())
    // let bar = new RMStatusBar({tintColor:'xx'})
    return (
       // <Modal
       //    animationType="fade"
       //    transparent={true}
       //    visible={this.state.modalVisible}
       //    onRequestClose={() => {
       //      Alert.alert('Modal has been closed.');
       //    }}>
       //    <View style={{marginTop: 22}}>
       //      <View>
       //        <Text>Hello World!</Text>
       //
       //        <TouchableHighlight
       //          onPress={() => {
       //            this.setModalVisible(!this.state.modalVisible);
       //          }}>
       //          <Text>Hide Modal</Text>
       //        </TouchableHighlight>
       //      </View>
       //    </View>
       //  </Modal>
        <View style ={mapStyles.container}>
          {this.state.showBackdrop &&
            <TouchableOpacity key={'backdropView'} style={styles.backdrop} onPress={(e)=>{
              // console.log(e);
              // this.setState({showBackdrop:false})
              this.clearModals();
              }}>
              <View>
              </View>
            </TouchableOpacity>
          }
          <RMStatusBar tintColor={this.state.tintColor}/>
          {/* {bar} */}
          <View style={[styles.navBarButtonContainer,topBarStyle]} key={'headerBar'}>
            <View style={{flexDirection: 'row', height:44, flex:1, backgroundColor:'transparent'}} key={'btnBack'}>
              <Icon name="back" size={21} color="#FFF" style={[styles.navBarButton,additionalStyle]}   onPress={()=>{this.goBack()}} />
            </View>
          {topButtons}
          </View>
          <View key={'headerMenu'} style={{zIndex:17}}>
            {headerMenu}
          </View>
          {/* 2nd line of top buttons row */}
          {topButtons2 && !allAreNull(topButtons2) &&
            <View style={styles.navBarButtonContainer2} key='topButtons2'>
              {topButtons2}
            </View>
          }
          {/* {topButtons3 && !allAreNull(topButtons3) &&
            <View style={styles.navBarButtonContainer2} key='topButtons3'>
              {topButtons3}
            </View>
          } */}
          {/*
          <View style={styles.filterBar}>
            <TouchableOpacity style={styles.filterBarLink}>
              <Icon name="md-arrow-dropdown" size={16} color="#E03131" style={styles.filterBarButton}   onPress={this.bottomNavPressed('Residential')} />
              <Text>City</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.filterBarLink}>
              <Icon name="md-arrow-dropdown" size={16} color="#E03131" style={styles.filterBarButton}   onPress={this.bottomNavPressed('Residential')} />
              <Text>Style</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.filterBarLink}>
              <Icon name="md-arrow-dropdown" size={16} color="#E03131" style={styles.filterBarButton}   onPress={this.bottomNavPressed('Residential')} />
              <Text>Price</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.filterBarLink}>
              <Icon name="md-arrow-dropdown" size={16} color="#E03131" style={styles.filterBarButton}   onPress={this.bottomNavPressed('Residential')} />
              <Text>More</Text>
            </TouchableOpacity>
          </View>*/
          }
          {(!this.featureFocused) && this.allFeatures.map( (f) => {
            // console.log(f.name)
            return f.renderOverlay(f.name);
          })}
          {/* {!this.state.mapReady && <View style={{
            position:'absolute',
            // paddingLeft:15,
            // paddingRight:15,
            left:15,
            right:15,
            backgroundColor:'white',
            bottom:68,
            height:44,
            zIndex:20,
            borderRadius:10,
            flexDirection:'row',
            width: width-30,
            }}>
            <TouchableOpacity style={{flex:9}} onPress={()=>{this.switchToWebMap()}}>
              <Text style={{paddingLeft:5,fontSize:12,color:'#666',paddingTop:10}}>Oops, seems like map not loaded, try use web map?</Text>
            </TouchableOpacity>
            <View style={{flex:1,alignContent:'center'}}>
              <Icon name="closeX"
                size={17}
                color={'black'}
                onPress={()=>{this.setState({mapReady:true})}}
                style={{paddingRight:15,marginLeft:3,marginTop:11,flex:1}}
                // hitSlop={{top: 10, bottom: 10, left: 0, right: 120}}
              />
            </View>
          </View>} */}
          <ZoomControl
            key={'mapZoomControl'}
            mapTypeCallback={this.switchMapType.bind(this)}
            locateMeCallback={this.locateMe.bind(this)}
            zoomCallback={this.mapZoom.bind(this)}
            drawPolygonOnMapCallback={this.drawPoly.bind(this)}
            isShowDrawPolygon={this.state.isShowDrawPolygon}
            isDrawActive={this.state.isDrawActive}
            mapTypeId={this.state.mapTypeId}
          >
          </ZoomControl>

          {/* <View style={styles.overlayOnOffMenu} key={"overlayMenuBar"}>
            {onOffMenu}
          </View> */}
          {/* {<FlatList
            style={styles.overlayOnOffMenu}
            data={onOffMenuData}
            renderItem={({item})=> (
              <View style={item.style}>
              <TouchableOpacity onPress={()=> item.onOff.toggleOnOff()}>
                  {item.onOff.image && <Image source={item.onOff.image} style={[styles.overlayButtonImage,item.estyle]} />}
                  {item.onOff.icon && <Icon name={item.onOff.icon} size={30} color={item.onOff.on?'#000':'#999'} style={[styles.overlayButtonImage,item.estyle]} />}
                  <Text style={[styles.overlayButtonText,item.estyle]}>{item.onOff.name}</Text>
              </TouchableOpacity>
              </View>)}
            keyExtractor={item => "onOffBtn"+item.onOff.name}
            //extraData={menuSelected}
          />} */}
          <FlashMessage
            msg='test msg'
            key={'flashMessage'}
            height={height}
            >
          </FlashMessage>
          {/* TODO: ios 使用apple map时，mapType='hybrid'卫星图层不加载 */}
          <MapView
            key={'mapSearchMapView'}
            provider={defaultProvider}
            style={mapStyles.map}
            //cacheEnabled={true}
            showsScale={true}
            onMapReady={() => this.mapOnReady()}
            // showsUserLocation={true}
            // followUserLocation={true}
            zoomEnabled={true}
            showsCompass={true}
            // paddingAdjustmentBehavior={'always'}
            mapType={this.state.mapTypeId}
            ref={ref => { this.map = ref; }}
            initialRegion={initialRegion}
            mapPadding={this.state.mapPadding}
            // region={this.state.focusedLocation}
            // onRegionChange={region => this.onRegionChange(region)}
            moveOnMarkerPress={false}
            onRegionChangeComplete={(region, gesture) => this.onRegionChangeComplete(region)}
            onPress={(e)=>{this.mapOnPress(e.nativeEvent)}}
            onLongPress={(e)=>{this.onLongPress(e.nativeEvent)}}
          >
          {/* ios默认使用苹果地图，不再需要，并且会遮挡默认 图层卫星地图切换显示。在android上自定义图层还是会依赖谷歌地图，所以没有谷歌服务是用不了的。国内使用需要更换SDK */}
          {/* {this.state.useTileOverlay &&
            <UrlTile
              //The url template of the tile server. The patterns {x} {y} {z} will be replaced at runtime
              //For example, https://c.tile.openstreetmap.org/{z}/{x}/{y}.png NOTE: not accessible in China
              // zIndex={100}
              opacity={1}
              tileSize={512}
              urlTemplate={'https://mptl.realmaster.cn/vt/lyrs=m&x={x}&y={y}&z={z}'}
              maximumZ={this.state.maximumZ}
              flipY={false}
            />
          } */}
          {onMapMarkers}
          {this.allFeatures.map((f) => {
            return(f.renderOnMap(f.name));
          })}
          </MapView>
          <View style={[styles.bottomBar,{height:bottomNavHeight}]} key={'mapBottomBar'}>
            {bottomBar}
          </View>
          {fullScreenModal}
          {bottomModel}
          {featurePanel}
          { /*
            Platform.OS == 'ios' && this.state.mapTypeId == 'standard' &&
            <Text style={styles.tipText}>Use 2 fingers push screen up to view 3d map</Text>
            */
          }
        {/* <RMBottomBar /> */}
        </View>
      );
    }
  renderActiveFeatureDisplay = (activeFeatureName)=>{
    let activeFeature = this.allFeatures.find((f) => {
      // console.log(f)
      return f.name == activeFeatureName
    })
    // console.log(activeFeatureName,activeFeature)
    if(activeFeature && activeFeature.renderFeatureNoPropDisplay){
      return activeFeature.renderFeatureNoPropDisplay()
    }
  }
  renderActiveFeatureName = (activeFeatureDisp)=>{
    return (
      <View key={'activeFeatureDisp'}
        style={{flexDirection:'row',
        justifyContent:'center',
        marginTop:13,
        // backgroundColor:'blue',
        flex:10,
        marginLeft:10,
        marginRight:0}}>
        <Text style={{color:'white',fontSize:16,fontWeight:'bold'}}>{activeFeatureDisp}</Text>
      </View>
    )
  }
  closePopup = (opt) => {
    //  console.log('closePopp in mapsearch -> ',opt)
    this.data.closePopup(opt)
  }
  goBack = (opt) => {
    //  console.log('++++goBack',opt,this.thisProps.d)
    storageIns.setCacheItem(Constants.NearbyPropItem,null)
    storageIns.setCacheItem(Constants.CurrentSavedSearch,null)
    if (this.data && this.data.closePopup) {
      if(!opt && this.thisProps && this.thisProps.d){
        opt = this.thisProps.d
        if (/\/index|\/home/<USER>
          var char = '?';
          if(opt.indexOf('?')>1){
            char = '&';
          }
          // &appmode=${this.state.appmode}
          opt=`${opt}${char}rand=${Math.random()}`
        }
      }
       //  console.log('++++',opt)
       // TODO: set bar color to init color
       // eventEmitter.emit(SYSTEM.CHANGE_APP_MODE,{val:initialMode});
      this.featureMapProps.goBack()
      global.rmLog(`[RMMapSearchNative.jsx:1846~~~~~~~~~~goBack]`,opt);
      this.data.closePopup(opt)
    }
  }
  fitToCoordinates(coordinates,opt={}){
    let edgePadding = {
      top: 5,
      right: 49,//0.001,
      bottom: 5,
      left: 9
    }
    opt = Object.assign({ edgePadding: edgePadding, animated: true },opt);
    this.map.fitToCoordinates(coordinates,opt)
  }
  fitToBBox(bbox=[],opt={}){
    var self = this;
    if(!bbox.length){
      return
    }
    var delta = bbox[3]-bbox[1];
    var lat = (bbox[1]+bbox[3])/2;
    var lng = (bbox[0]+bbox[2])/2;
    // zoom = self.calcZoomLevel((delta)/2)
    var mapRegion = {
      latitude: lat,
      longitude: lng,
      latitudeDelta: delta,
      longitudeDelta: delta * ASPECT_RATIO,
    }
    if(self.map == null){
      console.error('self no map!',self)
    } else {
      self.map.animateToRegion(mapRegion, 1);
    }
  }
  //  async getMapBoundaries(){
  //    return await this.map.getMapBoundaries()
  //  }
  setCenterAndZoom(loc,opt={}){
    let {zoom,delta} = opt
    if(loc.lat == null || loc.lng == null){
      return;
    }
    var lat = loc.lat, lng = loc.lng, self = this;
    // console.log('zoom: '+zoom)
    var latitudeDelta;
    if (!delta && zoom) {
      latitudeDelta = this.calcDeltaFromZoom(zoom)
    } else {
      if(!delta){
        delta = 0.05
      }
      latitudeDelta = delta;
    }
    var region = {
      latitudeDelta: latitudeDelta,
      longitudeDelta: latitudeDelta*ASPECT_RATIO,
      latitude:  lat,
      longitude: lng,
    }
    // console.log('latitudeDelta: '+latitudeDelta,loc,zoom,region)
    if(loc.showMarker){
      var state = {tracksViewChanges:true};
      var name = 'hMarker';
      var delName = null;
      if(loc.cMarker){
        name = 'cMarker';
      } else if (loc.uMarker){
        name = 'uMarker';
      }
      if(name == 'hMarker'){
        delName = 'cMarker'
      } else if (name == 'cMarker'){
        delName = 'hMarker'
      }
      state[name] = {
        coords:{latitude:lat,longitude:lng}
      }
      state[delName] = null
      self.setState(state,()=>{
        // console.log(this.state.uMarker)
      })
      // setTimeout(() => {
      //   this.setState({tracksViewChanges:false})
      // }, 100);
      self.trackOff();
    }
    if(self.map == null){
      console.error('self no map!',self)
    } else {
      self.map.animateToRegion(region, 1);
    }
  }
   //add reddot indicate house position, zoom in
  moveToRecord(p){
    return;
    var self = this;
    var cMarker = {
      // position: p,
      lat:p.lat,
      lng:p.lng,
      key:p.lat+':'+p.lng,
      // clickable:false,
      // optimized: this.isIOS(),
      icon: getServerDomain()+'/img/reddot.png',
      // map: self.mapObj.gmap
    };
    // if (self.cMarker && self.cMarker.setMap) {
    //   self.cMarker.setMap(null);
    // }
    // self.cMarker = new google.maps.Marker(cMarker);
    self.setState(cMarker);
    self.setCenterAndZoom(p, 18);
  }
  async mapZoom(cmd){
    var bnds = await this.map.getMapBoundaries()
     // this.region = {
     //   latitude: this.state.focusedLocation.latitude,
     //   longitude: this.state.focusedLocation.longitude,
     //   latitudeDelta: this.state.focusedLocation.latitudeDelta * 10,
     //   longitudeDelta: this.state.focusedLocation.longitudeDelta * 10
     // }
     // this.setState({
     //   focusedLocation: {
     //     latitudeDelta: this.region.latitudeDelta,
     //     longitudeDelta: this.region.longitudeDelta,
     //     latitude: this.region.latitude,
     //     longitude: this.region.longitude
     //   }
     // })
     var ne = bnds.northEast;
     var sw = bnds.southWest;
     var lat = (ne.latitude+sw.latitude)/2;
     var lng = (ne.longitude+sw.longitude)/2;
     var latitudeDelta = Math.abs(ne.latitude-sw.latitude);
     if (cmd == 'In') {
       latitudeDelta = latitudeDelta/2;
     } else {
       latitudeDelta = latitudeDelta*2;
     }
     var region = {
       latitudeDelta: latitudeDelta,
       longitudeDelta: latitudeDelta*ASPECT_RATIO,
       latitude:  lat,
       longitude: lng,
     }
     this.map.animateToRegion(region, 200);
   }
  onRegionChange(region) {
    // this.setState({ region });
  }
  getMapRef(){
    return this.map;
  }
  //  getRandomFloat(min, max) {
  //    return (Math.random() * (max - min)) + min;
  //  }
  //  animateToRandomBearing() {
  //    this.map.animateToBearing(this.getRandomFloat(-360, 360));
  //  }
  //  animateToRandomViewingAngle() {
  //    this.map.animateToViewingAngle(this.getRandomFloat(0, 90));
  //  }
};
RMMapSearchNative.defaultProps = {
  mapTypeId: MAP_TYPES.STANDARD
};

export default RMMapSearchNative;
