import Share from "react-native-share";
import {Alert} from 'react-native';
// var sendSMS = (opt,cb) => {
//   if (!opt.successTypes){
//     opt.successTypes = ['sent', 'queued']
//   }
//   SendSMS.send(opt,(completed, cancelled, error)=>{
//     cb({ok:1,completed:completed,cancelled:cancelled,err:error});
//   }).catch((err)=>{
//     cb({err:err});
//   })
// }
var socialShare = (opts, cb) => {
  // Alert.alert('+++++opts=',JSON.stringify(opts))
  Share.open(opts).then((result) => {
    // Alert.alert('+++++rets',JSON.stringify(result))
    // result = {"app": "ph.telegra.Telegraph.Share", "message": "ph.telegra.Telegraph.Share"}
    result.ok = 1;
    // NOTE: react-native-share dont have cb cancel when share
    return cb(result);
    if (result.action === Share.sharedAction) {
      var ret = { ok: 1 };
      if (result.activityType) {
        ret.tp = result.activityType;
      }
      return cb(ret);
    } else if (result.action === Share.dismissedAction) {
      return cb({ cancelled: 1 })
    }
  }).catch((err) => {
    // Alert.alert('+++++err',JSON.stringify(err),typeof(err) == 'object' && Object.keys(err).length == 0)
    // NOTE: user cancelled
    if(typeof(err) == 'object' && Object.keys(err).length == 0){
      return cb({ cancelled: 1 })
    }
    return cb({ err: err });
  });
}
/*
const shareOptions = {
  title: 'Share via',
  message: 'some message',
  url: 'some share url',
  social: Share.Social.WHATSAPP, //FACEBOOK/WHATSAPP/INSTAGRAM/INSTAGRAM_STORIES/GOOGLEPLUS/EMAIL
  whatsAppNumber: "9199999999",  // country code + phone number
  filename: 'test' , // only for base64 file in Android
  type:
  subject:
  email:
  receipient:
  forceDialog:
};
const shareOptions = {
    method: Share.InstagramStories.SHARE_BACKGROUND_AND_STICKER_IMAGE,
    backgroundImage: 'http://urlto.png',
    stickerImage: 'data:image/png;base64,<imageInBase64>', //or you can use "data:" link
    backgroundBottomColor: '#fefefe',
    backgroundTopColor: '#906df4',
    attributionURL: 'http://deep-link-to-app', //in beta
    social: Share.Social.INSTAGRAM_STORIES
};
*/
// https://react-native-community.github.io/react-native-share/docs/share-single
function setUpSingleShareSocial(opts){
  let m = opts.social;
  let list = ['FACEBOOK','WHATSAPP','INSTAGRAM','INSTAGRAM_STORIES','GOOGLEPLUS','EMAIL'];
  if(list.indexOf(m)<0){
    return false;
  }
  opts.social = Share.Social[m];
  return true;
}
var singleSocialShare = (opts,cb) => {
  // console.log(opts)
  if(!setUpSingleShareSocial(opts)){
    return cb({ok:0, err:"Not supported social media"})
  };
  Share.shareSingle(opts).then((result) => {
    // console.log('+++++',result)
    // result = {"app": "ph.telegra.Telegraph.Share", "message": "ph.telegra.Telegraph.Share"}
    result.ok = 1;
    // NOTE: react-native-share dont have cb cancel when share
    return cb(result);
  }).catch((err) => {
    return cb({ err: err });
  });
}

export {singleSocialShare,socialShare}
