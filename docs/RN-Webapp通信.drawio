<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" version="24.7.7">
  <diagram name="第 1 页" id="y_ntC7JaiY9P4yuWTQuM">
    <mxGraphModel dx="1145" dy="823" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="9jzCtsSKM4YODfhAh2Pr-1" value="启动App" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="320" y="80" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="9jzCtsSKM4YODfhAh2Pr-7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="9jzCtsSKM4YODfhAh2Pr-2">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="540" y="230" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="9jzCtsSKM4YODfhAh2Pr-2" value="setSource" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="80" y="200" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="9jzCtsSKM4YODfhAh2Pr-3" value="Webview" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
          <mxGeometry x="100" y="150" width="70" height="30" as="geometry" />
        </mxCell>
        <mxCell id="9jzCtsSKM4YODfhAh2Pr-4" value="App" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
          <mxGeometry x="580" y="150" width="40" height="30" as="geometry" />
        </mxCell>
        <mxCell id="9jzCtsSKM4YODfhAh2Pr-9" value="initWebReady: false" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="560" y="200" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="9jzCtsSKM4YODfhAh2Pr-12" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="9jzCtsSKM4YODfhAh2Pr-10">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="550" y="350.3333333333335" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="9jzCtsSKM4YODfhAh2Pr-10" value="onMessage:&amp;nbsp;&lt;div&gt;&lt;br&gt;&lt;/div&gt;&lt;div&gt;storage.getItemObj&lt;div&gt;permissions.check.notification&lt;br&gt;&lt;/div&gt;&lt;div&gt;hasGoogleService&lt;br&gt;&lt;/div&gt;&lt;div&gt;getUniqueId&lt;br&gt;&lt;/div&gt;&lt;div&gt;wechat.has&lt;br&gt;&lt;/div&gt;&lt;/div&gt;&lt;div&gt;hasGoogleService&lt;br&gt;&lt;/div&gt;&lt;div&gt;storage.setItemObj&lt;br&gt;&lt;/div&gt;&lt;div&gt;refreshSystemValue&lt;br&gt;&lt;/div&gt;" style="rounded=0;html=1;whiteSpace=wrap;align=left;" vertex="1" parent="1">
          <mxGeometry x="80" y="320" width="190" height="150" as="geometry" />
        </mxCell>
        <mxCell id="9jzCtsSKM4YODfhAh2Pr-15" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="9jzCtsSKM4YODfhAh2Pr-11" target="9jzCtsSKM4YODfhAh2Pr-13">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="9jzCtsSKM4YODfhAh2Pr-11" value="onMessage:&amp;nbsp;&lt;div&gt;&lt;br&gt;&lt;/div&gt;&lt;div&gt;storage.getItemObj,&amp;nbsp;&lt;/div&gt;&lt;div&gt;permissions.check.notification&lt;br&gt;&lt;/div&gt;&lt;div&gt;hasGoogleService&lt;br&gt;&lt;/div&gt;&lt;div&gt;getUniqueId&lt;br&gt;&lt;/div&gt;&lt;div&gt;wechat.has&lt;br&gt;&lt;/div&gt;&lt;div&gt;hasGoogleService&lt;br&gt;&lt;/div&gt;&lt;div&gt;storage.setItemObj&lt;br&gt;&lt;/div&gt;&lt;div&gt;refreshSystemValue&lt;br&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=left;" vertex="1" parent="1">
          <mxGeometry x="560" y="320" width="200" height="150" as="geometry" />
        </mxCell>
        <mxCell id="9jzCtsSKM4YODfhAh2Pr-13" value="&lt;div style=&quot;background-color: rgb(31, 31, 31); line-height: 20px;&quot;&gt;&lt;font color=&quot;#cccccc&quot; face=&quot;Menlo, Monaco, Courier New, monospace&quot;&gt;&lt;span style=&quot;font-size: 13px; white-space: pre;&quot;&gt; postMessage ~ p, cbId, tp, rmweb: &quot;0&quot; 1726402385119 null undefined &lt;/span&gt;&lt;/font&gt;&lt;/div&gt;&lt;div style=&quot;background-color: rgb(31, 31, 31); line-height: 20px;&quot;&gt;&lt;font color=&quot;#cccccc&quot; face=&quot;Menlo, Monaco, Courier New, monospace&quot;&gt;&lt;span style=&quot;font-size: 13px; white-space: pre;&quot;&gt;//rmweb = currentRoute.current.rmweb&lt;/span&gt;&lt;/font&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=left;" vertex="1" parent="1">
          <mxGeometry x="350" y="490" width="610" height="60" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
