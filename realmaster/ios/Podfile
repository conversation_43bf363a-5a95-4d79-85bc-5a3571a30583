# Resolve react_native_pods.rb with node to allow for hoisting
#require Pod::Executable.execute_command('node', ['-p',
#  'require.resolve(
#    "react-native/scripts/react_native_pods.rb",
#    {paths: [process.argv[1]]},
#  )', __dir__]).strip
# https://github.com/zoontek/react-native-permissions
def node_require(script)
  # Resolve script with node to allow for hoisting
  require Pod::Executable.execute_command('node', ['-p',
    "require.resolve(
      '#{script}',
      {paths: [process.argv[1]]},
    )", __dir__]).strip
end

require_relative '../node_modules/react-native/scripts/react_native_pods'
require_relative '../node_modules/react-native-permissions/scripts/setup.rb'

platform :ios, '13.4'
prepare_react_native_project!

# ⬇️ uncomment wanted permissions (don't forget to remove the last comma)
setup_permissions([
  # 'AppTrackingTransparency',
  # 'BluetoothPeripheral',
  'Calendars',
  'Camera',
  # 'Contacts',
  # 'FaceID',
  'LocationAccuracy',
  # 'LocationAlways',
  'LocationWhenInUse',
  'MediaLibrary',
  # 'Microphone',
  # 'Motion',
  'Notifications',
  'PhotoLibrary'
  # 'PhotoLibraryAddOnly',
  # 'Reminders',
  # 'SpeechRecognition',
  # 'StoreKit'
])

# If you are using a `react-native-flipper` your iOS build will fail when `NO_FLIPPER=1` is set.
# because `react-native-flipper` depends on (FlipperKit,...) that will be excluded
#
# To fix this you can also exclude `react-native-flipper` using a `react-native.config.js`
# ```js
# module.exports = {
#   dependencies: {
#     ...(process.env.NO_FLIPPER ? { 'react-native-flipper': { platforms: { ios: null } } } : {}),
# ```
# Flipper was removed in React Native 0.74.0, Inside Podfile remove this line.
# flipper_config = ENV['NO_FLIPPER'] == "1" ? FlipperConfiguration.disabled : FlipperConfiguration.enabled

# linkage = ENV['USE_FRAMEWORKS']
# if linkage != nil
#   Pod::UI.puts "Configuring Pod with #{linkage}ally linked Frameworks".green
#   use_frameworks! :linkage => linkage.to_sym
# end

Pod::UI.puts "Configuring Pod with RN-FireBase-ally linked Frameworks".green
# https://rnfirebase.io/
use_frameworks! :linkage => :static
# $RNFirebaseAsStaticFramework = true

target 'RealMaster' do
  pod 'RNVectorIcons', :path => '../node_modules/react-native-vector-icons'
  # pod 'react-native-config/Extension', :path => '../node_modules/react-native-config'
  pod 'react-native-config', :path => '../node_modules/react-native-config'
  # <---start---> React Native Maps dependencies
  # The following line is only needed if building on an Apple silicon Mac without rosetta.
  # pod 'Google-Maps-iOS-Utils', :git => 'https://github.com/Simon-TechForm/google-maps-ios-utils.git', :branch => 'feat/support-apple-silicon'

  rn_maps_path = '../node_modules/react-native-maps'
  pod 'react-native-google-maps', :path => rn_maps_path
  pod 'GoogleMaps', '7.4.0' # <<<<---- I added the '5.1.0' version here
  # pod 'Google-Maps-iOS-Utils', '4.1.0' # <<<<---- I added the '3.10.3' version here

  pod 'WechatOpenSDK'
  # pod 'react-native-blob-util', :path => '../node_modules/react-native-blob-util'
  # <---end--->
  # pod 'Firebase', :modular_headers => true
  # pod 'FirebaseCore', :modular_headers => true
  # pod 'GoogleUtilities', :modular_headers => true
  # $RNFirebaseAsStaticFramework = true
  config = use_native_modules!

  # Flags change depending on the env values.

  # https://github.com/rodgomesc/vision-camera-code-scanner/issues/62
  flags = get_default_flags()
  # pre_install do |installer|
  #   installer.pod_targets.each do |pod|
  #     if pod.name.eql?('vision-camera-code-scanner') || pod.name.eql?('VisionCamera')
  #       def pod.build_type
  #         Pod::BuildType.static_library
  #       end
  #     end
  #   end
  # end
  use_react_native!(
    :path => config[:reactNativePath],
    # Hermes is now enabled by default. Disable by setting this flag to false.
    :hermes_enabled => flags[:hermes_enabled],
    :fabric_enabled => flags[:fabric_enabled],
    # 添加配置
    # Enables Flipper.
    #
    # Note that if you have use_frameworks! enabled, Flipper will not work and
    # you should disable the next line.
    # :flipper_configuration => flipper_config,
    # An absolute path to your application root.
    :app_path => "#{Pod::Config.instance.installation_root}/.."
  )

  target 'RealMasterTests' do
    inherit! :complete
    # Pods for testing
  end

  post_install do |installer|
    # TEMPORARY FIX UNTIL FIX IS RELEASED IN REACT NATIVE

    # https://github.com/facebook/react-native/blob/main/packages/react-native/scripts/react_native_pods.rb#L197-L202
    react_native_post_install(
      installer,
      config[:reactNativePath],
      :mac_catalyst_enabled => false
    )

    # 设置所有 pods 的最低部署目标
    installer.pods_project.targets.each do |target|
      target.build_configurations.each do |config|
        config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '13.4'
      end
    end

    #  添加这段，M1/M2 模拟器能跑起来，wechat 小而美！
    installer.pods_project.build_configurations.each do |config|
      config.build_settings["EXCLUDED_ARCHS[sdk=iphonesimulator*]"] = "arm64"
    end
  end
end
