plugins {
  // id "dev.flutter.flutter-plugin-loader" version "1.0.0"
  // id "com.android.application" version "7.3.0" apply false
  // id "org.jetbrains.kotlin:kotlin-gradle-plugin" version "1.9.0" apply false
  // id 'org.jetbrains.kotlin.android' version '1.9.10' apply false
}

rootProject.name = 'RealMaster'
apply from: file("../node_modules/@react-native-community/cli-platform-android/native_modules.gradle"); applyNativeModulesSettingsGradle(settings)
include ':app'
includeBuild('../node_modules/@react-native/gradle-plugin')

// include ':react-native-vector-icons'
// project(':react-native-vector-icons').projectDir = new File(rootProject.projectDir, '../node_modules/react-native-vector-icons/android')

include ':react-native-notifications'
project(':react-native-notifications').projectDir = new File(rootProject.projectDir, '../node_modules/react-native-notifications/lib/android/app')

