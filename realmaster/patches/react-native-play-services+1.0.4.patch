diff --git a/node_modules/react-native-play-services/android/build.gradle b/node_modules/react-native-play-services/android/build.gradle
index c3fce18..e5dd4ac 100644
--- a/node_modules/react-native-play-services/android/build.gradle
+++ b/node_modules/react-native-play-services/android/build.gradle
@@ -20,7 +20,7 @@ buildscript {
 }

 apply plugin: 'com.android.library'
-apply plugin: 'maven'
+apply plugin: 'maven-publish'

 // Matches values in recent template from React Native 0.59 / 0.60
 // https://github.com/facebook/react-native/blob/0.59-stable/template/android/build.gradle#L5-L9
@@ -100,17 +100,17 @@ afterEvaluate { project ->
     task androidJavadoc(type: Javadoc) {
         source = android.sourceSets.main.java.srcDirs
         classpath += files(android.bootClasspath)
-        classpath += files(project.getConfigurations().getByName('compile').asList())
+        // classpath += files(project.getConfigurations().getByName('compile').asList())
         include '**/*.java'
     }

     task androidJavadocJar(type: Jar, dependsOn: androidJavadoc) {
-        classifier = 'javadoc'
+        archiveClassifier = 'javadoc'
         from androidJavadoc.destinationDir
     }

     task androidSourcesJar(type: Jar) {
-        classifier = 'sources'
+        archiveClassifier = 'sources'
         from android.sourceSets.main.java.srcDirs
         include '**/*.java'
     }
@@ -129,10 +129,15 @@ afterEvaluate { project ->

     task installArchives(type: Upload) {
         configuration = configurations.archives
-        repositories.mavenDeployer {
-            // Deploy to react-native-event-bridge/maven, ready to publish to npm
+        // repositories.mavenDeployer {
+        //     // Deploy to react-native-event-bridge/maven, ready to publish to npm
+        //     repository url: "file://${projectDir}/../android/maven"
+        //     configureReactNativePom pom
+        // }
+        repositories{
+            mavenDeployer {
             repository url: "file://${projectDir}/../android/maven"
             configureReactNativePom pom
-        }
+        }}
     }
 }
