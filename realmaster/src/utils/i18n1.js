/**
 * 国际化
 */

import { mainRequest } from './request'
import DefaultTransStrings from '../locales'
import { syncGetItem, syncSetItem, syncRemoveItem  } from '../utils/storage'


const TransCacheKey = 'i18n'
const SupportLangs = ['zh', 'zh-cn', 'en', 'kr']
const NewDefaultTransStrings = {}
const RequestBaseurl = 'https://realmaster.com/1.5/translate';
const gAppLang = 'en';

/**
 * Generates index key and context object based on the input key.
 * @param {string} key - The key to generate index key and context for.
 * @returns {Object} - The generated index key and context object.
 */
const generateIndexKeyAndCtx = (key)  => {
	let ret = {};
	if (/:/.test(key)) {
		let arr = key.split(':');
		let keyStr = arr[0];
		let ctx = arr[1];
		ret = { k: keyStr, c: ctx, index: keyStr.toLowerCase() };
	} else {
		ret = { k: key, index: key.toLowerCase() };
	}
	return ret;
}

/**
 * Returns an object containing all the keys from DefaultTransStrings.
 * The keys are the lowercased version of the keys in DefaultTransStrings.
 * Each key in the returned object is an object with two properties:
 * - k: the key from DefaultTransStrings
 * - c (optional): the context from DefaultTransStrings
 * @returns {Object} An object containing all the keys from DefaultTransStrings.
 */
const getAllKeys = () => {
	// Initialize the return object
	let ret = {};
	// Iterate over all the keys in DefaultTransStrings
	for (let key in DefaultTransStrings) {
		// Generate the index key and context object for the current key
		let { index, k, c } = generateIndexKeyAndCtx(key);
		// Add the index key and k to the return object
		ret[index] = { k };
		// If context exists, add it to the return object
		if (c) {
			ret[index].c = c;
		}
	}
	// Return the object containing all the keys from DefaultTransStrings
	return ret;
}

/**
 * Initializes the NewDefaultTransStrings object with empty objects for each supported language.
 */
const initNewDefaultTransStrings = () => {
    // Iterate over each supported language
    for (let lang of SupportLangs) {
        // Create an empty object for the current language in the NewDefaultTransStrings object
        NewDefaultTransStrings[lang] = {};
    }

    // Iterate over each key in the DefaultTransStrings object
    for (let key in DefaultTransStrings) {
        // Check if the key exists in the DefaultTransStrings object
        if (DefaultTransStrings.hasOwnProperty(key)) {
            let v = DefaultTransStrings[key];
            // Iterate over each supported language
            for (let lang of SupportLangs) {
                // Check if there is a translation for the current key in the current language
                if (v[lang]) {
                    // Create an object with the key, lowercased key, and translation for the current key in the current language
                    DefaultTransStrings[lang][key.toLowerCase()] = {
                        k: key,
                        v: v[lang],
                    };
                }
            }
        }
    }
}

const isTranslated = (v) => {
	return !/^(\w|\.|\s|\?|\/|\,|\-|\!)+$/.test(v);
}

/**
 * Applies server-side translations to the default translations.
 *
 * @param {Object} transCache - The cache containing the server-side translations.
 */
const applyServerTransToDefault = (transCache = {}) => {
	// Check if the locale in the cache is supported
	if (SupportLangs.indexOf(transCache.locale) < 0) {
		return;
	}

	// Get the language from the cache and the corresponding language object from the default translations
	let lang = transCache.locale;
	let langTransObj = NewDefaultTransStrings[lang] || {};

	// If there are keys in the cache, update the language object with the server-side translations
	if (transCache.keys) {
		for (let key in transCache.keys) {
			let v = transCache.keys[key];
			if (langTransObj[key]) {
				// Check if the server-side translation is different from the existing translation
				if (v !== langTransObj[key].v) {
					// Check if the server-side translation is translated
					if (isTranslated(v)) {
						langTransObj[key].v = v;
					} else {
						// Ignore not translated case
						// console.log('v is not translated!',v,'<-->',langTransObj[key].v)
					}
				}
			}
		}
	}

	// If there are abkeys in the cache, do nothing
	if (transCache.abkeys) {
	}

	// Update the timestamps for the last translation and the last clear
	NewDefaultTransStrings.tlmt = transCache.tlmt;
	NewDefaultTransStrings.clmt = transCache.clmt;

    // Update the storage with the new default translations
    // storage update listener
	syncSetItem(
		TransCacheKey,
		JSON.stringify(NewDefaultTransStrings),
	);
}

/**
 * Applies the translations from the local storage to the default translations.
 *
 * @param {Object} transCache - The cache containing the local translations.
 */
const applyStorageTranslateToDefault = (transCache={}) => {
    // Initialize the NewDefaultTransStrings object with empty objects for each supported language.
    initNewDefaultTransStrings();

    // Iterate over each key in the transCache object.
    for (let key in transCache) {
        // Get the value for the current key.
        let v = transCache[key];

        // Check if the current key is a supported language.
        if (SupportLangs.indexOf(key) > -1) {
            // Create a new object with all the properties from the current language object in NewDefaultTransStrings and the properties from the current value.
            NewDefaultTransStrings[key] = Object.assign(
                {},
                NewDefaultTransStrings[key],
                v,
            );
        } else {
            // If the current key is not a supported language, use the current value as the new default translation.
            NewDefaultTransStrings[key] = v;
        }
    }
}

/**
 * Asynchronously fetches translations from the server.
 *
 * @param {Object} options - The options object containing tList and abList.
 */
const getTranslateFromServerAsync = async ({ tList, abList = {} }) => {
    // Prepare the data object with keys, abkeys, varsLang, tlmt, and clmt.
    const data = {
        keys: tList,
        abkeys: abList,
        varsLang: gAppLang,
        // tlmt: NewDefaultTransStrings.tlmt,
        // clmt: NewDefaultTransStrings.clmt,
        tlmt: "",
        clmt: "",
    };

    try {
        // Make a main request to the server with the data object.
        console.log('RequestBaseurl', data);
        let ret = await mainRequest({
            url: RequestBaseurl,
            method: 'post',
            data
        });

        // Check if the request was successful.
        if (!ret.ok) {
            return;
        }

        // If server requests clearing cache, remove the translation cache.
        if (ret.clearCache) {
            syncRemoveItem(TransCacheKey);
            // Initialize i18n without querying the server.
            initI18n({ noQueryServer: 1 });
        }

        // Update the gAppLang if not set.
        if (!gAppLang) {
            gAppLang = ret.locale;
        }

        // Apply server translations to the default set.
        applyServerTransToDefault(ret);
    } catch (e) {
        // Log an error if there is an issue fetching translations.
        console.log('Error when fetch l10n:', e);
    }
}


/**
 * Reads the translation cache from storage asynchronously.
 * If the cache exists, it is parsed and applied to the default translation set.
 */
const readTransFromStorageAsync = async () => {
    // Get the translation cache from storage
    let transCache = await syncGetItem(TransCacheKey);

    // If the cache exists
    if (transCache) {
        // Parse the cache from a JSON string to an object
        transCache = JSON.parse(transCache);

        // Apply the translation cache to the default translation set
        applyStorageTranslateToDefault(transCache);
    }
}


/**
 * Initializes the i18n system by reading the translation cache from storage,
 * querying the server for any new translations, and applying them to the default translations.
 * If the `noQueryServer` option is set to true, the server will not be queried.
 *
 * @param {Object} opt - An optional object containing the following properties:
 *                        `noQueryServer` (boolean): If true, the server will not be queried.
 */
const initI18n = async (opt={}) => {
    // Get all keys from the default translation set
    let tList = getAllKeys();

    // Read the translation cache from storage
    await readTransFromStorageAsync();

    // If the option to not query the server is set, return
    if (opt.noQueryServer) {
        return;
    }

    // Query the server for any new translations
    await getTranslateFromServerAsync({ tList });
}

/**
 * Finds the translation for a given string in the default translations.
 * If a context is given, it will first search for a translation with the given context.
 * If the given language is not given, it will default to the currently set language.
 * @param {string} str - The string to search for.
 * @param {string} [ctx] - The context of the string.
 * @param {string} [lang] - The language to search in.
 * @return {Object} - An object with the translated string and a boolean indicating if a translation was found.
 */
const findTrans = (str, ctx, lang) => {
	let ret = { str, ok: 0 };

	// If the given string is not a string, return the original object
	if (!str || typeof str !== 'string') {
		return ret;
	}

	// Convert the string to lower case
	str = str.toLowerCase();

	// If the language is not given, use the currently set language
	if (!lang) {
		lang = gAppLang || 'en';
	}

	// Get the object containing the translations for the given language
	let langTransObj = NewDefaultTransStrings[lang] || {};

	// If a context is given
	if (ctx) {
		// Convert the context to lower case
		ctx = ctx.toLowerCase();

		// Search for a translation with the given context
		let fstr = str + ':' + ctx;
		if (langTransObj[fstr] && langTransObj[fstr]['v']) {
			// If a translation is found, return it
			ret.str = langTransObj[fstr]['v'];
			ret.ok = 1;
			return ret;
		}
	}

	// If no translation with the given context is found, search for a translation without context
	if (langTransObj[str] && langTransObj[str]['v']) {
		// If a translation is found, return it
		ret.str = langTransObj[str]['v'];
		ret.ok = 1;
		return ret;
	}

	return ret;
}
function getStrIndex(key, ctx) {
	if (typeof key == 'string') {
		return key.toLowerCase() + (ctx ? ':' + ctx.toLowerCase() : '');
	} else {
		console.error(key, ' is not string');
		return null;
	}
}


const $t = (str, ctx, lang, _isAb) => {
	// return ''
	let ret = findTrans(str, ctx, lang);
	// append to toBe translated list
	if (!ret.ok) {
		let index = getStrIndex(str, ctx);

        //TODO: think about it
		// if (_isAb) {
		// 	gAbList[index] = { k: str, c: ctx };
		// } else {
		// 	gTransList[index] = { k: str, c: ctx };
		// }
		// clearTimeout(debounce);
		// debounce = setTimeout(function () {
		//   debounce = null;
		//   getTranslateFromServerAsync({tList:gTransList,abList:gAbList})
		// }, 1200);
	}
	return ret.str;
}

export  {
    initI18n
}
