import React, {Component} from 'react';
import {Platform, StyleSheet, Text, View,Dimensions,TouchableOpacity,TouchableHighlight} from 'react-native';
// import {gotoSch} from './helper'
import RmIcon from '../RmIcon'
// import HistRight from './HistRight';
// import {l10n} from '../../RML10n';
// import {getColorByName} from '../../RMColors';

const {width} = Dimensions.get('window');
export default class Geo extends Component{
  constructor(props) {
    super(props);
  }
  render(){
    const {georet} = this.props;
    return(
      <TouchableHighlight onPress={()=>{this.props.search('addr',georet)}}>
        <View style={styles.addr}>
          <RmIcon style={styles.icon} color={'rgb(130,130,130)'} name={'map-marker'} size={20}/>
          <View>
            <Text style={{fontSize:14,fontWeight:'bold',color:'rgb(80,80,80)'}}>{georet.addr}</Text>
            <Text style={{color:'rgb(177,177,177)'}}>{georet.city||''}{georet.city?', '+georet.prov:georet.prov}</Text>
          </View>
        </View>
      </TouchableHighlight>
    );
  }
}
const styles = StyleSheet.create({
  header:{
    padding:10,
    alignItems:'center',
    flexDirection:'row',
    justifyContent:'space-between'
  },
  addr:{
    padding:10,
    backgroundColor:'#fff',
    flexDirection:'row',
    alignItems:'center'
  },
  icon:{
    marginRight:10
  }
})
