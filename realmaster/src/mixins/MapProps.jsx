/*
*/
import React, { Component } from 'react';
import {
  StyleSheet,
  View,
  Text,
  Image,
  ScrollView,
  ActivityIndicator,
  TouchableOpacity,
  // TouchableHighlight,
  Dimensions,
  Alert,
  FlatList,
  Platform,
  Switch,
  Animated,
  PanResponder,
  PureComponent,
  Picker
} from 'react-native';
// import Slider from '@react-native-community/slider';
import MapFeature from './MapFeature';
import { Marker, Polygon, Polyline } from 'react-native-maps';
import { Icon, eventEmitter } from '../utils/common';
import { getBottomBarHeight, getStatusBarHeight } from '../components/RMStatusBar';
var bottomBarHeight = getBottomBarHeight()
var statusBarHeight = getStatusBarHeight()
const windowSize = Dimensions.get('window');
//  LOG  windowSize {"fontScale": 1, "height": 852, "scale": 3, "width": 393}
// console.log('windowSize',windowSize)
var gWindowWidth = windowSize.width
var gWindowHeight = windowSize.height
import { l10n } from '../utils/i18n';
import PriceMarker from '../components/PriceMarker';
import HouseMarker from '../components/HouseMarker';
import DotMarker from '../components/DotMarker';
import storageIns from '../utils/storage';
// import {getUserLang} from '../RMFiles';
// import {RMPost,getServerDomain,getServerProtocol} from '../RMNetwork';
import serverDomainIns from '../utils/serverDomain'
import { mainRequest } from '../utils/request';
import {
  serializeData, addKToSerializedData, parseSerializedFilter, urlParamToObject, getIconChar,
  getIconSuffix, convert_rm_imgs, listingPicUrls, listingPicUrlReplace, isValidPolygonBnds
} from './mapSearch';
import { propPrice, currency, dotdate } from './filters';
import ListMarker from '../components/ListMarker';
import { WebView } from 'react-native-webview';
import { gotoAutocomplete } from '../components/search/helper'
import { getWxIsInstalled } from '../utils/wechat';
import Constants from '../config/constants';
import appConfigIns from '../config/appConfig'
import { getColor, getColorStatic } from '../utils/RMstyle';
import cookiesIns from '../utils/cookies'
import ListDragPan from './mapListDragPanResponder'
import { getPropPicUrl } from '../mixins/mapSearch';
import LayerMenu from '../components/LayerMenu';
const gRedMarker = require('../assets/images/props/none-selected.png');
const gRedMarkerFade = require('../assets/images/props/none-selected-fade.png');
// import Swiper from 'react-native-swiper'
// import Carousel from 'react-native-snap-carousel';
// https://github.com/dohooo/react-native-reanimated-carousel


// import { getColorByName } from '../RMColors';
// import Prop from '../components/search/Prop'
// import Proj from '../components/search/Proj'
// import { get } from 'react-native/Libraries/TurboModule/TurboModuleRegistry';
// import { opacity } from 'react-native-reanimated/lib/typescript/reanimated2/Colors';
// import {Picker} from '@react-native-picker/picker';
// import RNPickerSelect from 'react-native-picker-select';

// TODO : https://github.com/react-native-maps/react-native-maps/blob/b49453ecf2c39b96884db2502c58b87867cab7df/example/examples/AnimatedViews.js
// TODO: use this PanController for vertical props list
// import PanController from './PanController';
// import CustomWebView from "react-native-webview-android-file-upload";


// import RMWeb from '../RMWeb';
// import IMAGE_URIS from '../img/props/*.png'
// NOTE: not works if inside for loop
//   IMAGE_URIS[img] = require('../img/props/'+img)
// }
// import schoolImg from '../img/school.png';
// import schoolGreen from '../img/schoolGreen.png';
// import schoolOrange from '../img/schoolOrange.png';
// import IconText from '../components/IconText';
// import BottomPane from '../components/BottomPane';


function isValidData(d) {
  if ((d === '') || (d == null)) {
    return false;
  }
  if (Array.isArray(d) && d.length == 0) {
    return false;
  }
  return true;
}
function getBthrms(prop) {
  let bthrmStr = prop.rmbthrm || prop.tbthrms || prop.bthrms;
  if (bthrmStr != null && bthrmStr != '') {
    return bthrmStr + l10n('ba') + ',';
  }
  return ''
}
function getBdrms(prop) {
  let ret = ''
  if (prop.rmbdrm != null && prop.rmbdrm != '') {
    ret = prop.rmbdrm + ''
  } else if (prop.bdrms != null) {
    ret = prop.bdrms + (this.state.curProp.br_plus ? '+' + this.state.curProp.br_plus + '' : '');
  }
  if (ret != null && ret != '') {
    return ret + l10n('bd') + ','
  }
  return ret
}
const IMAGE_URIS = {
  create_exlisting: require('../assets/images/props/create_exlisting.png'),
  create_direct: require('../assets/images/props/create_direct.png'),
  share_more1: require('../assets/images/props/share_more1.png'),
  create_project: require('../assets/images/props/create_project.png'),
  create_exclusive: require('../assets/images/props/create_exclusive.png'),
  create_index_assignment: require('../assets/images/props/create_index_assignment.png'),
  create_commercial: require('../assets/images/props/create_commercial.png'),
  create_openhouse: require('../assets/images/props/create_openhouse.png'),
  create_sold: require('../assets/images/props/create_sold.png'),
}
const PROP_LIST_SEARCH_MODES = {
  'Sold': { k: 'dom', v: -90, displayVal: 'Off Market 3 months', saleDesc: 'Sold' },
  'Leased': { k: 'dom', v: -90, displayVal: 'Off Market 3 months', saletp: 'lease', saleDesc: 'Leased' },
  'Open House': { k: 'oh', v: true },
  'Residential': {
    src: 'mls',
    ptype: 'Residential',
    ptype2: [],
    functions: [
      { nm: 'ptypeSelect', params: ['Residential', 'Residential'] },
    ]
  },
  'Commercial': {
    src: 'mls',
    ptype: 'Commercial',
    functions: [
      { nm: 'ptypeSelect', params: ['Commercial', 'Commercial'] },
    ]
  },
  'Other': {
    src: 'mls',
    ptype: 'Other',
    functions: [
      { nm: 'ptypeSelect', params: ['Other', 'Other'] },
    ]
  },
  'Assignment': { src: 'rm', ltp: 'assignment', ptype: 'Assignment', ptype2: [], dom: '', saleDesc: 'Sale' },
  'Exclusive': {
    src: 'rm',
    ptype: 'Exclusive',
    cmstn: true,
    ptype2: [],
    dom: '',
    // saleDesc:'Sale',
    saletps: {
      sale: {
        ltp: 'exlisting'
      },
      lease: {
        ltp: 'rent',
        cmstn: true
      }
    }
  },
  'Landlord': { dom: '', src: 'rm', ltp: 'rent', ptype: 'Landlord', saletp: 'lease', ptype2: [], saleDesc: 'Rent' },
  'PreCons': {
    src: 'rm', ltp: 'projects',
    // type:'projects',
    oh: false,
    ptype: 'Project'
  },
  'Near MTR': { k: 'neartype', v: 'mtr' },
  'Price Off': { k: 'lpChg', v: 'off' },
  'Best School': { k: 'sch', v: 'best' },
  'Sold Fast': { k: 'sold', v: 'fast', saleDesc: 'Sold' },
  'Today': { k: 'dom', v: '0', displayVal: 'Today' }
}

function getCenterFromBbox(arr = []) {
  let lat = (arr[1] + arr[3]) / 2
  let lng = (arr[0] + arr[2]) / 2
  return { lat, lng }
}

// https://react.dev/reference/react/PureComponent
// TODO: use PureComponent
// class PropPreview extends PureComponent {
//   render() {
//     let prop = this.props.prop;
//     return <h1>Hello, {this.props.name}!</h1>;
//   }
// }

class MapProps extends MapFeature {
  constructor(map, props, onOff, opt = {}) {
    let {
      setShowDrawPolygon,
      setDrawActive,
      ASPECT_RATIO
    } = opt;
    super(map, "MapProps", props, onOff);

    this.isInitFiltersReady = false;

    this.viewMode = 'map';//list
    this.appMapMode = 'map';//fav
    // this.$parent = null;
    this.strings = {
      NEED_LOGIN: 'Need Login',
      NO_RESULT: 'No Result',
      TIME_OUT: 'Timeout',
      NOT_FOUND: 'Not found',
    },
      // TODO: use zustand or redux
      this.setShowDrawPolygon = (a) => {
        // console.log('+++++setshowdraw',a);
        setShowDrawPolygon(a)
      }//.bind(this)
    this.setDrawActive = (a) => {
      // console.log('+++++setDrawActive',a);
      this.setState({ isDrawActive: a })
      setDrawActive(a)
    }//.bind(this)
    this.set
    this.lastBbox = null,
      this.datas = [
        // 'isProdSales',
        // 'isProjAdmin',
        // 'isPaytop',
        'isCip',
        // 'isVipUser',
        // 'isVipRealtor',
        // 'isRealtor',
        // 'isVisitor',
        'lang',
        // 'allowedShareSignProp',
        'propPtypes',
        'ptpTypes',
        'propSortMethods',//need this
        'projSortMethods',
        // 'isApp',
        'isLoggedIn',
        'shareUID',
        // 'allowedPromoteProp',
        // 'hasFollowedRealtor',
        // 'propCounts',
        // 'reqHost',
        'shareAvt',
        'sessionUser',
        'domFilterVals',
        'domFilterValsShort',
        'domYearFilterVals',
        'propFeatureTags',
        // 'bsmtFilterVals',
        // 'allowedEditGrpName',
        // 'shareLinks',
        // 'autocomplete',
        // 'placeApi',
        // 'coreVer',
        'userCity',
        'hotCommercialPtypes',
        'dualHomepage',
        // showSoldPriceBtn
      ],
      this.domSelectRef = null,
      this.thisProps = Object.assign({}, this.props),
      this.previewCache = {},
      this.state = {
        // hlProp:props.hlProp||null,
        // hlPropKey:props.hlPropKey||null,
        mapProps: {},
        mapSoldProps: {},
        items: [],
        soldItems: [],
        quickFilter: { ptype2: [], dom: '', soldOnly: true },
        checkedList: {},
        dispVar: {},
        showQuickFilter: false,
        propTmpFilter: this.getPropDefaultFilter(),
        propFeatureTags: [],
        showMapSearchTip: true,
        propSoldFilter: {
          src: 'mls',
          saletp: 'sale',
          saleDesc: 'Sold',
          ptype: 'Residential',
          ptype2: ['House', 'Detached', 'Semi-Detached', 'Townhouse'],
          bdrms: '',
          bthrms: '',
          gr: '',
          min_lp: '',
          max_lp: '',
          no_mfee: false,
          max_mfee: '',
          sort: 'auto-ts',
          dom: '0',
          type: 'soldLayer',
          domYear: '',
          bsmt: [],
          ptp: '',
          pstyl: '',
          ltp: '',
          oh: false,
          soldOnly: true,
          lpChg: '',
          neartype: '',
          sch: '',
          yr_f: '',
          yr_t: '',
          sq_f: '',
          sq_t: '',
          frontFt_f: '',
          frontFt_t: '',
          depth_f: '',
          depth_t: '',
          isPOS: '',
          addr: '',
          remark: '',
          psn: '',
          min_poss_date: '',
          max_poss_date: '',
          isEstate: '',
          sold: '',//fast
          remarks: '',
          rltr: '',
          exposures: [],
          soldLoss: '',
        },
        propTmpFilterVals: {
          src: 'DDF/MLS',
          saletp: l10n('For Sale'),
          city: '',
          prov: '',
          cmty: '',
          ptype: l10n('Residential'),
          ptype2: [],
          bdrms: '',
          bthrms: '',
          gr: '',
          min_lp: null,
          max_lp: null,
          no_mfee: false,
          max_mfee: null,
          sort: l10n('Auto'),
          dom: '',
          domYear: '',
          bsmt: [],
          ptp: '',
          pstyl: '',
          oh: false,
          soldOnly: true,
        },
        pgNum: 0,
        curProp: {},
        cntRMprop: 0,
        cntTotal: 0,
        propHalfDetail: false,
        tracksViewChanges: false,
        searchModeSaletp: 'sale',
        // searchModes:[],
        mapSearchTipCount: 3,
        usePropImg: true,
        showMapMarkers: true,
        regionChangeSearch: false,
        selectedStop: null,
        ptype2s: [],
        pan: new Animated.ValueXY(),
        bndsDragHistory: [],
        isDragging: false,
        polygonPoints: [],
        isDrawActive: false,
        isNewSearch: false,
        renderItems: [],
        hideDummyPropPreview: true,
        isFromSoldLayer: false,
        cmtyBnds: null,
        showCmtyBnds: false,
        currentPropertyId: null,
        selectedMarkerId: null, // 用于永久保存选中的marker ID
      }//end of state
    // this.saleModeList=[
    //   {k:'Residential',img:'create_exlisting'},
    //   {k:'Sold',      img:'create_sold'},
    //   {k:'Open House',img:'create_openhouse'},
    //   {k:'Commercial',img:'create_commercial'},
    //   {k:'Assignment',img:'create_index_assignment'},
    //   {k:'Exclusive', img:'create_exclusive',ctx:'realtor sale'},
    //   {k:'Projects',  img:'create_project',ctx:'prop'},
    //   {k:'Other',     img:'share_more1'}
    // ],
    // this.leaseModeList=[
    //   {k:'Residential',    img:'create_exlisting'},
    //   {k:'Leased',         img:'create_sold'},
    //   {k:'Commercial',     img:'create_commercial'},
    //   {k:'Other',          img:'share_more1'},
    //   {k:'Landlord Direct',img:'create_direct'},
    //   {k:'Exclusive',      img:'create_exclusive',ctx:'lease exclusive'},
    // ],
    // this.titleString = '',
    this.curSearchMode = { k: 'Residential' };
    this.doSearchTimeout = null;
    this.showListView = this.showListView.bind(this);
    // if (this.props.grp != null) {
    //   this.grp = parseInt(this.props.grp) || 0;
    //   this.viewMode = 'list';
    //   this.appMapMode =  'fav';
    //   this.getFavs();
    // }
    // if(this.props.gps){
    //   setTimeout(() => {
    //     console.log('gps',this.props.gps)
    //     this.map.locateMe()
    //   }, 200);
    // }
    this.componentWillMount()
    this.componentDidMount()
    eventEmitter.on("map.setFilterVal", this.setFilterVal.bind(this))
    eventEmitter.on("map.showListView", this.showListView)
    eventEmitter.on("map.searchSchoolProp", this.searchSchoolPropStrParam)
    eventEmitter.on('map.props.city', this.setProjectCity);
    eventEmitter.on(Constants.MapProp, this.searchWithinShape);
    // eventEmitter.on(SYSTEM.MAP_FEATURE_PROP_STOP,this.setSelectedStop);
    // eventEmitter.on(SYSTEM.EVENT_CLEAR_FEATURE_PANEL,this.hideSoldDomSelect.bind(this));
    eventEmitter.on(Constants.MapPropPreview, this.hideProvPreview.bind(this));
    // const [selectedLanguage, setSelectedLanguage] = useState();
    this.cmtyBndsController = null; // 用于存储当前的AbortController
  }
  getQuickFilterPtypes() {
    let all = [{ k: 'all', v: l10n('All', 'ptype') }]
    // console.log(this.state.ptype2s)
    if (this.state.propTmpFilter.ptype == 'Commercial') {
      return all.concat(this.state.dispVar.hotCommercialPtypes || [])
    }
    return all.concat(this.state.ptype2s || [])
  }
  componentWillMount() {
    if (this.cmtyBndsController) {
      this.cmtyBndsController.abort();
    }
    // Add a listener for the delta value change
    // let nearbyProp = {
    //   addr: "123 Pemberton Ave",
    //   amount: "D3.2M",
    //   bdrms: 5,
    //   best_fir: 8.6,
    //   bltYr1: 2007,
    //   bltYr2: 2016,
    //   br_plus: 4,
    //   bsmt: ['Finished', 'Walk-Up'],
    //   bthrms: 7,
    //   canEditOhs: true,
    //   city: "Toronto",
    //   city_en: "Toronto",
    //   cmstn: "",
    //   cmty: "Newtonbrook East",
    //   cmty_en: "Newtonbrook East",
    //   daddr: "Y",
    //   dom: 75,
    //   ele_fir: 3.2,
    //   fav: false,
    //   favGrp: [],
    //   favcnt: 0,
    //   formatedMt: "2022.4.28",
    //   geoq: 105,
    //   gr: 2,
    //   hgh_fir: 8.6,
    //   id: "TRB********",
    //   img: "https://img.realmaster.com/trb/828.1648700591/1/326/********.jpg",
    //   isTRBProp: true,
    //   lat: 43.7823614,
    //   ld: 20220331,
    //   lng: -79.4069522,
    //   lp: 3188000,
    //   lst: "Pc",
    //   lstStr: "Price Change",
    //   ltp: "MLS",
    //   mfee: 0,
    //   origProv: "Ontario",
    //   pho: 30,
    //   picUrls: ['https://img.realmaster.com/trb/828.1648700591/1/326/********.jpg'],
    //   pricePerSqft: "637-910",
    //   priceValStrRed: "$3,188,000",
    //   prov: "ON",
    //   prov_en: "ON",
    //   pstyl: "2-Storey",
    //   pstyl_en: "2-Storey",
    //   ptp: "Detached",
    //   ptp_en: "Detached",
    //   ptype: "r",
    //   ptype2: ['House', 'Detached'],
    //   ptype2_en: ['House', 'Detached'],
    //   ptype_en: "r",
    //   rltr: "RE/MAX ATRIUM HOME REALTY, BROKERAGE",
    //   rmLot: "600-900(692)ft²",
    //   rmbdrm: "5+4",
    //   rmbthrm: 7,
    //   rmgr: "2(6)",
    //   saleOrRent: "for Sale",
    //   saleTpTag: "Sale",
    //   saletp: ['Sale'],
    //   saletp_en: ['Sale'],
    //   searchAddr: "123 pemberton ave",
    //   showAddr: "123 Pemberton Ave",
    //   showRealtorLogo: false,
    //   showSoldPrice: true,
    //   showing: true,
    //   showingText: "(Off: 2022-01-02 21:00)",
    //   sid: "********",
    //   spcts: "2022-04-29T01:41:36.922Z",
    //   sqft1: 3500,
    //   sqft2: 5000,
    //   src: "TRB",
    //   status: "A",
    //   status_en: "A",
    //   stp: "Sale",
    //   stp_en: "Sale",
    //   stplabel: "Sale",
    //   tax: 10763.32,
    //   taxyr: 2022,
    //   tbdrms: 9,
    //   tgr: 6,
    //   thumbUrl: "https://img.realmaster.com/trb/828.1648700591/1/326/********.jpg",
    //   trademarkDesc: "The listing data is provided under copyright by the Toronto Real Estate Board. The listing data is deemed reliable but is not guaranteed accurate by the Toronto Real Estate Board nor RealMaster.",
    //   uaddr: "CA:ON:TORONTO:123 PEMBERTON AVE",
    //   zip: "M2M1Y6",
    // }
    // RMStorage.setCacheItem(SYSTEM.NEARBY_PROP_ITEM,nearbyProp);
    // this.state.pan.addListener((value) => {
    //   value.y = Math.abs(value.y)
    //   if(value.y > 60){
    //     // value.y = 60
    //   }
    //   this._val = value
    //   // this.state.pan.setValue({ x:0, y:value.y})
    //   console.log('pan listened',this._val.y)
    // });
    // Initialize PanResponder with move handling
    // const maxY = 100;
    // const constrainedY = this.state.pan.y.interpolate({
    //   inputRange: [0, maxY],
    //   outputRange: [0, maxY],
    //   extrapolateRight: 'clamp'
    // })
    // adjusting delta value
    // this.state.pan.setValue({ x:0, y:0})
  }
  async componentDidMount() {
    let ret = await this.getPageData();
    // console.log('!!!!!',ret)
    this.mountTime = Date.now()
    // console.log('+++++readFilter',this.thisProps.readFilter)
    // NOTE: appmode in ['mls','rm']
    var appmode = await cookiesIns.getCookieValueByKey(null, 'appmode') || 'mls';
    // console.log('++++++thisProps',this.thisProps)
    this.initialAppmode = appmode;
    this.initialBarColor = this.thisProps.prevbarcolor;
    // NOTE: dualHomepage = true/false, whether dual homepage is onlined
    var dualHomepage = appConfigIns.getAppConfig('dualHomepage') || false;
    if (this.thisProps.appmode) {
      appmode = this.thisProps.appmode;
    }
    // NOTE: no sold mode when rm
    if (this.thisProps.mapmode == 'sold') {
      appmode = 'mls'
    }
    let state = {
      hasMadeChanges: false,
      appmode,
      dualHomepage,
      highlightTextColor: await getColor('highlightText', appmode),
      highlightBgColor: await getColor('highlightBg', appmode)
    }
    let nearbyOrigProp = storageIns.getCacheItem(Constants.NearbyPropItem);
    if (nearbyOrigProp) {
      state.nearbyOrigProp = nearbyOrigProp
    }
    let savedSearchCond = storageIns.getCacheItem(Constants.CurrentSavedSearch);
    if (savedSearchCond) {
      // test value for bnds
      // savedSearchCond.bnds = [
      //   43.6386266,
      //   -79.5341865,
      //   43.6225363,
      //   -79.465522,
      //   43.596497,
      //   -79.4987384,
      //   43.6276932,
      //   -79.5468036,
      //   43.6386266,
      //   -79.5341865
      // ]
      // console.log('+++++savedSearchCond',savedSearchCond)
      if (savedSearchCond.k && savedSearchCond.k.bnds) {
        savedSearchCond.bnds = savedSearchCond.k.bnds
        let polygonPoints = this.getPolygonPoints(savedSearchCond.bnds)
        state.polygonPoints = polygonPoints
        state.isNewSearch = false
        this.setDrawActive(true)
      }
      state.savedSearchCond = savedSearchCond
      if (this.featureOn) {
        this.setShowDrawPolygon(true)
      }
    }
    await this.setStateAsync(state)
    eventEmitter.emit(Constants.ChangeAppMode, { val: appmode, mapSwitch: true });
    // console.log('++++++appmode=',this.state.appmode,' dualHomepage=',
    // this.state.dualHomepage,'\n state= ',state, '\n thisProps= ',this.thisProps)
    // console.log('++++++readFilter',this.thisProps.readFilter)
    if (this.thisProps.readFilter) {
      // init filter after read localstorage, other wise overwrite
      await this.readFilterFromStorage({ initFilter: 1, doSearch: 1 })
    } else {
      global.rmLog(`MapProps.jsx:610~~~componentDidMount`);
      await this.initFiltersFromProps({ canSearch: true })
    }
    // if (this.props.city) {
    //   this.propTmpFilter.city = this.props.city;
    //   this.propTmpFilterVals.city = this.props.cityName || this.props.city;
    //   if (this.props.prov) {
    //     this.propTmpFilter.prov = this.props.prov;
    //     this.propTmpFilterVals.prov = this.props.provName || this.props.prov;
    //   }
    //   if (this.props.cmty) {
    //     this.propTmpFilter.cmty = this.props.cmty;
    //     this.propTmpFilterVals.cmty = this.props.cmtyName || this.props.cmty;
    //   }
    // }
    // this.getPageData.bind(this);
    // this.propTmpFilter = self.getPropDefaultFilter();

  }
  hideProvPreview(opt = {}) {
    // 同时清除房源卡片和社区边界
    this.clearPropertyAndCommunity();
  }
  setShowPanel(opt) {
    this.hideSoldDomSelect(opt);
    // 只有当事件来源不是 mapSearch 时才清除社区边界
    if (!opt || opt.src !== 'mapSearch') {
      this.clearPropertyAndCommunity();
    }
  }
  // case1: on event hide domselect, eg. clicked backdrop
  // case2: user click close, also trigger event
  // BUG: trigger 9 times
  hideSoldDomSelect(opt = {}) {
    // console.log('+++++hideSoldDomSelect triggered',opt)
    let state = {}
    if (opt.src !== 'mapSearch') {
      state.showQuickFilter = false;
    }
    this.setState(state)
    if (opt.hideBackdrop) {
      eventEmitter.emit('map.clearModals', { src: 'mapSearch', backdrop: false })
    }
  }
  setProjectCity = (c) => {
    // var self = this;
    if (c) {
      let state = {
        propTmpFilter: {
          ...this.state.propTmpFilter,
        },
        propTmpFilterVals: {
          ...this.state.propTmpFilterVals,
        }
      };
      // console.log('xxxxxsetProjectCity',state)
      state.propTmpFilter.city = c.o;
      state.propTmpFilter.prov = c.prov;
      state.propTmpFilterVals.city = c.n;
      state.propTmpFilterVals.prov = c.p;
      if (this.viewMode == 'map') {
        for (let i of ['city', 'prov', 'cmty']) {
          state.propTmpFilter[i] = '';
          state.propTmpFilterVals[i] = '';
        }
      }
      this.setState(state, () => {
        this.recenterMapForCities(c, 14);
        // console.log('setprojectcity dosearch8')
        this.doSearch({ clear: true });
      })
    }
  }
  recenterMapForCities(city = {}, zoom = 10) {
    // console.log('++++++recenterMapForCities',city)
    if (city.lat == null || city.lng == null) {
      return;
    }
    var self = this;
    // self.clearCache();
    // self.mapObj.recenterWithZoom(city, zoom);
    self.map.setCenterAndZoom(city, { zoom });
    // if (!city.bbox || city.bbox.length != 4) {
    //   return;
    // }
    // var box = city.bbox;
    // var items = [
    //   {lat:box[1],lng:box[0]},
    //   {lat:box[3],lng:box[2]}
    // ];
    // this.mapObj.recenter(items);
  }
  isMainFeature() { return true; }
  async initFiltersFromProps(opt = {}) {

    global.rmLog(`MapProps.jsx:700~~~initFiltersFromProps`);

    // console.log('@@@@@@',this.props)
    if (this.state.dualHomepage && !this.props.mapmode && this.state.appmode == 'rm') {
      this.props.mapmode = 'assignment'
    }
    if (this.props.saletp == 'lease') {
      await this.setSaleTp('lease');
      if (this.state.dualHomepage && !this.props.mapmode && this.state.appmode == 'rm') {
        this.props.mapmode = 'rent'
      }
    } else {
      await this.setSaleTp('sale');
    }
    let state = {
      propTmpFilter: {
        ...this.state.propTmpFilter,
      },
      propTmpFilterVals: {
        ...this.state.propTmpFilterVals,
      }
    };
    // if (this.props.dom != null) {
    //   this.selectSoldFilterVal({k:'-90',v:'3 month'});
    //   // this.state.propTmpFilter.dom = parseInt(this.props.dom) || 0;
    // }
    if (this.props.oh != null) {
      state.propTmpFilter.oh = true;
      await this.setStateAsync(state);
    }
    // if (this.props.mode == 'list'){
    //   this.viewMode = 'list';
    // }
    // watch will init after this, need timeout, watch propTmpFilter.ptype will undo changes

    if (this.props.mapmode == 'assignment') {
      // setTimeout(function () {
      // console.log('+++++setSearchMode assignment')
      await this.setSearchMode({ k: 'Assignment', noSearch: true });
      // }, 100);
    } else if (this.props.mapmode == 'commercial') {
      await this.setSearchMode({ k: 'Commercial', noSearch: true });
    } else if (this.props.mapmode == 'exlisting') {
      this.setSearchMode({ k: 'Exclusive', noSearch: true });
    } else if (this.props.mapmode == 'exrent') {
      state.searchModeSaletp == 'lease'
      state.propTmpFilter.saletp = 'lease';
      await this.setStateAsync(state);
      await this.setSearchMode({ k: 'Exclusive', noSearch: true });
    } else if (this.props.mapmode == 'rent') {
      await this.setSearchMode({ k: 'Landlord', noSearch: true });
    } else if (this.props.mapmode == 'projects') {
      if (this.props.appMapMode == 'fav') {
        this.getFavProj();
        this.appMapMode = 'fav';
        this.viewMode = 'list';
        if (this.props.mode == 'map') {
          this.viewMode = 'map';
        }
      } else {
        await this.setSearchMode({ k: 'PreCons', noSearch: true });
      }
    } else if (this.props.mapmode == 'sold') {
      // setTimeout(function () {
      await this.setSearchMode({ k: 'Sold', noSearch: true });
      // }, 100);
    }
    // if (this.props.city) {
    //   this.propTmpFilter.city = decodeURIComponent(this.props.city);
    //   this.propTmpFilterVals.city = decodeURIComponent(this.props.cityName || this.props.city);
    //   if (this.props.prov) {
    //     this.propTmpFilter.prov = decodeURIComponent(this.props.prov);
    //     this.propTmpFilterVals.prov = decodeURIComponent(this.props.provName || this.props.prov);
    //   }
    //   if (this.props.cmty) {
    //     this.propTmpFilter.cmty = decodeURIComponent(this.props.cmty);
    //     this.propTmpFilterVals.cmty = decodeURIComponent(this.props.cmtyName || this.props.cmty);
    //   }
    //   // self.getCmtyList({o:this.props.city,p:this.props.prov});
    // }
    state = {
      propTmpFilter: {
        ...this.state.propTmpFilter,
      },
      propTmpFilterVals: {
        ...this.state.propTmpFilterVals,
      }
    };
    // var d = urlParamToObject(val);
    // console.log('\n<<<<<<<',this.props)
    let tmpSerializeData = addKToSerializedData(this.props)
    var ret = parseSerializedFilter(tmpSerializeData);
    // console.log('\n>>>>>',ret)
    // var filterFromUrl = ['bdrms','gr','bthrms',
    //   'min_lp','max_lp','dom',
    //   'min_poss_date','max_poss_date','isEstate','isPOS',
    //   'yr_f','yr_t','addr','remarks','psn','rltr',
    //   'oh','neartype','sold','sch','lpChg','recent'];
    // filterFromUrl.forEach((key) => {
    //   if (this.props[key]) {
    //     state.propTmpFilter[key] = this.props[key];
    //   }
    //   if (key == 'sold' && this.props[key] == 'fast') {
    //     state.propTmpFilter.saleDesc = 'Sold';
    //     state.propTmpFilter.dom = -90;
    //   }
    // });

    // console.log('initFiltersFromProps ret==',ret)
    state.propTmpFilter = Object.assign({}, state.propTmpFilter, ret.state.propTmpFilter)
    if (this.props.sold == 'fast') {
      state.propTmpFilter.sold = 'fast'
      state.propTmpFilter.saleDesc = 'Sold';
      state.propTmpFilter.dom = -90;
    }
    // console.log('====ptype7:',state.propTmpFilter.ptype)
    if (this.props.ptype2) {
      this.thisProps.ptype2 = this.props.ptype2
    }
    if (state.propTmpFilter.ptype2 && state.propTmpFilter.ptype2.length) {
      this.thisProps.ptype2 = state.propTmpFilter.ptype2
    }
    // console.log('*****',state)
    // if (this.props.ptype2) {
    //   var ptype2Val;
    //   var ptype2 = this.props.ptype2;
    //   if (ptype2.includes('.')) {
    //     ptype2Val = ptype2.split('.');
    //   } else {
    //     ptype2Val = ptype2;
    //   }
    //   state.propTmpFilter.ptype2 = ptype2Val;
    // }

    // console.log('====ptype7.5:',state.propTmpFilter.bnds,this.state.propTmpFilter.bnds)

    global.rmLog(`MapProps.jsx:840~~~initFiltersFromProps`, opt);

    if (opt.canSearch) {
      state.regionChangeSearch = true
    }
    await this.setStateAsync(state);

    this.isInitFiltersReady = true;

    // console.log('====ptype8:',state.propTmpFilter.bnds,this.state.propTmpFilter.bnds)
    // console.log('====init complete:',this.state.regionChangeSearch,this.state.propTmpFilter.ptype2,Date.now())
  }
  timeoutCalcHasFilterChanged(opt = {}) {
    let timeout = opt.timeout || 300;
    let hasMadeChanges;
    if (this.calcHasFilterChangedTimeout) {
      clearTimeout(this.calcHasFilterChangedTimeout)
    }
    this.calcHasFilterChangedTimeout = setTimeout(async () => {
      try {
        hasMadeChanges = await this.calcHasFilterChanged({ timeout });
      } catch (error) {
        console.error(error)
        hasMadeChanges = false
      }
      if (hasMadeChanges) {
        // console.log('+++++hasMadeChanges=',data.bbox)
      }
      // console.log('+++++hasMadeChanges=',data,hasMadeChanges)
      this.setState({ hasMadeChanges })
    }, timeout);
  }
  async calcHasFilterChanged(opt = {}) {
    // checkIfMapMoved
    var self = this;
    function getThresholdFromDelta(delta) {
      // console.log('-> delta = ',delta)
      if (delta > 2.5) {
        return 0.09
      }
      if (delta > 2) {
        return 0.06
      }
      if (delta > 1) {
        return 0.03
      }
      if (delta > 0.7) {
        return 0.02
      }
      if (delta > 0.2) {
        return 0.01
      }
      if (delta > 0.08) {
        return 0.0081
      }
      if (delta > 0.05) {
        return 0.0071
      }
      if (delta > 0.03) {
        return 0.003
      }
      if (delta > 0.01) {
        return 0.001
      }
      return 0.0008
    }
    // a = current
    function isSameValue(a, b, key, hasBnds) {
      if (Array.isArray(a) && a.length == 0) {
        a = ''
      }
      if (Array.isArray(b) && b.length == 0) {
        b = ''
      }
      // a and b all empty
      if (!a && !b) {
        return true
      }
      if (key == 'bbox') {
        if (!a) { a = [] }
        if (!b) { b = [] }
      }
      // console.log('isSameValue: hasBnds=',hasBnds,' key=',key+'\t',a,'==',b)
      if (Array.isArray(a) && Array.isArray(b)) {
        if (key == 'bnds') {
          if (a.length != b.length) {
            return false
          }
          // console.log('xxxxx key=',key,a,b)
          // calc whether each point is within threshold, for now we use 0.00001
          let hasDiff = false;
          let threshold = 0.00001
          a.forEach((value, index) => {
            if (Math.abs(value - b[index]) > threshold) {
              hasDiff = true
              // console.log('xxxxx key=',index,value,b[index],Math.abs(value-b[index]),threshold)
            }
          })
          return !hasDiff
        } else if (key == 'bbox') {
          // use bnds as primary calc method
          if (hasBnds) {
            return true
          }
          if (a.length != b.length) {
            return false
          }
          // let arrayDiff = a.map((value,index)=>{
          //   return Math.abs(value-b[index])
          // })
          // let maxDiff = Math.max(...arrayDiff)
          let pointA = getCenterFromBbox(a)
          let pointB = getCenterFromBbox(b)
          let dist = Math.hypot(pointB.lat - pointA.lat, pointB.lng - pointA.lng)
          // console.log('xxxxx',dist)
          let deltaDiff = Math.abs((a[3] - a[1]) - (b[3] - b[1]))
          let threshold = getThresholdFromDelta(Math.max(Math.abs(a[3] - a[1]), Math.abs(b[3] - b[1])));
          let delthThres = threshold <= 0.001 ? 0.001 : threshold;
          // console.log('----> \n dist=',dist,' thres=',threshold,
          //   '\n dist<=threshold? =',dist<=threshold,
          //   '\n deltaDiff=',deltaDiff,
          //   ' delthThres=',delthThres,
          //   '\n deltaDiff<delthThres? =',deltaDiff<delthThres,
          //   'isSame?=',(dist <= threshold) && deltaDiff < delthThres,'\na,b=',a,b)
          return (dist <= threshold) && (deltaDiff < delthThres);
          a.every(function (value, index) {
            // if(Math.abs((Math.round(value * 100) / 100)-(Math.round(b[index] * 100) / 100))>0.02){
            // console.log('isSameVal:',(Math.round(value * 10000) / 10000) - (Math.round(b[index] * 10000) / 10000),Math.abs((Math.round(value * 100) / 100)-(Math.round(b[index] * 100) / 100))<=0.005)
            // }
            return Math.abs((Math.round(value * 10000) / 10000) - (Math.round(b[index] * 10000) / 10000)) <= 0.005
          })
        }
        return a.length === b.length && a.every(function (value, index) { return value === b[index] })
      }
      // console.log(a,'===',b,'? ',a==b)
      if (a != b) {
        // console.log('change made:',key,'\t cur:',a,' saved:',b)
      }
      return a == b
    }
    return new Promise((resolve, reject) => {
      let savedSearchCond = storageIns.getCacheItem(Constants.CurrentSavedSearch);
      // console.log(savedSearchCond)
      if (!savedSearchCond) {
        return resolve(false)
      }
      self.setState({}, async () => {
        storageIns.getItemObj(Constants.MapsearchFilter, async (err, val) => {
          if (err) {
            return reject(err);
          }
          // console.log('------',val)
          if (!val) {
            return resolve(false)
          }
          var d = urlParamToObject(val);
          var ret = parseSerializedFilter(d);
          // var state = {};
          // console.log('ret.state=',ret.state,'\nret.opt=',ret.opt)
          let curFilter, curFilterVal;
          curFilter = self.state.propTmpFilter;
          curFilterVal = self.state.propTmpFilterVals;
          let result = false;
          if (!opt.timeout) {
            opt.timeout = 1
          }
          // NOTE: move to region takes time
          setTimeout(() => {
            if (!curFilter.bbox) {
              curFilter.bbox = self.lastBbox;
            }
            // console.log(this.props.coords,this.lastBbox,'delta:',this.lastBbox[3]-this.lastBbox[1])
            // console.log('xxxx',curFilter)
            let hasBnds = false;
            if (curFilter.bnds && curFilter.bnds.length) {
              hasBnds = true
            }
            for (let key in curFilter) {
              let value = curFilter[key];
              let cacheValue = ret.state.propTmpFilter[key]
              if (!isSameValue(value, cacheValue, key, hasBnds)) {
                // console.log('change made:',key,'\t cur:',value,' saved:',cacheValue)
                result = true
                break;
              }
            }
            // state.propTmpFilter = Object.assign({},curFilter,ret.state.propTmpFilter);
            // state.propTmpFilterVals = Object.assign({},curFilterVal,ret.state.propTmpFilterVals);
            return resolve(result)
          }, opt.timeout);
        })
      })
    });
  }
  async readFilterFromStorage(opt = {}) {
    var self = this;
    // var val = await RMStorage.syncGetItem('mapSearch.filter');
    storageIns.getItemObj(Constants.MapsearchFilter, async (err, val) => {
      if (err) {
        return;
      }
      var d = urlParamToObject(val);
      var ret = parseSerializedFilter(d);
      if (opt.initFilter) {
        await self.initFiltersFromProps()
      }
      var state = {};
      // console.log('read filter from storage ret.state=',ret.state,'\nret.opt=',ret.opt)
      let curFilter, curFilterVal;
      if (opt.discardCurConditions) {
        curFilter = self.getPropDefaultFilter();
        curFilterVal = curFilter;
        // ret.state.propTmpFilter.bbox = ret.opt.bbox
      } else {
        curFilter = self.state.propTmpFilter;
        curFilterVal = self.state.propTmpFilterVals;
      }
      state.propTmpFilter = Object.assign({}, curFilter, ret.state.propTmpFilter);
      state.propTmpFilterVals = Object.assign({}, curFilterVal, ret.state.propTmpFilterVals);
      let savedSearchCond = self.state.savedSearchCond;
      if (savedSearchCond) {
        state.savedSearchCond = savedSearchCond
        state.savedSearchCond.bnds = ret.state.propTmpFilter.bnds
        let polygonPoints = this.getPolygonPoints(savedSearchCond.bnds)
        state.polygonPoints = polygonPoints
        if (polygonPoints.length) {
          self.setDrawActive(true)
        } else {
          self.setDrawActive(false)
        }
      }
      // state.appmode = 'mls'
      if (ret.state.appmode) {
        state.appmode = ret.state.appmode
        state.highlightTextColor = await getColor('highlightText', state.appmode)
        state.highlightBgColor = await getColor('highlightBg', state.appmode)
        eventEmitter.emit(Constants.ChangeAppMode, { val: state.appmode, mapSwitch: true });
        self.curSearchMode = self.getSearchMode({ propTmpFilter: state.propTmpFilter });
        // console.log(self.curSearchMode)
      }
      state.quickFilter = Object.assign({}, self.state.quickFilter, {
        soldOnly: state.propTmpFilter.soldOnly,
        dom: state.propTmpFilter.dom,
        ptype2: state.propTmpFilter.ptype2 || [],
      })
      state.searchModeSaletp = state.propTmpFilter.saletp
      state.bndsDragHistory = [];
      await self.setStateAsync(state)
      // console.log('MapProps.readFilterFromStorage: readData End',self.state.propTmpFilter,'\n',state.propTmpFilter.bdrms)
      await self.setStateAsync({ regionChangeSearch: true })
      if (opt.discardCurConditions) {
        // console.log('propTmpFilter.bbox =',state.propTmpFilter.bbox)
        if (state.savedSearchCond && state.savedSearchCond.bnds) {
          let { center, delta } = this.map.calcCenterAndDelta(state.savedSearchCond.bnds)
          this.map.setCenterAndZoom({ lat: center.lat, lng: center.lng }, { delta })
        } else if (state.propTmpFilter.bbox && state.propTmpFilter.bbox[0]) {
          // console.log('Move map pos',state.propTmpFilter.bbox)
          self.map.fitToBBox(state.propTmpFilter.bbox)
        }
      }
      if (opt.doSearch) {
        // console.log('before dosearch',self.state.propTmpFilter.bdrms)
        self.doSearch({ clear: true })
        // console.log('after dosearch',self.state.propTmpFilter)
      }
      // cb = async ()=>{
      // }
      // self.setState(Object.assign(state),cb);
    })
  }
  async setFilterVal(data) {
    // console.log('set filter val->',data)
    if (data.saletp == 'lease' || data.saletp == 'sale') {
      await this.setSaleTp(data.saletp);
    }
  }
  // pagedata-retrieved
  async getPageData() {
    var self = this;
    return new Promise((resolve, reject) => {
      // console.log('mapProps getPageData',self.state);
      var state = {}
      mainRequest({
        url: '/1.5/pageData',
        method: 'post',
        data: {
          datas: this.datas,
          page: 'mapSearch'
        }
      }).then(async ret => {
        if (ret.err || ret.e) {
          return this.flashMessage(ret.err || ret.e);
        }
        let d = ret.datas;
        if (d.propFeatureTags) {
          state.propFeatureTags = d.propFeatureTags;
        }
        if (ret.datas) {
          state.dispVar = ret.datas;
        }
        await this.setStateAsync(state)
        appConfigIns.setAppConfig(ret.datas)
        if (d.hasOwnProperty('isLoggedIn')) {
          // self.mapSearchTipLogin = d.isLoggedIn?false:true;
          await self.initTitleAndSort();
        }
        return resolve('done')
      }).catch(err => {
        if ('TIME_OUT' == err) {
          this.flashMessage(this.strings[err])
        }
        return reject(err);
      })
    })
  }
  isRMProp(prop = {}) {
    return /^RM/.test(prop.id);
  }
  picUrl(r) {
    const noPicUrl = 'https://' + serverDomainIns.getFullUrl("/img/noPic.png");

    const url = getPropPicUrl(r, {
      isCip: this.state.dispVar.isCip,
      protocol: serverDomainIns.getProtocol(),
      noPicUrl
    });

    return url
  }
  initFilterSort() {
    var self = this;
    // console.log('-------',self.state.dispVar.propSortMethods)
    if (!self.state.propTmpFilter.sort && self.state.dispVar.propSortMethods && self.state.dispVar.propSortMethods.length) {
      self.state.propTmpFilter.sort = self.state.dispVar.propSortMethods[0].k || 'auto-ts';
      self.state.propTmpFilterVals.sort = self.state.dispVar.propSortMethods[0].v || l10n('Auto');
    }
  }
  // ignore some field
  hasExtraFilter() {
    var defaultFilter = this.getPropDefaultFilter()
    var filterKeys = Object.keys(defaultFilter);
    var hasExtra = 0;
    var excludes = ['saletp', 'city', 'prov', 'ltp', 'src', 'soldOnly']//saleDesc,ptype
    var isNotActive = /^-/.test(this.state.propTmpFilter.dom)
    filterKeys.forEach((k) => {
      // console.log('k= ',k)
      var defaultFilterVal = defaultFilter[k];
      var propTmpFilterVal = this.state.propTmpFilter[k];
      if (excludes.indexOf(k) < 0) {
        if (k == 'dom' || k == 'domYear') {
          if (k == 'domYear') {
            if (propTmpFilterVal) { hasExtra += 1 }
          } else {
            if (propTmpFilterVal) { hasExtra += 1 }
          }
        } else if (isNotActive && (k == 'soldOnly')) {
          if (propTmpFilterVal == true) {
            hasExtra += 1;
          }
        } else if (k == 'ptype2' || k == 'exposures' || k == 'bsmt') {
          if (Array.isArray(propTmpFilterVal) && propTmpFilterVal.length) {
            // console.log('+++hasExtra ptype2=',propTmpFilterVal)
            hasExtra += propTmpFilterVal.length;
          }
        } else {
          if ((propTmpFilterVal != null) && (defaultFilterVal !== propTmpFilterVal)) {
            // console.log('++hasExtra',k,defaultFilterVal, ' <->',propTmpFilterVal);
            hasExtra += 1;
          }
        }
      }
    });
    return hasExtra;
  }
  getPropDefaultFilter() {
    return {
      src: 'mls',
      saletp: 'sale',
      saleDesc: 'Sale',
      city: '',
      prov: '',
      cmty: '',
      ptype: 'Residential',
      ptype2: [],
      bdrms: '',
      bthrms: '',
      gr: '',
      min_lp: '',
      max_lp: '',
      no_mfee: false,
      max_mfee: '',
      sort: 'auto-ts',
      dom: '',
      bsmt: [],
      ptp: '',
      pstyl: '',
      ltp: '',
      oh: false,
      soldOnly: true,
      lpChg: '',
      neartype: '',
      sch: '',
      yr_f: '',
      yr_t: '',
      sq_f: '',
      sq_t: '',
      isPOS: '',
      addr: '',
      remark: '',
      psn: '',
      domYear: '',
      min_poss_date: '',
      max_poss_date: '',
      isEstate: '',
      sold: '',//fast
      frontFt_f: '',
      frontFt_t: '',
      depth_f: '',
      depth_t: '',
      lotsz_code: '',
      irreg: '',
      remarks: '',
      rltr: '',
      exposures: [],
      soldLoss: '',
    };
  }
  resetFilter(opt = {}) {
    var propDefaultFilter = this.getPropDefaultFilter();
    var holder = {};
    var valHolder = { saletp: l10n('For Sale'), ptype: l10n('Residential') };
    if (opt.keepCity && this.viewMode == 'list') {
      for (let tp of ['city', 'prov']) {
        holder[tp] = this.state.propTmpFilter[tp];
        valHolder[tp] = this.state.propTmpFilterVals[tp];
      }
    }
    if (opt.keepBnds) {
      holder.bnds = this.state.propTmpFilter.bnds;
    }
    if (opt.keepType) {
      // src = rm/trb
      // ltp = exlisting/assignment/rent/projects
      // type = market/projects
      let arr = ['src', 'ltp', 'type'];
      if (/^-/.test(this.state.propTmpFilter.dom)) {
        arr.push('dom');
      }
      for (let tp of arr) {
        holder[tp] = this.state.propTmpFilter[tp];
        valHolder[tp] = this.state.propTmpFilterVals[tp];
      }
    } else {
      this.curSearchMode = { k: 'Residential' }
    }
    this.state.propTmpFilter = Object.assign({}, propDefaultFilter, holder);
    this.state.propTmpFilterVals = Object.assign({}, propDefaultFilter, valHolder);
    // if (opt.title) {
    this.initTitlePtype({ force: 1 });
    // }
    this.initFilterSort();
  }
  async initTitleAndSort(opt = {}) {
    var self = this;
    await self.initTitlePtype(opt);
    self.initFilterSort();
  }
  async initTitlePtype(opt = {}) {
    // return;
    var self = this, ptype = this.state.propTmpFilter.ptype;
    if (opt.ptype) {
      ptype = opt.ptype;
    }
    if (!self.state.ptype2s.length && self.appMapMode !== 'fav') {
      let re3 = await self.getPtype2s(ptype);
      // console.log('xxxxx',re3,self.state.quickFilter)
    }
    if (self.thisProps.readFilter && !opt.force) {
      return
    }
    // if (!this.props.ptype) {
    //   ptype = 'Residential';
    //   // self.titleString = 'Residential';
    // }
    // console.log('initTitlePtype ptype=',ptype)
    // if (self.appMapMode !== 'fav') {
    //   self.setPropTmpFilterLoopVal({tp:'ptype', k:ptype});
    // }
    // else {
    //   // for (let ptype of self.dispVar.propPtypes) {
    //   //   if (ptype.k == self.grp+'') {
    //   //     self.grpName = ptype.v;
    //   //     break;
    //   //   }
    //   // }
    // }
    // if (self.propTmpFilter.saletp != null) {
    //   var saletp = self.propTmpFilter.saletp;
    //   self.searchModes = self[saletp+'ModeList'];
    // }
  }
  setPropTmpFilterLoopVal(opt) {
    var self = this, types = self.state.dispVar.propPtypes || [];
    // console.log('+++++',self.state.dispVar.propPtypes)
    if (opt.tp == 'ptype') {
      var vv = opt.k;
      if (opt.vv) {
        vv = opt.vv
      } else {
        var k = opt.k;
        for (let ptype of types) {
          if (ptype.k == k) {
            vv = ptype.v;
            break;
          }
        }
      }
      // self.titleString = vv;
      // console.log('xxxxsetPropTmpFilterLoopVal,ptype',opt.k)
      self.state.propTmpFilter.ptype = opt.k;
      self.state.propTmpFilterVals.ptype = vv;
    }
  }
  // TODO: ignored for now, need this when list
  // selectTag(t){
  //   this.filterOnTop = 1;
  //   this.scrollElement.scrollTop = this.scrollThreshold;
  //   if (t=='today') {
  //     // TODO: fix
  //     // this.propTmpFilter.soldDesc = '';
  //     // this.setSearchMode({k:'Today',v:'1',clear:true})
  //     if(this.propTmpFilter.dom == '0'){
  //       this.propTmpFilter.dom = ''
  //     } else {
  //       this.propTmpFilter.dom = '0'
  //     }
  //     this.propTmpFilterVals.dom = this.getLongMappedValueDom();
  //     this.doSearch({clear:true})
  //   } else if (t=='sold'){
  //     // TODO: fix, if dom !=, need click twice
  //     this.propTmpFilter.oh = false;
  //     this.resetTags({except:'ptype'})
  //     let k = this.propTmpFilter.saletp == 'sale' ? 'Sold' : 'Leased';
  //     this.setSearchMode({k,v:'1',clear:true})
  //   } else {
  //     this.setSearchMode({k:t,v:'1',clear:true})
  //   }
  // },
  async queryPtye2sFromServer(k, v, opt) {
    return new Promise((resolve, reject) => {
      if (!k) {
        return reject('no k');
      }
      var self = this;
      mainRequest({
        url: '/1.5/props/ptype2s.json',
        data: { ptype: k },
        method: 'post',
      }).then(ret => {
        if (ret.err) {
          return reject(ret.err)
        }
        return resolve(ret)
      }).catch(err => {
        return reject(err)
      })
    })
  }
  async getPtype2s(k, v, opt) {
    if (!k) {
      return;
    }
    var self = this;
    let state = {
      propTmpFilter: {
        ...this.state.propTmpFilter
      },
      propTmpFilterVals: {
        ...this.state.propTmpFilterVals
      },
      quickFilter: {
        ...this.state.quickFilter
      }
    }
    let ret;
    try {
      ret = await this.queryPtye2sFromServer(k, v, opt)
    } catch (err) {
      console.error(err)
      return 'error' + err.toString()
    }
    // console.log('xxxxxthisProps.ptype2',self.thisProps.ptype2)
    state.ptype2s = ret.ptype2s;
    if (self.thisProps.ptype2) {
      // if url has ptype2 param, filter valid ptype2
      // TODO: use setPropTmpFilterLoopVal({tp:'ptype2})
      let ptype2sMap = {};
      state.ptype2s.forEach(ptype2 => {
        ptype2sMap[ptype2.k] = ptype2.v;
      });
      let ptype2sVars = [];
      if (Array.isArray(self.thisProps.ptype2)) {
        ptype2sVars = self.thisProps.ptype2
      } else if (self.thisProps.ptype2) {
        let ptype2 = '' + self.thisProps.ptype2;
        if (ptype2.indexOf(',') > 1) {
          ptype2sVars = self.thisProps.ptype2.split(',');
        } else {
          ptype2sVars = [self.thisProps.ptype2]
        }
      }
      // ptype2sVars = self.thisProps.ptype2 || [];
      // console.log('99999',ptype2sVars)
      state.propTmpFilter.ptype2 = [];
      state.propTmpFilterVals.ptype2 = [];
      ptype2sVars.forEach(ptype2 => {
        let ptype2Val = ptype2sMap[ptype2];
        if (ptype2Val) {
          state.propTmpFilter.ptype2.push(ptype2);
          state.propTmpFilterVals.ptype2.push(ptype2Val);
          state.quickFilter.ptype2.push(ptype2);
          // self.state.quickFilter.ptype2.push(ptype2Val);
        }
      });
      delete self.thisProps.ptype2;
    }
    // self.state.propTmpFilter.ptype = k;
    self.setPropTmpFilterLoopVal({ tp: 'ptype', k: k, vv: v });
    // self.showTitleTypeSelect = false;
    // self.halfDrop = false;
    await self.setStateAsync(state);
    if (opt && opt.doSearch) {
      self.clearItems();
      // console.log('dosearch getptype2s dosearch10')
      self.doSearch(opt);
    }
    // console.log('111111',self.state.quickFilter)
    return 'done ptype2'
  }
  setPropImg(prop, data) {
    var self = this;
    if (!prop.img) {
      prop.img = this.picUrl(prop);
    }
    if (!prop.favGrp) {
      prop.favGrp = [];
    }
    // insert adrltr for rmprop
    if (prop.id && !prop.adrltr) {
      var user = {};
      if (prop.flwng) {//use followed for this prop
        if (!data.uid) {
          data.uid = prop.uid
        }
        user = this.userDict[data.uid];
        prop.uid = data.uid;
      } else {
        user = this.userDict[prop.uid];
      }
      // console.log(user);
      prop.adrltr = user;
    }
  }
  filterMostRecentProps(props = {}) {
    function findMostRecent(marker = {}) {
      let arr = marker.objs || []
      arr.sort((a, b) => { new Date(a.ts) - new Date(b.ts) })
      return arr[0]
    }
    for (let clusterKey in props) {
      let marker = props[clusterKey];
      if (marker.ids && marker.ids.length > 1) {
        let mostRencent = findMostRecent(marker)
        // console.log('marker: ',marker.objs,' recent->',mostRencent.ts)
        props[clusterKey].ids = [mostRencent._id]
        props[clusterKey].objs = [mostRencent]
      }
    }
    return props
  }
  // setupProps
  processProps(data, opt = {}) {
    var state = {}, self = this, mapMarkerList = {}, l = data.items, propIdsMap = {};

    // 在这里添加新的代码，处理选中的房源：选中房源，有些筛选条件下不应该出现，逻辑不对，暂且去掉待6.6.3版本进行讨论
    // let curProp = this.state.curProp;
    // if (curProp && curProp._id && curProp.lat && curProp.lng && this.lastBbox) {
    //   let [swLng, swLat, neLng, neLat] = this.lastBbox;
    //   // 如果选中的房源在地图范围内
    //   if (curProp.lng >= swLng && curProp.lng <= neLng && 
    //       curProp.lat >= swLat && curProp.lat <= neLat) {
    //     // 检查是否已经在列表中
    //     let exists = l.find(p => p._id === curProp._id);
    //     if (!exists) {
    //       // 如果不在列表中，添加到列表
    //       l.push(curProp);
    //       global.rmLog(`MapProps.jsx~~~processProps`, '将选中的房源添加到列表中保持显示');
    //     }
    //   }
    // }

    // round according to addreess?
    function round(d, p) {
      if (p == null) {
        p = 4;
      }
      return Math.floor(d * Math.pow(10, p)) / Math.pow(10, p);
    };
    function cluster_key(l) {
      return round(l.lat) + "," + round(l.lng);
    };
    //TODO:getUserLang
    // var lang = getUserLang();
    const lang = 'en'
    function getProjName(p) {
      // console.log(p)
      var nm;
      if (p.nmOrig) {
        nm = p.nmOrig;
      } else {
        var nm = p.nm_en || '';
        if (lang !== 'en') {
          nm = p.nm || '';
        }
      }
      return nm.slice(0, 10);
    }
    var now = new Date();
    //put props to data
    // var found = false;
    // if (this.state.hlProp) {
    //   l.push(this.state.hlProp);
    // }
    // l = l.reverse()
    for (var p of l) {
      // if (this.state.hlProp && p._id == this.state.hlProp._id) {
      //   found = true;
      // }
      // console.log(p)
      propIdsMap[p._id] = true;
      if (p.topup_pts && new Date(p.topTs) > now) {
        p.isTopUp = true;
        // console.log('isTopUp',p)
      }
      if (p.isProj) {
        p.lpr = p.lpf;
        p.image = p.img;
        delete p.img;
      }
      if (p.src == 'RM') {
        p.rmtype = getIconSuffix(p, true);
        if (p.market && p.market.isV) {
          p.verified = true
        }
      }
      // console.log(p.suffix)
      this.setPropImg(p, data)
      let price = p.lp || p.lpr;
      if (this.state.propTmpFilter.soldOnly) {
        price = p.sp || price;
      }
      // console.log(p._id,price,p.amount)
      if (!p.amount) {
        if (p.isProj || p.tp1) {
          // console.log(p)
          p.amount = getProjName(p)
        } else if (price === 0) {
          p.amount = getIconChar(p) + l10n('TBD')
        } else {
          p.amount = getIconChar(p) + propPrice(price)
        }
        p.suffix = getIconSuffix(p, false)
      }
      // console.log(p.amount);
    }
    if (self.viewMode == 'map') {
      for (let obj of l) {
        let key = cluster_key(obj);
        // console.log(key);
        var mkObj = mapMarkerList[key] || {
          key: key,
          lat: obj.lat,
          lng: obj.lng,
          ids: [],
          objs: []
        };
        mkObj.ids.push(obj._id);
        mkObj.objs.push(obj);
        // if more than 1 and has top; show special marker;
        if (mkObj.ids.length > 1) {
          for (let i of mkObj.objs) {
            if (i.isTopUp) {
              mapMarkerList[key + 'Top'] = {
                key: key + 'Top',
                lat: obj.lat,
                lng: obj.lng,
                ids: [i._id],
                objs: [i],
                topMarker: true,
              };
            }
          }
        }
        mapMarkerList[key] = mkObj;
      }
      // console.log(mapMarkerList);
      let mapProps = 'mapProps'
      let items = 'items'
      if (opt.soldLayer) {
        mapProps = 'mapSoldProps'
        items = 'soldItems'
        mapMarkerList = this.filterMostRecentProps(mapMarkerList)
      } else {
        // delete previewCache for non-ids
        for (let key in this.previewCache) {
          if (!propIdsMap[key]) {
            console.log('delete previewCache:', key)
            delete this.previewCache[key]
          }
        }
      }
      state[mapProps] = mapMarkerList;
      state[items] = l;
    } else {
      state.items = self.state.items.concat(l);
    }
    if (!opt.soldLayer) {
      this.swiperCache = {};
      // console.log('clear swiperCache')
      // this._renderPropPreviewSlider();
    }
    // self.setState(state);
    return state;
    self.setState(state, () => {
      // const hlPropKey = this.state.hlPropKey;
      // if (hlPropKey && state.mapProps[hlPropKey]) {
      //   // self.markerPressed(state.mapProps[hlPropKey]);
      //   var data = state.mapProps[hlPropKey];
      //   var usePropImg = true;
      //   if (data.objs[0]._id != this.state.curProp._id) {
      //     //  imgPreview
      //     usePropImg = false;
      //   }
      //   var curProp = data.objs[0];
      //   this.setState({curProp,tracksViewChanges:true,usePropImg},()=>{this.getCpm()});
      //   this.trackOff();
      //   setTimeout(() => {
      //     this.setState({usePropImg:true})
      //   }, 1);
      //   this.toggleModal(null,'open');
      // }
    });
  }
  parseUserList(ul) {
    this.userDict = {};
    if (!ul) {
      ul = [];
    }
    for (let user of ul) {
      this.userDict[user._id] = user;
    }
  }
  clearProps() {
    this.setState({ mapProps: {}, mapSoldProps: {} });
  }
  clearItems() {
    var self = this, state = {};
    self.state.pgNum = 0;
    state.items = [];
    state.soldItems = [];
    state.checkedList = {};
    state.mapProps = {};
    state.mapSoldProps = {};
    // when from index, do search in listmode, no mapObj
    self.setState(state)
    // if (self.mapObj && self.mapObj.clearMarkers) {
    //   self.mapObj.clearMarkers(self.propMarkerGroupName);
    // }
  }
  // searchSchoolProp?
  // {
  //   cmd:'search',
  //   showMarker:true,
  //   lat:s.lat,
  //   lng:s.lng,
  //   radius:currentStopRadius
  // }
  setSelectedStop = async ({ stop, lat, lng, radius }) => {
    return
    let self = this;
    let selectedStop = null;
    if (stop) {
      selectedStop = { lat, lng, radius, _id: stop._id }
    }
    self.setStateAsync({ selectedStop }, () => {
      // console.log('afterset:',this.state.showMapMarkers)
    })
  }
  searchWithinShape = async (opt = {}) => {
    // let state = {}
    let self = this;
    if (opt.cmd == 'search') {
      // TODO: support bnds
      if (opt.lat && opt.lng && opt.radius) {
        opt.clear = true;
        self.doSearch(opt, () => {
          self.setState({ showMapMarkers: true, regionChangeSearch: false })
        })
      } else {
        console.error('invalid input')
      }
    } else if (opt.cmd == 'setShowMarker') {
      // console.log('-----',opt)
      // NOTE: if not showMarker, not regionChangeSearch
      self.setState({ showMapMarkers: opt.showMarker, regionChangeSearch: opt.showMarker }, () => {
        // console.log('afterset:',this.state.showMapMarkers)
      })
    }
  }
  // if region is small enough
  isSmallRegion(bbox = []) {
    let ret = { isSmallRegion: false };
    let delta = bbox[3] - bbox[1];
    if (delta < 0.0061) {
      ret.isSmallRegion = true;
      ret.showSoldAs = 'smallDot'
    }
    if (delta < 0.0045) {
      ret.isSmallRegion = true;
      ret.showSoldAs = 'dot'
    }
    // console.log(delta)
    if (delta < 0.0023) {
      ret.showSoldAs = 'marker'
    }
    // console.log(ret)
    return ret
  }
  async doSearchSold(opt = {}, cb) {
    var self = this;
    var data = { label: 1, page: 0 }, filter = self.state.propSoldFilter;
    var state = {}
    var bbox;
    if (opt.bbox) {
      bbox = opt.bbox;
      if (!opt.zoom) {
        let lat = (bbox[1] + bbox[3]) / 2;
        let lng = (bbox[0] + bbox[2]) / 2;
        let delta = bbox[3] - bbox[1];
      }
      delete filter.bbox; //remove bbox limit if moved map
    }
    if (self.viewMode == 'map') {
      if (!bbox) {
        bbox = self.lastBbox;
      }
    }
    if ((bbox && bbox[3]) && (bbox[0] == bbox[2] || bbox[1] == bbox[3])) {
      this.setState(state)
      return;
    }
    // if region too big do not search
    let { isSmallRegion, showSoldAs } = this.isSmallRegion(bbox)
    if (!isSmallRegion) {
      return this.setState({ soldItems: [], mapSoldProps: {} });
    }
    data.bbox = bbox;
    for (let key in filter) {
      if (filter.hasOwnProperty(key) && isValidData(filter[key])) {
        var filterVal = filter[key];
        // if (key == 'dom' && !self.isSoldCondition()) {
        //   // update dom to position if not in sold/Leased condition
        //   filterVal = Math.abs(filterVal);
        // }
        data[key] = filterVal;
      }
    }

    this.previousSoldRequestTs = Date.now();
    data.ts = this.previousSoldRequestTs;
    data.hasWechat = getWxIsInstalled()

    mainRequest({
      url: '/1.5/props/search',
      data,
      method: 'POST',
    }).then(ret => {
      var state = { showSoldAs }
      if (ret.err) {
        return
      }
      if (ret.ts !== this.previousSoldRequestTs) {
        return;
      }
      // console.log(ret)
      this.parseUserList(ret.ul);
      state = Object.assign({}, state, this.processProps(ret, { soldLayer: true }));
      this.setState(state, () => {
        this.resetCurPropByItems()
      });
      this.trackOff();
      if (cb) {
        cb()
      }
    }).catch(err => {
      console.log('server-error:');
      console.log(err);
      return;
    })
  }
  // items has changed, so reset curProp, because it might not be in items/soldItems
  resetCurPropByItems() {
    return
    let curProp = this.state.curProp;
    let items = this.state.items;
    if (this.state.isFromSoldLayer) {
      items = this.state.soldItems;
    }

    // if (curProp && items.length) {
    //   let found = items.find(p => p._id == curProp._id)
    //   if (!found) {
    //     this.clearPropertyAndCommunity();
    //   }
    // }

    // 如果有选中的房源，检查是否需要清除
    if (curProp && curProp._id && items.length) {
      let found = items.find(p => p._id == curProp._id);

      // 如果房源不在返回列表中，检查其经纬度是否在当前地图范围内
      if (!found && this.lastBbox && curProp.lat && curProp.lng) {
        let [swLng, swLat, neLng, neLat] = this.lastBbox;

        // 如果房源坐标不在当前地图范围内，清除选中状态
        if (curProp.lng < swLng || curProp.lng > neLng ||
          curProp.lat < swLat || curProp.lat > neLat) {
          global.rmLog(`MapProps.jsx~~~resetCurPropByItems`, '当前选中房源不在地图范围内，清除选中状态');
          this.clearPropertyAndCommunity();
        } else {
          // 房源在地图范围内，保持选中状态
          global.rmLog(`MapProps.jsx~~~resetCurPropByItems`, '当前选中房源在地图范围内，保持选中状态');
        }
      }
    }
  }
  // TODO: searchAfter
  async doSearch(opt = {}, cb) {
    var self = this;
    // return;
    // var ts1 = new Date()
    // console.log('##### start doSearch',ts1)
    // console.log('====ptype5:',this.state.propTmpFilter.ptype)
    var data = { label: 1, page: self.state.pgNum }, filter = self.state.propTmpFilter;
    // console.log('##### start doSearch',Date.now(),this.state.propTmpFilter.ptype2)
    // var bnds = opt.bnds;
    var state = {}
    if (opt.save && !self.state.dispVar.isLoggedIn) {
      return this.flashMessage(self.strings.NEED_LOGIN);
    }
    if (opt.clear) {
      // TODO: performance issue
      this.clearItems();
    }
    // if (self.state.propTmpFilterVals['ptype']) {
    //   self.titleString = self.state.propTmpFilterVals['ptype'];
    // }
    // NOTE: should not be here!
    if (self.state.searchModeSaletp !== self.state.propTmpFilter.saletp) {
      console.log('!!!set searchModeSaletp in dosearch')
      // TODO: performance issue
      await self.setSaleTp(self.state.propTmpFilter.saletp);
    }
    state.loading = true;
    // state.tracksViewChanges = true
    // TODO: performance issue
    this.setState(state)
    var bbox;
    var selectedStop = this.state.selectedStop;
    if (opt.radius && opt.lat && opt.lng) {
      data.lat = opt.lat
      data.lng = opt.lng
      data.dis = opt.radius
      data.near = true
    } else if (selectedStop) {
      data.lat = selectedStop.lat
      data.lng = selectedStop.lng
      data.dis = selectedStop.radius
      data.near = true
    } else if (opt.bbox) {
      bbox = opt.bbox;
      // let [swLng,swLat,neLng,neLat] = bbox;
      // self.mapObj.gmap.fitBounds([[swLng,swLat],[neLng,neLat]]);
      // NOTE: from cb of advSearch
      if (!opt.zoom) {
        // let lat = (bbox[1]+bbox[3])/2;
        // let lng = (bbox[0]+bbox[2])/2;
        // let delta = bbox[3]-bbox[1];
        // if(self.map){
        //   self.map.setCenterAndZoom({lat:lat,lng:lng},{delta})
        // } else {
        //   console.error('no self.map')
        // }
      }
      delete filter.bbox; //remove bbox limit if moved map
      // doSearch tiggered by change filter but still mapMdoe, no bbox unless regionChanged
    }
    if (self.viewMode == 'map') {
      if (!bbox && !data.near) {
        bbox = self.lastBbox;
      }
    } else {
      self.hasSearched = true
    }
    // caused by display none, trigger map resize -> bnds is point
    if ((bbox && bbox[3]) && (bbox[0] == bbox[2] || bbox[1] == bbox[3])) {
      state.loading = false;
      this.setState(state)
      // console.log('invalid bbox, return')
      return;
    }
    data.bbox = bbox;
    for (let key in filter) {
      // console.log(key,'=',filter[key])
      if (filter.hasOwnProperty(key) && isValidData(filter[key])) {
        var filterVal = filter[key];
        if (key == 'dom' && !self.isSoldCondition()) {
          // update dom to position if not in sold/Leased condition
          filterVal = Math.abs(filterVal);
        }
        data[key] = filterVal;
      }
    }
    this.quickFilterMode = '';
    this.halfDrop = false;
    // if (filter.no_mfee) { data.max_mfee = null; }
    this.previousRequestTs = Date.now();
    data.ts = this.previousRequestTs;
    data.hasWechat = getWxIsInstalled()
    // data = {datas:['isApp','lang','isLoggedIn']}
    // console.log('######doSearch ->',data);
    // return;
    // if (['Assignment','Landlord','Exclusive'].includes(data.ptype)) {
    //   data.ptype = 'Residential';
    // }
    // if (this.isSoldCondition()) {
    //   data.status = 'U'
    // }
    // console.log('doSearch data ->',data);
    // var ts2 = new Date()
    global.rmLog(`MapProps.jsx:1951~~~doSearch`, data);
    mainRequest({
      url: '/1.5/props/search',
      data,
      method: 'POST',
    }).then(ret => {
      // global.rmLog(`MapProps.jsx:1958~~~doSearch`, ret);
      var state = {}
      state.loading = false;
      state.tracksViewChanges = true;
      if (ret.err) {
        this.setState(state)
        return this.flashMessage(ret.err);
      }
      if (ret.ts !== this.previousRequestTs) {
        // console.log(self.previousRequestTs);
        // console.log('dump request');
        this.setState({ hasMadeChanges: false })
        return;
      }
      // return;
      this.parseUserList(ret.ul);
      state = Object.assign({}, state, this.processProps(ret));
      // console.log('+++++here2',state)
      // self.initPropListImg(ret);
      state.cntTotal = ret.cnt || ret.items.length;
      state.cntRMprop = ret.cnt2;
      // console.log('+++++here3')
      // setTimeout(function () {
      //   state.loading = false;
      // }, 300);
      if (this.state.mapSearchTipCount > -1) {
        state.showMapSearchTip = true;
      }
      clearTimeout(self.tipTimeout || null);
      self.tipTimeout = setTimeout(() => {
        // self.showMapSearchTip = false;
        self.setState({ showMapSearchTip: false })
      }, 5000);
      // NOTE: 2022-08-29 dont show alert when no prop
      // if (ret.cnt === 0 || ret.items.length == 0) {
      //   this.flashMessage(l10n(this.strings.NO_RESULT));
      // }
      this.lastSearch = {
        q: ret.q,
        readable: ret.readable
      };
      // setTimeout(function () {
      // console.log(ret.q);

      this.setState(state, async () => {
        let timeout = 100;
        // console.log(Date.now()-this.mountTime)
        if (Date.now() - this.mountTime < 2800) {
          timeout = 500
        }
        this.timeoutCalcHasFilterChanged({ timeout })
        this.resetCurPropByItems()
      });
      // }, 100);
      this.trackOff();
      if (opt.save && self.state.dispVar.isLoggedIn) {
        self.saveSearch();
      }
      if (opt.mapView) {
        self.hasSearched = true;
        // self.showMapView();
      }
      // var ts3 = new Date()
      // console.log('#####region change',ts3-this.regionChangedTime,'\tend doSearch net used -> ',ts3-ts2, 'cfg used->', ts2-ts1, 'size:', JSON.stringify(ret.items).length)
      if (cb) {
        cb()
      }
    }).catch(err => {
      console.log('server-error:');
      console.log(err);
      return;
    })
  }
  // TODO: move to web, save at web
  saveSearch() {
    var self = this;
    if (!(self.lastSearch.q && self.lastSearch.readable)) {
      self.flashMessage('no search yet');
      return;
    }
    mainRequest({
      url: '/1.5/props/search/save',
      data: self.lastSearch,
      method: 'POST',
    }).then(ret => {
      if (ret.err || ret.e) {
        return;
        return Alert.alert(ret.err || ret.e);
      }
      // console.log(ret);
      var msg = ret.e ? ret.e : ret.msg;
      self.flashMessage(msg);
    }).catch(err => {
      console.log(err, ret)
      return;
    })
  }
  // onRegionChangeComplete
  regionChanged(event, map) {
    global.rmLog(`MapProps.jsx:2056~~~regionChanged`);
    if (!this.featureOn) return;
    this.regionChangedTime = Date.now()
    super.regionChanged(event, map);
    var self = this;
    // console.log("map props regionChanged zoom ->" + event.zoom, 'changeEnd: '+Date.now(), self.state.regionChangeSearch);
    // if (vars.bbox) {
    //   function parseBbox(originalBbox) {
    //     let bbox = originalBbox.split(',');
    //     for (let i = 0; i < bbox.length; i++) {
    //       bbox[i] = parseFloat(bbox[i]);
    //       if (isNaN(bbox[i])) {
    //         return null;
    //       }
    //     }
    //     return bbox;
    //   }
    //   let bbox = parseBbox(vars.bbox);
    //   if (bbox) {
    //     opt.bbox = bbox;
    //   }
    //   delete vars.bbox;
    // }
    self.lastBbox = event.bbox;
    let timeout = 100;
    if (Platform.OS === 'android') {
      timeout = 600;
    }

    global.rmLog(`MapProps.jsx:2085~~~regionChanged`, self.state.regionChangeSearch);

    const doSearch = () => {
      self.doSearch(event);
      self.doSearchSold(event);
    }

    const handleRegionSearch = () => {
      if (self.state.regionChangeSearch) {
        clearTimeout(self.regionChangeSearchTimout);
        self.regionChangeSearchTimout = setTimeout(doSearch, timeout);
        return;
      }

      clearTimeout(self.initRegionChangeSearch);
      self.initRegionChangeSearch = setTimeout(() => {
        const checkInitReady = setInterval(() => {
          //fix初始化条件未设置完成的情况下，提前条用了dosearch，每次进入附近已售会显示在售，500ms在加拿大可能够，但是在其他区域请求慢就会出问题。这里逻辑太复杂，不敢大改动
          if (this.isInitFiltersReady) {
            doSearch();
            clearInterval(checkInitReady);
          }
        }, 100);
      }, 500);
    }

    handleRegionSearch();
    // if (event.self && event.self.state.dispVar) {
    //   this.state.dispVar = event.self.state.dispVar;
    // }
    // if (this.doSearchTimeout) {
    //   clearTimeout(this.doSearchTimeout)
    // }
    // this.doSearchTimeout = setTimeout(()=>{
    //   self.doSearch(event)
    // },1)
  }
  // limit to 4 decimal
  getLatLngKey(latLng) {
    lat =
      latLng.latitude + ',' + latLng.longitude
  }
  // get Unique idx for draging point, otherwise will cause drag fail
  getUniqueIdx(lat, lng) {
    return lat + ',' + lng
  }
  // bnds = [lat,lng,lat,lng]
  // @returns [{latitude,longitude}]
  convertObjectListBnds(bnds, title = '') {
    let acceptedBnds = []
    let bndName = '';
    let savedSearchCond = this.state.savedSearchCond;
    // NOTE: 如果key一样会导致marker不更新
    // console.log(savedSearchCond)
    if (title) {
      bndName = title
    } else if (savedSearchCond && savedSearchCond.title) {
      bndName = savedSearchCond.title
    } else {
      let d = new Date();
      bndName = d.getHours() + d.getMinutes() + 'TSID'
    }
    // console.log('xxxxx',bndName)
    // let bndLatLngMap = {}
    for (let i = 0; i < bnds.length; i += 2) {
      let latLng = {
        latitude: parseFloat(bnds[i]),
        longitude: parseFloat(bnds[i + 1]),
        idx: bndName + i
      }
      // bndLatLngMap[getLatLngKey(latLng)] = idx
      if (latLng.latitude && latLng.longitude) {
        acceptedBnds.push(latLng)
      }
    }
    // add index to each point
    // for(let i=0;i<acceptedBnds.length;i++){
    //   acceptedBnds[i].index = i
    // }
    return acceptedBnds
  }
  // @returns [lat,lng,lat,lng]
  // @input [{latitude,longitude}]
  convertBackToFlatBnds(bnds) {
    let flatBnds = []
    for (let i = 0; i < bnds.length; i++) {
      flatBnds.push(bnds[i].latitude)
      flatBnds.push(bnds[i].longitude)
    }
    return flatBnds
  }
  // render dotted Polyline
  renderPolyline(bnds = []) {
    return null
    if (!bnds) {
      return null
    }
    if (parseFloat(bnds[0])) {
      bnds = this.convertObjectListBnds(bnds)
    }
    // console.log('++++',bnds)
    return <Polyline
      key={'savedSearchPolyline'}
      coordinates={bnds}
      strokeColor={'#ef948e'}
      strokeWidth={2}
      lineDashPattern={[5, 5]}
      zIndex={100}
    >
    </Polyline>
  }
  // render polygon it self
  renderPolygon() {
    let bnds = this.state.savedSearchCond.bnds;
    // return null
    if (!bnds) {
      return null
    }
    if (parseFloat(bnds[0])) {
      bnds = this.convertObjectListBnds(bnds)
    }
    // console.log('++++',bnds)
    return <Polygon
      ref={ref => { this.polygon = ref; }}
      key={'savedSearchPolygon'}
      coordinates={bnds}
      strokeColor={'#e03131'}
      fillColor={'rgba(224,49,49,0.2)'}
      zIndex={100}
    >
    </Polygon>
  }
  // given {lat,lng} return {x,y} where x and y are pixels, eg,{x:100,y:200} on screen
  latLngToContainerPoint = (point, bbox) => {
    if (!bbox) {
      return
    }
    // console.log((point.longitude-bbox[0]),(bbox[2]-bbox[0]),gWindowWidth)
    // var delta = bbox[3]-bbox[1];
    var x = (point.longitude - bbox[0]) / (bbox[2] - bbox[0]) * gWindowWidth;
    var y = (point.latitude - bbox[1]) / (bbox[3] - bbox[1]) * gWindowHeight;
    return { x, y };
  }
  // calculate the distance between two points in pixels
  isClosePoints(node, nextNode, mapBnds) {
    if (!mapBnds) {
      return true
    }
    let p1 = this.latLngToContainerPoint(node, mapBnds);
    let p2 = this.latLngToContainerPoint(nextNode, mapBnds);
    let distance = Math.sqrt(Math.pow(p1.x - p2.x, 2) + Math.pow(p1.y - p2.y, 2))
    // console.log(distance,'distance <= 60? ',distance <= 60)
    return distance <= 70
  }
  //多边形marker拖拽问题，ref:https://github.com/react-native-maps/react-native-maps/issues/4896
  getDraggablePoint({ bnd, key, i, type }) {
    let opacity = 1
    let source = gRedMarker;
    if (type == 'dragAdd') {
      source = gRedMarkerFade
      opacity = 0.7
    }
    // NOTE: cannot add lat,lng to key, otherwise, it will not re-render can cause drag fail
    let key2 = 'polygonPoint' + key//+bnd.latitude+bnd.longitude
    // console.log(key2)
    bnd.i = i
    bnd.key = key2
    let elem = <Marker draggable
      // stopPropagation={true} this.state.tracksViewChanges
      tracksViewChanges={true}
      coordinate={{ latitude: bnd.latitude, longitude: bnd.longitude }}
      // onPress={null}
      key={key2}
      onDragStart={(e) => { this.onPolygonPointDragStart(e, bnd) }}
      onDrag={(e) => { this.onPolygonPointDrag(e, bnd) }}
      onDragEnd={(e) => { this.onPolygonPointDragEnd(e, bnd) }}
      anchor={{ x: 0.5, y: 0.5 }}
      zIndex={100}
    // image={source}
    // width={20}
    // height={20}
    // style={{width:20,height:20,opacity:opacity||1}}
    >
      {/* <DotMarker
        clickable={true}
        isSelected={false}
      ></DotMarker> */}
      <Image
        resizeMode='stretch'
        style={{ width: 36, height: 36, opacity: opacity || 1 }}
        source={source}//reddot.png
      />
    </Marker>
    return {
      idx: i,
      key: key2,
      type: bnd.type || 'corner',
      elem: elem
    }
  }
  // get corner points of polygon
  getPolygonPoints(bnds, title = '') {
    bnds = bnds || this.state.savedSearchCond.bnds;
    let result = []
    if (!bnds) {
      return result
    }
    // let isDragging = this.state.isDragging;
    if (parseFloat(bnds[0])) {
      bnds = this.convertObjectListBnds(bnds, title)
    }

    let getMiddlePoint = (bnd, nextNode) => {
      let lat = (bnd.latitude + nextNode.latitude) / 2
      let lng = (bnd.longitude + nextNode.longitude) / 2
      return { latitude: lat, longitude: lng, type: 'dragAdd' }
    }
    // let mapBnds = await this.map.getMapBoundaries()
    let mapBnds = this.lastBbox;
    // console.log('getPolygonPoints: ',bnds)
    for (let i = 0; i < bnds.length; i++) {
      // the last point is equal to the first point, no need to draw
      if (i == bnds.length - 1) {
        break
      }
      let bnd = bnds[i]
      // console.log('+++',bnd,i)
      result.push(this.getDraggablePoint({ bnd, i, key: bnd.idx || i, type: 'corner' }))
      let nextNode = bnds[i + 1]
      // console.log('////////',!this.isClosePoints(bnd,nextNode,mapBnds))
      if (nextNode && !this.isClosePoints(bnd, nextNode, mapBnds)) {
        let newMiddlePoint = getMiddlePoint(bnd, nextNode)
        // console.log('------middlePoint',newMiddlePoint)
        // if(!isDragging){
        result.push(this.getDraggablePoint({ bnd: newMiddlePoint, i, key: (bnd.idx || i) + 'middlePoint', type: 'dragAdd' }))
        // }
      }
    }
    // re-index
    for (let i = 0; i < result.length; i++) {
      let point = result[i];
      if (point.type !== 'dragAdd' || !this.state.isDragging) {
        point.idx = i
      }
    }
    // result.forEach((cur)=>{console.log(cur.elem.key+' -> key')})
    // console.log('++++Poly Points')
    // return []
    return result
  }
  // render polygon corner draggable points
  // @input bnds = [lat,lng,lat,lng]
  // will convert to [{latitude,longitude}]
  renderPolygonPoints() {
    let points = this.state.polygonPoints;
    let result = [];
    // return []
    let isDragging = this.state.isDragging;
    for (let i = 0; i < points.length; i++) {
      let point = points[i];
      // console.log(point.type,isDragging)
      if (point.type !== 'dragAdd' || !isDragging) {
        result.push(point.elem)
      }
    }
    // return []
    // console.log('----->Poly Points',result.length)
    return result;
  }
  // drag add new point to polygon list between to points
  onNewPolygonPointDragEnd(e, i) {
    return
    let bnds = this.state.propTmpFilter.bnds;
    let newPoint = [e.nativeEvent.coordinate.longitude, e.nativeEvent.coordinate.latitude]
    let newBnds = []
    for (let i = 0; i < bnds.length; i++) {
      let bnd = bnds[i]
      newBnds.push(bnd)
      let nextNode = bnds[i + 1]
      if (nextNode) {
        newBnds.push(newPoint)
      }
    }
    let savedSearchCond = this.state.savedSearchCond;
    let propTmpFilter = { ...this.state.propTmpFilter };
    let propTmpFilterVals = { ...this.state.propTmpFilterVals };
    savedSearchCond.bnds = newBnds;
    propTmpFilter.bnds = newBnds;
    this.setState({ savedSearchCond, propTmpFilter })
  }
  // set state.savedSearchCond.bndsTmp and ready to draw dotted line
  onPolygonPointDragStart(e, point = {}) {
    let cnd = this.state.savedSearchCond;
    if (!(cnd && cnd.bnds)) {
      return
    }
    let bndsDragHistory = this.state.bndsDragHistory;
    let bnds = cnd.bnds;
    let polygonPoints = this.state.polygonPoints;
    let i = 0
    if (point.i) {
      i = point.i
    }
    let isDragging = true;
    let lat = e.nativeEvent.coordinate.latitude
    let lng = e.nativeEvent.coordinate.longitude
    // for undo use of history
    if (bndsDragHistory.length == 0) {
      bndsDragHistory.push(bnds);
    }
    bnds = this.convertObjectListBnds(bnds)
    if (point.type == 'dragAdd') {
      // console.log('dragAdd start idx:',i)
      // isDragging = false;
      // console.log('dragAdd bnds start:',JSON.stringify(bnds,null,' '))
      bnds.splice(i + 1, 0, { latitude: lat, longitude: lng }) //idx:this.getUniqueIdx(lat,lng)
      // console.log('dragAdd bnds start after:',JSON.stringify(bnds,null,' '))
      // bnds = this.convertBackToFlatBnds(bnds)
      // this.setStateAsync({savedSearchCond:cnd})
      this.newPolyDragPointBnds = bnds
      // let newBnds = this.convertBackToFlatBnds(bnds)
      // polygonPoints = this.getPolygonPoints(newBnds)
      // change status of dragging point
      let draginPoint = polygonPoints.find((cur) => {
        if (point.key == cur.key) {
          return true
        }
      })
      draginPoint.type = 'corner';
      // console.log('dragAdd start found:',point,draginPoint)
    }
    this.setState({ tracksViewChanges: true, bndsDragHistory, isDragging, polygonPoints })
    return
    if (!(cnd && cnd.bnds)) {
      return
    }
    cnd.bndsTmp = [];
    this.setState({ savedSearchCond: cnd })
  }
  // NOTE: only need to draw polyline of dragged point + 2 neighbour points
  // TODO: fix this, idx incorrect
  getAdjacentAndCurrentPointAsBnds(idx, type, bnds) {
    let result = []
    if (!bnds) {
      return result
    }
    if (type == 'dragAdd') {
      idx = idx + 1
    }
    // if type is dragAdd, idx+1 is new point
    for (let i = 0; i < bnds.length; i++) {
      if (i == idx - 1 || i == idx || i == idx + 1) {
        result.push(bnds[i])
        // console.log(i)
      }
      if (idx == 0) {
        // last point is equal to first, so -2
        if (i == bnds.length - 2) {
          result.unshift(bnds[i])
          // console.log(i)
        }
      }
    }
    // console.log(idx,type,result)
    return result
  }
  // drag and change bnd on map directly
  // NOTE: dont change dragging marker, has performance issue
  onPolygonPointDrag(e, point = {}) {
    let cnd = this.state.savedSearchCond;
    if (!(cnd && cnd.bnds)) {
      return
    }
    let bnds = cnd.bnds;
    bnds = this.convertObjectListBnds(bnds)
    if (this.newPolyDragPointBnds) {
      bnds = this.newPolyDragPointBnds
    }
    let idx = 0
    if (point.i) {
      idx = point.i
    }
    let lat = e.nativeEvent.coordinate.latitude
    let lng = e.nativeEvent.coordinate.longitude

    // TODO: add new point will change index keys, but points will shift
    // console.log('dragging',idx,point.type)
    // return
    if (point.type == 'dragAdd') {
      // console.log('dragAdd',idx,lat,lng)
      // bnds.splice(idx+1,0,{latitude:lat,longitude:lng})
      // console.log('dragAdd2',bnds)
      bnds[idx + 1] = { latitude: lat, longitude: lng }
    } else {
      bnds[idx] = { latitude: lat, longitude: lng }
      if (idx == 0) {
        bnds[bnds.length - 1] = { latitude: lat, longitude: lng }
      }
      // console.log('dragExist',idx)
    }
    // NOTE: only need to draw polyline of dragged point + 2 neighbour points
    // let bndsTmp = this.getAdjacentAndCurrentPointAsBnds(idx,point.type,bnds);
    // bndsTmp = this.convertBackToFlatBnds(bndsTmp)
    // console.log('bnds before conv',bnds)
    let bnds2 = this.convertBackToFlatBnds(bnds)
    let savedSearchCond = this.state.savedSearchCond;
    // savedSearchCond.bndsTmp = bndsTmp;
    savedSearchCond.bnds = bnds2;
    // this.setState({savedSearchCond})
    // return
    if (!this.throttleSetState) {
      this.throttleSetState = this.throttle((state) => {
        this.setState(state)
        // console.log('setstate bnds:',JSON.stringify(bnds,null,' '))
      }, 1000 / 120)
    }
    this.throttleSetState({ savedSearchCond })
  }
  // trigger draw at most 24(4) frame/times a second
  throttle(fn, delay) {
    let lastTime = 0;
    return function (...args) {
      let now = Date.now();
      if (now - lastTime > delay) {
        lastTime = now;
        fn(...args)
      } else {
        // console.log('throttle')
      }
    }
  }
  // drag one point to another position, drag polygon point will change bnds
  onPolygonPointDragEnd(e, point = {}) {
    // return
    let cnd = this.state.savedSearchCond;
    this.newPolyDragPointBnds = null
    if (!(cnd && cnd.bnds)) {
      return
    }
    let bnds = cnd.bnds;
    let idx = 0
    if (point.i) {
      idx = point.i
    }
    // console.log(e.nativeEvent,idx,bnds)
    let lat = e.nativeEvent.coordinate.latitude
    let lng = e.nativeEvent.coordinate.longitude
    let bndsDragHistory = this.state.bndsDragHistory;
    bnds = this.convertObjectListBnds(bnds)
    // idx = bnds.findIndex((point)=>{
    // NOTE: DO nothing, we alreay added this point when drag start
    if (point.type == 'dragAdd') {
      // console.log('dragAdd',idx)
      // bnds.splice(idx+1,0,{latitude:lat,longitude:lng})
      // console.log('dragAdd2',bnds)
    } else {
      // NOTE: DO nothing, we alreay changed while draging
      // console.log('drag existed1',bnds,idx)
      // drag the first point also sets the last point, so that polygon can be closed
      // bnds[idx] = {latitude:lat,longitude:lng}
      // if(idx == 0){
      //   bnds[bnds.length-1] = {latitude:lat,longitude:lng}
      // }
      // console.log('drag existed2',bnds)
    }
    // console.log('--------------drag end',JSON.stringify(bnds,null,' '))
    bnds = this.convertBackToFlatBnds(bnds)
    let savedSearchCond = this.state.savedSearchCond;
    let propTmpFilter = { ...this.state.propTmpFilter };
    savedSearchCond.bnds = bnds;
    bndsDragHistory.push(bnds);
    savedSearchCond.bndsTmp = null;
    propTmpFilter.bnds = bnds;
    let polygonPoints = this.getPolygonPoints(savedSearchCond.bnds)
    this.setStateAsync({ savedSearchCond, propTmpFilter, bndsDragHistory, isDragging: false, polygonPoints })
    this.trackOff();
    this.timeoutCalcHasFilterChanged()
    this.doSearch();
  }
  renderPropMarker(prop, opt = {}) {
    let isFromSoldLayer = false;
    let curProp = this.state.curProp || {};
    if (opt.tp == 'mapSoldProps') {
      isFromSoldLayer = true
    }
    let showAsDot = (this.state.showSoldAs == 'dot') || (this.state.showSoldAs == 'smallDot');
    // console.log('xxxx',opt,showAsDot)
    let onPress = this.markerPressed(prop, isFromSoldLayer)
    let marker = null

    // 检查当前prop是否被选中 - 同时检查curProp和selectedMarkerId
    const isSelected = curProp._id === prop.objs[0]._id ||
      this.state.selectedMarkerId === prop.objs[0]._id;

    if (prop.topMarker) {
      marker = (<HouseMarker
        key={prop.objs[0]._id}
        isTopUp={prop.objs[0].isTopUp}
        offMarket={prop.objs[0].status_en == 'U' && !this.isPropSold(prop.objs[0].lst)}
        isSelected={isSelected}
        amount={prop.objs[0].amount}
        suffix={prop.objs[0].suffix} //TODO: merge
        adrltr={prop.objs[0].adrltr}
        verified={prop.objs[0].verified} //TODO: merge
        showing={prop.objs[0].showing} //TODO: merge
        showingPassed={prop.objs[0].showingPassed} //TODO: merge
      />)
    } else {
      if (isFromSoldLayer && showAsDot) {
        let clickable = this.state.showSoldAs == 'dot'
        marker = <DotMarker
          clickable={clickable}
          isSelected={isSelected} // 使用新的逻辑
        >
        </DotMarker>
        if (!clickable) {
          onPress = null
        }
        // console.log('+++',marker)
      } else {
        marker = (<PriceMarker
          key={prop.objs[0]._id}
          isTopUp={prop.objs[0].isTopUp}
          offMarket={prop.objs[0].status_en == 'U' && !this.isPropSold(prop.objs[0].lst)}
          isSold={prop.objs[0].status_en == 'U' && this.isPropSold(prop.objs[0].lst)}
          isFromSoldLayer={isFromSoldLayer}
          isSelected={isSelected} // 使用新的逻辑
          amount={prop.objs[0].amount}
          suffix={prop.objs[0].suffix}
          adrltr={prop.objs[0].adrltr}
          verified={prop.objs[0].verified}
          showing={prop.objs[0].showing}
          showingPassed={prop.objs[0].showingPassed}
          group={prop.objs[0].group}
        />)
      }
    }

    return (<Marker
      stopPropagation={true}
      tracksViewChanges={this.state.tracksViewChanges}
      coordinate={{ latitude: prop.lat, longitude: prop.lng }}
      onPress={onPress}
      key={prop.objs[0]._id}
      anchor={{ x: 0.5, y: prop.topMarker ? 1.2 : 1 }}
      // pinColor={prop.isTopUp ? '#c5a620' : '#5c6972'}
      style={prop.objs[0].isTopUp ? styles.topMarker : { zIndex: 10 }}
    >
      {marker}
    </Marker>)
  }
  renderPropNumberIcon(prop, opt = {}) {
    let isFromSoldLayer = false;
    let isSelected = false;
    // if curProp._id inside prop.objs then return true
    let curProp = this.state.curProp || {};
    prop.objs.find((obj) => {
      if (obj._id == curProp._id || obj._id == this.state.selectedMarkerId) {
        isSelected = true
        return true
      }
    })
    if (opt.tp == 'mapSoldProps') {
      isFromSoldLayer = true
    }
    return (<Marker
      stopPropagation={true}
      coordinate={{ latitude: prop.lat, longitude: prop.lng }}
      onPress={this.markerPressed(prop)}
      key={prop.key}
      tracksViewChanges={this.state.tracksViewChanges}
      // pinColor={prop.isTopUp ? '#c5a620' : '#5c6972'}
      style={{ zIndex: 10 }}
      anchor={{ x: 0.5, y: 0.5 }}
    >
      <ListMarker
        isFromSoldLayer={isFromSoldLayer}
        amount={prop.objs.length}
        isSelected={isSelected}
      />
    </Marker>)
  }
  async ptypeSelect(args, opt) {
    var k = args[0];
    var v = args[1];
    // return;
    var self = this;
    if (self.appMapMode !== 'fav') {
      // self.titleString = v;
      let state = {
        propTmpFilter: {
          ...this.state.propTmpFilter,
          ptype: k,
          ptype2: []
        },
        propTmpFilterVals: {
          ...this.state.propTmpFilterVals,
          ptype: v,
          ptype2: []
        }
      }
      // self.state.propTmpFilter['ptype'] = k;
      // self.state.propTmpFilterVals['ptype'] = v;
      // self.state.propTmpFilter['ptype2'] = [];
      // self.state.propTmpFilterVals['ptype2'] = [];
      let re2 = await self.getPtype2s(k, v, Object.assign(opt, { doSearch: 1 }));
      // console.log('cccccc',re2)
      await self.setStateAsync(state)
    } else {
      if (self.grpName !== v) {
        self.grpName = v;
        self.clearItems();
        self.getFavs(k);
      }
    }
  }
  toggleSaletpSelect(opt = 'close') {
    return
    // this.toggleModal('saleTypeSelect',opt);
    var val = opt == 'close' ? false : true;
    // this.state.showSaleTypeSelect = val;
    // console.log('val: '+val)
    this.setState({ showSaleTypeSelect: val })
  }
  async setSearchModeSaletp(tp) {
    var state = {}
    state.searchModeSaletp = tp;
    // state.searchModes = this[tp+'ModeList'];
    await this.setStateAsync(state);
  }
  async setSearchMode(m) {
    this.isSelectedTag = 1;
    // TODO: scroll list to top
    // document.querySelector('#list-container').scrollTop = 0;
    // console.log('++++++setSearchMode',m)
    var searchModeKey = m.k, state = {
      propTmpFilter: {
        ...this.state.propTmpFilter,
      },
      propTmpFilterVals: {
        ...this.state.propTmpFilterVals,
      },
      quickFilter: {
        ...this.state.quickFilter,
      }
    };
    // let tmp = {...this.state.propTmpFilter}
    if (state.propTmpFilter && state.propTmpFilter.hasOwnProperty('undefined')) {
      console.error('0state here', state.propTmpFilter, JSON.stringify(state.propTmpFilter))
    }
    // console.log('0state here:',this.state.propTmpFilter,JSON.stringify(state.propTmpFilter))
    state.showQuickFilter = false;
    var saletp = this.state.propTmpFilter.saletp;
    // if (this.appMapMode == 'fav') {
    //   if (k == 'Sold' || k == 'Leased') {
    //     var url = '/1.5/mapSearch?d=/1.5/settings&mode=map&mapmode=sold';
    //     var city =  this.dispVar.userCity;
    //     url = this.appendCityToUrl(url, city);
    //     return window.location = url;
    //   }
    //   if(k == 'Open House') {
    //     var url = '/1.5/mapSearch?mode=list&d=/1.5/settings&oh=true';
    //     var city =  this.dispVar.userCity;
    //     url = this.appendCityToUrl(url, city);
    //     return window.location = url;
    //   }
    // }
    // if (searchModeKey=='Commercial') {
    //   eventEmitter.emit(SYSTEM.EVENT_SET_FEATURE,{school:false})
    // } else {
    //   eventEmitter.emit(SYSTEM.EVENT_SET_FEATURE,{school:true})
    // }
    if (!m.skipSearchModeCheck && this.isSameSearchMode(m)) {
      // console.log('silent return')
      return;
    } else {
      state.pgNum = 0;
    }
    this.clearItems()
    // console.log('state here:',JSON.stringify(state.propTmpFilter))
    if (state.propTmpFilter && state.propTmpFilter.hasOwnProperty('undefined')) {
      console.error('1state here', state.propTmpFilter, JSON.stringify(state.propTmpFilter))
    }
    // NOTE: m.v = 1 indicateds its tag search, can reset tag
    // if (!m.noSearch && !m.v) {
    //   console.log('should reset filter');
    //   this.resetFilter({keepCity:true});
    // }
    state.propTmpFilter.saletp = this.state.searchModeSaletp;
    this.curSearchMode = m;
    var searchMode = PROP_LIST_SEARCH_MODES[searchModeKey];
    // console.log('$$$$$',searchMode,searchMode.k,searchMode.v)
    var filterField = searchMode.k;
    var filterVal = searchMode.v;
    // console.log('searchMode: ',searchMode)
    // if(state.propTmpFilter && state.propTmpFilter.hasOwnProperty('undefined')){
    //   console.error('1.5setSearchMode here',state.propTmpFilter,JSON.stringify(state.propTmpFilter))
    // }
    var { displayVal, ltp, src, ptype, type, functions, saletps, saleDesc, dom, oh, ptype2 } = searchMode;
    var curFilterVal = this.state.propTmpFilter[filterField];
    // if(state.propTmpFilter && state.propTmpFilter.hasOwnProperty('undefined')){
    //   console.error('1.8setSearchMode here',state.propTmpFilter,JSON.stringify(state.propTmpFilter))
    // }
    state.propTmpFilter.cmstn = '';
    var defaultValue = this.getPropDefaultFilter()[filterField];
    if (filterField) {
      let isFilterValEqual = filterVal == curFilterVal;
      let isSoldK = (searchModeKey == 'Sold' || searchModeKey == 'Leased');//
      let alreadSoldTag = this.isSoldCondition();
      let isSoldButDiffDays = alreadSoldTag && !isFilterValEqual;
      let curFilterHasVal = curFilterVal;
      if (isSoldK && alreadSoldTag && curFilterVal == '') {
        curFilterHasVal = true
      }
      if (curFilterHasVal && (isFilterValEqual || isSoldButDiffDays)) {
        state.propTmpFilter[filterField] = defaultValue;
        if (displayVal) {
          state.propTmpFilterVals[filterField] = '';
        }
        if (isSoldK) {
          delete state.propTmpFilter.saleDesc;
          saleDesc = ''
        }
      } else {
        state.propTmpFilter[filterField] = filterVal;
        if (displayVal) {
          state.propTmpFilterVals[filterField] = l10n(displayVal);
        }
      }
    }
    // if(state.propTmpFilter && state.propTmpFilter.hasOwnProperty('undefined')){
    //   console.error('2.3setSearchMode here',state.propTmpFilter,JSON.stringify(state.propTmpFilter))
    // }
    if (oh === false) {
      state.propTmpFilter.oh = false;
    }
    if (ltp) {
      state.propTmpFilter.ltp = ltp;
    } else {
      state.propTmpFilter.ltp = '';
    }
    if (src) {
      state.propTmpFilter.src = src;
    }
    if (ptype) {
      // console.log('====ptype1:',state.propTmpFilter.ptype)
      state.propTmpFilter.ptype = ptype;
      eventEmitter.emit('map.props.ptype', ptype)
    }
    if (Array.isArray(ptype2) && ptype2.length == 0) {
      state.propTmpFilter.ptype2 = [];
      state.propTmpFilterVals.ptype2 = [];
      state.quickFilter.ptype2 = []
    }
    // if (type) {
    //   state.propTmpFilter.type = type;
    // } else {
    //   state.propTmpFilter.type = '';
    // }
    if (saletps) {
      if (saletps[saletp]) {
        state.propTmpFilter.saletp = saletp;
        state.propTmpFilter.ltp = saletps[saletp].ltp;
        state.propTmpFilter.cmstn = saletps[saletp].cmstn;
      }
    }
    // console.log('====ptype:1.5',state.propTmpFilter.ptype)
    if (searchMode.saletp) {
      state.propTmpFilter.saletp = searchMode.saletp;
      // state.searchModeSaletp = 'lease';
    }
    // if(state.propTmpFilter && state.propTmpFilter.hasOwnProperty('undefined')){
    //   console.error('2.5setSearchMode here',state.propTmpFilter,JSON.stringify(state.propTmpFilter))
    // }

    // console.log('====ptype2:',state.propTmpFilter.ptype)
    if (saleDesc) {
      state.propTmpFilter.saleDesc = saleDesc;
    } else {
      state.propTmpFilter.saleDesc = this.state.propTmpFilter.saleDesc;
    }
    if (dom != null) {
      state.propTmpFilter.dom = dom;
      state.quickFilter.dom = dom;
    } else {
      // state.propTmpFilter.dom = this.state.propTmpFilter.dom;
    }
    await this.setSaleDesc(
      state.searchModeSaletp || this.state.searchModeSaletp,
      state.propTmpFilter.dom,
      state.propTmpFilter.saleDesc
    );
    // this.setState(state,()=>{
    // })
    // if(state.propTmpFilter && state.propTmpFilter.hasOwnProperty('undefined')){
    //   console.error('3setSearchMode here',state.propTmpFilter,JSON.stringify(state.propTmpFilter))
    // }
    // console.log('====ptype3:',state.propTmpFilter.ptype)
    // console.log('++++setSearchMode setState',state.propTmpFilter.ptype,state)
    await this.setStateAsync(state);
    // console.log('++++setSearchMode setState',this.state.propTmpFilter.ptype)

    // console.log('_______filterField,filterVal',filterField,filterVal,' propTmpFilter[field]=',state.propTmpFilter[filterField],'state.saleDesc=',state.propTmpFilter.saleDesc)
    eventEmitter.emit('map.clearModals', { src: 'mapSearch', backdrop: false })
    if (functions) {
      // for(const f of functions){
      //   var {nm,params} = f;
      //   if(nm == 'ptypeSelect'){
      //     this.ptypeSelect(params)
      //   }
      // }
      functions.forEach(async (f) => {
        var { nm, params } = f;
        if (nm == 'ptypeSelect') {
          await this.ptypeSelect(params, m)
        }
        // if (params) {
        //   await this[nm](params);
        // } else {
        //   await this[nm]();
        // }
      });
      return;
    }
    // if (this.isPtypeProject()) {
    //   // resize map if it is precon mode
    //   setTimeout(() => {
    //     // this.mapObj.resized();
    //     this.map.onRegionChangeComplete();
    //   }, 1);
    // }
    // console.log('setSearchMode end!')
    // console.log('====ptype4:',this.state.propTmpFilter.ptype)
    if (!m.noSearch) {
      // this.toggleSaletpSelect();
      // console.log('setSearchMode doSearch1!',this.state.propTmpFilter.ptype)
      this.doSearch({ clear: true });
    }
  }
  isPtypeProject() {
    return this.state.propTmpFilter.ptype == 'Project';
  }
  isSameSearchMode(mode) {
    var saletp = this.state.propTmpFilter.saletp;
    var isSameMode = (saletp == this.state.searchModeSaletp) && (mode.k == this.curSearchMode.k) && !mode.v;
    // if (true||isSameMode) {
    //   console.log(
    //     'Same search mode saletp=',saletp,
    //     ' searchModeSaletp=',this.state.searchModeSaletp,
    //     ' mode=',mode,
    //     ' curSearchMode.k=',this.curSearchMode.k
    //   );
    // }
    return isSameMode;
  }
  async setSaleDesc(saletp, dom, saleDesc) {
    // var saletp = tp;
    // console.log('++++setSaleDesc',saletp,dom,saleDesc)
    // var {dom,saleDesc} = this.propTmpFilter;
    let soldOnly = this.state.propTmpFilter.soldOnly;
    if (dom == null) {
      dom = this.state.propTmpFilter.dom;
    } else if (dom == '') {
      soldOnly = false
    }
    if (saleDesc == null) {
      saleDesc = this.state.propTmpFilter.saleDesc;
    }
    if (dom || saleDesc) {
      try {
        dom = parseInt(dom);
        if (dom < 0 || /sold|leased/i.test(saleDesc)) {
          saletp += '-';
        }
      } catch (error) {
        console.error('dom can not be parse: ', dom);
      }
    }
    var saletps = {
      'sale': { saleDesc: 'Sale', displayVal: l10n('For Sale') },
      'sale-': { saleDesc: 'Sold', displayVal: l10n('For Sale') },
      'lease': { saleDesc: 'Rent', displayVal: l10n('For Rent') },
      'lease-': { saleDesc: 'Leased', displayVal: l10n('For Rent') }
    }
    var { saleDesc, displayVal } = saletps[saletp];
    // console.log('xxxxxxxsetSaleDesc',saletp,saleDesc,displayVal)
    let state = {
      propTmpFilter: {
        ...this.state.propTmpFilter,
        saleDesc,
        soldOnly,
      },
      propTmpFilterVals: {
        ...this.state.propTmpFilterVals,
        saleDesc,
        saletp: displayVal
      }
    }
    // console.log('++++setSaleDesc',' saletp=',saletp,' dom=',dom,' saleDesc=',state.propTmpFilter.saleDesc)
    // if(state.propTmpFilter && state.propTmpFilter.hasOwnProperty('undefined')){
    //   console.error('4xxxxxxxsetSaleDesc here',state.propTmpFilter,JSON.stringify(state.propTmpFilter))
    // }
    await this.setStateAsync(state);
    this.state.propTmpFilter.saleDesc = saleDesc;
    this.state.propTmpFilterVals.saleDesc = saleDesc;
    this.state.propTmpFilterVals.saletp = displayVal;
    // console.log('++++setSaleDesc',' saletp=',this.state.propTmpFilterVals.saletp,' dom=',this.state.propTmpFilter.dom,' saleDesc=',this.state.propTmpFilter.saleDesc)
    // this.setState(state,()=>{
    //   let state = this.state;
    //   if(state.propTmpFilter && state.propTmpFilter.hasOwnProperty('undefined')){
    //     console.error('4xxxxxxxsetSaleDesc here',state.propTmpFilter,JSON.stringify(state.propTmpFilter))
    //   }
    // });
  }
  // when list mode
  addDefaultCity() {
    var { city, prov } = this.state.propTmpFilter;
    if (!(city && prov)) {
      var userCity = this.dispVar.userCity;
      this.state.propTmpFilter.city = userCity.o;
      this.state.propTmpFilter.prov = userCity.p;
      this.state.propTmpFilterVals.city = userCity.n;
      this.setState({})
    }
  }
  // TODO: when have list
  // listScrolled(e){
  //   var self = this, wait = 400, bbox;
  //   if (!self.scrollElement) {
  //     return;
  //   }
  //   if (this.filterOnTop && this.isSelectedTag) {
  //     //keep filterOnTop stay if change tags
  //     if (this.propTmpFilter.type != 'projects') {
  //       self.scrollElement.scrollTop = 130;
  //     }
  //     self.isSelectedTag = 0;
  //     return;
  //   }
  //   // console.log(self.scrollElement.scrollTop);
  //   if (self.scrollElement.scrollTop > self.scrollThreshold) {
  //     self.filterOnTop = 1;
  //   } else {
  //     self.filterOnTop = 0;
  //   }
  //   if (!self.waiting) {
  //     self.waiting = true;
  //     setTimeout(function () {
  //       self.waiting = false;
  //       var element = self.scrollElement;
  //       //update 460 to 40 since if ptype has one prop, it will search next page automatically
  //       if (element.scrollHeight - element.scrollTop <= (element.clientHeight + 40)) {
  //         // console.log('self.cntTotal',self.cntTotal,self.items.length)
  //         // >= self.items.length
  //         if ((self.cntTotal != 0) && self.dispVar.isApp && !self.loading) {
  //           // console.log('real load more');
  //           if (self.appMapMode == 'map') {
  //             var opt = {};
  //             // console.log(self.viewMode,self.propTmpFilter.city);
  //             if (self.viewMode == 'list' && !self.propTmpFilter.city && self.mapObj && self.mapObj.getBounds) {
  //               var bnds = self.mapObj.getBounds();
  //               if (bnds) {
  //                 var ne = bnds.getNorthEast();
  //                 var sw = bnds.getSouthWest();
  //                 if (typeof(ne.lng) == 'function') {
  //                   bbox = [
  //                     sw.lng(), sw.lat(),
  //                     ne.lng(), ne.lat()
  //                   ]
  //                 } else {
  //                   bbox = [
  //                     sw.lng, sw.lat,
  //                     ne.lng, ne.lat
  //                   ]
  //                 }
  //               }
  //             }
  //             // console.log(opt);
  //             self.showMore(opt);
  //           } else {
  //             self.pgNum+=1;
  //             self.getFavs();
  //           }
  //         }
  //       }
  //     }, wait);
  //   }
  // }
  setStateAsync(state) {
    // console.log('setStateAsync',JSON.stringify(state.propTmpFilter))
    // if(state.propTmpFilter && state.propTmpFilter.hasOwnProperty('undefined')){
    //   console.error('state undefined',state.propTmpFilter,JSON.stringify(state.propTmpFilter))
    // }
    return new Promise((resolve) => {
      this.setState(state, resolve)
    });
  }
  // first line of buttons
  async setSaleTp(tp, doSearch) {
    // console.log('setSaleTp triggered!!')
    // let wording;
    // if (tp == 'lease') {
    //   wording = l10n('For Rent');
    // } else {
    //   wording = l10n('For Sale');
    // }
    // let isDoSearch = false;
    // if (this.state.propTmpFilter.saletp != tp) {
    //   isDoSearch = true;
    // }
    // this.state.propTmpFilter.saletp = tp;
    // if(src){
    //   this.state.propTmpFilter.src = src;
    // }
    // this.state.propTmpFilterVals.saletp = wording;
    // this.setSearchModeSaletp(tp);
    // this.setState({showQuickFilter:false},()=>{
    //   if(isDoSearch){
    //     this.doSearch();
    //   }
    // });
    let state = {}
    this.isSelectedTag = 1;
    var { ptype, saletp } = this.state.propTmpFilter;
    if ((saletp == tp) && this.isSameSearchMode({ k: ptype })) {
      return;
    }
    await this.resetFilter({ keepBnds: true });
    if (this.viewMode == 'list') {
      this.addDefaultCity();
    }
    // var searchModesChange = {
    //   Project:'Residential',
    //   Assignment:'Landlord',
    //   Landlord:'Assignment'
    // }
    // var searchModeChange = searchModesChange[ptype];
    // if (searchModeChange) {
    //   console.log('call setSearchMode')
    //   await this.setSearchMode({k:searchModeChange});
    // } else {
    //   await this.setSaleDesc(tp);
    // }
    state = {
      propTmpFilter: {
        ...this.state.propTmpFilter,
        saletp: tp
      }
    }
    this.state.propTmpFilter.saletp = tp;
    // state.pgNum = 0;
    await this.setStateAsync(state)
    await this.setSearchModeSaletp(tp);
    await this.setSaleDesc(tp);
    eventEmitter.emit('map.props.ptype', 'Residential')
    eventEmitter.emit('map.clearModals', { src:'mapSearch',backdrop: false })
    // if (ptype == 'Project') {
    //   state.propTmpFilter.ltp = ''
    // }
    // if (ptype == 'Exclusive') {
    //   if (tp == 'sale') {
    //     state.propTmpFilter.ltp = 'exlisting';
    //   }
    //   if (tp == 'lease') {
    //     state.propTmpFilter.ltp = 'rent';
    //     state.propTmpFilter.cmstn = true;
    //   }
    // }
    // console.log('++++nornal setState',state)
    // console.log('filter=',this.state.propTmpFilter.ptype2)
    // if not mls mode, switch between assignment <-> landlord
    // TODO: fix this
    if (this.state.appmode !== 'mls') {
      if (tp == 'sale') {
        await this.setSearchMode({ k: 'Assignment', noSearch: false });
      } else {
        await this.setSearchMode({ k: 'Landlord', noSearch: false });
      }
      return;
    }
    if (doSearch) {
      // console.log('setSaleTp doSearch2!')
      this.doSearch({ clear: true });
    }
  }
  getLongMappedValueDom(cur, isShort) {
    // var cur = this.propTmpFilter.dom;
    var itras = this.state.dispVar.domFilterVals
    if (isShort) {
      itras = this.state.dispVar.domFilterValsShort
    }
    if (!itras) {
      return null;
    }
    for (let i of itras) {
      if (i.k == cur) {
        return i.v;
      }
    }
    return null;
  }
  getDomYearValueInLoop(yearDom = '-2022') {
    var itras = this.state.dispVar.domYearFilterVals
    if (!itras) {
      return null;
    }
    let b = Math.abs(parseInt(yearDom))
    for (let i of itras) {
      let a = Math.abs(parseInt(i.k))
      if (a == b) {
        return i;
      }
    }
    return null;
  }
  async selectSoldFilterVal(t) {
    //  propTmpFilter.dom = t.k;
    //  propTmpFilterVals.dom = this.getLongMappedValueDom(t.k);
    //  this.setState({propTmpFilter,propTmpFilterVals,showQuickFilter:false},()=>{
    //    this.doSearch();
    //  });
    if (!t.k) { return }
    let { saletp } = this.state.propTmpFilter;
    await this.resetTags({ except: 'ptype' });
    let propTmpFilter = { ...this.state.propTmpFilter };
    let propTmpFilterVals = { ...this.state.propTmpFilterVals };
    propTmpFilter.dom = t.k;
    propTmpFilterVals.dom = this.getLongMappedValueDom(t.k);
    propTmpFilter.saleDesc = (saletp == 'sale') ? 'Sold' : 'Leased';
    //  this.state.propTmpFilterVals.dom = dom;
    //  this.toggleQuickFilter()
    propTmpFilter.soldOnly = true;
    this.setState({ showQuickFilter: false, propTmpFilter }, () => {
      eventEmitter.emit('map.clearModals', { src: 'mapSearch', backdrop: false })
      // console.log('selectSoldFilterVal do search3 ')
      this.doSearch({ clear: true })
    })
  }
  searchSchoolPropStrParam = (p) => {
    if (typeof (p) == 'string') {
      var d = urlParamToObject(p);
      // console.log(d);
      d.showMarker = false;
    } else {
      d = p;
    }
    // return;
    this.searchSchoolProp(d);
  }
  async searchSchoolProp(d) {
    var self = this;
    if (self.viewMode !== 'map') {
      self.viewMode = 'map'
    }
    await self.setSaleTp(d.saletp)
    var lat, lng;
    if (d.lat) {
      lat = parseFloat(d.lat);
      lng = parseFloat(d.lng);
    } else if (d.loc) {
      lat = d.loc[0];
      lng = d.loc[1];
    }
    // toggleModal('schoolDetailModal','close');
    if (lat && lng) {
      if (d.dom) {
        await self.selectSoldFilterVal({ k: '-90', v: '3 month' });
        // self.map.moveToRecord({lat:d.loc[0],lng:d.loc[1]});
        // self.map.setCenterAndZoom({lat:d.loc[0],lng:d.loc[1],hMarker:1,showMarker:1}, 16);
      }// else {
      var opt = { lat: lat, lng: lng, hMarker: 1, showMarker: 1 }
      if (d.showMarker === false) {
        opt.showMarker = false;
      }
      // console.log('++++',d)
      if (d.cMarker) {
        delete opt.hMarker
        opt.cMarker = 1;
      }
      self.map.setCenterAndZoom(opt, { zoom: 16 });
      // }
    }
    // if (d.hlProp) {
    //   var st = {
    //     hlProp:d.hlProp,
    //     hlPropKey:d.hlPropKey
    //   }
    //   this.setState(st);
    // }
    // self.clearModals();
    // console.log('searchSchoolprop dosearch5')
    self.doSearch();
  }
  propChanged(e) {
    // console.log('propChanged');
    var prop = this.state.curProp;
    var self = this;
    var propId;
    if (/^RM/.test(prop.id)) {
      propId = prop.id;
    } else {
      propId = prop._id;
    }
    var base = "/1.5/prop/detail/inapp?lang=";
    if (prop.isProj || prop.tp1) {
      base = '/1.5/prop/projects/detail?inframe=1&lang='
    }
    if (Object.keys(prop).length < 2) {
      // console.error('Error: '+JSON.stringify(prop));
    }
    var url = base + self.state.dispVar.lang + "&id=" + propId + '&mode=' + self.appMapMode;
    // console.log(url);
    if (this.state.propTmpFilter.remarks) {
      url += '&keywords=' + this.state.propTmpFilter.remarks
    }
    var cb = (val) => {
      // console.log('prop change cb val: ',val);
      if (val == ':cancel') {
        return;
      }
      // let state = {};
      // let nearbyOrigProp = RMStorage.getCacheItem(SYSTEM.NEARBY_PROP_ITEM);
      // state.nearbyOrigProp = nearbyOrigProp
      // let savedSearchCond = RMStorage.getCacheItem(SYSTEM.CURRENT_SAVED_SEARCH);
      // console.log('+++++',savedSearchCond)
      // state.savedSearchCond = savedSearchCond
      // self.setState(state)
      if (/^redirect|^cmd-redirect:/.test(val)) {
        // return window.location = val.split('redirect:')[1]
        var url = val.split('redirect:')[1];
        // console.log('close and redirect from propDetail: '+url)
        return self.closePopup(url);
        // return window.location = url;
        // return;
      }
      try {
        // var val = 'loc=43.5723199141038,-79.5785565078259&zoom=15&saletp=lease';
        var d = urlParamToObject(val);
        // Alert.alert(JSON.stringify(d));
        // window.bus.$emit('school-prop', d);
        self.searchSchoolProp(d);
      } catch (e) {
        // console.error(e);
      }
    }
    var opt = {
      hide: false,
      sel: '#callBackString',
      tp: 'pageContent',
      // noClose:true,
      title: l10n('RealMaster'),
      // toolbar:false,
      url: serverDomainIns.getFullUrl(url),
    }
    eventEmitter.emit("app.message", { msg: JSON.stringify(opt), cb: cb });
  }
  getCityInfo() {
    var self = this;
    let params = {
      city: this.state.propTmpFilter.city,
      prov: this.state.propTmpFilter.prov,
      cmty: this.state.propTmpFilter.cmty,
      saletp: this.state.propTmpFilter.saletp
    }
    // v6.1.3 autocomplete did not pass prov, ignore req
    if (!params.prov) {
      return;
    }
    self.$http.post('/1.5/prop/stats/briefCityInfo', params).then(
      function (ret) {
        ret = ret.data;
        if (ret.e || ret.err) {
          window.bus.$emit('flash-message', (ret.e || ret.err));
        } else {
          self.stat = ret.stat;
        }
      },
      function (ret) {
        console.error("server-error");
        // RMSrv.dialogAlert( "server-error" );
      }
    );
  }
  async resetTags(opt = {}) {
    let state = {
      propTmpFilter: {
        ...this.state.propTmpFilter,
      },
      propTmpFilterVals: {
        ...this.state.propTmpFilterVals,
      }
    };
    if (opt.except == 'ptype') {
    } else {
      state.propTmpFilter.src = 'mls';
      state.propTmpFilter.ptype = 'Residential';
      state.propTmpFilterVals.ptype = l10n('Residential');
      this.curSearchMode = { k: 'Residential' };
    }
    //clear feature tags
    var tagFields = ['ltp', 'cmstn', 'dom', 'status', 'soldOnly', 'oh', 'sch', 'sold', 'lpChg', 'neartype', 'soldLoss'];
    let propTmpFilter = this.getPropDefaultFilter();
    tagFields.forEach((tagField) => {
      let tmp = propTmpFilter[tagField];
      if (tmp == null) {
        tmp = ''
      }
      state.propTmpFilter[tagField] = tmp;
      // state.propTmpFilter[tagField] = '';
      state.propTmpFilterVals[tagField] = tmp;
    })
    this.setStateAsync(state)
  }
  getSearchMode(opt = {}) {
    // get search mode from propTmpFilter
    let statFilterVal = this.state.propTmpFilter;
    if (opt.propTmpFilter) {
      statFilterVal = opt.propTmpFilter
      delete opt.propTmpFilter
    }
    let { ptype, ltp, saletp, cmstn } = statFilterVal
    let k = ptype;
    if (ltp) {
      if (ltp == 'assignment') {
        k = 'Assignment';
      } else if (ltp == 'exlisting') {
        k = 'Exclusive';
      } else if (ltp == 'rent') {
        if (saletp == 'lease') {
          k = 'Landlord';
          if (cmstn) {
            k = 'Exclusive';
          }
        }
      }
    }
    // console.log('getSearchMode',k)
    return Object.assign(opt, { k, skipSearchModeCheck: true, noSearch: true });
    return { k, noSearch: true };
  }
  // given bbox, calc latlng of 4 points
  getLatLngOfDefault4PointPoly(bbox) {
    let center = [(bbox[0] + bbox[2]) / 2, (bbox[1] + bbox[3]) / 2]
    let delta = Math.max(bbox[2] - bbox[0], bbox[3] - bbox[1])
    let pointDistance = delta / 6
    let point1 = [center[1] - pointDistance, center[0] - pointDistance]
    let point2 = [center[1] + pointDistance, center[0] - pointDistance]
    let point3 = [center[1] + pointDistance, center[0] + pointDistance]
    let point4 = [center[1] - pointDistance, center[0] + pointDistance]
    return [point1, point2, point3, point4, point1].flat()
  }
  // draw or calcel a default polygon on map
  drawDefaultPoly() {
    // 1. clear current drawing and clear
    let savedSearchCond = this.state.savedSearchCond;
    let propTmpFilter = { ...this.state.propTmpFilter };
    let lastSearch = this.lastSearch;
    if (!savedSearchCond && !lastSearch) {
      return
    }
    if (savedSearchCond && savedSearchCond.bnds) {
      savedSearchCond.bnds = null
      savedSearchCond.bndsTmp = null
      propTmpFilter.bnds = null
      if (savedSearchCond.isNew) {
        savedSearchCond = null
      }
      // this.setDrawActive(true)
      this.setState({ savedSearchCond, propTmpFilter, bndsDragHistory: [], polygonPoints: [], isDrawActive: false, isNewSearch: false })
    } else {
      // 2. create/draw default polygon
      let isNewSearch;
      let mapBnds = this.lastBbox;
      let bnds = this.getLatLngOfDefault4PointPoly(mapBnds);
      if (savedSearchCond) {
        savedSearchCond.bnds = bnds
        isNewSearch = false
      } else {
        savedSearchCond = {
          title: l10n('Save Search & Watched Area'),
          desc: l10n('Quick search and get notified'),
          bnds,
          isNew: true
        }
        isNewSearch = true
      }
      propTmpFilter.bnds = bnds
      // console.log('drawDefaultPoly',bnds)
      let bndsDragHistory = []
      // bndsDragHistory.push(bnds)
      let polygonPoints = this.getPolygonPoints(bnds)
      // console.log('++++++',savedSearchCond.title,l10n('Save Search & Watched Area'))
      this.setState({
        tracksViewChanges: true,
        savedSearchCond, propTmpFilter, bndsDragHistory,
        polygonPoints, isDrawActive: true, isNewSearch
      })
      this.trackOff()
    }
    this.doSearch({ clear: true })
  }
  // 展示高级检索并且获取返回值
  async showAdvFilter(opt = {}) {
    // console.log('show filter in new page');
    this.isSelectedTag = 1;
    var state = {
      propHalfDetail: false,
      quickFilterMode: '',
      halfDrop: false,
      showTitleTypeSelect: false,
    }
    var self = this;
    var cb = async (val) => {
      if (val == ':cancel') {
        // console.log('canceled');
        return;
      }
      try {
        // console.log('showAdvFilter cb here',val)
        var d = urlParamToObject(val);
        var ret = parseSerializedFilter(d);
        console.log('adv search: ', ret);
        eventEmitter.emit('map.clearModals', { src: 'mapSearch', backdrop: false })
        // eventEmitter.emit(SYSTEM.EVENT_CLEAR_FEATURE_PANEL,{});
        ret.state.searchModeSaletp = ret.state.propTmpFilter.saletp || self.state.propTmpFilter.saletp;
        ret.state.showQuickFilter = false;
        // console.log('+++++advRet: ',ret.state.propTmpFilter)
        if (ret.state.appmode !== this.state.appmode) {
          // console.log('+++++diff appmode',ret.state.appmode)
          let nextMode = ret.state.appmode;
          ret.state.highlightTextColor = await getColor('highlightText', nextMode)
          ret.state.highlightBgColor = await getColor('highlightBg', nextMode)
          // let opt = {store:1}; //{store:1}
          // RMStorage.setCacheItem('appmode',ret.state.appmode,opt)
          eventEmitter.emit(Constants.ChangeAppMode, { val: nextMode, mapSwitch: true });
        }
        let savedSearchCond = storageIns.getCacheItem(Constants.CurrentSavedSearch);
        if (savedSearchCond) {
          // console.log('+++++savedSearchCond',savedSearchCond)
          if (savedSearchCond.k && savedSearchCond.k.bnds) {
            // console.log('+++++savedSearchCond bnds',savedSearchCond.title)
            savedSearchCond.bnds = savedSearchCond.k.bnds
            let now = new Date()
            let polygonPoints = this.getPolygonPoints(savedSearchCond.bnds, savedSearchCond.title + now.getSeconds())
            if (isValidPolygonBnds(savedSearchCond.bnds)) {
              // fit map to this bnds, calc center and delta, bnds is array of points
              // this.map.fitToCoordinates(coordsAll)
              let { center, delta } = this.map.calcCenterAndDelta(savedSearchCond.bnds)
              this.map.setCenterAndZoom({ lat: center.lat, lng: center.lng }, { delta })
            }
            ret.state.polygonPoints = polygonPoints
            ret.state.isNewSearch = false
            this.setDrawActive(true)
          } else if (savedSearchCond.k.bbox) {
            this.setDrawActive(false)
            self.map.fitToBBox(ret.opt.bbox)
          }
          ret.state.bndsDragHistory = [] //[savedSearchCond.bnds]
          ret.state.savedSearchCond = savedSearchCond
          this.setShowDrawPolygon(true)
          // console.log('------',ret.state.savedSearchCond)
          // let lat = (bbox[1]+bbox[3])/2;
          // let lng = (bbox[0]+bbox[2])/2;
          // let delta = bbox[3]-bbox[1];
          // if(self.map){
          //   self.map.setCenterAndZoom({lat:lat,lng:lng},{delta})
          // } else {
          //   console.error('no self.map')
          // }
          // 用户点击了画图后打开高级检索
        } else if (self.state.savedSearchCond && self.state.savedSearchCond.bnds) {
          // let propTmpFilter = {...this.state.propTmpFilter};
          // console.log('------state saved search condition',self.state.savedSearchCond)
          ret.state.propTmpFilter.bnds = self.state.savedSearchCond.bnds;
        } else if (ret.opt && ret.opt.bbox) {
          // console.log('!!!!advRet bbox: ',ret.opt)
          self.map.fitToBBox(ret.opt.bbox)
        }
        let nearbyOrigProp = storageIns.getCacheItem(Constants.NearbyPropItem);
        ret.state.nearbyOrigProp = nearbyOrigProp
        await self.setStateAsync(ret.state)
        // NOTE: polygon has bug for fill color, need to set it again
        // see: https://github.com/react-native-maps/react-native-maps/issues/2698
        // see: https://github.com/react-native-maps/react-native-maps/issues/3057
        if (this.polygon) {
          this.polygon.setNativeProps({ fillColor: 'rgba(224,49,49,0.2)' });
        }
        // self.state.searchModeSaletp = self.state.propTmpFilter.saletp;
        // console.log('showAdvFilter cb here2',ret)
        // try{
        //   await self.setSearchMode(self.getSearchMode(ret.opt));
        // } catch(e){
        //   console.error(e)
        // }
        self.curSearchMode = self.getSearchMode(opt);
        // console.log('ret.opt',ret.opt)
        // await self.setStateAsync(ret.state)
        self.doSearch(ret.opt);
        // console.log('showAdvFilter cb here5')

        return
        self.setState(ret.state, () => {
          // console.log('after setstate')
          // let bbox;
          // if (ret && ret.opt && (bbox=ret.opt.bbox)) {
          //   let lat = (bbox[1]+bbox[3])/2;
          //   let lng = (bbox[0]+bbox[2])/2;
          //   let delta = bbox[3]-bbox[1];
          //   if(self.map){
          //     self.map.setCenterAndZoom({lat:lat,lng:lng},delta)
          //   } else {
          //     console.error('no self.map')
          //   }
          // }
          self.doSearch(ret.opt);
          // self.getCityInfo()
        });
      } catch (e) {
        console.error(e);
      }
    }
    var url = `/1.5/mapSearch/advFilter?viewmode=map&appmode=${this.state.appmode}&`
    // var propTmpFilter = {...this.state.propTmpFilter}
    // propTmpFilter.bbox = this.lastBbox
    let data = { bbox: this.lastBbox }
    if (opt.saveThisSearch) {
      data.saveThisSearch = true
    }
    url += serializeData({
      prefix: 'k',
      data: this.state.propTmpFilter
    }) + '&' + serializeData({
      prefix: 'v',
      data: this.state.propTmpFilterVals
    }) + '&' + serializeData({
      prefix: 'opt',
      data: data
    })
    // console.log('+++++adv URL',this.state.propTmpFilter,url)
    var opt = {
      hide: false,
      sel: '#callBackString',
      tp: 'pageContent',
      barColor: await getColor('commonBarColor', this.state.appmode),
      textColor: await getColor('commonBarText'),
      title: l10n('Advanced Search'),
      // toolbar:false,
      url: serverDomainIns.getFullUrl(url),
    }
    eventEmitter.emit("app.message", { msg: JSON.stringify(opt), cb: cb });
  }
  unmount() {
    super.unmount();
    var self = this;
    clearTimeout(self.tipTimeout);
    clearTimeout(self.doSearchTimeout);
    clearTimeout(self.initRegionChangeSearch);
    // this.unsetNearbyPropItem()
    eventEmitter.removeListener("map.setFilterVal", this.setFilterVal.bind(this));
    eventEmitter.removeListener("map.showListView", this.showListView);
    eventEmitter.removeListener("map.searchSchoolProp", this.searchSchoolPropStrParam);
    eventEmitter.removeListener('map.props.city', this.setProjectCity);
    eventEmitter.removeListener(Constants.MapProp, this.searchWithinShape);
    // eventEmitter.removeListener(SYSTEM.EVENT_CLEAR_FEATURE_PANEL,this.hideSoldDomSelect.bind(this));
    // eventEmitter.removeListener(SYSTEM.MAP_FEATURE_PROP_STOP,this.setSelectedStop);
  }
  isActiveOn(type) {
    // let isActive = false;
    // let ret = /^-/.test(this.state.propTmpFilter.dom);
    // if(/sold/.test(type)){
    //   isActive = ret
    // } else {
    //   isActive = !ret
    // }
    // console.log('======',isActive,ret,type)
    let ret = false;
    if (type == 'sale') {
      ret = !this.isSoldCondition()
    } else if (type == 'sold') {
      ret = this.isSoldCondition()
    } else if (/Assignment|exclusive|landlord/ig.test(type)) {
      ret = (this.curSearchMode.k == type)
    }
    // console.log('ret = ',ret,'type=',type)
    if (ret) {
      return {
        color: this.state.highlightTextColor,
        fontWeight: 'bold',
        backgroundColor: this.state.highlightBgColor
      }
    }
    return {}
  }
  getShortMappedValueDom() {
    var domYear = this.state.propTmpFilter.domYear;
    if (domYear) {
      return (domYear + '').replace(/^-/, '')
    }
    var cur = this.state.propTmpFilter.dom || '';
    // console.log('++++++',cur,this.state.dispVar.domFilterValsShort)
    let itras = this.state.dispVar.domFilterValsShort || [];
    cur = (cur + '').replace('-', '')
    // console.log('cur',cur,itras,this.state.dispVar.domFilterValsShort)
    for (let i of itras) {
      let k = (i.k + '').replace('-', '')
      if (k == cur) {
        return i.sv || i.v || i.vv;
      }
    }
    return '';
  }
  isSoldCondition() {
    return ['Sold', 'Leased'].includes(this.state.propTmpFilter.saleDesc);
  }
  computedOffMarketDays() {
    if (this.isSoldCondition()) {
      return this.getShortMappedValueDom();
    }
    return '';
  }
  gotoAutocomplete() {
    gotoAutocomplete({ referer: 'mapSearch' })
    // console.log('dosth!')
  }
  // render 2nd row of buttons on headers
  async switchToExclusive() {
    await this.setSearchMode({ k: 'Exclusive', noSearch: false });
  }
  async switchToAssignment() {
    await this.setSearchMode({ k: 'Assignment', noSearch: false });
  }
  async switchToLandlord() {
    await this.setSearchMode({ k: 'Landlord', noSearch: false });
  }
  unsetNearbyPropItem() {
    storageIns.setCacheItem(Constants.NearbyPropItem, null)
    this.setState({ nearbyOrigProp: null })
  }
  unsetCurrentSavedSearch() {
    storageIns.setCacheItem(Constants.CurrentSavedSearch, null)
    let propTmpFilter = { ...this.state.propTmpFilter };
    propTmpFilter.bnds = null
    this.setStateAsync({ savedSearchCond: null, bndsDragHistory: [], propTmpFilter, polygonPoints: [] })
    this.doSearch()
    this.setDrawActive(false)
    // this.setShowDrawPolygon(false)
  }
  // reset modified search condition to saved search
  applyCurrentSavedSearch() {
    let savedSearchCond = storageIns.getCacheItem(Constants.CurrentSavedSearch);
    // console.log(savedSearchCond)
    if (!savedSearchCond) {
      return null
    }
    this.readFilterFromStorage({ discardCurConditions: 1, doSearch: true })
  }
  // open adv fileter modal
  addNewSearch() {
    // if not loggedin redirect to login
    var showLogin = false;
    // console.log(this.state.dispVar)
    if (!this.state.dispVar.isLoggedIn) {
      showLogin = true;
    }
    if (showLogin) {
      return this.closePopup('/1.5/user/login')
    }
    this.showAdvFilter({ saveThisSearch: true })
  }
  //post to backend
  updateCurrentSavedSearch(opt = {}) {
    // if new search, create new saved search using this.lastSearch
    // if(opt.isNewSearch){
    //   this.saveSearch()
    //   return
    // }
    let savedSearchCond = storageIns.getCacheItem(Constants.CurrentSavedSearch);
    if (!savedSearchCond) return;
    let { k, r, idx, ts } = savedSearchCond;
    let lastSearch = this.lastSearch;
    if (lastSearch) {
      k = lastSearch.q,
        r = lastSearch.readable
    }
    let self = this;
    // bnds is array of latlng forming a polygon
    let bnds = savedSearchCond.bnds || this.state.savedSearchCond.bnds;
    if (bnds) {
      k.bnds = savedSearchCond.bnds
    }
    // console.log('updateCurrentSavedSearch',k,r,idx,ts)
    mainRequest({
      url: '/1.5/props/updateSavedSearch',
      method: 'POST',
      data: { k, r, idx, ts },
    }).then(ret => {
      if (ret.err) {
        // return RMSrv.dialogAlert(err||ret.err);
        console.error(err || ret.err)
      }
      if (!ret.ok) {
        return RMSrv.dialogAlert(err || ret.err);;
      }
      var str = '&' + serializeData({
        prefix: 'k',
        data: self.state.propTmpFilter
      }) + '&' + serializeData({
        prefix: 'v',
        data: self.state.propTmpFilterVals
      })
      storageIns.setCacheItem(Constants.MapsearchFilter, str, false, (err, ret) => {
        if (err) {
          return self.flashMessage(err);
        }
      })
      // bndsDragHistory:[]
      self.setState({ hasMadeChanges: false })
      self.flashMessage(ret.msg)
    }).catch(err => {
      console.error(err)
    })
  }
  // recover polygon to last saved state in state.bndsDragHistory
  async undoDragPolygon() {
    let bndsDragHistory = this.state.bndsDragHistory;
    let propTmpFilter = { ...this.state.propTmpFilter };
    // console.log(bndsDragHistory.length,bndsDragHistory)
    if (!bndsDragHistory || !bndsDragHistory.length) {
      return null
    }
    let savedSearchCond = this.state.savedSearchCond;
    let lastBnds = [];
    // NOTE: should not =1
    if (bndsDragHistory.length > 2) {
      bndsDragHistory.pop()
      lastBnds = bndsDragHistory[bndsDragHistory.length - 1];
      savedSearchCond.bnds = lastBnds
    } else if (bndsDragHistory.length == 2) {
      // NOTE: 0 is default, 1 is new/latest
      bndsDragHistory.pop()
      lastBnds = bndsDragHistory.pop();
      savedSearchCond.bnds = lastBnds;
    }
    savedSearchCond.bnds = lastBnds;
    propTmpFilter.bnds = lastBnds;
    let polygonPoints = this.getPolygonPoints(lastBnds)
    await this.setStateAsync({ savedSearchCond, bndsDragHistory, propTmpFilter, polygonPoints })
    this.timeoutCalcHasFilterChanged()
    this.doSearch();
  }
  // render tip when has active polygon but no undo button
  _renderDragTip() {
    // console.log('_renderDragTip',this.featureOn,this.state.isDrawActive)
    if (!this.featureOn) return null;
    if (!this.state.isDrawActive) return null;
    let bndsDragHistory = this.state.bndsDragHistory;
    if (bndsDragHistory && bndsDragHistory.length) {
      return null;
    }
    let marginTop = 0;
    if (Platform.OS == 'android') {
      marginTop = -4;
    }
    return (
      <View
        key='undoBtn'
        style={{
          position: 'absolute',
          bottom: 80,
          // left: left,
          zIndex: 14,
          // flex:1,
          alignContent: 'center',
          justifyContent: 'center',
          flexDirection: 'row',
          height: 30,
          width: '100%',
          backgroundColor: 'transparent',
          borderWidth: 0,
          // shadowColor:'#d2d2d2',//'#d2d2d2',
          // shadowOffset:{width:1,height:3},
          // shadowRadius:3,
          // shadowOpacity:0.8,
          // borderColor:'#3899EC'
        }}
      >
        <View style={{
          width: 'auto',
          paddingLeft: 15,
          paddingRight: 15,
          borderRadius: 15,
          height: 30,
          flexDirection: 'row',
          backgroundColor: 'rgba(102,102,102,0.7)'
        }}>
          <Icon name="lightbulb-o"
            size={14}
            color={'white'}
            key={'tipIcon'}
            // onPress={()=>{this.applyCurrentSavedSearch()}}
            style={{
              height: 30,
              marginLeft: 0, paddingTop: 7,
              marginRight: 6,
            }}
          // hitSlop={{top: 10, bottom: 10, left: 0, right: 120}}
          />
          <Text numberOfLines={1} style={{
            alignSelf: 'center',
            marginTop,
            fontSize: 14, height: 28, paddingTop: 5,
            fontWeight: 'normal',
            color: 'white'
          }}>
            {l10n('Hold red endpoint to adjust shape')}
          </Text>
        </View>
      </View>
    )
  }
  // Undo button for polygon drawing
  _renderUndoButton() {
    if (!this.featureOn) return null;
    let bndsDragHistory = this.state.bndsDragHistory;
    if (!bndsDragHistory || !bndsDragHistory.length) {
      return null
    }
    let bubWidth = 88;
    let left = gWindowWidth / 2 - bubWidth / 2
    let marginTop = 0;
    if (Platform.OS == 'android') {
      marginTop = -4;
    }
    return (
      <TouchableOpacity
        key='undoBtn'
        onPress={() => { this.undoDragPolygon() }}
        style={{
          position: 'absolute',
          bottom: 80,
          left: left,
          zIndex: 14,
          // flex:1,
          alignContent: 'center',
          justifyContent: 'center',
          flexDirection: 'row',
          height: 30,
          width: bubWidth,
          backgroundColor: 'white',
          borderWidth: 0,
          borderRadius: 15,
          shadowColor: '#d2d2d2',//'#d2d2d2',
          shadowOffset: { width: 1, height: 3 },
          shadowRadius: 3,
          shadowOpacity: 0.8,
          // borderColor:'#3899EC'
        }}
      >
        <Icon name="rmrefresh"
          size={14}
          color={'black'}
          // onPress={()=>{this.applyCurrentSavedSearch()}}
          style={{
            height: 30,
            marginLeft: 0, paddingTop: 7,
            marginRight: 4,
            transform: [{ rotateY: '180deg' }], alignSelf: 'center'
          }
          }
        // hitSlop={{top: 10, bottom: 10, left: 0, right: 120}}
        />
        <Text numberOfLines={1} style={{
          alignSelf: 'center',
          // textAlign:'center',
          // verticalAlign:'top',
          // backgroundColor:'blue',
          marginTop,
          fontSize: 15, height: '17',
          fontWeight: 'normal',
          color: 'black'
        }}>{l10n('Undo')}
        </Text>
      </TouchableOpacity>
      // <View style={{
      //   position:'absolute',
      //   bottom: 60,
      //   left: 0,
      //   zIndex: 100,
      //   backgroundColor:'white',
      //   borderRadius: 5,
      //   padding: 5,
      //   }}>
      //   {/* <Icon name="undo"
      //     size={20}
      //     color={'black'}
      //     onPress={()=>{this.undoDragPolygon()}}
      //     style={{paddingRight:5,marginLeft:3,marginTop:11,flex:1}}
      //     // hitSlop={{top: 10, bottom: 10, left: 0, right: 120}}
      //   /> */}

      // </View>
    )
  }
  // NOTE: from saved search in advSearch, similar to pin prop
  renderSavedSearchCondition() {
    // return null;
    if (!this.featureOn) return null;
    if (this.isPtypeProject()) return null;
    // console.log('_++++++',savedSearchCond)
    let savedSearchCond = this.state.savedSearchCond;
    if (!savedSearchCond) {
      // this.setState({savedSearchCond:null})
      return null
    }

    // this.setState({savedSearchCond},()=>{
    // TODO: open edit,update cur search, close
    // console.log('++++++',this.state.savedSearchCond)
    let dispVal1 = this.state.savedSearchCond.title;
    let dispVal2 = this.state.savedSearchCond.desc;
    let hasMadeChanges = this.state.hasMadeChanges;//calcHasFilterChanged()
    let isNewSearch = this.state.isNewSearch;
    let marginTop = 0;
    if (Platform.OS == 'android') {
      marginTop = -4;
    }
    // return null
    return (
      <View style={[styles.overLayPinProp, { opacity: 1 }]} key={'savedSearchBar'}>
        <View style={{
          flex: 2,
          // backgroundColor:'red',
          paddingTop: 5, paddingLeft: 8, paddingRight: 5
        }}
          key={'savedSearchCond'}
        >
          <TouchableOpacity style={{}}
            onPress={() => { }}
          >
            <Text style={{ color: 'black', fontSize: 14 }} numberOfLines={1}>
              {dispVal1}
            </Text>
            <Text style={{ color: 'rgb(119,119,119)', fontSize: 11, paddingTop: 3, }} numberOfLines={1}>
              {dispVal2}
            </Text>
          </TouchableOpacity>
        </View>
        {isNewSearch &&
          <View style={{
            // backgroundColor:'yellow',
            width: 56,
            marginRight: 18,
            marginTop: 2,
            // height: 26,
            // flex:1,
            // flexDirection:'row',justifyContent:'flex-start'
          }}
            key={'savedSearchCondForceSaveBtn'}
          >
            <TouchableOpacity
              color={'black'}
              onPress={() => { this.addNewSearch() }}
              style={{
                // paddingLeft:5,
                // paddingRight:5,
                marginTop: 6,
                // flex:1,
                alignContent: 'center',
                justifyContent: 'center',
                height: 26,
                // width:44,
                backgroundColor: '#3899EC',
                borderWidth: 0,
                borderRadius: 2,
                // borderColor:'#3899EC'
              }}
            >
              <Text numberOfLines={1} style={{
                alignSelf: 'center',
                fontSize: 13, height: 17,
                marginTop,
                color: 'white'
              }}>{l10n('Save')}
              </Text>
            </TouchableOpacity>
          </View>
        }
        {hasMadeChanges &&
          <View style={{
            // backgroundColor:'yellow',
            width: 56,
            marginRight: 18,
            marginTop: 2,
            // height: 26,
            // flex:1,
            // flexDirection:'row',justifyContent:'flex-start'
          }}
            key={'savedSearchCondUpdateBtn'}
          >
            <TouchableOpacity
              color={'black'}
              onPress={() => { this.updateCurrentSavedSearch() }}
              style={{
                // paddingLeft:5,
                // paddingRight:5,
                marginTop: 6,
                // flex:1,
                alignContent: 'center',
                justifyContent: 'center',
                height: 26,
                // width:44,
                backgroundColor: '#3899EC',
                borderWidth: 0,
                borderRadius: 2,
                // borderColor:'#3899EC'
              }}
            >
              <Text numberOfLines={1} style={{
                alignSelf: 'center',
                fontSize: 13, height: 17,
                marginTop,
                color: 'white'
              }}>{l10n('Update')}
              </Text>
            </TouchableOpacity>
          </View>
        }
        {hasMadeChanges &&
          <View style={{
            // backgroundColor:'yellow',
            width: 26,
            marginRight: 14,
            marginTop: 2,
            // height: 26,
            // flex:1,
            // flexDirection:'row',justifyContent:'flex-start'
          }}
            key={'savedSearchCondApplyBtn'}
          >
            <Icon name="rmrefresh"
              size={17}
              color={'black'}
              onPress={() => { this.applyCurrentSavedSearch() }}
              style={{ paddingRight: 5, marginLeft: 3, marginTop: 11, flex: 1 }}
            // hitSlop={{top: 10, bottom: 10, left: 0, right: 120}}
            />
          </View>
        }
        <View style={{
          // backgroundColor:'yellow',
          width: 26,
          marginTop: 0,
          // height: 26,
          // flex:1,
          // paddingRight:10,
          marginRight: 4,
          // flexDirection:'row',justifyContent:'flex-start'
        }}
          key={'savedSearchCondCloseBtn'}
        >
          <Icon name="closeX"
            size={17}
            color={'black'}
            onPress={() => { this.unsetCurrentSavedSearch() }}
            style={{ paddingRight: 5, marginLeft: 3, marginTop: 11, flex: 1 }}
          // hitSlop={{top: 10, bottom: 10, left: 0, right: 120}}
          />
        </View>
      </View>)
    // })
  }
  // cannot have async render functions!
  // ref: https://reactjs.org/blog/2018/03/27/update-on-async-rendering.html
  renderPinProp() {
    if (!this.featureOn) return null;
    if (this.isPtypeProject()) return null;
    // console.log('_++++++',nearbyOrigProp)
    if (!this.state.nearbyOrigProp) {
      return null
    }
    nearbyOrigProp = this.state.nearbyOrigProp
    if (nearbyOrigProp.img && /^\/\//.test(nearbyOrigProp.img)) {
      nearbyOrigProp.img = 'https:' + nearbyOrigProp.img
    }
    let clickObj = {
      ids: [nearbyOrigProp._id],
      objs: [nearbyOrigProp],
      key: `${nearbyOrigProp.lat},${nearbyOrigProp.lng}`,
      lat: nearbyOrigProp.lat,
      lng: nearbyOrigProp.lng,
    }
    // let similarSearchCondition = 'similar search cond string'
    if (nearbyOrigProp) {
      let price = nearbyOrigProp.lp || nearbyOrigProp.lpr;
      if (nearbyOrigProp.saleTpTag_en !== "Delisted" && this.state.propTmpFilter.soldOnly) {
        price = nearbyOrigProp.sp || price;
      }
      let bdrms = getBdrms(nearbyOrigProp);
      let bthrms = getBthrms(nearbyOrigProp);
      if (bdrms) {
        bthrms = ' ' + bthrms
      }
      let sid = nearbyOrigProp.sid || nearbyOrigProp.id;
      let dispVal1 = nearbyOrigProp.dispVal1 || `${nearbyOrigProp.addr}, ${nearbyOrigProp.city}, ${sid}`
      let dispVal2 = nearbyOrigProp.dispVal2 || `$${propPrice(price)} • ${bdrms}${bthrms} ${nearbyOrigProp.propType}`
      return (
        <View style={styles.overLayPinProp} key={nearbyOrigProp._id}>
          <TouchableOpacity
            style={{ width: 58, height: 44 }}
            onPress={this.markerPressed(clickObj)}>
            <Image
              resizeMode='stretch'
              style={{ width: 58, height: 44 }}
              source={{ uri: nearbyOrigProp.img || nearbyOrigProp.thumbUrl }}
            />
          </TouchableOpacity>
          <View style={{
            // flex:6,
            width: gWindowWidth - 32 - 58,
            // backgroundColor:'red', paddingTop 1 android/3 ios
            paddingTop: 5, paddingLeft: 8, paddingRight: 5
          }}>
            <TouchableOpacity style={{}}
              onPress={this.markerPressed(clickObj)}
            >
              <Text style={{ color: 'black', fontSize: 13 }} numberOfLines={1}>
                {dispVal1}
              </Text>
              <Text style={{ color: 'rgb(119,119,119)', fontSize: 11, paddingTop: 1, }} numberOfLines={1}>
                {dispVal2}
              </Text>
            </TouchableOpacity>
          </View>
          <View style={{
            // backgroundColor:'yellow',
            width: 30,
            // flex:1,
            // flexDirection:'row',justifyContent:'flex-start'
          }}>
            <Icon name="closeX"
              size={17}
              color={'black'}
              onPress={() => { this.unsetNearbyPropItem() }}
              style={{ paddingRight: 5, marginLeft: 3, marginTop: 11, flex: 1 }}
            // hitSlop={{top: 10, bottom: 10, left: 0, right: 120}}
            />
          </View>
        </View>)
    }
    return null;
  }
  renderButton2() {
    // return null;
    if (!this.featureOn) return null;
    if (this.isPtypeProject()) return null;
    var strSaleRent = 'For Sale';
    var strSoldLeased = 'Sold';
    var exclusiveString = l10n('Exclusive')
    if (this.state.propTmpFilter.saletp !== 'sale') {
      strSaleRent = 'For Rent'
      strSoldLeased = 'Leased'
      exclusiveString = l10n('Exclusive', 'rental')
    }
    strSaleRent = l10n(strSaleRent);
    strSoldLeased = l10n(strSoldLeased) + ' ' + this.computedOffMarketDays();
    let marTop = 1, margin = 0;
    if (Platform.OS !== 'ios') {
      marTop = 2;
      margin = -2;
    }
    var filterIconColor = "#777";
    var filterIconNum = 0;
    let filterIcon = (
      <Icon name="rmfilter"
        size={16}
        color={filterIconColor}
        style={{ marginLeft: 5, marginTop: marTop }}
      // hitSlop={{top: 10, bottom: 10, left: 0, right: 120}}
      />
    )
    if (filterIconNum = this.hasExtraFilter()) {
      var fontSize, marginTop, textMarginTop;
      if (Platform.OS === 'ios') {
        fontSize = 15
        marginTop = 0
        textMarginTop = 0
      } else {
        fontSize = 14
        marginTop = 1
        textMarginTop = -1
      }
      filterIconColor = this.state.highlightTextColor
      filterIcon = (
        <View style={{
          backgroundColor: filterIconColor,
          width: 18,
          height: 18,
          borderRadius: 3,
          marginLeft: 3,
          marginTop,
        }}>
          <Text style={{ alignSelf: 'center', marginTop: textMarginTop, fontSize, color: 'white' }}>{filterIconNum}</Text>
        </View>
      )
    }
    let isActiveStyle = this.isActiveOn('sale')
    let isActiveStyle2 = this.isActiveOn('sold')
    var secondBarButtons = [
      <TouchableOpacity
        key={'strSaleRent'}
        style={[styles.btnListView, styles.navListBtn, { marginRight: 8 }]}
        onPress={(e) => { this.toggleQuickFilter({ sold: false }) }}
      >
        <View style={[styles.subTag, isActiveStyle]}>
          <Text style={[{ color: '#777', fontSize: 12, marginTop: margin }, isActiveStyle]}>{strSaleRent}</Text>
        </View>
      </TouchableOpacity>,
      <TouchableOpacity
        style={[styles.btnListView, styles.navListBtn, { marginRight: 10, maxWidth: 120 }]}
        onPress={(e) => { this.toggleQuickFilter({ sold: true }) }}
        key={'strSoldLeased'}
      >
        <View style={[styles.subTag, isActiveStyle2]}>
          <Text style={[{ color: '#777', fontSize: 12, marginTop: margin }, isActiveStyle2]} numberOfLines={1} ellipsizeMode={'clip'}>{strSoldLeased}</Text>
        </View>
      </TouchableOpacity>
    ]
    if (this.state.dualHomepage && this.state.appmode !== 'mls') {
      secondBarButtons = [
        <TouchableOpacity
          key={'exclusive'}
          style={[styles.btnListView, styles.navListBtn, { marginRight: 10, maxWidth: 120 }]}
          onPress={(e) => { this.switchToExclusive({}) }}
        >
          <View style={[styles.subTag, this.isActiveOn('Exclusive')]}>
            <Text style={[{ color: '#777', fontSize: 12, marginTop: margin }, this.isActiveOn('Exclusive')]} numberOfLines={1} ellipsizeMode={'clip'}>{exclusiveString}</Text>
          </View>
        </TouchableOpacity>
      ]
      if (this.state.propTmpFilter.saletp !== 'sale') {
        secondBarButtons.unshift(
          <TouchableOpacity
            style={[styles.btnListView, styles.navListBtn, { marginRight: 8 }]}
            onPress={(e) => { this.switchToLandlord() }}
            key={'landlord'}
          >
            <View style={[styles.subTag, this.isActiveOn('Landlord')]}>
              <Text style={[{ color: '#777', fontSize: 12, marginTop: margin }, this.isActiveOn('Landlord')]}>{l10n('Landlord')}</Text>
            </View>
          </TouchableOpacity>
        )
      } else {
        // console.log([styles.subTag,this.isActiveOn('Assignment')])
        // NOTE: BUG: style=[a,b] has bug rendering, use style=Object.assign(a,b)
        let activeAssignment = this.isActiveOn('Assignment')
        secondBarButtons.unshift(
          <TouchableOpacity
            style={[styles.btnListView, styles.navListBtn, { marginRight: 8 }]}
            onPress={(e) => { this.switchToAssignment() }}
            key={'assignment'}
          >
            <View style={Object.assign({}, styles.subTag, activeAssignment)}>
              <Text style={Object.assign({}, { color: '#777', fontSize: 12, marginTop: margin }, activeAssignment)}>{l10n('Assignment')}</Text>
            </View>
          </TouchableOpacity>
        )
      }
    }
    return (
      // backgroundColor:'yellow'
      <View key={'buttons2secondary'} style={{ flexDirection: 'row', justifyContent: 'space-between', flex: 10, marginLeft: 10, marginRight: 0 }}>
        <View key={'saleSelect'} style={{ flexDirection: 'row' }}>
          {secondBarButtons}
        </View>
        <View key={'filter'} style={{ flexDirection: 'row' }}>
          <TouchableOpacity style={[styles.btnListView, styles.navListBtn, { paddingTop: 4, paddingBottom: 3, paddingRight: 10 }]} onPress={(e) => { this.showAdvFilter(e) }}>
            <View style={{ flexDirection: 'row' }}>
              <Text style={{ color: '#777', fontSize: 15 }}>{l10n('FILTER')}</Text>
              {filterIcon}
            </View>
          </TouchableOpacity>
        </View>
      </View>
    )
  }
  // type = Buy/Rent/PreCons
  isActiveMainCate(type) {
    let isActive = false;
    if (type == 'PreCons') {
      isActive = this.state.propTmpFilter.src == 'rm' && this.state.propTmpFilter.ltp == 'projects';
    } else if (type == 'Buy') {
      isActive = this.state.propTmpFilter.saletp == 'sale' && this.state.propTmpFilter.ltp !== 'projects';
    } else {
      isActive = this.state.propTmpFilter.saletp !== 'sale' && this.state.propTmpFilter.ltp !== 'projects';
    }
    if (isActive) {
      return [{ borderBottomColor: 'white', borderBottomWidth: 3, height: 44 }, { fontWeight: 'bold' }]
    }
    return [{}, {}]
  }
  renderButton(id = 'MapProps') {
    if (!this.featureOn) return null;
    // return null;
    // space-between flex:10
    return (
      // backgroundColor:'blue'
      <View key={id} style={{ flexDirection: 'row', justifyContent: 'space-between', flex: 10, marginLeft: 10, marginRight: 10 }}>
        {/* {this.state.propTmpFilter.src=='mls' &&
          <TouchableOpacity style={[styles.btnListView,styles.navFilterBtn,{marginRight:10}]} onPress={(e)=>{this.showAdvFilter(e)}}>
            <View>
              <Text style={{color: 'white', fontSize:16}}>{l10n('Filter')}</Text>
            </View>
          </TouchableOpacity>
        } */}
        {/* TODO:fix this.setSearchMode({k:'Residential'}) or setsaletp */}
        <View style={{ flexDirection: 'row' }}>
          <TouchableOpacity key={'buy'}
            style={[styles.btnListView, styles.navListBtn, { marginRight: 15, paddingBottom: 2 }, this.isActiveMainCate('Buy')[0]]}
            onPress={(e) => { this.setSaleTp('sale', 1) }}>
            <View>
              <Text style={[{ color: 'white', fontSize: 16 }, this.isActiveMainCate('Buy')[1]]}>{l10n('Buy', 'buyhouse')}</Text>
            </View>
          </TouchableOpacity>
          <TouchableOpacity key={'rent'}
            style={[styles.btnListView, styles.navListBtn, { marginRight: 15, paddingBottom: 2 }, this.isActiveMainCate('Rent')[0]]}
            onPress={(e) => { this.setSaleTp('lease', 1) }}>
            <View>
              <Text style={[{ color: 'white', fontSize: 16 }, this.isActiveMainCate('Rent')[1]]}>{l10n('Rent', 'renthouse')}</Text>
            </View>
          </TouchableOpacity>
          {((this.state.dualHomepage !== true) || (this.state.appmode !== 'mls')) &&
            <TouchableOpacity key={'PreCons'}
              style={[styles.btnListView, styles.navListBtn, { paddingBottom: 2 }, this.isActiveMainCate('PreCons')[0]]}
              onPress={(e) => { this.setSearchMode({ k: 'PreCons', clear: true }) }}>
              <View>
                <Text style={[{ color: 'white', fontSize: 16 }, this.isActiveMainCate('PreCons')[1]]}>{l10n('PreCon')}</Text>
              </View>
            </TouchableOpacity>
          }
        </View>
        <View style={{ flexDirection: 'row' }}>
          <TouchableOpacity key={'searchbtn'} style={[styles.btnListView, styles.navListBtn]} onPress={(e) => { this.gotoAutocomplete() }}>
            <Icon name="rmsearch"
              size={16}
              color="white"
              style={{ paddingRight: 10 }}
            // hitSlop={{top: 10, bottom: 10, left: 0, right: 120}}
            />
          </TouchableOpacity>
          {this.state.dualHomepage !== undefined && !this.state.dualHomepage &&
            <TouchableOpacity key={'listBtn'} style={[styles.btnListView, styles.navListBtn, { paddingLeft: 13 }]} onPress={(e) => { this.showListView() }}>
              <View>
                <Text style={{ color: 'white', fontSize: 16 }}>{l10n('LIST')}</Text>
              </View>
            </TouchableOpacity>
          }
        </View>
      </View>
    )
  }
  computedMapSearchTip() {
    var showLogin = false;
    // console.log(this.state.dispVar)
    if (!this.state.dispVar.isLoggedIn) {
      showLogin = true;
    }
    if (this.state.loading) {
      return { str: l10n('Searching...'), showLogin };
    }
    // var zoom = this.state.propTmpFilterVals.saletp;
    var results = l10n('results');
    if (this.state.propTmpFilter.src === 'rm') {
      results = l10n(this.curSearchMode.k, this.curSearchMode.ctx);
    } else if (this.state.propTmpFilter.src === 'mls') {
      results = this.state.propTmpFilterVals.ptype;
    }
    if (this.state.propTmpFilter.oh) {
      results = l10n('Open House');
    } else if (/^-/.test(this.state.propTmpFilter.dom)) {
      results = l10n('Sold')
    }
    var limit = 50;//this.propTmpFilter.src == 'rm'?20:50;
    var length = this.state.items.length;
    if (this.state.cntRMprop) {
      if (length > limit) {
        length = length - this.state.cntRMprop;
      }
    }
    length = length >= limit && this.state.cntRMprop ? limit + '+' : length;
    var counts = this.state.cntTotal;//>=limit?limit+'+':length;
    var countStr = length;
    if (this.state.cntRMprop) {
      countStr += '(+' + this.state.cntRMprop + ' ' + l10n('Exclu', 'exclusive') + ') ';
    }
    countStr += '/' + counts;
    return { str: countStr + ' ' + results, showLogin }
  }
  // NOTE: always shows on botton list switch
  _renderMapListDragPan() {
    // this.state.loading
    // this.state.showMapSearchTip
    let bottom = 50;
    if (this.isPtypeProject() || this.state.dualHomepage) {
      bottom = 0;
    }
    var computedMapSearchTip = this.computedMapSearchTip();
    var loginBtn = null;
    let resaleOrExclusive = l10n('RESALE')
    if (this.state.appmode == 'mls') {
      resaleOrExclusive = l10n('EXCLUSIVE', 'rm')
    }
    // console.log('computedMapSearchTip',computedMapSearchTip.showLogin)
    if (computedMapSearchTip.showLogin) {
      let loginTop = -34;
      if (Platform.OS != 'ios') {
        loginTop = -38;
      }
      loginBtn = <TouchableOpacity
        style={{
          // backgroundColor:'blue',
          zIndex: 120,
          marginTop: loginTop,
          height: 40,
          // alignItems:'flex-end'
        }}
        key={'loginBtn'}
        onPress={() => { this.closePopup('/1.5/user/login') }}>
        <View style={{ flexDirection: 'row', alignSelf: 'center' }}>
          <Text style={{ color: '#3899EC', paddingRight: 5, paddingLeft: 5 }}>{l10n('LOGIN')}</Text>
          <Text>{l10n('to see all')}</Text>
          <Icon name="rmclose" size={17} color={"#ddd"}
            onPress={() => { this.setState({ showMapSearchTip: false }) }}
            style={{ marginLeft: 7 }} />
        </View>
      </TouchableOpacity>
    }

    // let panStyle = {
    //   transform: this.state.pan.getTranslateTransform()
    //   // height:68+this._val.y
    // }
    // let panStyle = this.state.pan.getLayout() //this.state.pan.getLayout()
    // console.log('++++++',this.state.pan.getLayout(),this._val.y)
    // return (
    //   <Animated.View
    //     {...this.panResponder.panHandlers}
    //     style={[panStyle,styles.circle]}
    //   />
    // )
    let base = -40
    let leftTop = 4;
    if (Platform.OS != 'ios') {
      leftTop = 0
    }
    let left = (
      <TouchableOpacity style={{
        paddingLeft: 10,
        marginTop: base + leftTop,
        zIndex: 120,
        flex: 1,
        // backgroundColor:'yellow'
      }}
        key={'leftListBtn'}
        onPress={(e) => { this.showListView() }}
      >
        <Text numberOfLines={1} style={{ color: 'black', fontSize: 16 }}>{computedMapSearchTip.str}</Text>
        {/* {this.state.loading &&
          <ActivityIndicator size="small" color="#e03131" />
        } */}
        {/* <Text numberOfLines={1} style={{color:'#777'}}>{l10n('List results')}</Text> */}
      </TouchableOpacity>
    )
    let rightTop = -7;
    if (Platform.OS != 'ios') {
      rightTop = -9
    }

    let right = (<View
      style={{ paddingRight: 10, flex: 1, alignItems: 'flex-end' }}
      key={'rightListBtn'}
    >
      {this.state.showMapSearchTip && loginBtn}
      {(!loginBtn || !this.state.showMapSearchTip) &&
        <View style={{
          flexDirection: 'row',
          // zIndex:120,
          // marginTop:-45,
          // position:'absolute',
          // width:100,
          // alignContent:'flex-start',
          // backgroundColor:'red',
        }}
          key={'listViewContainer'}
        >
          <TouchableOpacity style={{
            height: 48,
            zIndex: 120,
            // position:'absolute',
            paddingLeft: 7,
            paddingRight: 7,
            paddingTop: 0,
            marginTop: base + rightTop,
            paddingBottom: 0,
            // backgroundColor:'lightblue',
            // alignItems:'flex-end'
          }}
            key={'exclusiveBtn'}
            onPress={(e) => { this.switchBetweenExclusiveAndSaleMode() }}>
            <View>
              <Text style={{ marginTop: 10, color: '#3899EC', fontSize: 16, fontWeight: 'bold' }}>{resaleOrExclusive}</Text>
            </View>
          </TouchableOpacity>
          <TouchableOpacity style={{
            height: 48,
            zIndex: 120,
            // position:'absolute',
            paddingLeft: 7,
            paddingRight: 7,
            paddingTop: 0,
            marginTop: base + rightTop,
            paddingBottom: 0,
            // backgroundColor:'blue',
            // alignItems:'flex-end'
          }}
            key={'listBtn'}
            onPress={(e) => { this.showListView() }}>
            <View>
              <Text style={{ marginTop: 10, color: '#3899EC', fontSize: 16, fontWeight: 'bold' }}>{l10n('LIST')}</Text>
            </View>
          </TouchableOpacity>
        </View>
      }

    </View>)
    let lang = this.state.dispVar.lang
    let propList = null

    // (
    //   <FlatList
    //     keyboardDismissMode='on-drag'
    //     keyboardShouldPersistTaps={'always'}
    //     data={this.state.items}
    //     // extraData={this.props.lst}
    //     style={{marginTop:20}}
    //     keyExtractor={(item,idx) => {
    //       if(item._id){
    //         return item._id.toString()
    //       } else {
    //         return idx.toString()
    //       }
    //     }}
    //     renderItem={({item}) => {
    //       if (!item.isProj) {
    //         return <Prop referer={'mapSearch'} prop={item} lang={lang} closePopup={()=>{}} search={()=>{}} />;
    //       } else if (item.isProj) {
    //         return <Proj proj={item} lang={lang} search={()=>{}} searchStr={''}/>;
    //       }
    //     }}
    //   />
    // )
    // return null
    return (
      <ListDragPan
        // left={left}
        // right={right}
        // list={[]}
        id={'mapPropsList'}
        key={'mapPropsList'}
        bottom={bottom}
        openListView={this.showListView.bind(this)}
        LIST_HEIGH={170}
        PAN_SHOW_VALUE={110}
      >
        <View
          key={'contentsOfBottom'}
          style={{
            width: '100%',
            flexDirection: 'row',
            alignItems: 'flex-start',
            // marginTop:-40,
            // position:'absolute',
            // zIndex:101,
            // backgroundColor:'grey',
            // marginTop:1,
            justifyContent: 'space-between',
            paddingLeft: 5, 
            paddingRight: 5,
          }}
        // onStartShouldSetResponder={(event) => true}
        // onTouchEnd={(e) => {
        //   e.stopPropagation();
        // }}
        >
          {left}
          {right}
        </View>
        <LayerMenu map={this.map}/>
        {/* {propList} */}
      </ListDragPan>
    )
    return (
      <Animated.View
        key={'loadingSpinNew'}
        // {...this.panResponder.panHandlers}
        style={[styles.loadingTipWrapperNew, { bottom: bottom + bottomBarHeight }, panStyle]}>
        {/* TODO: add on drag to this */}
        <View key={'dragHolder'}
          style={[{
            height: 19,
            // backgroundColor:'blue'
          }]}>
          <TouchableOpacity
            // {...this.panResponder.panHandlers}
            style={{ width: 50, height: 6, marginTop: 7, backgroundColor: '#D3D3D3', borderRadius: 3 }}
            onPress={(e) => { this.showListView() }}>
          </TouchableOpacity>
        </View>
        <View key={'contentsOfBottom'} style={{
          width: '100%',
          flexDirection: 'row',
          alignItems: 'flex-start',
          // backgroundColor:'red',
          marginTop: 1,
          justifyContent: 'space-between', paddingLeft: 15, paddingRight: 15
        }}>


        </View>
      </Animated.View>
    )
  }
  _renderLoadingSpin() {
    return
    if (!(this.state.loading || this.state.showMapSearchTip)) {
      // console.log('returned not render')
      return null;
    }
    let bottom = 50;
    if (this.isPtypeProject() || this.state.dualHomepage) {
      bottom = 0;
    }
    var computedMapSearchTip = this.computedMapSearchTip();
    var loginBtn = null;
    if (computedMapSearchTip.showLogin) {
      loginBtn = <TouchableOpacity onPress={() => { this.closePopup('/1.5/user/login') }}>
        <View style={{ flexDirection: 'row' }}>
          <Text style={{ color: '#007aff', paddingRight: 5, paddingLeft: 5 }}>{l10n('LOGIN')}</Text>
          <Text>{l10n('to see all')}</Text>
        </View>
      </TouchableOpacity>
    }
    return (
      <View key={'loadingSpin'} style={[styles.loadingTipWrapper, { bottom: bottom + bottomBarHeight }]}>
        <Text numberOfLines={1}>{computedMapSearchTip.str}</Text>
        {loginBtn}
        <View style={{ marginLeft: 9, borderRadius: 9, width: 17, height: 17, overflow: 'hidden' }}>
          {!this.state.loading &&
            <Icon name="rmclose" size={17} color={"#ddd"} onPress={() => { this.closeMapSearchTip() }} style={{}} />
          }
          {this.state.loading &&
            <ActivityIndicator size="small" color="#e03131" />
          }
        </View>
      </View>
    )
  }
  closeMapSearchTip() {
    var state = {};
    state.showMapSearchTip = false;
    state.mapSearchTipCount -= 1;
    this.setState(state);
  }
  getBottomNavColor(cur) {
    var self = this;
    var computedMapSearchMode = self.state.propTmpFilter.ptype;;
    // if (self.state.propTmpFilter.src === 'rm') {
    //   computedMapSearchMode =  self.curSearchMode.k;
    // } else if (self.state.propTmpFilter.src === 'mls') {
    //   computedMapSearchMode =  self.state.propTmpFilter.ptype;
    // }
    // var isOtherFooterActive =  !/Residential|Commercial|Assignment|Exclusive|Landlord/.test(computedMapSearchMode);
    // if (isOtherFooterActive) {
    //   computedMapSearchMode = 'Other'
    // }
    // console.log('computedMapSearchMode:',computedMapSearchMode,cur,computedMapSearchMode==cur)
    if (cur == computedMapSearchMode) {
      return '#e03131';
    }
    return '#929292';

  }
  renderBottomBar() {
    // return null;
    if (this.state.dualHomepage) {
      return null;
    }
    if (!this.featureOn) return null;
    if (this.isPtypeProject()) return null;
    var self = this;
    // n == text == (text to disp)
    let iconStyles = [3, 4, 4, 4, 4, 8]
    if (Platform.OS !== 'ios') {
      iconStyles = [4, 5, 5, 5, 5, 9]
    }
    let bottomNavPtypes = {
      resi: {
        k: 'Residential', n: 'Resi', icon: 'residential', ctx: 'residential props',
        iconStyle: { paddingTop: iconStyles[0] },
        textStyle: { fontSize: 11 }
      },
      comm: {
        k: 'Commercial', n: 'Comm', icon: 'commercial', ctx: 'commercial props',
        iconStyle: { paddingTop: iconStyles[1] },
        textStyle: { fontSize: 11 }
      },
      lndl: {
        k: 'Landlord', n: 'Landlord', icon: 'assignment', ctx: 'landlord direct props', saletp: 'lease',
        iconStyle: { paddingTop: iconStyles[2] },
        textStyle: { fontSize: 11 }
      },
      asgm: {
        k: 'Assignment', n: 'Asgmt', icon: 'assignment', ctx: 'assignment props', saletp: 'sale',
        iconStyle: { paddingTop: iconStyles[3] },
        textStyle: { fontSize: 11 }
      },
      excl: {
        k: 'Exclusive', n: 'Exclu', icon: 'exclusive', ctx: 'exclusive props',
        iconStyle: { paddingTop: iconStyles[4] },
        textStyle: { fontSize: 11 }
      },
      other: {
        k: 'Other', n: 'Other', icon: 'option', ctx: '', iconSize: 16,
        iconStyle: { paddingTop: iconStyles[5] },
        textStyle: { fontSize: 11 }
      },
      // othr:{k:'Other',n:'More',icon:'option',act:()=>{this.toggleSaletpSelect('open')}},
      // prcn:{k:'PreCons',n:'PreCon',icon:'option'}
    };
    let ret = [];
    // show=ptype.saletp?propTmpFilter.saletp == ptype.saletp:true
    if (this.state.propTmpFilter.saletp == 'sale') {
      delete bottomNavPtypes.lndl;
      // delete bottomNavPtypes.other
    } else {
      delete bottomNavPtypes.asgm;
      // delete bottomNavPtypes.prcn
    }
    Object.values(bottomNavPtypes).forEach(kn => {
      if (!kn.act) { kn.act = () => { self.setSearchMode({ k: kn.k, clear: true }) } }
      kn.color = this.getBottomNavColor(kn.k);
    });
    this.bottomButtons(ret, Object.values(bottomNavPtypes));
    return ret;
  }
  renderModal(id) {
    // console.log('renderModal',new Date())
    if (!this.featureOn) return null;
    // var style = {};
    // if(Platform.OS == 'ios'){
    //   style = {zIndex:15};
    // }
    var a = [], v;
    if (v = this._renderSaleTypeSelect()) { a.push(v); }
    // if (v = this._renderPropPreviewCover()){a.push(v);}
    if (v = this._renderPropPreview()) { a.push(v); }
    if (a.length == 0) { // when has preview or select type menu, no need to render bottom nav
      if (v = this._renderMapListDragPan()) { a.push(v); }
      // a.push(this.renderBottomBar())
    }
    return a;
  }
  getHomeSchools() {
    eventEmitter.emit('school.getHomeSchools', this.state.curProp)
  }
  _renderSaleTypeSelect() {
    return
    if (this.state.showSaleTypeSelect) {
      var saleStyle = {}
      var rentStyle = {}
      var saleColor = {}
      var rentColor = {}
      var style = {
        // color:'#e03131', //ExceptionsManager.js:71 Warning: Failed prop type: Invalid props.style key `color` supplied to `View`.
        borderBottomColor: '#e03131',
        borderBottomWidth: 2,
        // backgroundColor:'#f1f1f1',
      }
      if (this.state.searchModeSaletp == 'sale') {
        saleStyle = style
        saleColor = { color: '#e03131' }
      } else {
        rentStyle = style
        rentColor = { color: '#e03131' }
      }
      return (
        // <div id="saleTypeSelect" class="modal modal-55pc">
        //   <div class="content">
        //     <div class="inline-wrapper">
        //       <div class="inline" :class="{active:searchModeSaletp == 'sale'}" @click="setSearchModeSaletp('sale')"><span>{{_('For Sale')}}</span></div>
        //       <div class="inline" :class="{active:searchModeSaletp == 'lease'}" @click="setSearchModeSaletp('lease')"><span>{{_('For Rent')}}</span></div>
        //     </div>
        //     <div class="types-wrapper">
        //       <div v-for="s in searchModes" @click="setSearchMode(s)" class="type">
        //         <img :src="s.img"/>
        //         <div class="tp-name">{{_(s.v||s.k, s.ctx)}}</div>
        //       </div>
        //     </div>
        //   </div>
        <View key={'saleTypeSelect'} style={[styles.propPreviewWrapper, styles.saleTypesModalWrapper]}>
          <View
            style={styles.saleRentSelectWrapper}
            key={'saleRentSelectWrapper'}
          >
            <TouchableOpacity
              style={[{ width: gWindowWidth / 2 - 1 }, styles.center]}
              onPress={() => { this.setSearchModeSaletp('sale') }}
              key={'forSaleBtn'}
            >
              <View style={saleStyle}>
                <Text style={[{ fontSize: 18, paddingBottom: 2 }, saleColor]}>{l10n('For Sale')}</Text>
              </View>
            </TouchableOpacity>
            <TouchableOpacity
              style={[{ width: gWindowWidth / 2 }, styles.center]}
              onPress={() => { this.setSearchModeSaletp('lease') }}
              key={'forLeaseBtn'}
            >
              <View style={rentStyle}>
                <Text style={[{ fontSize: 18, paddingBottom: 2 }, rentColor]}>{l10n('For Rent')}</Text>
              </View>
            </TouchableOpacity>
          </View>
          <View
            style={styles.saleTypesWrapper}
            key={'saleTypesWrapper'}
          >
            {this.state.searchModes.map((s) => {
              return (
                <TouchableOpacity style={styles.saleTypesBtnWrapper} key={s.k} onPress={() => { this.setSearchMode(s) }}>
                  <View style={styles.saleTypes}>
                    <Image style={[{ width: 35, height: 35 }, styles.center]} source={IMAGE_URIS[s.img]} />
                    <View style={styles.center}>
                      <Text style={{ color: '#666', fontSize: 14, paddingTop: 3, textAlign: 'center' }} >{l10n(s.v || s.k)}</Text>
                    </View>
                  </View>
                </TouchableOpacity>
              )
            })}
          </View>
          <View
            style={styles.saleTypesCloseBtn}
            key={'saleTypesCloseBtn'}
          >
            <TouchableOpacity onPress={() => { this.toggleSaletpSelect() }}>
              <Text style={{ alignSelf: 'center', fontSize: 18 }}>{l10n('Close')}</Text>
            </TouchableOpacity>
          </View>
        </View>
      )
    }
  }

  isPropSold(lst) {
    return /Sc|Sld|Lsd|Pnd|Cld/.test(lst);
  }
  // render dummy prop preview for swiper, after swiper load remove this
  _renderPropPreviewCover() {
    var self = this;
    let wrapperHeight = this._getWrapperHeight();
    return null
    if (this.state.hideDummyPropPreview) {
      return null
    }
    if (!this.state.propHalfDetail) {
      return null
    }
    if (!this.state.curProp._id) {
      return null
    }
    return <View style={[styles.propPreviewWrapper, {
      zIndex: 1000,
      // left:10,
      // bottom:20,
      // right:10,
      opacity: 0.7,
      // borderRadius:15,
      position: 'absolute',
      width: gWindowWidth,
      marginBottom: -wrapperHeight - 70,
      // backgroundColor:'rgba(255,255,255,0.7)',
      backgroundColor: 'transparent',
      // overflow:'hidden',
      height: wrapperHeight + 70
    }]}>
      <TouchableOpacity style={[styles.previewIconWrapper,
      {
        right: 10,
        width: 32,
        padding: 4,
        // height:26,
      }]}
        key='swipeCloseBtn'
        onPress={(e) => { this.toggleModal(e, 'close') }}>
        <View style={[styles.previewIconBg, { backgroundColor: 'white', width: 24 }]}>
          <Icon name="rmclose" size={25} color="black" style={{ opacity: 0.8, marginTop: -1 }} />
        </View>
      </TouchableOpacity>
      <View>
        {self._renderPropPreviewForProp(this.state.curProp, {
          noClose: true,
          style: {
            borderRadius: 15,
            overflow: 'hidden',
            left: 10,
            right: 10,
            bottom: 30,
            flex: 1,
            // justifyContent: 'center',
            // alignItems: 'center',
            backgroundColor: 'white',
            // position:'absolute',
          }
        })}
      </View>
    </View>

  }

  _renderCarouselItem({ item, index }, index2) {
    // console.log(item._id,index,index2)
    let self = this;
    // console.log(item._id)
    // return (
    //   <View
    //   key={item._id}
    //   style={{
    //     height:220,
    //     backgroundColor:'blue',
    //     // flex:1
    //     // zIndex:1000,
    //     borderRadius:15,
    //     }}>
    //     <Text style={{}}>{ item._id }</Text>
    //     <Text style={{}}>{ item.ptype2 }</Text>
    //   </View>
    // );
    return (self._renderPropPreviewForProp(item, {
      noClose: true,
      index,
      style: {
        // borderRadius:15,
        overflow: 'hidden',
        left: 10,
        right: 10,
        bottom: 30,
        flex: 1,
        // justifyContent: 'center',
        // alignItems: 'center',
        backgroundColor: 'white',
        // position:'absolute',
      }
    }))
  }
  // render prop preview using swiper
  // TODO: this render is time consuming, need to optimize
  _renderPropPreviewSlider() {
    return null
    var self = this;
    let curProp = this.state.curProp || {};
    if (!this.state.items.length) {
      return null
    }
    if (!this.state.propHalfDetail) {
      return null
    }
    if (!curProp._id) {
      return null
    }
    // this changes when state.items changes

    // only render 3 items
    let renderItems = this.state.items || []
    if (this.state.isFromSoldLayer) {
      renderItems = this.state.soldItems || []
    }
    // TODO:  VirtualizedList: You have a large list that is slow to update - make sure your renderItem function renders components that follow React performance best practices like PureComponent, shouldComponentUpdate, etc. {"contentLength": 19160, "dt": 2768, "prevDt": 15365}
    let index = 0;
    renderItems.forEach((item, i) => {
      if (item._id == curProp._id) {
        index = i;
      }
    })
    let wrapperHeight = this._getWrapperHeight();

    // find matching index for current prop
    let navStyle = {
      color: '#FFF',
      shadowColor: '#e03131',//'#d2d2d2',
      shadowOffset: { width: 1, height: 2 },
      shadowRadius: 3,
      shadowOpacity: 0.8,
      fontWeight: 'bold',
    }
    let itemWidth = gWindowWidth - 10;
    let cacheKey = index + '' + this.state.isFromSoldLayer
    // if(this.swiperCache[cacheKey]){
    //   return this.swiperCache[cacheKey]
    // }
    // console.log('++++++render slider index:',index)
    // this.swiperCache[cacheKey] =
    return <View style={[styles.propPreviewWrapper, {
      // zIndex:1000,
      // left:10,
      // bottom:20,
      // right:10,
      // borderRadius:15,
      // position:'fixed',
      flex: 1,
      flexDirection: 'row',
      justifyContent: 'center',
      width: gWindowWidth,
      backgroundColor: 'rgba(255,255,100,0.3)',
      // backgroundColor:'transparent',
      // overflow:'hidden',
      height: wrapperHeight + 30
    }]}>
      <TouchableOpacity style={[styles.previewIconWrapper,
      {
        right: 25,
        width: 32,
        padding: 4,
        marginTop: -45,
        // height:26,
      }]}
        key='swipeCloseBtn'
        onPress={(e) => { this.toggleModal(e, 'close') }}>
        <View style={[styles.previewIconBg, { backgroundColor: 'white', width: 24 }]}>
          <Icon name="rmclose" size={25} color="black" style={{ opacity: 0.8, marginTop: -1 }} />
        </View>
      </TouchableOpacity>
      {this._renderCarousel(renderItems, index, itemWidth)}
    </View>
    return this.swiperCache[cacheKey]
  }
  _renderCarousel(renderItems, index, itemWidth) {
    return (
      <Carousel
        // style={{flex:1,zIndex:1000}}
        ref={(c) => { this._carousel = c; }}
        data={renderItems}
        onSnapToItem={(i) => {
          // console.log('onSnapToItem:',i)
          let curProp = this.state.items[i];
          if (this.state.isFromSoldLayer) {
            curProp = this.state.soldItems[i];
          }
          this.setState({ curProp, tracksViewChanges: true })
          this.trackOff();
        }}
        firstItem={index}
        initialScrollIndex={index}
        getItemLayout={(data, index) => (
          { length: itemWidth, offset: (itemWidth) * index, index }
        )}
        // layout={'stack'}
        initialNumToRender={3}
        maxToRenderPerBatch={3}
        windowSize={3}
        updateCellsBatchingPeriod={80}
        renderItem={(item) => { return this._renderCarouselItem(item, index) }}
        sliderWidth={gWindowWidth}
        itemWidth={itemWidth}
      />
    )
    /* <Swiper
        key='propPreviewSwiper'
        loadMinimal
        loadMinimalSize={1}
        showsPagination={false}
        removeClippedSubviews={true}
        index={index}
        style={{
          // flex:1,
          // borderRadius:5,
          // overflow:'hidden'
        }}
        onIndexChanged={(i)=>{
          let curProp = this.state.items[i];
          this.setState({curProp,tracksViewChanges:true})
          this.trackOff();
        }}
        loop={false}
        showsButtons={true}
        // buttonWrapperStyle={{color:'red'}}
        nextButton={<Icon name="back" size={23} color="#FFF" style={[navStyle,{transform: [{scaleX:-1}]}]} />}
        prevButton={<Icon name="back" size={23} color="#FFF" style={[navStyle]} />}
      >
        {renderItems.map((item, i) => (
          // <View testID="Hello" style={{
          //   flex: 1,
          //   justifyContent: 'center',
          //   alignItems: 'center',
          //   backgroundColor: '#9DD6EB'
          // }}>
          //   <Text style={styles.text}>Hello Swiper+{i}</Text>
          // </View>
          self._renderPropPreviewForProp(item,{
          noClose:true,
          style:{
            borderRadius:15,
            overflow:'hidden',
            left:10,
            right:10,
            bottom:30,
            flex:1,
            // justifyContent: 'center',
            // alignItems: 'center',
            backgroundColor: 'white',
            // position:'absolute',
          }})
        ))}
      </Swiper> */
  }
  _getWrapperHeight() {
    var wrapperHeight = 210;
    return wrapperHeight;
  }
  /**
   * 渲染房产预览卡片组件
   * @param {Object} prop - 房产信息对象
   * @param {string} prop.id - 房产ID
   * @param {string} prop._id - 房产MongoDB ID
   * @param {string} prop.img - 房产图片URL
   * @param {number} prop.lp - 挂牌价格
   * @param {number} prop.lpr - 参考价格
   * @param {string} prop.rmbdrm - 卧室数量(RM格式)
   * @param {number} prop.bdrms - 卧室数量
   * @param {number} prop.br_plus - 额外卧室
   * @param {string} prop.rmbthrm - 浴室数量(RM格式)
   * @param {number} prop.tbthrms - 浴室总数
   * @param {number} prop.bthrms - 浴室数量
   * @param {string} prop.rmgr - 车库数量(RM格式)
   * @param {number} prop.tgr - 车库总数
   * @param {number} prop.gr - 车库数量
   * @param {string} prop.tax - 税费
   * @param {string} prop.taxyr - 税费年份
   * @param {number} prop.dom - 上市天数
   * @param {string} prop.desc - 描述
   * @param {string} prop.desc_en - 英文描述
   * @param {string} prop.rmLot - 地块信息
   * @param {string} prop.daddr - 地址显示标志
   * @param {string} prop.addr - 地址
   * @param {string} prop.unt - 单元号
   * @param {string} prop.apt_num - 公寓号
   * @param {Array} prop.ptype2 - 房产类型
   * @param {string} prop.builder - 开发商
   * @param {string} prop.sid - 房源编号
   * @param {Object} opt - 配置选项
   * @param {boolean} opt.noClose - 是否显示关闭按钮
   * @returns {React.ReactElement|null} 预览卡片组件或null
   */
  _renderPropPreviewForProp(prop = {}, opt = {}) {
    // 1. 基础验证
    if (!(prop.id || prop._id)) {
      return null
    }
    if (!this.state.propHalfDetail) {
      return null
    }

    // 2. 处理房产基本信息
    let hasBdrms = !!(prop.rmbdrm || prop.bdrms);
    let bdrmStr = '';
    if (prop.rmbdrm) {
      bdrmStr = prop.rmbdrm;
    } else {
      bdrmStr = prop.bdrms + (prop.br_plus ? '+' + prop.br_plus + ' ' : ' ');
    }
    let bthrmStr = prop.rmbthrm || prop.tbthrms || prop.bthrms;
    let hasBthrms = !!(bthrmStr);
    let grStr = prop.rmgr || prop.tgr || prop.gr;
    let hasGr = !!(grStr);

    // 3. 处理价格显示样式
    var through = {};
    if (prop && (this.isPropSold(prop.lst))) {
      through = { textDecorationLine: 'line-through', textDecorationStyle: 'solid', fontSize: 14 }
    }

    // 4. 计算布局高度和图片设置
    var wrapperHeight = this._getWrapperHeight();
    var preViewImage = require('../assets/images/ajax-loader.gif')
    var resizeMode = 'stretch';
    if (this.state.usePropImg) {
      preViewImage = { uri: prop.img }
    }

    // 5. 计算底部布局间距
    let bottom1 = Platform.OS == 'ios' ? 20 : 12;
    var step = 18;
    let bottom2 = step + bottom1;
    let bottom3 = prop.rmLot ? step + bottom2 : 0;
    let bottom4 = step + Math.max(bottom2, bottom3);
    let bottom5 = step + bottom4;
    let bottom6 = step + bottom5;

    // 6. 设置关闭按钮样式
    let closeWidth = Platform.OS == 'ios' ? 24 : 25;
    let closeMarginTop = Platform.OS == 'ios' ? 0 : -1;

    // 7. 渲染预览卡片
    return (<View key={'propPreview' + prop._id}
      style={[styles.propPreviewWrapper, { height: wrapperHeight }, opt.style || {}]
      }>
      <TouchableOpacity onPress={(e) => { this.propChanged(e) }}>
        <View style={{ height: 210 }}>
          {/* 学校信息按钮 */}
          <TouchableOpacity style={[styles.previewIconWrapper,
          {
            right: opt.noClose ? 10 : 52,
            width: 32,
            padding: 4,
          }]} onPress={(e) => { this.getHomeSchools() }}>
            <View style={styles.previewIconBg}>
              <Icon name="graduation-cap" size={14} color="#FFF" style={{ paddingLeft: 2 }} />
            </View>
          </TouchableOpacity>

          {/* 关闭按钮 */}
          {!opt.noClose &&
            <TouchableOpacity style={[styles.previewIconWrapper,
            {
              right: 10,
              width: 32,
              padding: 4,
            }]} onPress={(e) => { this.toggleModal(e, 'close') }}>
              <View style={[styles.previewIconBg, { backgroundColor: 'white', width: closeWidth }]}>
                <Icon name="rmclose" size={25} color="black" style={{ opacity: 0.8, marginTop: closeMarginTop }} />
              </View>
            </TouchableOpacity>
          }

          {/* 标签区域 */}
          <View style={[styles.previewIconWrapper,
          {
            left: 15,
          }]}>
            {this.isPropToplising() &&
              <View style={[styles.previewTag, styles.ad]}>
                <Text style={styles.preViewText}>{l10n('TOP')}</Text>
              </View>
            }
            {this.showPropOhTag() &&
              <View style={[styles.previewTag, styles.oh, this.isTopCurProp() ? { marginLeft: 6 } : {}]}>
                <Text style={styles.preViewText}>{l10n('Open House')}</Text>
              </View>
            }
          </View>

          {/* 房产图片 */}
          <Image
            onLoad={() => {}}
            resizeMode={resizeMode}
            style={{ width: '100%', height: 210, borderRadius: 0 }}
            source={preViewImage}
          />

          {/* 房产详情区域 */}
          <View style={{}}>
            {/* 渐变背景 */}
            <Image
              resizeMode='stretch'
              style={{ position: 'absolute', bottom: 0, width: '100%', height: 130 }}
              source={require('../assets/images/g-01.png')}
            />

            {/* 已售价格信息 */}
            {prop.showSoldPrice && prop.saleTpTag_en !== "Delisted" && prop.sp && prop.status_en !== 'A' &&
              <View style={[styles.previewPropDetails, { bottom: bottom6, alignItems: 'flex-end' }]}>
                <Text style={[styles.shadowText, styles.price]}>{currency(prop.sp, '$', 0)}</Text>
                <Text style={[styles.shadowText, { color: 'white', fontSize: 14 }]}>{l10n('Sold')} ({dotdate(prop.spcts || prop.mt)})</Text>
              </View>
            }

            {/* 价格和房间信息 */}
            <View style={[styles.previewPropDetails, { bottom: bottom5, justifyContent: 'space-between', alignItems: 'flex-end' }]}>
              <View style={[{ flexDirection: 'row', alignItems: 'flex-end', flex: 2, textAlignVertical: 'center' }]}>
                <Text style={[styles.shadowText, styles.price, through]}>{this.formatPropPrice(prop)}</Text>
                {(prop.showingText != null) &&
                  <Text style={[styles.shadowText, { color: 'white', fontSize: 11, paddingLeft: 0 }]}> {prop.showingText != null ? prop.showingText : ''}</Text>
                }
                {(prop.rmtype != null) &&
                  <Text style={[styles.shadowText, styles.rmtype]}>{l10n(prop.rmtype)}</Text>
                }
                {!!prop.nm &&
                  <Text numberOfLines={1} style={[styles.shadowText, { color: 'white' }]}>{prop.nm}</Text>
                }
              </View>

              {/* 房间配置信息 */}
              <View style={[{ flexDirection: 'row', alignItems: 'flex-end', flex: 1, justifyContent: 'flex-end' }]}>
                {hasBdrms &&
                  <View style={{ flexDirection: 'row' }}>
                    <Icon name="rmbed" size={14} color="#fff" style={{ paddingRight: 2 }} />
                    <Text style={[styles.shadowText, styles.bdbthgr]}>{bdrmStr}</Text>
                  </View>
                }
                {hasBthrms &&
                  <View style={{ flexDirection: 'row' }}>
                    <Icon name="rmbath" size={14} color="#fff" style={{ paddingRight: 2 }} />
                    <Text style={[styles.shadowText, styles.bdbthgr]}>{bthrmStr}</Text>
                  </View>
                }
                {hasGr &&
                  <View style={{ flexDirection: 'row' }}>
                    <Icon name="rmcar" size={15} color="#fff" style={{ paddingRight: 2, paddingLeft: 4 }} />
                    <Text style={[styles.shadowText, styles.bdbthgr]}>{grStr}</Text>
                  </View>
                }
              </View>
            </View>

            {/* 税费和描述信息 */}
            <View style={[styles.previewPropDetails, { bottom: bottom4 }]}>
              {!!prop.tax &&
                <Text style={[styles.bdbthgr]}>{l10n('TAX')}: {prop.tax} / {prop.taxyr}</Text>
              }
              {(prop.dom != null) &&
                <Text style={[styles.shadowText, styles.dom]}>{l10n('DOM', 'prop')} {prop.dom != null ? prop.dom : ''}</Text>
              }
              {!!prop.desc &&
                <Text numberOfLines={1} style={[styles.bdbthgr]}>
                  {this.state.dispVar.lang == 'en' ? prop.desc_en : prop.desc}
                </Text>
              }
            </View>

            {/* 地块信息 */}
            {prop.rmLot &&
              <View style={[styles.previewPropDetails, { bottom: bottom3, paddingLeft: 8 }]}>
                <Text style={[styles.bdbthgr]}> {prop.rmLot}</Text>
              </View>
            }

            {/* 地址和房产类型信息 */}
            <View style={[styles.previewPropDetails, { bottom: bottom2, justifyContent: 'space-between' }]}>
              <View style={[{ flexDirection: 'row', alignItems: 'flex-end', flex: 1, overflow: 'hidden', paddingRight: 18 }]}>
                {prop.daddr !== 'N' &&
                  <Text numberOfLines={1} style={[styles.bdbthgr]}>{(prop.unt || prop.apt_num) ? (prop.unt || prop.apt_num) + ' ' : ''}{prop.addr + ' •'}</Text>
                }
                {prop.propType &&
                  <Text numberOfLines={1} style={[styles.bdbthgr, { flex: 1 }]}>{prop.propType}</Text>
                }
                {!!prop.builder &&
                  <Text numberOfLines={1} style={[styles.bdbthgr]}>{prop.builder}</Text>
                }
              </View>
              <View style={[{ flexDirection: 'row', alignItems: 'flex-end', width: 100, justifyContent: 'flex-end' }]}>
                {!!prop.sid &&
                  <Text numberOfLines={1} style={[styles.bdbthgr]}>{prop.sid}</Text>
                }
              </View>
            </View>
        
            {/* 社区信息: 固定高度，避免首次渲染高度变化 */}
            <View style={[styles.previewPropDetails, { bottom: bottom1}]}>
              <Text numberOfLines={1} style={[styles.bdbthgr]}>{this.state.cmtyBnds?.nm || ''}</Text>
            </View>
          </View>
        </View>
      </TouchableOpacity>
    </View>)
  }
  _renderPropPreview() {
    // console.log('+++++',preViewImage)
    // return null
    let prop = this.state.curProp;

    return this._renderPropPreviewForProp(prop)
  }
  formatPropPrice = (prop) => {
    // TODO: use priceValStrRed
    let price = prop.lp || prop.lpr;
    if (price === 0) {
      return l10n('TBD')
    }
    return currency(price, '$', 0);
  }
  onShouldStartLoadWithRequest = (navState) => {
    var url = navState.url.split('?')[0];
    if (!url) {
      return true;
    }
    if (/^(tel|geo|mailto|sms|market)\:/.test(url)) {
      // console.log('linking--->>>>'+url)
      // Linking.openURL(url);
      // NOTE: react-native-webview changed
      return false;
    }
    return true;
  }
  strFormatDate(now) {
    var ret = now.getFullYear() + '-';
    ret += ('0' + (now.getUTCMonth() + 1)).slice(-2) + '-';
    ret += ('0' + now.getUTCDate()).slice(-2);
    return ret;
  }
  isPassed(oh = '') {
    var now = new Date();
    var nowOh = this.strFormatDate(now);
    if (nowOh > oh.split(' ')[0]) {
      return true;
    }
    return false;
  }
  nearestOhDate(prop) {
    if (!prop.oh) {
      return false;
    }
    for (var i = 0; i < prop.oh.length; i++) {
      var oh = prop.oh[i]
      if (!this.isPassed(oh[1])) {
        return oh;
      }
    }
    return null;
  }
  showPropOhTag() {
    var prop = this.state.curProp;
    return !!(prop.oh && prop.oh.length && this.nearestOhDate(prop))
  }
  isPropToplising(prop) {
    return new Date(prop) > new Date();
  }
  isTopCurProp() {
    return new Date(this.state.curProp.topTs) > new Date();
  }
  onMessage(event) {
    let str = event.nativeEvent.data;
    // console.log('onMessage: '+str);
    try {
      str = decodeURIComponent(str)
      // console.log(str);
    } catch (e) {
      console.log(e.toString());
    }
    // console.log('onMessage: ',str)
    eventEmitter.emit("app.message", str);
  }
  fnClose() {
    let self = this;
    return () => {
      self.setState({ schoolID: null, sch: null, tracksViewChanges: true });
      this.trackOff();
    };
  }
  closeModal() {
    this.toggleModal(null, 'close')
  }
  // @param[a] = modal name
  // @param[b] = action
  async toggleModal(a, b) {
    let open;
    // console.log('$$$$',b);
    if (b == null) {
      open = !this.state.propHalfDetail
    } else if (b == 'open') {
      open = true
      this.closeOtherModals();
    }
    if (!open) {
      // 手动关闭时，同时清理社区边界
      this.clearPropertyAndCommunity();
    } else {
      this.setState({ propHalfDetail: open });
    }
  }
  showListView(opt = {}) {
    // console.log('show list view in new page',opt);
    var self = this;
    if (self.showedListView) {
      return;
    }
    self.showedListView = true
    var url = `/1.5/mapSearch?mode=list&src=nativeMap&readFilter=1&appmode=${this.state.appmode}`;
    if (opt.focus) {
      url += '&focus=1'
    }
    var pageOpt = {
      hide: false,
      sel: '#callBackString',
      tp: 'pageContent',
      toolbar: false,
      forVerticalIOS: true,
      url: serverDomainIns.getFullUrl(url),
      // barColor:await getColor('mainTheme',this.state.appmode),
    }
    var props = [];
    // console.log(self);
    // return;
    if (opt.props && opt.props.length) {
      props = opt.props;
    } else {
      // console.log(this.state.mapProps,this.calcZoomLevel)
      props = this.state.items
      // Object.keys(this.state.mapProps).map( (prop) => {
      //   prop = this.state.mapProps[prop]
      //   if (prop.ids.length == 1){
      //     props.push(prop.objs[0])
      //   } else {
      //     // console.log(prop.objs[0])
      //     props = props.concat(prop.objs)
      //   }
      // })
    }
    // console.log(props);
    // return
    var cb = (val) => {
      // console.log('mapsearch cb val: ',val);
      if (/:cancel/.test(val)) {
        // console.log('canceled');
        if (/:readFilter/.test(val)) {
          this.readFilterFromStorage({ doSearch: 1 });
        }
        return;
      }
      // console.log(val)
      if (val == ':gps') {
        this.map.locateMe()
        return;
      }
      if (/^:loc=/.test(val)) {
        var loc = val.split('=')[1].split(',')
        // console.log(loc)
        this.map.setCenterAndZoom({ lat: parseFloat(loc[1]), lng: parseFloat(loc[0]), showMarker: true, cMarker: 1 }, { zoom: 16 })
        return;
      }
      if (/^redirect|^cmd-redirect:/.test(val)) {
        // return window.location = val.split('redirect:')[1]
        var url = val.split('redirect:')[1];
        // console.log('close and redirect root: '+url)
        return self.closePopup(url);
        // return window.location = url;
      }
      try {
        // var val = 'loc=43.5723199141038,-79.5785565078259&zoom=15&saletp=lease';
        var d = urlParamToObject(val);
        // console.log('+++++++',d);
        // Alert.alert(JSON.stringify(d));
        // window.bus.$emit('school-prop', d);
        self.searchSchoolProp(d);
      } catch (e) {
        // console.error(e);
      }
    }
    var str = '&' + serializeData({
      prefix: 'k',
      data: this.state.propTmpFilter
    }) + '&' + serializeData({
      prefix: 'v',
      data: this.state.propTmpFilterVals
    })
    // console.log(str)
    storageIns.setCacheItem(Constants.MapsearchFilter, str, false, (err, ret) => {
      if (err) {
        return self.flashMessage(err);
      }
      // console.log('listPageData,props=',props)
      storageIns.setCacheItem(Constants.ListPageData, props, false, (err, ret) => {
        if (err) {
          console.error(err);
          return self.flashMessage(err);
        }
        // console.log('showListView:',pageOpt)
        setTimeout(() => {
          self.showedListView = false;
        }, 1000);



        eventEmitter.emit("app.message", { msg: JSON.stringify(pageOpt), cb });
      })
    })
  }
  setSoldOnlyQuick() {
    let quickFilter = {
      ...this.state.quickFilter,
    };
    quickFilter.soldOnly = !quickFilter.soldOnly;
    this.setState({ quickFilter }, () => {
      // console.log('setSoldOnlyQuick',quickFilter)
      // this.doSearch();
      this.applyQuickFilter({ showFilter: true })
    });
  }
  setSoldOnly() {
    // let propTmpFilter = {...this.state.propTmpFilter};
    // let propTmpFilterVals = {...this.state.propTmpFilterVals};
    let state = {
      propTmpFilter: {
        ...this.state.propTmpFilter,
      },
      propTmpFilterVals: {
        ...this.state.propTmpFilterVals,
      }
    };
    if (!/^-/.test(this.state.propTmpFilter.dom)) {
      state.propTmpFilter.dom = '-90';
      state.propTmpFilterVals.dom = this.getLongMappedValueDom('-90');
    }
    state.propTmpFilter.soldOnly = !this.state.propTmpFilter.soldOnly;
    // propTmpFilter,propTmpFilterVals
    if (this.state.propTmpFilter.soldOnly) {
      var { saletp } = this.state.propTmpFilter;
      state.propTmpFilter.saleDesc = saletp == 'sale' ? 'Sold' : 'Leased';
    }
    this.setState(state, () => {
      // console.log('dosearch setSoldOnly do search6')
      this.doSearch();
    });
  }
  // val is index of domFilterValsShort
  setDomValue(item, opt = {}) {
    // console.log('setDomValue',item,opt)
    let quickFilter = this.state.quickFilter;
    if (item.tp == 'year') {
      quickFilter.dom = '';
      quickFilter.domYear = item.k
    } else {
      quickFilter.dom = item.k;
      quickFilter.domYear = ''
    }
    this.lastDomItem = item;
    this.setState({ quickFilter }, () => {
      this.domSelectRef.scrollToItem({ animated: true, item, viewPosition: 0.5 })
      this.applyQuickFilter({ showFilter: true })
    })
    // console.log(idx,quickFilter,domFilterValsShort)
  }
  selectQuickPtype(newPtype) {
    let quickFilter = this.state.quickFilter;
    let ptype2 = quickFilter.ptype2 || [];
    let found = ptype2.findIndex((element) => {
      // console.log(element.k,newPtype.k,element.k == newPtype.k)
      return element == newPtype.k
    })
    if (found !== -1) {
      ptype2.splice(found, 1)
    } else {
      ptype2.push(newPtype.k)
    }
    if (newPtype.k == 'all') {
      ptype2 = []
    }
    quickFilter.ptype2 = ptype2;
    this.setState({ quickFilter }, () => {
      this.applyQuickFilter({ showFilter: true })
    })
    // console.log(found,ptype2)
  }
  getSaleDesc(saletp = 'sale', showSoldOnly) {
    if (showSoldOnly) {
      return (saletp == 'sale') ? 'Sold' : 'Leased';
    } else {
      return (saletp == 'sale') ? 'Sale' : 'Rent';
    }
  }
  parseDom(dom, showSoldOnly) {
    let k = dom || '';
    if (showSoldOnly) {
      if (/^-/.test(k + '')) {
        return k
      } else if (k) {
        return '-' + k
      }
      return k
    }
    if ('string' !== typeof (k)) {
      k = k + ''
    }
    return k.replace('-', '')
  }
  async applyQuickFilter(opt = {}, cb) {
    // console.log(this.state.quickFilter)
    // todo: changedSaletp = true if sale<->sold;
    let showQuickFilter = false;
    if (opt.showFilter) {
      showQuickFilter = true
    }
    // return
    let isSold = this.isSoldCondition()
    let showSoldOnly = this.state.showSoldOnly;
    if (isSold !== showSoldOnly) {
      // console.log('changed!!')
      // NOTE: this is where slow happens, so we need to wait for the next tick for render
      await this.resetTags({ except: 'ptype' });
    }
    let quickFilter = this.state.quickFilter;
    let soldOnly = quickFilter.soldOnly;
    let dom = quickFilter.dom || ''
    let ptype2 = quickFilter.ptype2;
    // // parse dom to on/off days
    let domParsed = this.parseDom(dom, showSoldOnly)
    let saletp = quickFilter.saletp;
    let propTmpFilter = { ...this.state.propTmpFilter };
    let propTmpFilterVals = { ...this.state.propTmpFilterVals };
    propTmpFilter.dom = domParsed;
    propTmpFilter.domYear = this.parseDom(quickFilter.domYear, showSoldOnly)
    propTmpFilter.ptype2 = ptype2;
    propTmpFilterVals.dom = this.getLongMappedValueDom(dom.k);
    propTmpFilter.saleDesc = this.getSaleDesc(saletp, showSoldOnly)
    propTmpFilter.soldOnly = soldOnly;
    // if(!opt.showFilter){
    //   eventEmitter.emit('map.clearModals',{src:'mapSearch',backdrop:false})
    // }
    this.setState({ showQuickFilter, propTmpFilter }, () => {
      // this do search has to use new value, maybe we can solve this with redux
      setTimeout(() => {
        this.doSearch({ clear: true })
        if (cb) {
          cb()
        }
      }, 1);
    })
    // console.log('soldOnly:',soldOnly,' dom:',domParsed,'ptype:',ptype2)
  }
  isSameDom(dom1 = '', dom2 = '') {
    dom1 = dom1 + '';
    dom1 = dom1.replace('-', '')
    dom2 = dom2 + '';
    dom2 = dom2.replace('-', '')
    return dom1 == dom2
  }
  getDomDisplayValue(dom = '', domYear = '', domFilterValsShort, domYearFilterVals) {
    dom = dom + '';
    let domDisplayValue = '1 week';
    let ret = { domYear }// yearValue:'2022',}
    dom = dom.replace('-', '')
    ret.dom = dom
    // let initialDomYear = this.state.yearDom || '';
    for (let i in domFilterValsShort) {
      let tmp = domFilterValsShort[i]
      tmp.k = tmp.k.replace('-', '')
      // console.log(tmp.k,dom)
      if (this.isSameDom(tmp.k, dom)) {
        domDisplayValue = tmp.v || tmp.sv;
        break
      }
    }
    var dupOfdomYearFilterVals = []
    for (var i of domYearFilterVals) {
      i.tp = 'year'
      // console.log(i)
      dupOfdomYearFilterVals.push(i)
    }
    if (domYear && domYear != '') {
      // ret.yearValue = ''+Math.abs(parseInt(domYear))
      domDisplayValue = '' + Math.abs(parseInt(domYear))
    }
    ret.domDisplayValue = domDisplayValue
    ret.domValuesList = domFilterValsShort.concat(dupOfdomYearFilterVals)
    return ret
  }
  renderDomSelectBar() {
    let domFilterValsShort = this.state.dispVar.domFilterValsShort || [];
    let domYearFilterVals = this.state.dispVar.domYearFilterVals || [];
    let { dom, domYear, domDisplayValue, domValuesList } = this.getDomDisplayValue(
      this.state.quickFilter.dom,
      this.state.quickFilter.domYear,
      domFilterValsShort,
      domYearFilterVals,
    )
    // console.log('++++',domDisplayValue,domValuesList)
    let renderDomItem = ({ item }) => {
      let borderColor = '#f5f5f5'
      let borderWidth = 2;
      if (item.tp == 'year') {
        if (this.isSameDom(item.k, domYear)) {
          borderColor = '#40bc93'
        }
      } else {
        if (!domYear && this.isSameDom(item.k, dom)) {
          borderColor = '#40bc93'
        }
      }
      return (
        <TouchableOpacity key={'domVal' + item.k}
          style={[styles.domElement, { borderColor, borderWidth }]}
          onPress={() => { this.setDomValue(item) }
          }>
          <Text style={{ fontSize: 12, color: '#888' }}>{item.sv || item.v}</Text>
        </TouchableOpacity>)
    }
    return [<FlatList
      ref={ref => { this.domSelectRef = ref; }}
      horizontal={true}
      // initialScrollIndex={3}
      ListHeaderComponent={null}
      showsVerticalScrollIndicator={false}
      showsHorizontalScrollIndicator={false}
      style={{ backgroundColor: 'white', width: '100%', flexDirection: 'row', height: 33, marginTop: 10 }}
      data={domValuesList}
      renderItem={renderDomItem}
      onScrollToIndexFailed={({ index, averageItemLength }) => {
        // console.error('onScrollToIndexFailed, index=',index,averageItemLength)
        setTimeout(() => {
          this.scrollToCorrentYear()
        }, 100);
      }}
      keyExtractor={(item) => { return 'list' + item.k }}
    />, domDisplayValue]
    let domSelectBar2 = (<ScrollView
      style={{
        horizontal: true,
        flex: 1,
        flexDirection: 'row', marginTop: 10, width: 'auto'
      }}
      horizontal={true}
      showsHorizontalScrollIndicator={false}
    >
      {domFilterValsShort.map((t, idx) => {
        let borderColor = '#f5f5f5'
        let borderWidth = 2;
        if (t.k == dom + '') {
          borderWidth = 2;
          borderColor = '#40bc93'
        }
        return (
          <TouchableOpacity key={'domVal' + idx}
            style={[styles.domElement, { borderColor, borderWidth }]}
            onPress={() => { self.setDomValue(idx) }
            }>
            <Text style={{ fontSize: 12, color: '#888' }}>{t.sv || t.v}</Text>
          </TouchableOpacity>)
      })}

      {/* <RNPickerSelect
        key={'yearPicker'}
        style={{...pickerSelectStyles}}
        // itemStyle={{fontSize:9,color:'#888',padding:0,margin:0}}
        value={yearValue}
        onValueChange={(value, index) =>
          // self.setDomValue(index,{value,isYear:true})
          console.log('itemValue:',value,'itemIndex:',index)
        }
        placeholder={{lable:'2022',value:'-0'}}
        items = {this.state.years}
        >
      </RNPickerSelect> */}
      {/* <View style={{flexDirection:'row',width:'100%',justifyContent:'space-between'}}>
      {this.state.dispVar.domFilterValsShort.map(dom => (
        <View key={dom.k} style={{flex:1,alignItems:'center'}}>
          <Text style={{color:'#888',fontSize:14}}>{dom.sv||dom.v}</Text>
        </View>
      ))}
      </View> */}
    </ScrollView>)
  }
  _renderDomList() {
    // <div id="domFilterVals" class="side-pane" v-show="showQuickFilter">
    // <div class="soldOnly" @click="setSoldOnly()">{{_('Sold Only')}}<span class="pull-right fa" :class="[propTmpFilter.soldOnly?'fa-check-square-o':'fa-square-o']"></span></div>
    // <div v-for="t in domFilterVals" @click="selectSoldFilterVal(t)">{{t.v}}</div>
    // </div>
    if (!this.state.showQuickFilter) {
      return null;
    }
    var self = this;
    let style = {};
    // if (this.state.dispVar.domFilterValsShort && this.state.dispVar.domFilterValsShort.length) {
    //   style.height = this.state.dispVar.domFilterValsShort.length*35+35;
    // }
    let quickFilterPtypes = this.getQuickFilterPtypes()
    let quickFilter = this.state.quickFilter;
    let soldOnly; //= this.state.propTmpFilter.soldOnly;
    if (quickFilter.soldOnly != null) {
      soldOnly = quickFilter.soldOnly;
    }
    // console.log('+++++Sold only=',soldOnly, typeof(soldOnly))
    var ptype2 = quickFilter.ptype2 || [];
    let getBorderColor = (cur) => {
      let found = ptype2.find((element) => { return element == cur.k })
      if (cur.k == 'all' && ptype2.length == 0) {
        found = true
      }
      if (found) {
        return '#3FBB93'
      }
      return '#ddd'
    }
    let soldLeasedOnly = 'Including Delisted'//'Sold Only';
    // if (this.state.propTmpFilter.saletp !== 'sale'){
    //   soldLeasedOnly = 'Leased Only';
    // }
    // console.log(this.state.dispVar.domFilterValsShort);
    // console.log(self.flashMessage)
    // console.log('******',self.state.dispVar.domFilterValsShort)
    let trackFalseColor = '#ddd'
    if (Platform.OS == 'ios') {
      trackFalseColor = '#fff'
    }
    let [domSelectBar, domDisplayValue] = this.renderDomSelectBar()
    let daysOnOffMarket = 'Days on Market'
    if (this.isSoldCondition()) {
      daysOnOffMarket = 'Days off Market'
    }
    return (
      <View key={'propDomSelect'} style={[styles.overLayDomWrapper, style]}>
        <View style={{ padding: 10 }}>
          {this.state.showSoldOnly &&
            // <TouchableOpacity onPress={()=>{this.setSoldOnly()}}>
            <View style={[styles.overLayDomItem, { flexDirection: 'row', justifyContent: 'space-between' }]}>
              <Text style={{ fontWeight: 'bold', fontSize: 15 }}>{l10n(soldLeasedOnly)}</Text>
              <Switch
                style={{ marginRight: -4, transform: [{ scaleX: 0.8 }, { scaleY: .8 }] }}
                trackColor={{ false: trackFalseColor, true: "#3FBB93" }}
                thumbColor={!soldOnly ? "#fff" : "#3FBB93"}
                // ios_backgroundColor="#3FBB93"
                onValueChange={() => { this.setSoldOnlyQuick() }}
                value={!soldOnly}
              />
              {/* <Icon name={this.state.propTmpFilter.soldOnly?'check-square-o':'square-o'} size={12} color="#888" style={{paddingRight:12, paddingTop:1}}/> */}
            </View>
            // </TouchableOpacity>
          }
          {this.state.dispVar.domFilterValsShort &&
            // this.state.dispVar.domFilterValsShort.map(dom => (
            //   <TouchableOpacity
            //     key={dom.k}
            //     onPress={()=>{this.selectSoldFilterVal(dom)}}
            //     // style={{backgroundColor:'red'}}
            //     >
            //     <View style={[styles.overLayDomItem]}>
            //       <Text style={{color:'#888',fontSize:14}}>{dom.sv||dom.v}</Text>
            //     </View>
            //   </TouchableOpacity>
            // ))
            <View style={{ flexDirection: 'column', marginTop: 10 }}>
              <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                <Text style={{ fontSize: 15, fontWeight: 'bold' }}>{l10n(daysOnOffMarket)}</Text>
                <Text style={{ fontSize: 12, color: '#3FBB93' }}>{domDisplayValue}</Text>
              </View>
              {/* <Slider
              style={{width: '100%', height: 36}}
              minimumValue={0}
              maximumValue={this.state.dispVar.domFilterValsShort.length-1}
              minimumTrackTintColor="#3FBB93"
              step={1}
              value={initialDragValue}
              onSlidingComplete={(idx)=>{this.setDomValue(idx)}}
              // maximumTrackTintColor="#000000"
            /> */}
              {domSelectBar}
            </View>
          }
          <View style={{ flexDirection: 'column', marginTop: 20 }}>
            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
              <Text style={{ fontSize: 15, fontWeight: 'bold' }}>{l10n('Prop Type')}</Text>
              <Text style={{ fontSize: 12, color: '#ddd' }}>{l10n('Multi-Select')}</Text>
            </View>
            <View style={[styles.center, {
              width: '103%',
              marginLeft: 9,
              flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'flex-start', marginTop: 10
            }]}>
              {quickFilterPtypes.map(dom => (
                <TouchableOpacity key={dom.k} style={[{ width: '25%' }]} onPress={() => { this.selectQuickPtype(dom) }}>
                  <View style={[styles.center, {
                    alignSelf: 'stretch',
                    borderColor: getBorderColor(dom),//'#f1f1f1',
                    borderWidth: 2,
                    height: 33,
                    // width:'92%',
                    // marginLeft:10,
                    paddingLeft: 3,
                    paddingRight: 3,
                    overflow: 'hidden',
                    marginTop: 3,
                    marginBottom: 3,
                    // backgroundColor:'red',
                    marginRight: 10
                  }]}>
                    <Text numberOfLines={1} style={{ color: '#888', fontSize: 11 }}>{dom.v}</Text>
                  </View>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </View>
        <View style={{ padding: 0, flexDirection: 'row' }}>
          <TouchableOpacity
            style={[styles.center, { width: '100%', backgroundColor: '#3FBB93', height: 45 }]}
            onPress={() => { this.hideSoldDomSelect({ hideBackdrop: true }) }}
          >
            <Text style={{ color: '#fff', fontSize: 17 }}>{l10n('OK')}</Text>
          </TouchableOpacity>
          {/* <TouchableOpacity
            onPress={()=>{this.toggleQuickFilter('close')}}
            style={[styles.center,{width:'50%',height:45}]}>
            <Text style={{color:'black',fontSize:17}}>{l10n('Cancel')}</Text>
          </TouchableOpacity> */}
        </View>
        {/* {this.state.dispVar.domFilterValsShort &&
          <FlatList
            data={this.state.dispVar.domFilterValsShort}
            keyExtractor={(item) => item.k}
            renderItem={({item}) => {
              let dom = item;
              // console.log('++++++',dom)
              return (
              <TouchableOpacity key={dom.k} onPress={()=>{this.selectSoldFilterVal(dom)}}>
                <View style={[styles.overLayDomItem]}>
                  <Text style={{color:'#616161',fontSize:13}}>{dom.v}</Text>
                </View>
              </TouchableOpacity>
            )}
            }>
          </FlatList>
        } */}
      </View>
    )
  }
  // show/hide quick Filter
  toggleQuickFilter(opt = {}) {
    let state = {
      showQuickFilter: true,//!this.state.showQuickFilter,
      showSoldOnly: false,
      quickFilter: { ...this.state.quickFilter },
      propTmpFilter: { ...this.state.propTmpFilter }
    };

    if (opt.cmd == 'close') {
      state.showQuickFilter = false;
    }
    if (opt.sold) {
      state.showSoldOnly = true
    }
    let isSold = this.isSoldCondition()
    let showSoldOnly = state.showSoldOnly;
    // sale <-> sold
    if (isSold !== showSoldOnly) {
      // console.log('changed!')
      if (showSoldOnly) {
        state.quickFilter.dom = '-90'
      } else {
        state.quickFilter.dom = ''
      }
      state.quickFilter.domYear = ''
    } else {
      state.quickFilter.dom = this.state.propTmpFilter.dom

    }
    let saletp = this.state.propTmpFilter.saletp;
    let dom = state.quickFilter.dom || ''
    let domParsed = this.parseDom(dom, showSoldOnly)
    // NOTE: change these 2 value to force render new value, otherwise, it will not render, and applyQuickFilter will be async function when resetting tags
    state.propTmpFilter.saleDesc = this.getSaleDesc(saletp, showSoldOnly)
    state.propTmpFilter.dom = domParsed;
    if (state.propTmpFilter.domYear) {
      state.quickFilter.domYear = state.propTmpFilter.domYear
      this.lastDomItem = this.getDomYearValueInLoop(state.propTmpFilter.domYear)
    }
    // state.propTmpFilter.domYear = this.parseDom(quickFilter.domYear,showSoldOnly)
    // console.log('++++show quickfilter',state.propTmpFilter,state.quickFilter,state.lastDomItem)
    // NOTE: when show dom select, hide other model. eg. propPreview/school
    eventEmitter.emit('map.clearModals', { src: 'mapSearch', backdrop: state.showQuickFilter })
    this.setState(state, () => {
      this.scrollToCorrentYear()
      this.applyQuickFilter({ showFilter: true })
    });
    // this.setState(prevState => ({
    //   showQuickFilter:!prevState.showQuickFilter
    // }))
  }
  scrollToCorrentYear() {
    let state = this.state
    // console.log('scroll to correct year',state.quickFilter.domYear,this.lastDomItem)
    if (state.quickFilter.domYear && this.lastDomItem && this.lastDomItem.tp == 'year') {
      // setTimeout(() => {
      this.domSelectRef.scrollToItem({ animated: false, item: this.lastDomItem, viewPosition: 0.5 })
      // }, 300);
    }
  }
  async switchToSaleMode() {
    let propTmpFilter = { ...this.state.propTmpFilter };
    let propTmpFilterVals = { ...this.state.propTmpFilterVals };
    // // var self = thhis;
    // this.setState({propTmpFilter,propTmpFilterVals},()=>{
    //   this.doSearch();
    // });
    var state = {};
    var { saletp, dom } = this.state.propTmpFilter;
    if (dom) { ///^-/.test(dom)
      propTmpFilter.dom = '';
      propTmpFilterVals.dom = '';
    }
    propTmpFilter.saleDesc = (saletp == 'sale') ? 'Sale' : 'Rent';
    state.showQuickFilter = false;
    state.propTmpFilter = propTmpFilter;
    state.propTmpFilterVals = propTmpFilterVals;
    await this.setStateAsync(state)
    await this.resetTags({ except: 'ptype' });
    this.doSearch({ clear: true });
    eventEmitter.emit('map.clearModals', { src:'mapSearch',backdrop: false })
    this.toggleQuickFilter()
    // this.state.propTmpFilter.dom = '';
    // this.state.propTmpFilterVals.dom  = '';

    // var saletp = this.state.propTmpFilter.saletp;
    // var state = {};
    // var newTp = 'sale';
    // if (this.state.propTmpFilter.saletp == 'sale') {
    //   newTp = 'lease'
    // }
    // this.setSaleTp(newTp)
    // state.propTmpFilter = propTmpFilter;
    // this.setState(prevState => ({
    //     jasper: {
    //         ...prevState.jasper,
    //         name: 'something'
    //     }
    // }))
    // var self = this;
    // setTimeout(function () {
    //   self.doSearch({clear:true});
    //   // console.log(self.state);
    // }, 100);
    // this.doSearch({clear:true});
  }
  // set bar color to inital
  goBack() {
    // console.log(this.state.appmode,this.initialAppmode,this.thisProps)
    // if(this.initialBarColor){
    //   eventEmitter.emit('navbar.setBarColor',{barColor:this.initialBarColor});
    // } else {
    //   eventEmitter.emit(SYSTEM.CHANGE_APP_MODE,{val:this.initialAppmode});
    // }
    this.clearItems()
    this.unsetNearbyPropItem()
    this.unsetCurrentSavedSearch()
    this.setShowDrawPolygon(false)
  }
  async switchBetweenExclusiveAndSaleMode() {
    let nextMode = 'rm'
    if (this.state.appmode !== 'mls') {
      nextMode = 'mls'
    }
    let opt = { store: 1 }; //{store:1}
    let state = {
      appmode: nextMode,
      highlightTextColor: await getColor('highlightText', nextMode),
      highlightBgColor: await getColor('highlightBg', nextMode)
    }
    // RMStorage.setCacheItem('appmode',nextMode,opt)
    this.setStateAsync(state)
    // console.log('++++emit,CHANGE_APP_MODE in set cache,')
    eventEmitter.emit(Constants.ChangeAppMode, { val: nextMode, mapSwitch: true });
    if (nextMode !== 'mls') {
      // remote dom?
      // await this.setSaleDesc('sale','','Sale');
      this.setSearchMode({ k: 'Exclusive', noSearch: false });
    } else {
      // set to mls sale
      this.setSearchMode({ k: 'Residential' })
    }
  }
  _renderResaleButton() {
    if (!this.state.dualHomepage) {
      return null
    }
    let resaleOrExclusive = l10n('RESALE')
    if (this.state.appmode == 'mls') {
      resaleOrExclusive = l10n('EXCLUSIVE', 'rm')
    }
    return <View key={'propResaleButton'} style={[styles.overLayResaleWrapper]}>
      <TouchableOpacity style={[styles.btnListView, styles.navListBtn, {
        height: 40,
        flex: 1
      }]}
        onPress={(e) => { this.showListView() }}>
        <View style={{ flexDirection: 'row' }}>
          <Icon name="rmlist"
            size={16}
            color="white"
            style={{ marginLeft: 5, marginTop: 2 }}
          // hitSlop={{top: 10, bottom: 10, left: 0, right: 120}}
          />
          <Text style={{ color: 'white', fontSize: 16, paddingLeft: 5 }}>{l10n('LIST')}</Text>
        </View>
      </TouchableOpacity>
      <TouchableOpacity style={[styles.btnListView, styles.navListBtn, {
        flex: 1,
        borderLeftColor: '#666',
        height: 40,
        borderLeftWidth: 1
      }]}
        onPress={(e) => { this.switchBetweenExclusiveAndSaleMode() }}>
        <View>
          <Text style={{ color: 'white', fontSize: 16 }}>{resaleOrExclusive}</Text>
        </View>
      </TouchableOpacity>
    </View>
  }
  renderOverlay(id) {
    var style = {};
    // if(Platform.OS == 'ios'){
    //   style = {zIndex:17};
    // }

    return [
      this.renderPinProp(),
      this.renderSavedSearchCondition(),
      this._renderDomList(),
      this._renderUndoButton(),
      this._renderDragTip(),
    ] //this._renderResaleButton()
    return (
      <View key={id} style={style}>
        {/* <View > */}
        {this._renderDomList()}
        {/* </View> */}
        {/* <View style={{zIndex:1}}> */}
        {this._renderResaleButton()}
        {/* </View> */}
      </View>
    );
    if (this.state.propTmpFilter.src !== 'mls') {
      return null;
    }
    var strDomValue = l10n('1 week');
    var colorSaleBg = '#007aff'
    var colorSoldBg = 'white'
    var colorSaleFont = 'white'
    var colorSoldFont = 'black'
    var domValColor = '#aaa'
    if (/^-/.test(this.state.propTmpFilter.dom)) {
      strDomValue = this.getLongMappedValueDom(this.state.propTmpFilter.dom, true);
      colorSaleBg = 'white'
      colorSoldBg = '#007aff'
      colorSaleFont = 'black'
      colorSoldFont = 'white'
      domValColor = '#e2dddd'
    }
    return (
      <View key={id} style={style}>
        <View style={[styles.overLayShadow, styles.overLaySaletpWrapper]}>
          <TouchableOpacity
            onPress={() => this.switchToSaleMode()}
            style={[styles.overLaySaletp, { backgroundColor: colorSaleBg }]}
          >
            <Text style={{ color: colorSaleFont, fontSize: 13, fontWeight: 'bold' }}>{strSaleRent}</Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => this.toggleQuickFilter()}
            style={[]}
          >
            <View style={[styles.overLaySaletpSold, { backgroundColor: colorSoldBg }]}>
              <Text style={{ color: colorSoldFont, fontSize: 13, fontWeight: 'bold' }}>{strSoldLeased}</Text>
              <Text style={{ color: 'black', fontSize: 11, color: domValColor, paddingLeft: 5 }}>{strDomValue}</Text>
            </View>
          </TouchableOpacity>
        </View>
        {this._renderDomList()}
      </View>
    )
  }
  markerPressed(data, isFromSoldLayer = false) {
    return (e) => {
      // console.log(e.nativeEvent)
      if (e.persist) {
        e.persist();  // Avoids warnings relating to https://fb.me/react-event-pooling
      }

      // 取消之前的请求
      if (this.cmtyBndsController) {
        this.cmtyBndsController.abort();
      }


      // 发起社区边界请求 - 添加去重机制
      if (data && data.ids && data.ids.length > 0) {
        // 检查是否已经渲染了该社区的边界
        const currentPropertyId = data.ids[0];
        const alreadyRendered = this.state.cmtyBnds?._id &&
          this.state.showCmtyBnds &&
          this.state.currentPropertyId === currentPropertyId;

        if (!alreadyRendered) {
          // 创建新的 AbortController
          this.cmtyBndsController = new AbortController();
          // 生成新的请求ID
          mainRequest({
            url: '/1.5/props/cmtyBnds',
            data: { _id: currentPropertyId },
            method: 'post',
            signal: this.cmtyBndsController.signal // 添加 signal 参数
          }).then(ret => {
            if (ret.err || ret.e) {
              global.rmLog(`MapProps.jsx:6325~~~markerPressed`, ret.err, 'error');
              return;
            }

            // 获取新的边界数据
            const newCmtyBnds = ret.cmtyBnds;
            const isEqual = this.state.cmtyBnds?._id === newCmtyBnds?._id;
            // 比较新旧数据是否相同
            if (!isEqual) {
              // 只有在数据确实发生变化时才更新状态
              this.setState({
                cmtyBnds: newCmtyBnds,
                showCmtyBnds: true,
                currentPropertyId: currentPropertyId
              });
              global.rmLog(`MapProps.jsx:6340~~~markerPressed`, '社区边界数据已更新');
            } else {
              global.rmLog(`MapProps.jsx:6342~~~markerPressed`, '社区边界数据未发生变化，跳过更新');
            }

          }).catch(err => {
            // 忽略取消请求的错误
            // 只有在不是取消请求的错误时才打印日志
            if (err.name !== 'CanceledError' && err.name !== 'AbortError') {
              global.rmLog(`MapProps.jsx:6346~~~markerPressed`, err, 'error');
            }
          });
        } else {
          global.rmLog(`MapProps.jsx:6349~~~markerPressed`, '社区边界已渲染，跳过请求');
        }
      }

      // 清除模态框
      eventEmitter.emit('map.clearModals', { src: 'mapSearch', backdrop: false });
      eventEmitter.emit(Constants.MapTransitStopModal, { val: false });

      // 处理多个属性的情况
      if (data.ids.length > 1) {
        this.showListView({ props: data.objs });
        return;
      }

      this.setState({ hideDummyPropPreview: false });

      var usePropImg = true;
      if (data.objs[0]._id != this.state.curProp._id) {
        // usePropImg = false;
      }

      var curProp = data.objs[0];
      this.state.pan.setValue({ x: 0, y: 0 });

      this.setState({
        curProp,
        selectedMarkerId: curProp._id, // 永久保存选中的marker ID
        tracksViewChanges: true,
        usePropImg,
        isFromSoldLayer,
      });

      this.trackOff();
      this.toggleModal(null, 'open');
      return false;
    }
  }
  renderHeaderMenu() {
    return null;
  }
  onOffView() {
    // on/off setting to show on map
    // return null;
    return {
      icon: 'rmhouse',
      iconSize: 26,
      paddingLeft: 3,
      toggle: true,
      name: l10n('Property'),
      type: 'mapProperty',
      toggleOnOff: (onOff) => { this.toggleOnOff(onOff) },
      on: this.featureOn
    }
  }
  toggleOnOff(onOff = '') {
    // let realOnOff;
    // if(onOff !== null){
    //   if(onOff == 'off'){
    //     realOnOff = false
    //   } else {
    //     realOnOff = true
    //   }
    // }
    // console.log('toggle on off: ->', onOff)
    if (onOff == 'off') {
      this.featureOn = true;
    } else if (onOff == 'on') {
      // do nothing if already on
      if (this.featureOn) {
        return
      }
    }
    eventEmitter.emit('map.props.fetureOn', !this.featureOn)
    this.setShowDrawPolygon(!this.featureOn)
    if (this.featureOn = !this.featureOn) {
      this.map.onRegionChangeComplete();
    } else {
      this.setState({})
    }
  }

  // 添加渲染社区边界的方法
  renderCommunityBoundaries() {
    if (!this.state.showCmtyBnds || !this.state.cmtyBnds.bnds) {
      return null;
    }

    // 辅助函数：创建单个多边形组件
    const createPolygonComponent = (coordinates, holes, key) => (
      <Polygon
        key={key}
        coordinates={coordinates}
        holes={holes.length > 0 ? holes : undefined}
        strokeWidth={2}
        strokeColor="#3388FF"
        fillColor="rgba(51, 136, 255, 0.2)"
      />
    );

    // 辅助函数：转换GeoJSON坐标为react-native-maps格式
    const convertCoordinates = coords => coords.map(coord => ({
      latitude: coord[1],
      longitude: coord[0]
    }));

    // 将GeoJSON格式的边界数据转换为react-native-maps可用的格式
    return this.state.cmtyBnds.bnds.features.flatMap((feature, featureIndex) => {
      const featureKey = feature.properties.bnid || featureIndex;

      if (feature.geometry.type === 'Polygon') {
        // Polygon 处理
        const coordinates = convertCoordinates(feature.geometry.coordinates[0]);
        const holes = feature.geometry.coordinates.slice(1).map(convertCoordinates);

        return createPolygonComponent(
          coordinates,
          holes,
          `cmtyBnd-${featureIndex}-${featureKey}`
        );
      } else if (feature.geometry.type === 'MultiPolygon') {
        // MultiPolygon 处理 - 为每个多边形创建独立的Polygon组件
        return feature.geometry.coordinates.map((polygon, polygonIndex) => {
          const coordinates = convertCoordinates(polygon[0]);
          const holes = polygon.slice(1).map(convertCoordinates);

          return createPolygonComponent(
            coordinates,
            holes,
            `cmtyBnd-${featureIndex}-${featureKey}-poly-${polygonIndex}`
          );
        });
      }
      return null;
    });
  }

  // 清除当前房产和社区边界
  clearPropertyAndCommunity() {
    this.setState({
      curProp: {},              // 清空当前房产
      propHalfDetail: false,    // 关闭房产卡片
      cmtyBnds: null,          // 清空社区边界数据
      showCmtyBnds: false,     // 隐藏社区边界
      currentPropertyId: null   // 清空当前房产ID
    });
  }
  // 修改renderOnMap方法，添加社区边界渲染
  renderOnMap() {
    if (!this.featureOn) return;
    if (!this.state.showMapMarkers) return;

    let self = this;
    let view = [];
    let renderedProps = {}

    // 添加社区边界到视图数组
    const communityBoundaries = this.renderCommunityBoundaries();
    if (communityBoundaries) {
      view = view.concat(communityBoundaries);
    }

    //Notes:"单个房源的marker渲染，如果房源在mapSoldProps(layer)已经搜索出来，避免渲染两遍。
    //else里是房源列表marker（ ⑨ ）渲染 "
    /**
     * 增加可读性：
     * renderOnMap(){
        a = renderA()
        if a ...
        b = renderB()
        if b ...
        c = renderC()
        if c ...
        }
     */
    for (let i of ['mapProps', 'mapSoldProps']) {
      if (!Object.keys(this.state[i]).length) {
        continue
      }
      Object.keys(this.state[i]).map((propid) => {
        let prop = this.state[i][propid]
        let renderThisProp = true

        if (prop.ids.length == 1) {
          if (i == 'mapSoldProps' && renderedProps[propid]) {
            renderThisProp = false
          }
          renderedProps[propid] = true

          if (renderThisProp) {
            view.push(this.renderPropMarker(prop, { tp: i }))
          }
        } else {
          view.push(this.renderPropNumberIcon(prop, { tp: i }))
        }
      })
    }

    // 渲染保存的搜索多边形
    if (this.state.savedSearchCond && this.state.savedSearchCond.bnds) {
      view.push(this.renderPolygon())
      view = view.concat(this.renderPolygonPoints())
    }

    return view;
  }
}

let CIRCLE_RADIUS = 30;
const pickerSelectStyles = StyleSheet.create({
  inputIOS: {
    flex: 2, height: 27,
    fontSize: 12,
    minWidth: 36,
    // alignItems:'center',
    // paddingVertical: 12,
    // paddingHorizontal: 10,
    paddingTop: 3,
    paddingBottom: 2,
    marginTop: -1,
    // borderWidth: 1,
    // borderColor: 'gray',
    // borderRadius: 2,
    color: '#888',
    backgroundColor: '#f5f5f5',
    // paddingRight: 30, // to ensure the text is never behind the icon
  },
  inputAndroid: {
    // fontSize: 16,
    // paddingHorizontal: 10,
    // paddingVertical: 8,
    // borderWidth: 0.5,
    // borderColor: 'purple',
    // borderRadius: 8,
    // color: 'black',
    // paddingRight: 30, // to ensure the text is never behind the icon
  },
});
const styles = StyleSheet.create({
  container: {
    flexDirection: 'column',
    alignSelf: 'flex-start',
  },
  propPreviewWrapper: {
    position: 'absolute',
    bottom: 0,
    // top:74,
    backgroundColor: 'white',
    left: 0,
    right: 0,
    zIndex: 20,
    height: 170,
  },
  previewIconBg: {
    backgroundColor: 'rgba(0,0,0,0.7)',
    width: 24,
    height: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  previewIconWrapper: {
    position: 'absolute',
    top: 7,
    zIndex: 10,
    alignItems: 'center',
    justifyContent: 'center',
    height: 32,
    flexDirection: 'row',
  },
  price: {
    color: 'white',
    paddingRight: 5,
    fontWeight: '500',
    fontSize: 19,
  },
  shadowText: {
    textShadowColor: 'rgb(0, 0, 0)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2
  },
  rmtype: {
    borderRadius: 0,
    borderWidth: 1,
    borderColor: 'white',
    paddingLeft: 3,
    paddingRight: 3,
    paddingTop: 1,
    paddingBottom: 1,
    color: 'white',
    fontSize: 10,
    marginBottom: 3,
  },
  previewPropDetails: {
    position: 'absolute',
    flexDirection: 'row',
    paddingLeft: 10,
    paddingRight: 10,
    // backgroundColor:'rgba(0,0,0,0.7)'
  },
  previewPropDetailsWrapper: {
    // backgroundColor: 'linear-gradient(transparent, #000000)',
    // backgroundColor:'rgba(0,0,0,0.7)'
    // position:'absolute',bottom:0,height:70,width:this.screenWidth,backgroundColor:'rgba(0,0,0,0.7)'
  },
  propPreview: {
    position: 'absolute',
    bottom: 10,
  },
  dom: {
    color: '#f9c730',
    fontSize: 11,
    marginTop: 1,
  },
  bdbthgr: {
    fontWeight: '500',
    color: 'white',
    fontSize: 13,
    paddingRight: 5,
    // paddingLeft:0,
  },
  cpmAds: {
    height: 64,//width/5.77,
    // backgroundColor:'yellow'
  },
  btnListView: {
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
  },
  overLayShadow: {
    shadowColor: '#d2d2d2',
    shadowOffset: { width: 1, height: 3 },
    shadowRadius: 3,
    shadowOpacity: 0.8,
  },
  overLaySaletpWrapper: {
    position: 'absolute',
    top: 20,
    left: 10,
    width: 160,
    height: 30,
    zIndex: 15,
    borderRadius: 5,
    overflow: 'hidden',
    backgroundColor: 'transparent',
    flex: 1,
    justifyContent: 'space-between',
    alignItems: 'center',
    flexDirection: 'row',
  },
  overLaySaletp: {
    // borderRadius:4,
    width: 40,
    height: 30,
    // backgroundColor: '#007aff',
    // padding: 3,
    // flex:1,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
  },
  overLaySaletpSold: {
    // borderRadius:4,
    width: 120,
    height: 30,
    // backgroundColor: 'white',
    // flex:1,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
  },
  overLayDomItem: {
    // width: 120,
    height: 40,
    backgroundColor: 'white',
    flexDirection: 'row',
    // borderBottomWidth: 0.5,
    // borderBottomColor: '#f1f1f1',
    alignItems: 'center',
    // paddingLeft: 12,
    zIndex: 50,
  },
  overLayPinProp: {
    position: 'absolute',
    top: 44 * 2 + statusBarHeight,
    left: 0,
    right: 0,
    zIndex: 16,
    flexDirection: 'row',
    paddingLeft: 0,
    marginRight: 0,
    paddingTop: 0,
    justifyContent: 'space-between',
    flexWrap: 'nowrap',
    borderTopColor: '#f1f1f1',
    // backgroundColor:'blue',
    borderTopWidth: 1,
    width: '100%',
    backgroundColor: '#fff',
    opacity: 0.8,
    height: 44,
    overflow: 'hidden',
    alignContent: 'center'
  },
  overLayDomWrapper: {
    // paddingTop:30,
    position: 'absolute',
    top: 44 * 2 + statusBarHeight,
    left: 0,
    right: 0,
    // width: 120,
    // height:160,
    overflow: 'hidden',
    zIndex: 17,
    // borderRadius:5,
    // overflow: 'scroll',
    // backgroundColor:'red',
    backgroundColor: 'white',
    // flex: 1,
    // justifyContent: 'left',
    // alignItems: 'center',
    // flexDirection: 'row',
  },
  overLayResaleWrapper: {
    // alignItems:'center',
    justifyContent: 'center',
    position: 'absolute',
    // alignSelf:'center',
    top: gWindowHeight - 105,
    // bottom: 155,
    left: gWindowWidth / 2 - 105,
    right: 0,
    width: 210,
    height: 40,
    zIndex: 13,
    borderRadius: 24,
    flexDirection: 'row',
    backgroundColor: getColorStatic('greyButton'),
    borderColor: getColorStatic('darkGrey'),
    borderWidth: 0.5,
  },
  bottomNavLink: {
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    width: '20%',
    paddingBottom: 2,
  },
  buttonContainer: {
    flexDirection: 'row',
    // marginVertical: 20,
    zIndex: 15,
    height: 50,
    backgroundColor: 'white',
  },
  loadingTipWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: 28,
    backgroundColor: 'white',
    position: 'absolute',
    // bottom:50,
    left: 0,
    right: 0,
    zIndex: 15,
    borderBottomColor: '#f1f1f1',
    borderBottomWidth: 1,
  },
  circle: {
    position: 'absolute',
    backgroundColor: "skyblue",
    width: CIRCLE_RADIUS * 2,
    height: CIRCLE_RADIUS * 2,
    borderRadius: CIRCLE_RADIUS,
    zIndex: 15,
    left: 30,
    bottom: 30,
  },
  loadingTipWrapperNew: {
    shadowColor: '#c1c1c1',
    shadowOffset: {
      width: 3,
      height: -3,
    },
    shadowOpacity: 0.7,
    shadowRadius: 3,
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'flex-start',
    height: 268,
    marginBottom: -220,
    backgroundColor: 'white',
    position: 'absolute',
    bottom: 50,
    left: 0,
    right: 0,
    zIndex: 15,
    borderRadius: 13,
    // borderBottomColor:'#f1f1f1',
    // borderBottomWidth:1,
  },
  botNavBarButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    height: 30,
    paddingTop: 6,
  },
  saleRentSelectWrapper: {
    paddingTop: 21,
    paddingRight: 3,
    paddingBottom: 15,
    paddingLeft: 3,
    // alignItems:'center',
    // justifyContent:'center',
    flexDirection: 'row',
  },
  saleTypesWrapper: {
    // alignItems:'center',
    // justifyContent:'center',
    flexDirection: 'row',
    flexWrap: 'wrap',
    // backgroundColor:'yellow',
    // paddingTop:10,
    paddingBottom: 10,
    height: 150,
    overflow: 'hidden',
  },
  center: {
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
  },
  saleTypes: {
    width: gWindowWidth / 4,
    paddingTop: 10,
  },
  saleTypesCloseBtn: {
    paddingTop: 10,
    paddingBottom: 10,
    marginTop: 10,
    alignSelf: 'center',
    borderTopColor: '#f1f1f1',
    borderTopWidth: 1,
    width: gWindowWidth,
  },
  saleTypesModalWrapper: {
    width: gWindowWidth,
    height: (270 + bottomBarHeight),
  },
  topMarker: {
    zIndex: 13
  },
  previewTag: {
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 10,
    height: 20,
    flexDirection: 'row',
  },
  preViewText: {
    fontSize: 10,
    color: 'white',
    paddingTop: 3,
    paddingBottom: 3,
    paddingLeft: 10,
    paddingRight: 10,
  },
  ad: {
    backgroundColor: '#E03131'
  },
  oh: {
    backgroundColor: '#E7AE00'
  },
  subTag: {
    borderRadius: 12,
    paddingTop: 4,
    paddingLeft: 15,
    paddingRight: 15,
    paddingBottom: 4,
    backgroundColor: '#f5f5f5'
  },
  domElement: {
    flex: 1, borderStyle: 'solid',
    paddingTop: 7,
    paddingLeft: 4,
    paddingRight: 4,
    height: 33,
    paddingBottom: 2,
    margin: 0, backgroundColor: '#f5f5f5',
    alignItems: 'center'
  }
});

export default MapProps;
