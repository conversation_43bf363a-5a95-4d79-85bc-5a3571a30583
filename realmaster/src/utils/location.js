/**
 * @fileoverview 位置服务工具类，用于处理地理位置相关功能
 * @module utils/location
 */

import Geolocation from 'react-native-geolocation-service';
import Constants from '../config/constants';
import { l10n } from './i18n';
import { alert, logError } from './logger';
import storageIns from './storage';
import { validLocationInCa } from './business';

/** @constant {string} GEOPOSITION - 存储地理位置数据的键名 */
const GEOPOSITION = Constants.GeoPositon;
/** @constant {number} GEO_TIMEOUT - 地理位置请求超时时间（毫秒） */
const GEO_TIMEOUT = 20000;

/** @type {Array|null} 追踪正在进行的地理位置请求的回调函数数组 */
let geoInProgress = null;
/** @type {TimeoutID|null} 地理位置请求超时计时器 */
let geoTimeout = null;
/** @type {number|null} 位置监听器ID */
let watchID;
/** @type {Object|null} 缓存的地理位置数据 */
let geoPosition = null;

/**
 * 处理各种地理位置错误场景
 * @param {Error|string} error - 错误对象或错误消息
 * @param {Object} [opt={}] - 选项对象
 * @param {boolean} [opt.noAlert] - 如果为true，则不显示错误提示
 * @returns {Object} 包含错误消息和静默状态的结果对象
 * @private
 */
const handleGeolocationError = (error, opt = {}) => {
  const errorPatterns = {
    NO_PROVIDER: { pattern: /No\savailable/, message: l10n('No available location provider') },
    PERMISSION_DENIED: { pattern: /denied\saccess/, message: l10n('GEO_ERROR: User Denied access to location service'), code: 1 },
    DISABLED: { pattern: /No\slocation\sprovider/, message: l10n('GEO_ERROR: User disabled location service'), code: 2 },
    TIMEOUT: { pattern: /timed\sout|within/, message: l10n('GEO_ERROR: Location service timed out'), code: 3 }
  };

  let toasted = false;
  
  for (const [key, { pattern, message, code }] of Object.entries(errorPatterns)) {
    if ((code && error.code === code) || pattern.test(error.message || error)) {
      if (!opt.noAlert && key !== 'TIMEOUT') {
        alert(l10n(message));
      }
      toasted = true;
      break;
    }
  }

  if (!toasted) {
    logError('GPS Error:' + error);
  }

  return { err: error.message || error, silence: toasted };
};

/**
 * 更新并存储当前地理位置
 * @param {Object} pos - 地理位置API返回的位置对象
 * @returns {Promise<Object|null>} 更新后的位置对象，更新失败则返回null
 * @private
 */
const updateGeoPosition = async (pos) => {
  if (!pos?.coords) return null;
  
  try {
    const tmp = { ...pos };
    await storageIns.setItem(GEOPOSITION, JSON.stringify(tmp));
    geoPosition = tmp;
    global.rmLog(`[location.js:75~updateGeoPosition]`, geoPosition);
    return tmp;
  } catch (error) {
    global.rmLog(`[location.js:78~updateGeoPosition]`, error, 'error');
    return null;
  }
};

/**
 * 从存储中读取地理位置数据
 * @param {Function} cb - 接收位置数据的回调函数
 * @public
 */
const readGeoPosition = (cb) => {
  storageIns.getItemObj(GEOPOSITION, (err, pos) => {
    if (err) {
      logError('ERROR readGeoPostion' + err);
    } else {
      geoPosition = pos;
    }
    if (cb) cb(geoPosition);
  });
};

/**
 * 获取当前地理位置
 * @param {Object} [opt={}] - 地理位置请求的选项
 * @param {Function} [cb] - 接收位置数据的回调函数
 * @public
 */
const getGeoPosition = (opt = {}, cb) => {
  if (!cb && typeof opt === 'function') {
    cb = opt;
    opt = {};
  }

  if (cb) {
    if (geoInProgress) {
      geoInProgress.push(cb);
      return;
    }
    geoInProgress = [cb];
  }

  const callbackAll = (ret) => {
    clearTimeout(geoTimeout);
    geoInProgress?.forEach(fn => fn?.(ret));
    geoInProgress = null;
  };

  geoTimeout = setTimeout(() => {
    if (geoInProgress) {
      callbackAll({ error: "Timeout" });
    }
  }, GEO_TIMEOUT + 50);

  Geolocation.getCurrentPosition(
    async pos => {
      const gpsRes = validLocationInCa(pos);      
      await updateGeoPosition(gpsRes);
      if (geoInProgress) callbackAll(gpsRes);
      clearTimeout(geoTimeout);
    },
    error => {
      const errorResult = handleGeolocationError(error, opt);
      if (geoInProgress) callbackAll(errorResult);
    },
    { 
      enableHighAccuracy: true, // 启用高精度定位
      timeout: GEO_TIMEOUT,     // 定位超时时间
      maximumAge: 10000         // 缓存有效期
    }
  );
};

/**
 * 开始监听用户位置变化
 * @public
 */
const watchGeoPosition = () => {
  watchID = Geolocation.watchPosition(updateGeoPosition);
};

/**
 * 停止监听用户位置
 * @public
 */
const clearWatch = () => {
  Geolocation.clearWatch(watchID);
};

/**
 * 获取缓存的地理位置数据，并验证是否在加拿大境内
 * @returns {Object|null} 有效的位置对象或null
 * @public
 */
const getCacheGeoPos = () => {
  if (geoPosition?.coords) {
    const validPos = validLocationInCa(geoPosition);
    // 如果缓存的位置不在加拿大境内，返回有效的位置
    if (validPos.coords.latitude !== geoPosition.coords.latitude ||
        validPos.coords.longitude !== geoPosition.coords.longitude) {
      return validPos;
    }
  }
  return geoPosition;
};

// 导出公共API
export { 
  readGeoPosition,    // 读取存储的位置
  watchGeoPosition,   // 监听位置变化
  getCacheGeoPos,     // 获取缓存的位置
  getGeoPosition,     // 获取当前位置
  clearWatch         // 停止位置监听
};
