{"LogInfo with file and line": {"prefix": "_log", "body": ["global.rmLog(`${TM_FILENAME}:${TM_LINE_NUMBER}~~~${TM_CURRENT_FUNCTION}`, );"], "description": "Log info with file, line, and function"}, "LogInfoError with file and line": {"prefix": "_loge", "body": ["global.rmLog(`${TM_FILENAME}:${TM_LINE_NUMBER}~~~${TM_CURRENT_FUNCTION}`, error, 'error');"], "description": "Log error info with file, line, and function"}}