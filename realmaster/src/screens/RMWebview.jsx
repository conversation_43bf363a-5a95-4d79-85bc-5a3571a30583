import React, { useState, useRef, forwardRef, useImperativeHandle, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ActivityIndicator,
  Platform,
  KeyboardAvoidingView,
  Image,
  Linking
} from 'react-native';
import { styles } from '../styles/RMWebview.styles';
import { CardStyleInterpolators } from '@react-navigation/stack';
import { eventEmitter } from '../utils/common';
import RMNavBar from '../components/RMNavBar';
import { WebView } from 'react-native-webview';
import { mapUtil, getUserAgent, urlFix } from '../utils/business';
import { l10n } from '../utils/i18n';
import Constants from '../config/constants';
import { RMStatusBar, RMBottomBar } from '../components/RMStatusBar';
import serverDomainIns from '../utils/serverDomain';
import appConfigIns from '../config/appConfig';
import { getColor } from '../utils/RMstyle';
import { injectedJavaScript, androidInjectedJavaScript, createPostMessageCode } from '../utils/webviewInjectCode';

import { useKeyboardListener } from '../hooks/useKeyboardListener';


const SpinMinTimeout = 60;

const RMWebview = forwardRef((props, ref) => {
  // const [source, setSource] = useState({uri: 'https://realmaster.com/app?sv=6.5.0&appmode=mls'});
  const [source, setSource] = useState({ uri: '/home/<USER>' });
  const [isPopup, setIsPopup] = useState(false);
  const [hideVal, setHideVal] = useState(false);
  const [userAgent, setUserAgent] = useState('');
  const [loading, setLoading] = useState(false);
  const [barColor, setBarColor] = useState('#E03131');
  const [toolbar, setToolbar] = useState(false);
  const [textColor, setTextColor] = useState('');
  const [title, setTitle] = useState('');
  const [rightButton, setRightButton] = useState({});
  const [bold, setBold] = useState(true);
  const [oldUrl, setOldUrl] = useState('');
  const [canGoBack, setCanGoBack] = useState(false);

  const webviewRef = useRef(null);
  const hadError = useRef(false);
  const naviStateTimeout = useRef(null);
  const checkAliveTimeout = useRef(null);
  const autocompleteTimeout = useRef(null);
  const mapSearchTimeout = useRef(null);
  const getContentTimeout = useRef(null);
  const loadEndTimeout = useRef(null);
  const pageContentTimeout = useRef(null);

  /**
   * 定义hooks执行需要的函数
   */
  const postMessage = msg => {
    if (!webviewRef.current) {
      return console.warn('webview: postMessage not ready', msg);
    }

    const msgString = typeof msg === 'string' ? msg : JSON.stringify(msg);
    global.rmLog(`RMWebview.jsx:384~~~postMessage`, msgString);
    const msgEncoded = encodeURIComponent(msgString);
    const messageCode = createPostMessageCode(msgEncoded);
    if (webviewRef.current) {
      webviewRef.current.injectJavaScript(messageCode);
    } else {
      console.log('user closed webview before ready')
    }
  };

  const goBack = () => {
    if (/((com|cn|ca|8080)\/1\.5\/index)|\/home\/<USER>\/home\/marketplace/.test(oldUrl)) {
      return false;
    }
    if (canGoBack && webviewRef.current) {
      webviewRef.current.goBack();
      return true;
    }
    return false;
  };

  useKeyboardListener(postMessage);
  useImperativeHandle(ref, () => {
    return {
      postMessage,
      goBack,
      index: props.routeData.index,
    }
  }, []);


  useEffect(() => {
    const eventHandlers = {
      'navigation.replace': navigationReplaceHandler,
      'webview.forceUpdate': forceUpdateHandler,
      'network.online': networkOnlineHandler,
      'webview.checkAlive': webviewCheckAliveHandler,
      'webview.appStateChange': webviewAppStateChangeHandler,
      'startup.jump': startupJumpHandler,
      [Constants.ChangeAppMode]: changeAppModeHandler,
      'webview.isAlive': webViewIsAliveHandler,
    };

    // 注册所有事件监听器
    Object.entries(eventHandlers).forEach(([event, handler]) => {
      eventEmitter.on(event, handler);
    });

    // webview初始化
    initWebview();

    // 清理函数
    return () => {
      // 移除所有事件监听器
      Object.entries(eventHandlers).forEach(([event, handler]) => {
        eventEmitter.removeListener(event, handler);
      });
      // 清除所有定时器
      [
        mapSearchTimeout,
        autocompleteTimeout,
        naviStateTimeout,
        getContentTimeout,
        loadEndTimeout,
        pageContentTimeout,
        checkAliveTimeout,
      ].forEach(timer => {
        if (timer.current) {
          clearTimeout(timer.current);
          timer.current = null;
        }
      });
    };
  }, []); // 依赖数组现在为空

  const initWebview = async () => {
    global.rmLog(`RMWebview.jsx:834~~~initWebview`, props.routeData.url);

    const defaultCommonColor = await getColor('commonBarColor');
    //设置screenOption
    if (Platform.OS === 'ios' && props.routeData.forVerticalIOS) {
      props.navigation.setOptions({
        cardStyleInterpolator: CardStyleInterpolators.forVerticalIOS,
      });
    }

    //setUserAgent
    let userAgent = await getUserAgent();
    if (!Platform.isPad) {
      // Orientation.lockToPortrait();
    } else {
      userAgent += ' (iPad; Platform)';
    }
    setUserAgent(userAgent);
    let rightButton = { cbClose: closePopup };

    //之前的某些页面如翻译，可能需要隐藏一下。保留这个判断逻辑
    let hideVal = props.routeData.hide;
    let isPopup = false;
    let title = props.routeData.title || 'RealMaster';

    if (props.routeData.index !== 0 && !hideVal) {
      isPopup = true;
      hideVal = true;
      loadEndTimeout.current = setTimeout(() => {
        hideLoadingIcon();
      }, 5000);
    }
    if (props.routeData.selector) {
      rightButton.cbConfirm = getContent;
    }
    if (props.routeData.selector && props.routeData.cancelable) {
      title = '';
    }
    if (props.routeData.selector == '#callBackString') {
      rightButton.cbClose = closePopup;
      delete rightButton.cbConfirm;
    }

    const _source = { ...source };

    //setSource
    if (props.routeData.url) {
      let url = props.routeData.url;
      if (props.routeData.init) {
        let char = '?';
        if (url.indexOf('?' > 0)) {
          char = '&';
        }
        url += char + 'src=appnative';
      }
      _source.uri = urlFix(url);

      setSource(_source);
    }

    if (props.routeData.url) {
      setOldUrl(props.routeData.url);
    }

    setToolbar(props.routeData.toolbar || toolbar);
    setBarColor(props.routeData.barColor || defaultCommonColor);
    setTextColor(props.routeData.textColor || textColor);
    setHideVal(hideVal);
    setIsPopup(isPopup);
    setTitle(title);
    setRightButton(rightButton);
    setBold(props.routeData.bold || bold);
  };



  /**
   * 事件处理
   */

  const navigationReplaceHandler = (opts) => {
    if (!props.navigation.isFocused()) {
      global.rmLog(`[RMWebview.jsx] navigationReplaceHandler - 页面未激活`);
      return;
    }

    // //避免短时间重复replace
    // if (isNavigating.current) return;
    // isNavigating.current = true;

    if (opts.index >= props.routeData.index) {
      const changeUrl = () => {
        let newUrl = urlFix(opts.url);

        if (source.uri === newUrl) {
          newUrl += (newUrl.includes('?') ? '&' : '?') + 'rmrand=' + Date.now();
        }

        if (opts.init) {
          newUrl += (newUrl.includes('?') ? '&' : '?') + 'src=appnative';
        }

        global.rmLog(`[RMWebview.jsx] navigationReplaceHandler - 设置新URL`, {
          oldUrl: source.uri,
          newUrl
        });

        setSource({ uri: newUrl });

        // setTimeout(() => {
        //   isNavigating.current = false;
        // }, 30);
      };
      changeUrl();
    }
  };

  const forceUpdateHandler = (opts) => {
    if (!props.navigation.isFocused()) return;

    global.rmLog(`[RMWebview.jsx] forceUpdateHandler`, {
      url: opts.url,
      currentSource: source.uri
    });

    // 强制触发一次 source 更新
    const tempUrl = urlFix(opts.url);
    setSource({
      uri: tempUrl + (tempUrl.includes('?') ? '&' : '?') + '_t=' + Date.now()
    });
  };

  //网络中断再次连接正常后，触发App中退回页面操作，同时页面reload
  const networkOnlineHandler = () => {
    if (hadError.current) {
      hadError.current = false;
      webviewReload();
    }
  };

  const webviewCheckAliveHandler = (opts = {}) => {
    if (Date.now() - appConfigIns.getAppConfig('appStateTs') > SpinMinTimeout * 1000) {
      setLoading(true);
    }
    postMessage({ tp: 'checkAlive' });
    if (checkAliveTimeout.current) {
      clearTimeout(checkAliveTimeout.current);
    }
    let timeout = appConfigIns.getAppConfig('webViewCheckaliveTimeout') * 1000 || 3000;
    checkAliveTimeout.current = setTimeout(() => {
      webviewRefresh(opts);
    }, timeout);
  };




  const webviewAppStateChangeHandler = (opts) => {
    let data = { tp: 'appStateChange', p: opts };
    postMessage(data);
  };

  const startupJumpHandler = (opt = {}) => {
    if (opt.url && props.routeData.init) {
      setSource({ uri: urlFix(opt.url) });
    }
  };

  const changeAppModeHandler = async (opt = {}) => {
    if (opt.val) {
      if (opt.mapSwitch) {
        return;
      }
      const barColor = await getColor('mainTheme', opt.val);
      setBarColor(barColor);
    }
  };
  const webViewIsAliveHandler = (opts = {}) => {
    if (opts.val) {
      clearTimeout(checkAliveTimeout.current);
    }
  };

  /**
   * 其他函数
   */

  const hideLoadingIcon = () => {
    setHideVal(false);
  };

  const webviewReload = () => {
    if (webviewRef.current) {
      setLoading(true);
      hideLoadingIcon();

      // 先获取当前的 URL
      const currentUrl = source.uri;

      // 添加随机参数强制刷新
      const reloadUrl = currentUrl.includes('?')
        ? `${currentUrl}&_t=${Date.now()}`
        : `${currentUrl}?_t=${Date.now()}`;

      // 更新 source 触发重新加载
      setSource({ uri: reloadUrl });

      // 可选：在短暂延迟后执行 reload
      setTimeout(() => {
        if (webviewRef.current) {
          webviewRef.current.reload();
        }
      }, 100);
    }
  };

  const closePopup = () => {
    if (props.routeData.selector) {
      props.routeData.callback(':cancel');
    }
    clearTimeout(naviStateTimeout.current);
    clearTimeout(getContentTimeout.current);
    clearTimeout(loadEndTimeout.current);
    clearTimeout(pageContentTimeout.current);
    props.closePopup();
  };

  const getContent = (optSelector = '') => {
    let msg = '';
    if (props.routeData.selector == 'html:wechat') {
      msg = ':html:wechat';
    } else if (props.routeData.selector == 'html') {
      msg = ':html';
    } else if (optSelector) {
      msg = ':sel:' + optSelector;
    } else {
      msg = ':sel:' + props.routeData.selector;
    }
    if (props.routeData.selector == '#callBackString') {
      return postMessage(':cancel');
    }
    postMessage(msg);
  };
  const doGetPageContent = opt => {
    let wait = 0;
    if ((wait = parseInt(props.routeData.wait || 0)) >= 0) {
      pageContentTimeout.current = setTimeout(() => {
        getContent();
      }, wait); //wait
    } else {
      throw new Error('Bad wait parameter');
    }
  };



  const webviewDeepRefresh = () => {
    if (webviewRef.current) {
      webviewRef.current.stopLoading();
      if (Platform.OS === 'android') {
        webviewRef.current.clearHistory();
        webviewRef.current.clearCache();
      }
    }
    webviewReload();
  };

  const webviewRefresh = (opts = {}) => {
    if (webviewRef.current) {
      if (opts.tp == 'bootup') {
        // NOTE: if from popup , do not show splash
        // if(Date.now() > getAppConfig('noSplashBefore')){
        // console.log('++++send refresh in webview',getAppConfig('noSplashBefore'))
        // EventEmitter.emit("app.rebootup",'app refresh not respond');
        // }
        return webviewDeepRefresh();
      }
      webviewReload();
    }
  };
  //ios only, 页面崩溃
  const onContentProcessDidTerminate = () => {
    console.log('onContentProcessDidTerminate');
    webviewReload();
  };

  const renderLoadingSpinner = () => (
    <View style={styles.indicatorWrapper}>
      <ActivityIndicator
        size='small'
        color='#bfbfbf'
      />
      {/* {showNetworkError && <View style={styles.indicatorContent}>
        <Text style={styles.indicatorNetworkText}>网络波动，请耐心等待</Text>
        <TouchableOpacity style={styles.indicatorButton} onPress={() => {
          webviewReload();
        }}>
          <Text style={styles.indicatorButtonText}>点击刷新</Text>
        </TouchableOpacity>
      </View>} */}
    </View>
  );


  /**
   * webview相关方法
   */

  const webviewStyle = () => {
    if (isPopup && hideVal) {
      return styles.webViewHide;
    } else if (hideVal) {
      return styles.webViewTrueHide;
    } else if (toolbar) {
      return styles.webView2bar;
    }
    return styles.webView;
  };

  const onMessage = async event => {
    let str = event.nativeEvent.data;
    global.rmLog(`RMWebview.jsx:419~~~onMessage`, decodeURIComponent(str));
    let json;
    if (isPopup) {
      hideLoadingIcon();
    }
    try {
      str = decodeURIComponent(str);
      // console.log(str);
      if (/^{/.test(str)) {
        json = JSON.parse(str);
      }
    } catch (e) {
      console.warn('onMessage parse:', str, e.toString());
    }

    //NOTE: Android only
    if (/^\:cmd\:\:goReady$/.test(str)) {
      if (webviewRef.current) {
        webviewRef.current.injectJavaScript(androidInjectedJavaScript);
      } else {
        console.log('on msg webview not ready');
      }
      eventEmitter.emit('webview.goReady', { ts: new Date(), index: props.routeData.index });
      clearTimeout(checkAliveTimeout.current);
      setLoading(false);
      return;
    }
    if (str == ':ctx:alive') {
      if (Platform.OS !== 'ios') {
        eventEmitter.emit('webview.isAlive', { val: new Date() });
      }
      clearTimeout(checkAliveTimeout.current);
      setLoading(false);
      return;
    }
    if (props.routeData.callback && /^\:ctx\:/.test(str)) {
      // NOTE: invoke App.js routePop
      clearTimeout(naviStateTimeout.current);
      clearTimeout(getContentTimeout.current);
      clearTimeout(loadEndTimeout.current);
      global.rmLog(`RMWebview.jsx:466~~~onMessage`, str, props.routeData.noClose);
      if (!props.routeData.noClose) {
        props.closePopup();
      }
      str = str.substr(5);
      props.routeData.callback(str);
      return;
    }
    // TODO: handle barColor message here instead of app level
    // pass to parent(App.js)
    if (json && json.tp == 'setBarColor') {
      if (json.name) {
        json.barColor = await getColor(json.name);
      }
      if (!json.barColor) {
        json.barColor = await getColor('commonBarColor');
      }
      if (!json.textColor) {
        json.textColor = await getColor('commonBarText');
      }
      // console.log(json)
      setBarColor(json.barColor);
      setTextColor(json.textColor);
      return;
    }
    props.onMessage(str, props.routeData.index);
  };

  //onLoadEnd表示成功或者失败都会调用
  const onLoadEnd = (syntheticEvent) => {
    global.rmLog(`[RMWebview.jsx:477~~~~~~~~~~onLoadEnd]`, syntheticEvent.nativeEvent.url);
    clearTimeout(checkAliveTimeout.current);
    setLoading(false);
    props.onLoadEnd(props.routeData.index);
    if (isPopup) {
      return hideLoadingIcon();
    }
    setTimeout(() => {
      getContent('#rmPageBarColor');
    }, 20);
    if (hideVal && props.routeData.selector) {
      clearTimeout(getContentTimeout.current);
      getContentTimeout.current = setTimeout(() => {
        doGetPageContent();
      }, 2000);
    }
  };

  const onLoadStart = (syntheticEvent) => {
    const { nativeEvent } = syntheticEvent;
    // clearTimeout(setSourceTimeout.current);
    // setSourceTimeout.current = setTimeout(() => {
    //   setSource({uri: nativeEvent.url});
    // }, 500);
    global.rmLog(`[RMWebview.jsx:680~~~~~~~~~~onLoadStart]`, nativeEvent.url);
    props.onLoadStart(props.routeData.index);
  };


  const onError = err => {
    console.log('onError', err.nativeEvent);
    err = err.nativeEvent || {};
    hadError.current = true;
    //页面加载失败后，再触发一次网络检查
    // eventEmitter.emit('checkNetwork');
  };

  const renderError = (errorDomain, errorCode, errorDesc) => {
    // 使用 errorInfo 状态来获取更详细的错误信息

    return (!loading &&
      <View style={styles.errorContainer}>
        <View style={styles.errorContent}>
          <Image
            source={require('../assets/images/html-loadfailed.png')}
            style={styles.errorImage}
            resizeMode="contain"
          />
          <Text>{l10n('Load failed')} code:{errorCode}</Text>
          <TouchableOpacity
            style={styles.reloadButton}
            onPress={() => {
              webviewReload();
            }}
          >
            <Text style={styles.reloadButtonText}>
              {l10n('Reload')}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  const onLoadProgress = (syntheticEvent) => {
    const { nativeEvent } = syntheticEvent;
    console.log('onprogress', nativeEvent);
      };

  const onHttpError = syntheticEvent => {
    const { nativeEvent } = syntheticEvent;
    console.log('HTTP error: ', nativeEvent);
  }

  const onShouldStartLoadWithRequest = navState => {
    const fullUrl = navState.url;
    const url = fullUrl.split('?')[0];
    const shouldJump = url => {
      // 对于所有域名，包括 app.test，设置当前域名
      if (url) {
        // 特殊处理 https://app.test 开头的URL，转换为 http 协议
        if (url.startsWith('https://') && url.includes('app.test')) {
          const httpUrl = url.replace(/^https:/, 'http:');
          setSource({ uri: httpUrl });
          return false; // 阻止当前加载，使用新的URL
        }
        
        const str = url.split('/');
        if (str[2]) {
          // 使用包含协议的完整域名，而不是手动拼接
          const protocol = str[0];
          const domain = str[2];
          serverDomainIns.setDomain(`${protocol}//${domain}`);
        }
      }
      return true;
    };

    global.rmLog(`RMWebview.jsx:552~~~onShouldStartLoadWithRequest`, navState.url);
    // https://github.com/facebook/react-native/issues/20917
    if (/^file\:\/\/.*RealMaster\.app\/$/.test(navState.url)) {
      const opt = {
        tp: 'closePopup',
        url: '/1.5/index',
      };
      eventEmitter.emit('app.message', { msg: JSON.stringify(opt) });
      return false;
    }
    if (!url) {
      return true;
    }
    if (Platform.OS === 'android' && /assets\/src\/html\/index\.html/.test(url)) {
      return true;
    }
    if (/\/1\.5\/autocomplete/.test(navState.url)) {
      let url = navState.url;
      if (url.indexOf('?') > -1) {
        url = url.split('?')[1];
      }
      const opt = {};
      url = url.split('&');
      for (let i of url) {
        const tmp = i.split('=');
        const p = tmp[0];
        const v = decodeURIComponent(tmp[1]);
        opt[p] = v;
      }
      opt.tp = 'autocomplete';
      clearTimeout(autocompleteTimeout.current);
      autocompleteTimeout.current = setTimeout(() => {
        // console.log('send ts:',Date.now())
        eventEmitter.emit('app.message', { msg: JSON.stringify(opt) });
      }, 200);
      return false;
    }
    // IOS case?
    if (mapUtil.getGoogleMapProviderNeedUpdate()) {
      const cb = idx => {
        // console.log(idx)
        if (idx + '' == '2') {
          mapUtil.goToMarket(false);
        }
      };
      // TODO: translation?
      eventEmitter.emit('app.message', {
        msg: JSON.stringify({
          tp: 'confirm',
          title: 'Update Google Play service',
          msg: "RealMaster won't run properly unless you update Google Play service.",
          btns: ['Cancel', 'OK'],
        }),
        cb,
      });
      return false;
    }
    if (mapUtil.getGoogleMapProviderAvailable() && mapUtil.isMapSearchMode(navState.url)) {
      const opt = mapUtil.extractMapOptFromUrl(navState.url);
      clearTimeout(mapSearchTimeout.current);
      mapSearchTimeout.current = setTimeout(() => {
        eventEmitter.emit('app.message', { msg: JSON.stringify(opt) });
      }, 100);
      return false;
    }
    if (Platform.OS === 'android' && /^http\:\/\/10\.0\.2\.2/.test(url)) {
      return shouldJump(url);
    }
    if (/192\.168\.|^https?\:\/\/app\.test/.test(url)) {
      return shouldJump(url); // for local test
    }
    if (/^https?\:\/\/[^\/\?]*[d\d+]*realmaster/.test(url)) {
      // console.log('here own url',/^https?\:\/\/[^\/\?]*[d\d+]*realmaster/.test(url),url);
      return shouldJump(url);
    }
    if (/^https?\:\/\/[^\/\?]*realexpert/.test(url)) {
      return shouldJump(url);
    }
    if (Platform.OS === 'ios' && navState.navigationType !== 'click') {
      return true;
    }
    if (Platform.OS !== 'ios' && /youtube|youtu/.test(url)) {
      return true;
    }
    if (props.routeData.selector && props.routeData.selector !== '#callBackString') {
      return true;
    }
    if (/^rmCall|^file\:|about\:blank/.test(url)) {
      return true;
    }
    if (/^(tel|geo|maps|mailto|sms|market)\:/.test(fullUrl)) {
      Linking.openURL(fullUrl).catch(er => {
        Alert.alert("Failed to open Link: " + er.message);
      });
      return false;
    }

    // stripe pay
    if (/^https?\:\/\/[^\/\?]*checkout\.stripe\.com/.test(url)) {
      return true; // services we use
    }
    if (/^https?\:\/\/[^\/\?]*walkscore.com/.test(url)) {
      return true; // services we use
    }
    if (/R\d\.M\d/.test(navState.url)) {
      // for full url
      return true; // for purposely outgoing link
    }

    if (navState.url.startsWith("tel:")) {
      Linking.openURL(navState.url).catch(er => {
        Alert.alert("Failed to open Link: " + er.message);
      });
      return false;
    }

    console.log('Invalid Url in app -> ' + navState.url);
    return false;
  };

  // only for android device
  const onNavigationStateChange = navState => {
    const { canGoBack, url } = navState;
    if (url != oldUrl) {
      if (!onShouldStartLoadWithRequest(navState, { ignoreMap: true }) && !/about\:blank/.test(url)) {
        // NOTE:not working in 0.55 https://github.com/facebook/react-native/issues/15679
        webviewRef.current.stopLoading();
        naviStateTimeout.current = setTimeout(() => {
          setSource({ uri: urlFix(url) });
        }, 350);
      } else {
        setOldUrl(url);
        setCanGoBack(canGoBack);
      }
    }
  };



  const commonWebViewProps = {
    ref: webviewRef,
    source: { uri: urlFix(source.uri) },
    style: webviewStyle(),
    originWhitelist: ['*'], //需要添加以保证tel:会被捕捉
    bounces: false,
    onMessage,
    onLoadEnd,
    onLoadStart,
    onError,
    renderError,
    onLoadProgress,
    onHttpError,
    userAgent,
    mixedContentMode: 'always',
    onShouldStartLoadWithRequest,
    injectedJavaScript,
    allowsFullscreenVideo: true,
    mediaPlaybackRequiresUserAction: false,
    cacheEnabled: false,
    javaScriptEnabled: true,
    domStorageEnabled: true,
    // 只添加这两个属性来隐藏默认loading
    renderLoading: () => null,
  };

  const renderNavbar = () => {
    let navbar = <RMStatusBar tintColor={barColor} />;
    let customStyle = { backgroundColor: barColor };
    if (toolbar) {
      navbar = (
        <RMNavBar
          statusBar={{ tintColor: barColor }}
          textColor={textColor}
          style={[styles.navBar, customStyle]}
          title={{ title }}
          rightButton={rightButton}
          hide={false}
          bold={bold}
          tintColor={barColor}
        />
      );
    }
    return navbar;
  };

  const ActivityIndicatorLoadingView = (opt = {}) => {
    let wrapperStyle = [styles.indicatorWrapper];
    if (opt.hasBar) {
      wrapperStyle.push(styles.indicatorWrapperWithBar);
    }
    return (
      <View style={wrapperStyle}>
        <Image
          source={require('../assets/images/RealmasterLogoLoading.gif')}
          style={{ width: 54, height: 54 }}
        />
      </View>
    );
  };

  return (
    <View
      style={styles.container}
      scrollEnabled={true}>
      {renderNavbar()}
      {Platform.OS === 'android' ? (
        <KeyboardAvoidingView
          behavior='padding'
          style={webviewStyle()}>
          <WebView
            {...commonWebViewProps}
            allowFileAccess={true}
            allowUniversalAccessFromFileURLs={true}
            onNavigationStateChange={onNavigationStateChange}
            showsHorizontalScrollIndicator={false}
          />
        </KeyboardAvoidingView>
      ) : (
        <WebView
          {...commonWebViewProps}
          scrollEnabled={true}
          allowsInlineMediaPlayback={true}
          onContentProcessDidTerminate={onContentProcessDidTerminate}
        />
      )}
      {hideVal && ActivityIndicatorLoadingView({ hasBar: true })}
      {loading && renderLoadingSpinner()}
      <RMBottomBar />
    </View>
  );
});

export default RMWebview;
