<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="22154" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" launchScreen="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES" initialViewController="01J-lp-oVM">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22130"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--View Controller-->
        <scene sceneID="EHf-IW-A2E">
            <objects>
                <viewController id="01J-lp-oVM" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="Ze5-6b-2t3">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" image="splash" translatesAutoresizingMaskIntoConstraints="NO" id="cvV-cR-t5x">
                                <rect key="frame" x="-154" y="0.0" width="683" height="667"/>
                            </imageView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="Bcu-3y-fUS"/>
                        <color key="backgroundColor" red="0.87848502399999995" green="0.1927825511" blue="0.19168490169999999" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstItem="cvV-cR-t5x" firstAttribute="centerY" secondItem="Ze5-6b-2t3" secondAttribute="centerY" id="6cN-vZ-CDC"/>
                            <constraint firstItem="cvV-cR-t5x" firstAttribute="centerX" secondItem="Ze5-6b-2t3" secondAttribute="centerX" id="DBQ-aO-fzv"/>
                            <constraint firstItem="cvV-cR-t5x" firstAttribute="top" secondItem="Ze5-6b-2t3" secondAttribute="top" id="cmz-Vf-rzc"/>
                            <constraint firstAttribute="bottom" secondItem="cvV-cR-t5x" secondAttribute="bottom" id="zBW-wA-Nzd"/>
                        </constraints>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="iYj-Kq-Ea1" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="52" y="374.66266866566718"/>
        </scene>
    </scenes>
    <resources>
        <image name="splash" width="682.66668701171875" height="682.66668701171875"/>
    </resources>
</document>
