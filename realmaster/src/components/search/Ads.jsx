import React, {Component} from 'react';
import {Platform, StyleSheet, Text,View,ImageBackground,Dimensions,FlatList,Image,TouchableOpacity} from 'react-native';
import {gotoProp} from './helper'
import {currency} from '../../mixins/filters';
// import {getServerDomain, getServerProtocol} from '../../RMNetwork';
import serverDomainIns from '../../utils/serverDomain'
import Rooms from './Rooms'
import colorTheme from '../../utils/colors';
import {l10n} from '../../utils/i18n';
const {width} = Dimensions.get('window')
const defaultAvt = null; //'https://realmaster.com/img/logo.png';

export default Ads = (props) => {
  const {ads,closePopup,lang} = props;
  let handleImageError = (e)=>{
    // TODO: handle image error
    // console.error(e)
  }

  const handleImgUrl = (imgUrl) => {
    if(imgUrl.indexOf('http')!=0 && imgUrl.indexOf('//')==0){
      // img = getServerDomain()+img;
      return serverDomainIns.getProtocol()+':'+imgUrl;
    }
    return imgUrl
  }

  var styles = StyleSheet.create({
    adsContainer:{
      backgroundColor:'#fff',
    },
    ads:{
      padding:10,
      position:'relative',
    },
    label:{
      position:'absolute',
      top:0,
      left:0,
      backgroundColor:colorTheme['secondary_red'],//'rgb(255,90,90)',
      padding:2,
      color:'#fff',
      fontSize:10
    },
    avtContiner:{
      marginTop:5,
      marginRight:5,
      alignItems:'flex-end',
      marginBottom:10
    },
    avt:{
      width:30,
      height:30,
      borderRadius:50,
    },
    ad:{
      width:width*2/5,
      marginBottom:5,
      height:120
    },
    info:{
      backgroundColor:'rgba(0,0,0,0.4)',
      padding:5,
      position:'absolute',
      bottom:0,
      width:'100%',
    },
    lp:{
      color:'#fff',
      fontWeight:'bold',
      fontSize:13,
      // maxHeight:15,
      marginBottom:3
    },
    desc:{
      lineHeight:15,
      height:35,
      color:'#fff',
      fontSize:13
    },
    city:{
      // color:'#fff',
      fontSize:12,
      width:width/3
    }
  })
  return (
    <View style={styles.adsContainer}>
    <FlatList
      keyboardShouldPersistTaps='always'
      contentContainerStyle={styles.ads}
      ItemSeparatorComponent={()=><View style={{width:10}}/>}
      data={ads}
      horizontal
      showsHorizontalScrollIndicator={false}
      keyExtractor={(item,idx) => item._id}
      renderItem={({item})=> {
        let avt = defaultAvt,label,nm,img,loc = null;
        if (item.p == 'project') {
          label = item.type||l10n('PreCon');
          nm = item.nm || item.name;
          let descStr = item.desc;
          // console.log('lang+++++',lang,item.desc_en)
          if(lang == 'en' && item.desc_en){
            descStr = item.desc_en;
            if(item.name_en){
              nm = item.name_en
            }
          }
          desc = <Text numberOfLines={2} style={styles.desc}>{descStr}</Text>;
          if (item.src.indexOf('http')==-1) {
            img = serverDomainIns.getFullUrl(item.src)
          } else {
            img = item.src;
          }
          if(item.avt){
            avt = item.avt;
          }
        } else {
          label = item.type||'TOP'
          nm = currency(item.lp,'$',0);
          desc = <Rooms prop={item} page={'ad'} />
          if (item.img) {
            img = handleImgUrl(item.img)
          } else {
            img = serverDomainIns.getFullUrl('/img/noPic.png')
          }

          if (item.adrltr && item.adrltr.avt) {
            avt = item.adrltr.avt;
          }
        }
        // console.log('ads proj img',img)
        if(item.city){
          loc = <Text numberOfLines={1} style={styles.city}>{item.city},{item.prov}</Text>;
        }
        return(
        <TouchableOpacity onPress={()=>gotoProp(item,lang,closePopup)}>
          <View>
            <ImageBackground style={styles.ad} source={{uri:img}} onError={(e)=>{handleImageError(e)}}>
              <Text style={styles.label}>{label}</Text>
              <View style={styles.avtContiner}>
                {!!avt &&
                  <Image source={{uri:avt}} style={styles.avt} />
                }
              </View>
              <View style={styles.info}>
                <Text numberOfLines={1} style={styles.lp}>{nm}</Text>
                {desc}
              </View>
            </ImageBackground>
            {loc}
          </View>
        </TouchableOpacity>
        )
      }
      }
    />
    </View>
  );
}
