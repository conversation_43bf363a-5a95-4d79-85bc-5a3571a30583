#import <RCTAppDelegate.h>
#import <UIKit/UIKit.h>
#import <React/RCTBridgeDelegate.h>
//#import <UIKit/UIKit.h>
#import "WXApi.h"
//@interface AppDelegate : RCTAppDelegate
//
//@end
//@interface AppDelegate : UIResponder <UIApplicationDelegate, RCTBridgeDelegate, WXApiDelegate>
//
//@property (nonatomic, strong) UIWindow *window;
//
//@end
@interface AppDelegate : RCTAppDelegate <UIApplicationDelegate, RCTBridgeDelegate, WXApiDelegate>

@end
