import React, {Component} from 'react';
import {Platform, StyleSheet, Text, View,ImageBackground,TouchableOpacity,Dimensions} from 'react-native';
import {gotoProp,gotoMap} from './helper'
import RmIcon from '../RmIcon'
import Rooms from './Rooms';
import HistRight from './HistRight';
import PropImg from './PropImg';
import {currency} from '../../mixins/filters';
// import icoMoonConfig from '../../config/selection.json';
// import { createIconSetFromIcoMoon } from 'react-native-vector-icons';
import {l10n} from '../../utils/i18n';
// import {getColorByName} from '../../RMColors';
import colorTheme from '../../utils/colors'
// import { backgroundColor } from 'react-native/Libraries/Components/View/ReactNativeStyleAttributes';

// const Icon = createIconSetFromIcoMoon(
//   icoMoonConfig
// );
const {width} = Dimensions.get('window');

function getTs(prop) {
  // TODO: use ts||top_ts||mt||onD
  if (prop.ts) {
    return prop.ts.substr(0,10);
  } else {
    //?
    return '2019-11-07';
  }
}

export default class Prop extends Component {
  constructor(props) {
    super(props);
  }
  render() {
    var styles = StyleSheet.create({
      needLogin:{
        // borderColor:'#e03131',
        // borderWidth:1,
        borderStyle:'solid',
        height:'100%',
        width:width-160,
        position:'absolute',
        top:0,
        left:90,
        alignContent:'center',
        alignItems:'center',
        backgroundColor:'white',
        zIndex:10,
      },
      propListWrapper:{
        backgroundColor:'#fff',
        padding:10,
        flexDirection:'row',
        justifyContent:'space-between',
        alignItems:'center',
        borderColor:'rgb(240,240,240)',
        borderBottomWidth:0.5,
        borderStyle:'solid'
      },
      left:{
        flexDirection:'row',
        width:width - 73,
        // backgroundColor:'blue',
        // overflow:'hidden'
      },
      addr:{
        color:'rgb(80,80,80)',
        fontSize:14,fontWeight:'bold',
        marginBottom:2,
        width:width -56-80-20,
        // backgroundColor:'yellow'
      },
      lpSaletp:{
        flexDirection:'row',
        alignItems:'center',
        marginBottom:2
      },
      price:{
        color:colorTheme['secondary_red'], //'rgb(250,62,62)',
        fontSize:14,
        fontWeight:'bold',
      },
      lp:{
        marginLeft:2,
        color:'rgb(177,177,177)',
        fontSize:10,
        fontWeight:'bold',
        textDecorationStyle:'solid',
        textDecorationLine:'line-through'
      },
      saletp:{
        borderRadius:2,
        backgroundColor:'#808080',
        borderRadius:1,
        textTransform:'capitalize',
        marginLeft:4,
        color:'#fff',
        paddingLeft:3,
        paddingRight:3,
        fontSize:10
      },
      green:{
        backgroundColor:'rgba(30,166,27,0.9)',
      },
      red:{
        backgroundColor:'#fa4850',
      },
      assignment:{
        backgroundColor:'#4a148c'
      },
      roomNType:{
        flexDirection:'row',
        // flexWrap:"nowrap",
        // alignItems:'center',
        width:width - 162,
        overflow:'hidden',
        // backgroundColor:'grey'
      },
      ptype:{
        fontSize:12,
        flex:1
      },
      info:{
        marginTop:5,
        fontSize:10,
        color:'rgb(177,177,177)'
      },
      right:{
        flexDirection:'row',
        justifyContent:'flex-end',
        width:50,
        // backgroundColor:'red'
        // paddingRight:10
      },
      openMapBtn:{
        paddingLeft:5,
        // paddingRight:10
      },
      mapLink:{
        fontSize:14,
        paddingTop:3,
        color:colorTheme['main_blue']
      },
      blurText:{
        // color: "inhr",
        textShadowColor: "rgba(177,177,177,0.8)",
        textShadowOffset: {
          width: 0,
          height: 0,
        },
        textShadowRadius: 10,
        fontSize: 14,
        fontWeight: "600",
        textTransform: "capitalize",
      }
    })
    function hasRoom(prop){
      // prop.bdrms || prop.bthrms || prop.gr
      return prop.rmbdrm || prop.rmbthrm || prop.rmgr || prop.bdrms || prop.bthrms || prop.gr;
    }
    const {prop,page,lang,closePopup,referer} = this.props;
    let tpView;
    if (prop.saleTpTag) {
      const saletpcolor = styles[prop.tagColor];
      tpView = <Text style={[styles.saletp,saletpcolor]}>{prop.saleTpTag}</Text>
    }
    let info = getTs(prop) + l10n(' listed')+' · '+prop.city;
    let propType = '';
    if (prop.propType) {
      var split = '';
      if(hasRoom(prop)){
        split = ' | ';
      }
      propType = split + prop.propType
    }
    let rooms = null;
    if (hasRoom(prop)) {
      rooms = <Rooms prop={prop} />
    }
    let price = currency(prop.lp || prop.lpr,'$',0);
    let lp;
    if (prop.saleTpTag_en !== "Delisted" && prop.sp) {
      price = currency(prop.sp,'$',0);
      lp = <Text style={styles.lp}>{currency(prop.lp,'$',0)}</Text>
    }
    let rightSection;
    if (page == 'hist') {
      rightSection = <HistRight item={prop} removeHist={this.props.removeHist} />;
    } else {
      rightSection = (
        <View style={styles.right}>
          <RmIcon size={20} name={'angle-right'} color={'rgb(153,153,153)'} />
          <TouchableOpacity style={styles.openMapBtn} onPress={()=>{gotoMap(prop,closePopup,referer)}}>
            {/* <Icon size={20} name={'map-marker'} color={'rgb(130,130,130)'} /> */}
            <Text numberOfLines={1} style={styles.mapLink}>{l10n('MAP')}</Text>
          </TouchableOpacity>
        </View>
      );
    }
    return(
      <TouchableOpacity onPress={()=>{this.props.search('prop',prop);gotoProp(prop,lang,closePopup)}}>
        <View style={styles.propListWrapper}>
          <View style={styles.left}>
            <PropImg prop={prop}/>
            {prop.login && <View style={styles.needLogin}>
              <Text numberOfLines={1} style={[{paddingTop:20,alignItems:'center',alignSelf:'center'},styles.price]}>{l10n('Need Login')}</Text>
            </View>}
            <View >
              <Text numberOfLines={1} style={[styles.addr]}>{prop.v || (`${prop.addr}, ${prop.prov}`)}</Text>
              <View style={styles.lpSaletp}>
                <Text style={styles.price}>{price}</Text>
                {lp}
                {tpView}
              </View>
              <View style={styles.roomNType}>
                {rooms}
                <Text ellipsizeMode='tail' style={styles.ptype} numberOfLines={1}>{propType}</Text>
              </View>
              <Text numberOfLines={1} style={[styles.info]}>{info}</Text>
            </View>
          </View>
          {rightSection}
        </View>
      </TouchableOpacity>
    );
  }
}
