测试步骤：
安卓和苹果（测试服+正式服）使用测试机制（testflight/apk)

- [x] 测试vector icons
- [x] 测试设备orientation
- [x] 测试地图maps功能
- [x] ios 测试setSctollEnable in edit WeCard
- [ ] 下载后登录，注册过程(apple/google/facebook/wechat) -> ANDROID: wechat：签名不对，请检查签名是否与开放平台上填写的一致 google:DEVELOPER_ERROR
- [ ] 升级后登录，注册过程, 包括facebook/google切换账户登录
- [x] 首页升级提示 ->  暂不考虑
- [ ] 分享测试，微信（包括海报图片分享），email，sms -> ANDROID: wechat not work
- [x] init变量 pn，deviceId
- [ ] push notify 测试 -> 生产和测试都收不到
- [x] camera QRCode test ->关闭页面后一直有loading
- [x] add to Calendar
- [x] location and permissions 
- [ ] universal links, open app from link, eg: https://www.realmaster.cn/1.5/prop/detail?id=TRBC7033030&lang=zh-cn&fub_src=fub
- [x] webview test tel: mailto: (DO NOT TEST IN EMULATOR!!!)
- [x] 检查app name， 语言strings.xml是否对应
- [ ] 测试分享的链接能否打开app，deeplink
- [x] 测试iOS房源返回首页crash（点击任意房源，关闭最上层页面，下层页面返回首页时）


* vip save share img
* push notifications
* facebook/google/apple share/login
* mapsearch locate me
* prop +calendar
* wechat login
* camera scan qr code
* get page content
* off line test, show red screen
