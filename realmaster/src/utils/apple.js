import { AppleButton, appleAuth} from '@invertase/react-native-apple-authentication';


// clientSecret: '9IE0XrIOOpA5ZmN0YWzyZXcM'
var APPLE_OPTS = {
}

// TODO: signout??

var fetchAndUpdateCredentialState = async () => {
  if (this.user === null) {
    this.setState({ credentialStateForUser: 'N/A' });
  } else {
    const credentialState = await appleAuth.getCredentialStateForUser(this.user);
    if (credentialState === appleAuth.State.AUTHORIZED) {
      this.setState({ credentialStateForUser: 'AUTHORIZED' });
    } else {
      this.setState({ credentialStateForUser: credentialState });
    }
  }
}

var AppleLogin = async (opt,cb) => {
  try {
    // performs login request
    const appleAuthRequestResponse = await appleAuth.performRequest({
      requestedOperation: appleAuth.Operation.LOGIN,
      requestedScopes: [
        appleAuth.Scope.EMAIL,
        appleAuth.Scope.FULL_NAME,
      ],
    });
    // console.log('appleAuthRequestResponse', appleAuthRequestResponse);
    const {
      user: newUser,
      email,
      nonce,
      fullName,
      identityToken,
      authorizationCode,
      authorizedScopes: scopes,
      realUserStatus /* etc */,
    } = appleAuthRequestResponse;
    let real = realUserStatus === appleAuth.UserStatus.LIKELY_REAL;
    // this.user = newUser;
    // get current authentication state for user
    // /!\ This method must be tested on a real device. On the iOS simulator it always throws an error.
    const credentialState = await appleAuth.getCredentialStateForUser(appleAuthRequestResponse.user);

    // use credentialState response to ensure the user is authenticated
    if (credentialState === appleAuth.State.AUTHORIZED) {
      // // user is authenticated
      // RMPost('/1.5/pageData',{datas:this.datas,page:'autocomplete'},(err,ret)=>{
      //   // let state = {loading:false}
      //   if(err){
      //     return;
      //   }
      //   if ('TIME_OUT'==err) {
      //     this.flashMessage(this.strings[err])
      //   }
      //   if (ret.err || ret.e) {
      //     return this.flashMessage(ret.err || ret.e);
      //   }
      //   if (ret.datas) {
      //     dispVar = ret.datas;
      //   }
      //   this.setState({dispVar},()=>{
      //     // console.log(this.state.dispVar);
      //   });
      // })
      // userInfo.ok = 1;
      return cb({user:newUser,email,code:authorizationCode,nonce,token:identityToken,real,scopes,fullName})
    } else {
      return cb({err:'Error: no auth:'+credentialState,state:credentialState})
    }
    // console.log(credentialState)

    // this.fetchAndUpdateCredentialState()
    // .then(res => this.setState({ credentialStateForUser: res }))
    // .catch(error =>
    //   this.setState({ credentialStateForUser: `Error: ${error.code}` }),
    // );

    // if (identityToken) {
    //   // e.g. sign in with Firebase Auth using `nonce` & `identityToken`
    //   console.log(nonce, identityToken);
    // } else {
    //   // no token - failed sign-in?
    // }

    // if (realUserStatus === appleAuth.UserStatus.LIKELY_REAL) {
    //   console.log("I'm a real person!");
    // }
    // console.warn(`Apple Authentication Completed, ${this.user}, ${email}`);

  } catch (error) {
    if (error.code === appleAuth.Error.CANCELED) {
      console.warn('User canceled Apple Sign in.');
      return cb({cancelled:true})
    } else {
      console.error(error);
      return cb({err:error});
    }
  }
};
function AppleRevoke(){
  // onCredentialRevoked returns a function that will remove the event listener. useEffect will call this function when the component unmounts
  return appleAuth.onCredentialRevoked(async () => {
    console.warn('If this function executes, User Credentials have been Revoked');
  });
}
export {AppleLogin,AppleRevoke}
