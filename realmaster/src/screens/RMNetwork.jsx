import { useState} from 'react';
import {ImageBackground, View, Text, StyleSheet, Pressable} from 'react-native';
import { eventEmitter } from '../utils/common';
import {l10n} from '../utils/i18n';
import networkIns from '../utils/network';

const RMNetwork = ({route}) => {
  const [message, setMessage] = useState(l10n('Network Offline'));
  const [showRetry, setShowRetry] = useState(true);

  const handleRetry = () => {
    setMessage(l10n('Connecting...'));
    setShowRetry(false);

    // Emit the network check event
    setTimeout(() => {
      eventEmitter.emit('checkNetwork');
    }, 500);

    // Set a timeout to re-enable the retry button if still offline after 6 seconds
    setTimeout(() => {
      if (!networkIns.isConnected()) {
        setMessage(l10n('Network Offline'));
        setShowRetry(true);
      }
    }, 6000);
  };

  return (
    <ImageBackground
      source={require('../assets/images/screenEn.jpg')}
      style={styles.container}>
      <View>
        <Text style={styles.message}>{message}</Text>
        {showRetry && (
          <Pressable
            onPress={handleRetry}
            style={styles.retry}>
            <Text style={styles.retryText}>{l10n('Retry')}</Text>
          </Pressable>
        )}
      </View>
    </ImageBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    flexDirection: 'column-reverse',
    paddingBottom: 200,
  },
  message: {
    color: '#fff',
    marginBottom: 10,
  },
  retry: {
    borderRadius: 3,
    borderWidth: 1,
    borderColor: '#fff',
    paddingTop: 4,
    paddingBottom: 4,
  },
  retryText: {
    color: '#fff',
    fontSize: 16,
    textAlign: 'center',
  },
});

export default RMNetwork;
