import React,{Component} from 'react';
import PropTypes from 'prop-types';

import {
  StyleSheet,
  View,
  Text,
  Image,
} from 'react-native';

const propTypes = {
  text: PropTypes.string,
  fontSize: PropTypes.number,
};

const defaultProps = {
  fontSize: 13,
  backgroundColor: '#5A5FFF',
  borderColor: '#e03131'//'#202020',
};

class IconText extends Component {
  render() {
    const { fontSize, text, icon, isTopUp, backgroundColor, borderColor } = this.props;
    return (
      <View key={'curSchool'} style={styles.container}>
        <View style={[styles.bubble,{backgroundColor: backgroundColor, borderColor: borderColor}]}>
          <Image resizeMode='stretch' style={styles.marker} source={icon} />
          <Text style={[styles.text, { fontSize }]}>{text||''}</Text>
        </View>
        <View style={[styles.arrowBorder,{borderTopColor:borderColor}]} />
        <View style={[styles.arrow,{borderTopColor:backgroundColor}]} />
      </View>
    );
  }
}

IconText.propTypes = propTypes;
IconText.defaultProps = defaultProps;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'column',
    alignSelf: 'flex-start',
  },
  marker:{
    //flex:1,
    width: 22,
    height: 22,
  },  
  bubble: {
    flex: 0,
    flexDirection: 'row',
    alignSelf: 'flex-start',
    backgroundColor: 'transparent',
    padding: 0,
    paddingRight: 5,
    paddingLeft: 0,
    borderRadius: 11,
    borderColor: 'transparent',
    borderWidth: 1,
  },
  dollar: {
    color: '#FFFFFF',
    fontSize: 10,
  },
  text: {
    color: '#FFFFFF',
    fontSize: 13,
    padding: 3,
  },
  arrow: {
    backgroundColor: 'transparent',
    borderWidth: 4,
    borderColor: 'transparent',
    borderTopColor: '#5A5FFF',
    alignSelf: 'center',
    marginTop: -9,
  },
  arrowBorder: {
    backgroundColor: 'transparent',
    borderWidth: 4,
    borderColor: 'transparent',
    borderTopColor: '#3F44D2',
    alignSelf: 'center',
    marginTop: -0.5,
  },
});

export default IconText;