import React, {Component} from 'react';
import {Platform, StyleSheet, Text, View,Dimensions,ImageBackground} from 'react-native';
import {getPropPicUrl} from '../../mixins/mapSearch';
import serverDomainIns from '../../utils/serverDomain'
const noPic = require('../../assets/images/noPic.png');
const loadingPic = require('../../assets/images/ajax-loader.gif');

export default class PropImg extends Component {
  constructor(props) {
    super(props);
    this.state = {
      img:loadingPic,
      prop:props.prop,
      imgReady:false,
    }
  }
  componentDidMount() {
    let img = this.picUrl(this.state.prop);
    this.setState({img})
  }
  picUrl(r) {
    const noPicUrl = 'https://' + serverDomainIns.getFullUrl("/img/noPic.png");
    //保留原来DYR代码，防止thumbUrl策略出问题
    // const url = getPropPicUrl(r, {
    //   protocol: serverDomainIns.getProtocol(),
    //   noPicUrl
    // });
    const url = r.thumbUrl || noPicUrl
    return {uri: url};
  }
  onError(error){
    this.setState({ img: noPic})
  }
  onLoad(e){
    this.setState({imgReady:true})
  }
  render() {
    const {prop,img,imgReady} = this.state;
    let computedBlurRadius = 0;
    if(prop.login){
      computedBlurRadius = 15;
    }
    return(
      <View>
        <ImageBackground style={[styles.img,styles.overlay]}
          source={img}
          blurRadius={prop.blurRadius || computedBlurRadius}
          onError={this.onError.bind(this)}
          onLoad={this.onLoad.bind(this)}>
          <Text style={styles.sid}>{prop.sid||prop.id}</Text>
        </ImageBackground>
        {/* {opacity:imgReady?0:1} */}
        <ImageBackground style={[styles.img]} source={loadingPic} >
          {/* <Text style={styles.sid}>{prop.sid||prop.id}</Text> */}
        </ImageBackground>
      </View>
    );
  }
}
const styles = StyleSheet.create({
  img:{
    marginRight:10,
    width:80,
    height:60,
    position:'relative',
    zIndex:1,
  },
  overlay:{
    position:'absolute',
    left:0,
    right:0,
    bottom:0,
    top:0,
    zIndex:2,
  },
  sid:{
    fontSize:10,
    textAlign:'center',
    position:'absolute',
    bottom:0,
    left:0,
    width:'100%',
    padding:2,
    color:'#fff',
    backgroundColor:'rgba(0,0,0,0.5)'
  },
})
