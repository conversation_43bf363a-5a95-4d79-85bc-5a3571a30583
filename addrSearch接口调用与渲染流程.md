# addrSearch 接口调用与渲染流程分析

## 概述

本文档详细分析了 React Native 应用中 `addrSearch` 接口的调用流程和数据渲染过程。该接口用于搜索房产地址信息，是搜索功能的核心组件之一。

## 整体架构图

```mermaid
graph TD
    A[用户输入搜索关键词] --> B[RMAutoCompleteNative.onChangeText]
    B --> C[searchResultsAtSameTime]
    C --> D[调用 requestStdFn]
    D --> E[发送 addrSearch 请求]
    E --> F[handleAddrSearchResult 处理响应]
    F --> G[更新 state.props]
    G --> H[ResList 组件渲染]
    H --> I[Prop 组件渲染单个房产]
    I --> J[用户点击后导航到详情页]
```

## 详细流程分析

### 1. 接口调用入口

**文件位置**: `realmaster/src/screens/RMAutoCompleteNative.jsx`

#### 触发条件
- 用户在搜索框输入关键词
- 关键词长度 >= 3
- 关键词不包含特殊字符 `*` 或 `)`

```javascript
onChangeText(val) {
  this.setState({searchStr: val});
  if (val && val.length < 3) {
    return;
  }
  if (val.trim() == '' || val.includes(')') || val.includes('*')) {
    return;
  }
  // ... 清理之前的搜索
  this.searchResultsAtSameTime(600, val);
}
```

### 2. 搜索执行函数

#### searchResultsAtSameTime 方法
- **功能**: 同时执行多种类型的搜索（房产、项目、学校、社区、地理位置）
- **延迟**: 600ms 防抖处理
- **并发**: 同时发起 5 个请求，提高搜索效率

```javascript:693-701:realmaster/src/screens/RMAutoCompleteNative.jsx
requestStdFn('addrSearch', {s: val, limit: 10, loc: 1, ts: this.addrSearchTs}).then(ret => {
  handleAddrSearchResult(ret); // 处理地址搜索结果
}).catch(err => {
  // handle error, ））9 特殊指令处理，服务端不接受，但是需要返回其他方法结果
  this.notFoundProp = true; // 标记未找到房产
  this.hasSearchedlisting = true; // 标记已搜索过房产列表
  this.setState({firstMatchedAddr: null, props: [],searchingProps:false}); // 清空搜索结果和状态
  console.warn('error addrSearch', err); // 输出地址搜索错误警告
})
```

### 3. 网络请求层

**文件位置**: `realmaster/src/utils/request.js`

#### requestStdFn 函数
```javascript
const requestStdFn = async (fnName, param) => {
  return new Promise((resolve, reject) => {
    const data = {
      q: [
        {
          fn: fnName,           // 'addrSearch'
          p: param,             // {s: val, limit: 10, loc: 1, ts: timestamp}
        },
      ],
    };

    request({
      url: '/1.5/stdfun',      // 标准函数接口
      method: 'post',
      data,
    })
    .then(res => {
      const {ret, err} = res.r[0];
      if (err) {
        reject(err);
      } else {
        resolve(ret);
      }
    })
    .catch(err => {
      reject(err);
    });
  });
};
```

#### 请求参数说明
- `s`: 搜索关键词
- `limit`: 返回结果数量限制（默认10）
- `loc`: 是否包含位置信息（1为包含）
- `ts`: 时间戳，用于去重和排序

### 4. 响应数据处理

#### handleAddrSearchResult 函数
```javascript
let handleAddrSearchResult = ret => {
  var props = [];
  var firstMatchedAddr = null;
  this.hasSearchedlisting = true;
  
  if (ret && ret.ok) {
    if (ret.ts !== this.addrSearchTs) {
      return; // 时间戳校验，防止旧请求覆盖新请求
    }
    props = ret.l; // 房产列表数据
    
    if (ret.l.length == 0) {
      this.notFoundProp = true;
    }
    if (props && props.length) {
      firstMatchedAddr = props[0]; // 第一个匹配的地址
    }
  } else {
    this.notFoundProp = true;
    if (ret && ret.err) {
      console.error(err);
    }
  }
  
  this.setState({
    firstMatchedAddr, 
    props: props, 
    searchingProps: false
  });
};
```

#### 数据结构示例
```javascript
// addrSearch 返回的数据结构
{
  ok: true,
  l: [
    {
      _id: "property_id",
      addr: "123 Main St",
      city: "Toronto",
      prov: "ON",
      lp: 800000,        // 列表价格
      sp: 750000,        // 售价
      lat: 43.6532,      // 纬度
      lng: -79.3832,     // 经度
      stplabel: "sale",  // 销售类型
      ptype2: ["Condo"], // 房产类型
      // ... 其他房产信息
    }
  ],
  ts: 1609459200000    // 时间戳
}
```

### 5. 数据渲染层

#### 5.1 ResList 组件
**文件位置**: `realmaster/src/components/search/ResList.jsx`

```javascript
// 根据 tab 类型渲染不同的数据
switch (currentTab) {
  case 'listing':
    tp = 'prop';          // 房产类型
    tl = l10n('Listing'); // 显示标题
    lst = props;          // 房产数据列表
    more = '/1.5/search/prop?d=/1.5/index&id=' + searchStr;
    break;
  // ... 其他类型
}

// 传递给 ResList 组件
<ResList
  referer={this.data.referer}
  schoolAction={this.schoolAction}
  lang={this.data.lang}
  closePopup={this.data.closePopup}
  search={this.search}
  searchStr={this.state.searchStr}
  lst={lst}              // 房产数据列表
  tl={tl}               // 标题
  tp={tp}               // 类型
  more={more}           // 更多链接
/>
```

#### 5.2 ResList 渲染逻辑
```javascript
// ResList.jsx - renderItem 方法
renderItem={({item}) => {
  if (tp == 'prop') {
    return <Prop 
      referer={referer} 
      prop={item}          // 单个房产数据
      lang={lang} 
      closePopup={closePopup} 
      search={this.props.search} 
    />;
  }
  // ... 其他类型的渲染
}}
```

#### 5.3 Prop 组件（单个房产项）
**文件位置**: `realmaster/src/components/search/Prop.jsx`

关键渲染元素：
- **地址**: `prop.v || prop.addr + ', ' + prop.prov`
- **价格**: `currency(prop.lp || prop.lpr, '$', 0)`
- **房型**: `prop.ptype2.join(' ')`
- **房间信息**: `<Rooms prop={prop} />`
- **状态标签**: `prop.stplabel`（sale/rent/sold等）
- **上市时间**: `getTs(prop) + ' listed'`

```javascript
return(
  <TouchableOpacity onPress={()=>{
    this.props.search('prop',prop);
    gotoProp(prop,lang,closePopup)  // 点击跳转到详情页
  }}>
    <View style={styles.propListWrapper}>
      <View style={styles.left}>
        <PropImg prop={prop}/>        {/* 房产图片 */}
        <View>
          <Text style={styles.addr}>   {/* 地址 */}
            {prop.v || (`${prop.addr}, ${prop.prov}`)}
          </Text>
          <View style={styles.lpSaletp}>
            <Text style={styles.price}>{price}</Text>  {/* 价格 */}
            {lp}                                      {/* 原价（如果有折扣） */}
            {tpView}                                  {/* 类型标签 */}
          </View>
          <View style={styles.roomNType}>
            {rooms}                                   {/* 房间信息 */}
            <Text style={styles.ptype}>{ptype2}</Text> {/* 房产类型 */}
          </View>
          <Text style={styles.info}>{info}</Text>    {/* 其他信息 */}
        </View>
      </View>
      {rightSection}                                 {/* 右侧操作按钮 */}
    </View>
  </TouchableOpacity>
);
```

### 6. 用户交互处理

#### 6.1 点击房产项
- 调用 `search('prop', prop)` 添加到历史记录
- 调用 `gotoProp(prop, lang, closePopup)` 跳转到详情页

#### 6.2 跳转详情页
**文件位置**: `realmaster/src/components/search/helper.js`

```javascript
async function gotoProp(prop,lang,closePopup) {
  var propId = prop._id || prop.id;
  var base = "/1.5/prop/detail/inapp?lang=";
  
  if (prop.isProj || prop.tp1 || prop.p == 'project') {
    base = '/1.5/prop/projects/detail?inframe=1&lang='
  }
  
  var url = base + lang + "&id=" + propId;
  
  // 通过 WebView 或应用内浏览器打开详情页
  var opt = {
    hide: false,
    sel: '#callBackString',
    tp: 'pageContent',
    title: l10n('RealMaster'),
    url: serverDomainIns.getFullUrl(url),
  }
  eventEmitter.emit("app.message", {msg: JSON.stringify(opt), cb});
}
```

## 相关文件清单

### 核心文件
1. **`realmaster/src/screens/RMAutoCompleteNative.jsx`** - 主搜索组件
2. **`realmaster/src/utils/request.js`** - 网络请求工具
3. **`realmaster/src/components/search/ResList.jsx`** - 搜索结果列表
4. **`realmaster/src/components/search/Prop.jsx`** - 单个房产项组件
5. **`realmaster/src/components/search/helper.js`** - 跳转和导航辅助函数

### 辅助组件
1. **`realmaster/src/components/search/PropImg.jsx`** - 房产图片组件
2. **`realmaster/src/components/search/Rooms.jsx`** - 房间信息组件
3. **`realmaster/src/components/search/HistRight.jsx`** - 历史记录右侧操作
4. **`realmaster/src/components/RmIcon.jsx`** - 图标组件

### 工具文件
1. **`realmaster/src/utils/i18n.js`** - 国际化处理
2. **`realmaster/src/utils/colors.js`** - 颜色主题
3. **`realmaster/src/mixins/filters.js`** - 数据格式化（货币等）

## 性能优化要点

1. **防抖处理**: 用户输入后延迟 600ms 执行搜索
2. **时间戳校验**: 防止旧请求覆盖新请求结果
3. **并发搜索**: 同时执行多种类型搜索，提高效率
4. **列表虚拟化**: 使用 FlatList 优化长列表性能
5. **图片懒加载**: PropImg 组件实现图片按需加载

## 错误处理

1. **网络错误**: catch 捕获并显示警告信息
2. **数据校验**: 检查返回数据的完整性
3. **时间戳验证**: 防止过期数据覆盖
4. **用户反馈**: 通过 loading 状态和错误提示改善用户体验

## 总结

`addrSearch` 接口的调用和渲染流程体现了现代 React Native 应用的最佳实践：
- 组件化设计，职责清晰
- 异步处理和错误捕获
- 性能优化和用户体验
- 可维护和可扩展的代码结构

整个流程从用户输入到数据展示，涉及网络请求、状态管理、组件渲染等多个层面，是一个完整的前端数据处理范例。 