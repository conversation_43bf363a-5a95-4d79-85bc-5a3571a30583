 LOG  🚀 ~ file: location.js:215 ~ getGeoPosition ~ pos: {"coords": {"accuracy": 5, "altitude": 0, "altitudeAccuracy": -1, "heading": -1, "latitude": 37.785834, "longitude": -122.406417, "speed": -1}, "timestamp": 1733996080262.712}
 LOG  🚀 ~ file: business.js:182 ~ validLocationInCa ~ pos: {"coords": {"accuracy": 5, "altitude": 0, "altitudeAccuracy": -1, "heading": -1, "latitude": 37.785834, "longitude": -122.406417, "speed": -1}, "timestamp": 1733996080262.712}
 LOG  🚀 ~ file: business.js:214 ~ validLocationInCa ~ isInCanada:
 LOG  🚀 ~ file: location.js:166 ~ getGeoPosition ~ gpsRes: {"coords": {"accuracy": 5, "altitude": 0, "altitudeAccuracy": -1, "heading": -1, "latitude": 43.6448, "longitude": -79.3958, "speed": -1}, "timestamp": 1733996080262.712}
 LOG  🚀 ~ file: location.js:72 ~ updateGeoPosition ~ tmp: {"coords": {"accuracy": 5, "altitude": 0, "altitudeAccuracy": -1, "heading": -1, "latitude": 43.6448, "longitude": -79.3958, "speed": -1}, "timestamp": 1733996080262.712}
 LOG  🚀 ~ file: location.js:72 ~ updateGeoPosition ~ tmp: {"coords": {"accuracy": 5, "altitude": 0, "altitudeAccuracy": -1, "heading": -1, "latitude": 37.785834, "longitude": -122.406417, "speed": -1}, "timestamp": 1733996079437.607}




 location.js:220 ~ getCacheGeoPos ~ geoPosition: {"coords": {"accuracy": 5, "altitude": 0, "altitudeAccuracy": -1, "heading": -1, "latitude": 37.785834, "longitude": -122.406417, "speed": -1}, "timestamp": 1733996079437.607}
