
import {
  Platform,
  Vibration,
  Alert,
} from 'react-native';
import {Notifications} from 'react-native-notifications';
import storageIns from './storage'
import Constants from '../config/constants';



var pushMessages = [];
var fromPNMessage = false;
const PN_TOKEN = Constants.PnToken;
var gNotificationEvent;
var gListenerForground;
var gListenerBackground;
var gListenerOpen;
var gListenerRegister;

// Notifications.postLocalNotification({
//   title: "测试通知",
//   body: "这是一个本地推送通知的测试消息",
//   sound: "chime.aiff",
//   silent: false,
//   category: "SOME_CATEGORY",
//   userInfo: {}
// });


var formatNotification = (n)=>{
  let str = "";
  for (const key in n) {
    if (n.hasOwnProperty(key)) {
      const ele = n[key];
      str+=key+' -> '+(ele+'').substr(0,10)+'\n'
    }
  }
  return str;
}
function shouldHandleThisMessage(notification){
  if (Platform.OS=='ios'){
    return notification.userInteraction
  } else {
    return notification.userInteraction
  }
}
var pnConfig = async (app,opt={}) => {

  var handleNotification = (notification) => {
    // {
    //   "foreground": true,
    //   "identifier": "2B2CB7EC-E7E3-4A8F-8C01-C54CED463826",
    //   "payload": {
    //     "aps": {
    //       "alert": [Object],
    //       "badge": 1,
    //       "sound": "al1"
    //     },
    //     "body": "test msg中文内容",
    //     "category": "",
    //     "date": "2024-09-05T13:33:05.146+08:00",
    //     "id": 30,
    //     "identifier": "2B2CB7EC-E7E3-4A8F-8C01-C54CED463826",
    //     "messageFrom": "RealMaster test",
    //     "thread": "",
    //     "title": "pn title3",
    //     "url": "/1.5/prop/detail/inapp?lang=zh-cn&id=TRBC5382555"
    //   },
    //   "userInteraction": false
    // }

    if (typeof notification !== 'object') {
      return;
    } else if (notification == null) {
      notification = {};
    }
    Vibration.vibrate([1000]);
    var url = '';
    var title = '';
    var msg = '';

    ['message','data','payload'].forEach((i)=>{
      if(notification[i]){
        if(notification[i].url != null){
          url = notification[i].url
        }
        if( notification[i].title != null){
          title = notification[i].title
        }
        if( notification[i].body != null){
          body = notification[i].body
        }
      }
    })
    if(notification.url){
      url = notification.url
    }
    if(notification.title){
      title = notification.title
    }
    // !notification.foreground &&
    // NOTE: bug for https://github.com/zo0r/react-native-push-notification#readme https://github.com/zo0r/react-native-push-notification/issues/2241
    // TODO: consider new libs
    if(shouldHandleThisMessage(notification)){
      fromPNMessage = true;
      setTimeout(() => {
        fromPNMessage = false;
      }, 10000);
      pushMessages.push({url:url, title:title, msg:msg});

      // if(app.adScreenDone == 'true' || app.adScreenDone == 'timeout'){
      //   app.debounceHandlePushMessage('adScreenDone=true, appin background, user click')
      // // NOTE: case when app in background but not killed, wont show adScreen but refresh app.js
      // // android:launchMode="singleTask" solved reload app when click pn
      // } else {
      //   setTimeout(() => {
      //     app.debounceHandlePushMessage('force handle after 5.5s')
      //   }, 5500);
      // }
      app.debounceHandlePushMessage()
    }
    // console.log('xxxxxforeground:',notification.foreground,' userInteraction:',notification.userInteraction, Date.now())
    // @note: if foreground and !userInteraction => code push notice from sys -> ignore
    if ((notification.foreground && !notification.userInteraction)) {
      // NOTE: dialogConfirm jump?
      // NOTE: pn test need a way to pass data to webview, cant confirm here
      var noticeTimeout = 0;
      if (Platform.OS === 'android') {
        noticeTimeout = 2000;
      }
      // NOTE: @allen dont show alert in native, direct open if user click
      pushMessages.push({url:url, title:title, msg:msg});
      // ios & android handle differently
      if (Platform.OS == 'ios' && !/rmpntest/.test(url)){
        return
      }
      // # type notice{
      // #   url: string,
      // #   title: string,
      // #   message: string,
      // #   foreground: boolean,
      // #   data: DEPRECATED!
      // # }
      let webMessage = {
        message: msg,
        title: title,
        url: url,
        foreground: notification.foreground,
      }
      // console.log('++++',webMessage)
      app.whenAppReady(() => {
        setTimeout(() => {
          // console.log('when ready registered; timeout called',notification)
          app.postMessage(webMessage, null, 'pushNotice', 0);
        }, noticeTimeout);
      });
    }
  }
  const hasPermissions = await Notifications.isRegisteredForRemoteNotifications();

  if(!hasPermissions){
    return
  }
  // NOTE: if not remove, app will crash, see: https://github.com/wix/react-native-notifications/issues/913
  if(gListenerForground){
    // console.log('+++++removed listener')
    gListenerForground.remove()
  }
  if(gListenerBackground){
    // console.log('+++++removed listener')
    gListenerBackground.remove()
  }
  if(gListenerOpen){
    // console.log('+++++removed listener')
    gListenerOpen.remove()
  }
  if(gListenerRegister){
    // console.log('+++++removed listener')
    gListenerRegister.remove()
  }
  gNotificationEvent = Notifications.events()



  // console.log(gNotificationEvent)
  // gFailed = gNotificationEvent.registerRemoteNotificationsRegistrationFailed((event) => {
  //   console.error(event);
  // });
  //   Notifications.ios.checkPermissions().then((currentPermissions) => {
  //     console.log('Badges enabled: ' + !!currentPermissions.badge);
  //     console.log('Sounds enabled: ' + !!currentPermissions.sound);
  //     console.log('Alerts enabled: ' + !!currentPermissions.alert);
  //     console.log('Car Play enabled: ' + !!currentPermissions.carPlay);
  //     console.log('Critical Alerts enabled: ' + !!currentPermissions.criticalAlert);
  //     console.log('Provisional enabled: ' + !!currentPermissions.provisional);
  //     console.log('Provides App Notification Settings enabled: ' + !!currentPermissions.providesAppNotificationSettings);
  //     console.log('Announcement enabled: ' + !!currentPermissions.announcement);
  // });

  gListenerForground = gNotificationEvent.registerNotificationReceivedForeground((notification, completion) => {
    notification.foreground = true
    notification.userInteraction = false
    handleNotification(notification);
    completion({alert: true, sound: false, badge: false});
  });
  gListenerOpen = gNotificationEvent.registerNotificationOpened((notification, completion) => {
    notification.userInteraction = true
    handleNotification(notification);
    completion({alert: true, sound: true, badge: false});
  });
  gListenerBackground = gNotificationEvent.registerNotificationReceivedBackground((notification, completion) => {
    notification.foreground = false
    handleNotification(notification);
    // Calling completion on iOS with `alert: true` will present the native iOS inApp notification.
    completion({alert: true, sound: true, badge: false});
  });
  gListenerRegister = gNotificationEvent.registerRemoteNotificationsRegistered((event) => {
    let token = event.deviceToken
    // token = e1f4ef88d2cb9cdb759919a5183e680d75f16adba0ad4411320635cd591eb919

    // token.tp = 'fcm';
    // type tokenObj{
    //   token:string,
    //   os:'ios'
    // }
    let tokenObj = {token:token,os:Platform.OS}
    // this version only support fcm, dump gcm support
    if(Platform.OS == 'android'){
      tokenObj.tp = 'fcm';
    }
    storageIns.setItem(PN_TOKEN, JSON.stringify(tokenObj));
    setTimeout(() => {
      app.postMessage(tokenObj, null, 'pushToken', 0)
    }, 3000);

  });

  Notifications.registerRemoteNotifications();
  // if android
  if(Platform.OS == 'android'){
    Notifications.setNotificationChannel({
      channelId: 'RealMaster-android-pn-channel-id',
      name: 'RealMaster channel',
      importance: 5,
      description: 'RealMaster channel for prop update and news',
      enableLights: true,
      enableVibration: true,
      showBadge: false,
      // soundFile: 'custom_sound.mp3',  // place this in <project_root>/android/app/src/main/res/raw/custom_sound.mp3
      vibrationPattern: [200, 1000, 500, 1000, 500],
    })
  }

  var notification = await Notifications.getInitialNotification();

  // if(Platform.OS == 'android' ){
  if(notification) {
    //应用冷启动
    app.hideSplash();
    console.log('getInitialNotification:',notification);
    notification.initialNotification = true
    notification.foreground = false
    notification.userInteraction = true
  }
  handleNotification(notification);
}
// pnConfig()
// const instructions = Platform.select({
//   ios: 'Press Cmd+R to reload,\n' +
//     'Cmd+D or shake for dev menu',
//   android: 'Double tap R on your keyboard to reload,\n' +
//     'Shake or press menu button for dev menu'
// });
function getPushMessages(){
  // console.log('pushmessages:',pushMessages.length)
  return pushMessages;
}
function clearPushMessages(){
  pushMessages = [];
}
function getFromPNMessage(){
  return fromPNMessage;
}
// will open in popup
// otherwise change root
function isPopupPushMessageUrl(url=''){
  if(/\/prop\/detail\/inapp/.test(url)){
    return true;
  }
  if(/1\.5\/showing\/detail/.test(url)){
    return true;
  }
  if(/nativePopup/i.test(url)){
    return true
  }
  return false;
}
function isFubUrl(url=''){
  if(/\/prop\/detail/.test(url)){
    if(/fub\_src=fub/.test(url)){
      return true;
    }
  }
  return false;
}
function isFullScreenPushMsgUrl(url=''){
  if(/\/forum/.test(url)){
    if(/postid=\w+/.test(url)){
      return true;
    }
  }
  if(/1\.5\/mapSearch.*mode/.test(url)){
    return true;
  }
  if(/nativeFullscreen/i.test(url)){
    return true;
  }
  return false;
}
export { pnConfig,
  isFullScreenPushMsgUrl,
  isPopupPushMessageUrl,
  isFubUrl,
  clearPushMessages,
  getPushMessages,
  getFromPNMessage }
