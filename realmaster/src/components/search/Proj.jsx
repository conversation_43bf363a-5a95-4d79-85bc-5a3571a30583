import React, {Component} from 'react';
import {Platform, StyleSheet, Text, View,ImageBackground,TouchableOpacity,Dimensions} from 'react-native';
import {gotoProp} from './helper'
import RmIcon from '../RmIcon'
import HistRight from './HistRight';
const {width} = Dimensions.get('window');

export default Proj = (props) =>{
  const {proj,page,lang,searchStr} = props;
  let img = 'https://www.realmaster.com/img/noPic.png';
  if (proj.img && proj.img.l) {
    img = proj.img.l[0];
  }
  let rightSection,nm = proj.nm;
  if (page == 'hist') {
    rightSection = <HistRight item={proj} removeHist={props.removeHist} />;
  } else {
    rightSection = (
      <View style={styles.right}>
        <RmIcon size={20} name={'angle-right'} color={'rgb(153,153,153)'} />
      </View>
    );
  }
  // console.log('++++++',lang,searchStr,proj.nm,'+++++',proj.nm_en)
  if (lang == 'en' && proj.nm_en) {
    nm = proj.nm_en;
    var reg = RegExp(searchStr,'ig')
    if(!reg.test(nm)){
      nm = proj.nm;
    }
  }
  return(
    <TouchableOpacity onPress={()=>{props.search('proj',proj);gotoProp(proj,lang)}}>
      <View style={styles.proj}>
        <View style={styles.left}>
          <ImageBackground style={[styles.img]} source={{uri:img}}>
          </ImageBackground>
          <View>
            <Text numberOfLines={2} style={styles.nm}>{nm}</Text>
            <Text style={styles.city}>{proj.city}{proj.prov?', '+proj.prov:''}</Text>
          </View>
        </View>
        {rightSection}
      </View>
    </TouchableOpacity>
  );  
}
const styles = StyleSheet.create({
  proj:{
    backgroundColor:'#fff',
    padding:10,
    flexDirection:'row',
    justifyContent:'space-between',
    alignItems:'center',
    borderColor:'rgb(240,240,240)',
    borderBottomWidth:0.5,
    borderStyle:'solid'
  },
  left:{
    flexDirection:'row',
    alignItems:'center'
  },
  img:{
    marginRight:10,
    width:80,
    height:60,
    position:'relative'
  },
  nm:{
    width:width - 150,
    color:'rgb(80,80,80)',
    fontSize:14,
    marginBottom:5,
    fontWeight:'bold'
  },
  city:{
    fontSize:12,
    color:'rgb(177,177,177)'
  },
  ptype:{
    color:'rgb(135,161,185)',
    fontSize:12,
    marginLeft:5
  }
})