/*
Show transit on map
1. get transit routes
2. show overlay, small region images or large region tile images
3. show stops & names
4. show circles when stop clicked, reclick to hide. Maximun 25 stops
*/

import React, {Component} from 'react';
import {StyleSheet, View, Text, TouchableOpacity, TouchableHighlight, FlatList} from 'react-native';
import {Icon, eventEmitter} from '../utils/common';
import StopMarker from '../components/StopMarker';
import MapFeature from './MapFeature';
import {Marker, Overlay, Circle, Polyline} from 'react-native-maps';
import BottomPane from '../components/BottomPane';
import {requestStdFn} from '../utils/request';
import serverDomainIns from '../utils/serverDomain';
import {l10n, getAppLang} from '../utils/i18n';
// import {isOverlap,getPointByDistance} from "./helper";
import {RMBottomBar} from '../components/RMStatusBar';
import {deepAssign, deepCloneArray} from './helper';
import Constants from '../config/constants';
import {distanceBySmallestUnit, round} from './helper';
import ListDragPan from './mapListDragPanResponder'
import LayerMenu from '../components/LayerMenu';
// import { getIconChar } from './mapSearch';
// import { registerApp } from 'react-native-wechat-lib';

// const hMarker = require('../img/transit/regions/Canada_Alberta_Edmonton_Bus_L-0-4.png');
const DEFAULT_LINE_COLOR = '#007aff'; //'#e03131';
const ROUTE_ITEM_HEIGHT = 54;
const STOP_ITEM_HEIGHT = 38;
var gTransitRegions = null;
// var gTransitProvs = [
//   {k:'ON',v:'GTA'},
//   {k:'BC',v:'GVA'},
//   {k:'AB',v:'Alberta'},
//   {k:'QC',v:'Quebec'}
// ];
var gRouteTypes = [
  {route_tp: 'Sub', v: 'Sub'},
  {route_tp: 'Rail', v: 'Rail'},
  {route_tp: 'Bus', v: 'Bus'},
];
const STOP_STROKE_COLOR = '#F4604D';
const STOP_FILL_COLOR = 'rgba(224,96,77,0.1)';
const MAX_RENDER_ROUTES = 15;
var gAllRouteTpsByProv = {};
// requestStdFn('getTransitProvs',{},(err,ret)=>{
//   if (err){
//     console.error('getTransitProvs: ',err);
//     return;
//   }
//   console.log('++++++getTransitProvs',ret)
//   if (ret.mapping && ret.mapping.length > 0) {
//     gTransitProvs = ret.mapping;
//     // console.log(gTransitRegions)
//   }
// })
// function genTransitProvRegionTps(region){
//   if (region.rttp) {
//     var rttp = region.rttp;
//     if (!gAllRouteTpsByProv[region.prov]) {
//       gAllRouteTpsByProv[region.prov] = {} // Ontario:{Bus:['TTC','GO']};
//     }
//     let provTp = gAllRouteTpsByProv[region.prov];
//     if (!provTp[rttp]) {
//       provTp[rttp] = [];
//     }
//     if (tp = region.tp) {
//       provTp[rttp].push(tp);
//     }
//   }
// }
// function genTransitRegionTps(regions, routeTps) {
//   // console.log(regions,routeTps)
//   if (regions.rttp) {
//     var rttp = regions.rttp;
//     // legacy
//     if (!routeTps[rttp]) {
//       routeTps[rttp] = [];
//     }
//     if (tp = regions.tp) {
//       routeTps[rttp].push(tp);
//     }
//   }
// }
function getLineName(item) {
  let nm = item.lnm || item.nm || item.fnm || '';
  // if(/\(/.test(nm)){
  // }
  return nm;
}
// if is bus/tram dont do a lot of things
function isBusOrTram(route_tp) {
  if (Array.isArray(route_tp)) {
    return /bus|tram/i.test(route_tp.join(','));
  }
  return ['Bus', 'Tram'].includes(route_tp);
}
// TODO: move to backend
function getArrayFirstVal(arr) {
  if (Array.isArray(arr)) {
    return arr[0];
  }
  return arr;
}
function getStopNameShort(stop = {}, selectedRoute = {}) {
  // console.log(stop)
  // NOTE: do not show name for bus stop, just dot
  // if(stop.route_tp && selectedRoute && selectedRoute.route_tp){
  //   if(isBusOrTram(stop.route_tp)){
  //     return ''
  //   }
  // }
  return stop.nm || getArrayFirstVal(stop.fnm);
  // let nm = stop.nm[0] || '';
  // if(/-/.test(nm)){
  //   return nm.split('-')[0].replace('STATION','').trim()
  // }
  // let NM_MAX_LEN = 13;
  // if(nm.length > NM_MAX_LEN){
  //   nm = nm.substr(0,12)+'..'
  // }
  // return nm
}
// coords = [44,-79] -> {lat:44,lng:-79}
// INPUT: f is feature
function parseCoordsToLatLng(f) {
  ['coords14', 'coords17'].forEach(i => {
    if (f[i] && Array.isArray(f[i])) {
      // f[i+'back'] = f[i]
      let coords = [];
      f[i].forEach(line => {
        if (Array.isArray(line)) {
          // console.log('Error:',i,f[i],region)
          let tmp = [];
          line.forEach(p => {
            tmp.push({latitude: p[1], longitude: p[0]});
          });
          coords.push(tmp);
        }
      });
      f[i] = coords;
    }
  });
}
// when user click transportation
// when have data, dont get from server
// function getRemoteTransitRegions(cb){
//   requestStdFn('getTransitRegions',{},(err,ret)=>{
//     if (err){
//       console.error('getTransitRegions: ',err);
//       return;
//     }
//     if (ret.rgns && ret.rgns.length > 0) {
//       console.log('+++++getTransitRegions',JSON.stringify(ret).length)
//       // regions bbox, max - min 2-0,3-1
//       // tilewidth = latDelta(2-0)/cropY, tileheight = lngDelta(3-1)/cropX
//       // caculate bbox for each tile
//       // TODO: latDelta/lngDelta cropY/cropX => latTileSpan/lngTileSpan
//       // calc and save all tile l size bbox: 0~...
//       for (var region of ret.rgns) {
//         var bbox = region.bbox;
//         // console.log('--------',region.img)
//         var regionTiles = [];
//         if(!(region.img && region.img.l)){
//           console.error('region has no img.l');
//           region.regionTiles = regionTiles;
//           continue;
//         }
//         //if l or img;
//         var {cropX,cropY} = region.img.l;
//         var minLng = bbox[0];
//         var minLat = bbox[1];
//         var maxLng = bbox[2];
//         var maxLat = bbox[3];
//         var tileWidth = Math.abs(maxLng - minLng)/cropX;
//         var tileHeight = Math.abs(maxLat - minLat)/cropY;
//         region.tileWidth = tileWidth;
//         region.tileHeight = tileHeight;
//         for (var y = 0; y < cropY; y++) {
//           for (var x = 0; x < cropX; x++) {
//             var tile = {bbox:[minLng+tileWidth*x,maxLat-tileHeight*(y+1),minLng+tileWidth*(x+1),maxLat-tileHeight*y]};
//             regionTiles.push(tile);
//           }
//         }
//         region.regionTiles = regionTiles;
//         genTransitProvRegionTps(region)
//         console.log('feat length-> ',region.feat.length)
//         // when user click certain route, get route
//         if(region.feat && region.feat.length){
//           region.feat.forEach((f)=>{
//             // NOTE: coords is [[],[],[]]
//             parseCoordsToLatLng(f)
//           })
//         }
//       }
//       gTransitRegions = ret;
//     }
//     if(cb){
//       cb()
//     }
//   })
// }
// getRemoteTransitRegions()
// var dummyData = [
//   {k:'DB',v:'De Borris'},
//   {k:'KFC',v:'KFC'},
//   {k:'MS',v:'Mount Saint'},
//   {k:'YSL',v:'Ye Saint lahuntr'},
// ]
function getRemoteRouteTpsByProv(cb) {
  requestStdFn('getRouteTpsByProv', {})
    .then(ret => {
      if (ret && ret.ok) {
        gAllRouteTpsByProv = ret.map;
        gRouteTypes = ret.routeTypes;
        // gTransitProvs = ret.provs;//.concat(dummyData);
      }
      if (cb) {
        cb();
      }
    })
    .catch(err => {
      console.error('getRouteTpsByProv: ', err);
      return;
    });
}
// getRemoteRouteTpsByProv()

class MapTransit extends MapFeature {
  constructor(map, props, onOff) {
    super(map, 'MapTransit', props, onOff);
    // let tmp = gTransitProvs;
    // console.log('gAllRouteTpsByProv',gAllRouteTpsByProv)
    // if (gTransitProvs){
    //   tmp = deepAssign(gTransitProvs);
    //   console.log('tmp=',tmp)
    // };
    this.state = {
      transits: [], //? not used
      mapStops: [], //map stops
      routes: [], // list of routes by selected type
      mapRoutes: [], // routes rendered on map
      curRouteStops: [], //list of stops accroding to curSelectedRoute , post {routes_id}
      // regions:[], //overlapped regions
      // mapRouteTps:{},//Bus,Sub,Tram,Rail  -> 1st layer, on map
      // allRouteTps:deepAssign(allRouteTps),//Bus,Sub,Tram,Rail  -> 1st layer, all
      allRouteTpsByProv: deepAssign(gAllRouteTpsByProv),
      routeTypes: deepCloneArray(gRouteTypes),
      selectedRouteTps: {route_tp: 'Sub', v: 'Sub'}, //null, //{nm,routetp,agnet} //Bus,Sub
      // Bus: (2) ["TTC", "GO"]
      // Rail: (2) ["GO", "UP"]
      // Sub: ["TTC"]
      // Tram: ["TTC"]
      prov: '', //'prov Name or region name',
      // region:[],
      // provDisp:'',
      // transitProvsMapping:deepCloneArray(gTransitProvs),
      // transitTps:[],//UP,TTC,YRT,GO  -> 2nd layer
      // selectedRegions:[],
      // selectedTiles:[],
      // selectedTransitTps:[], //UP,TTC
      bbox: null,
      showStopModal: false,
      selectedStop: null,
      selectedRoute: null, //{nm,_id,route_id} cur selected route
      zoom: null,
      curStopCircleLabelTimer: 5, //timeout show circle labels
      showPanel: false,
      currentStopRadius: 500,
      radiusList: [200, 500, 1000, 2000],
      tracksViewChanges: false,
      alertCount: 3,
      showAllRouteFontWeight: 'normal',
      propFeatureOn: true,
      stopsCnt: 0,
    };
    // this.featureOn = true;
    this.stopCircleRef = null;
    this.stopListRef = null;
    this.provSelectRef = null;
    this.routesListRef = null;
    this.needZoomIn = false;
    if(props.propFeatureOn != null){
      this.state.propFeatureOn = props.propFeatureOn
    }
    // getRemoteTransitRegions(()=>{
    //   this.setState({
    //     allRouteTpsByProv:deepAssign(gAllRouteTpsByProv)
    //   })
    // });
    getRemoteRouteTpsByProv(() => {
      // let stateProvk = this.state.prov
      // let prov = gTransitProvs.find((i)=>{ return i.k == stateProvk})
      // console.log('xxxxx',prov)
      this.setState({
        allRouteTpsByProv: deepAssign(gAllRouteTpsByProv),
        // transitProvsMapping:deepCloneArray(gTransitProvs),
        routeTypes: deepCloneArray(gRouteTypes),
        // region:prov.region
      });
    });
    // eventEmitter.on(SYSTEM.EVENT_CLEAR_FEATURE_PANEL,this.setShowPanel);
    eventEmitter.on(Constants.MapTransitStopModal, this.setShowStopModal);
    eventEmitter.on("map.props.fetureOn",this.recordPropFeatureOn.bind(this))
  }
  recordPropFeatureOn(val){
    this.setState({propFeatureOn:val})
  }
  // if stop in map&stops return stop, else null
  checkCurStopInMap(selectedStop, bbox, stops) {
    // console.log('xxxxxcheckCurStopInMap',selectedStop,bbox)
    if (!(selectedStop && bbox)) return null;
    const {lat, lng} = selectedStop;
    if (bbox[0] <= lng && lng <= bbox[2] && bbox[1] <= lat && lat <= bbox[3]) {
      for (var stop of stops) {
        if (stop._id == selectedStop._id) {
          return selectedStop;
        }
      }
    }
    return null;
  }
  // TODO?: scroll to this route in listView, if route not in list, append.
  isDiffRouteTp(route = {}, selected = {}) {
    let isDiff = false,
      rttp = {};
    let {prov, allRouteTpsByProv} = this.state;
    let allRouteTps = allRouteTpsByProv[prov] || [];
    for (let i of ['route_tp']) {
      if (route[i] != selected[i]) {
        isDiff = true;
        rttp[i] = route[i];
      }
    }
    if (isDiff) {
      // rttp may = {route_tp:'Tram'} no agency info, agency is same
      rttp = Object.assign({}, route, rttp);
      for (let j of allRouteTps) {
        // console.log(rttp,j,j.agency == rttp.agency && j.route_tp == rttp.agency)
        if (j.agency == rttp.agency && j.route_tp == rttp.route_tp) {
          rttp = j;
          break;
        }
      }
    }
    return {isDiff, rttp};
  }
  colorMapStops() {
    let {curRouteStops, selectedRoute, mapStops} = this.state;
    // console.log('======',curRouteStops)
    let colorMap = {};
    if (curRouteStops) {
      for (let stop of curRouteStops) {
        if (!stop.color) {
          stop.color = selectedRoute.color;
        }
        colorMap[stop._id] = stop.color;
      }
    }
    if (mapStops) {
      for (let stop of mapStops) {
        if (colorMap[stop._id]) {
          stop.color = colorMap[stop._id];
        }
      }
    }
  }
  setCurRoute(route, opt = {}) {
    // console.log('setCurRoute triggered',route,opt)
    var self = this;
    let {selectedRouteTps, selectedRoute} = this.state;
    let {isDiff, rttp} = this.isDiffRouteTp(route, selectedRouteTps);
    if (selectedRoute && selectedRoute._id == route._id) {
      // TODO: show list, .prev is curRoute
      // console.log('ignore same route!',selectedRoute)
      this.colorMapStops();
      return;
    }

    requestStdFn('getRouteInfo', {id: route._id, route_id: route.route_id})
      .then(ret => {
        let selectedRoute = ret.route;
        parseCoordsToLatLng(selectedRoute);
        // TODO: update selected route_TP
        let state = {
          showAllRouteFontWeight: 'normal',
          selectedRoute,
          selectedStop: null,
          showStopModal: false,
        };
        if (opt.keepStop) {
          delete state.selectedStop;
          delete state.showStopModal;
        }
        if (isDiff) {
          state.selectedRouteTps = rttp;
          state.routes = [];
        }
        // console.log('-------getRouteInfo route; selectedRouteTps->',selectedRouteTps,' ret->',ret.route,' opt->',opt)//isDiff,rttp,
        this.setState(state, () => {
          // console.log('isDiff-> ',isDiff,' rttp->',rttp)
          if (isDiff) {
            // console.log('-----1 doSearchRoutesAndStops')
            this.doSearchRoutesAndStops(null, {selectedRoute: 1});
          } else {
            this.searchStops(null, {selectedRoute: 1});
            // console.log('searchStops showStopModal',this.state.showStopModal)
          }
        });
      })
      .catch(err => {
        console.error(err);
        return;
      });
  }
  // user selected all stops, fit coords
  renderCurrentRoute() {
    let selectedRoute = this.state.selectedRoute;
    // console.log('+++++selectedRoute',selectedRoute)
    if (selectedRoute.coords14) {
      let coordsAll = [];
      selectedRoute.coords14.forEach(r => {
        coordsAll = coordsAll.concat(r);
      });
      // console.log('++++++coordsAll',coordsAll.length)
      this.map.fitToCoordinates(coordsAll);
    }
    this.setState({showAllRouteFontWeight: 'bold', selectedStop: null, showStopModal: false, tracksViewChanges: true});
    eventEmitter.emit(Constants.MapPropStop, {stop: null});
    this.trackOff();
  }
  // getRegion(reg){
  //   let region = this.state.region
  //   if(!Array.isArray(reg) && reg){
  //     reg = [reg]
  //   }
  //   let ret = [].concat(region||[])
  //   if(reg){
  //     ret=ret.concat(reg)
  //   }
  //   return ret
  // }
  searchStops(event = {}, opt = {}) {
    let {selectedRouteTps, bbox, selectedStop, selectedRoute, zoom} = this.state;
    // console.log('---selected RouteTp',selectedRouteTps)
    if (!selectedRouteTps) {
      return;
    }
    // if sub/rail and not selected route, dont search
    // also for bus !this.isBusOrTram(selectedRouteTps.route_tp) &&
    // if(!selectedRoute){
    //   return
    // }
    var data = {
      bbox,
      // agency:[selectedRouteTps.agency],
      // region:this.getRegion(selectedRouteTps.region),
      route_tp: [selectedRouteTps.route_tp],
      zoom,
    };
    // TODO: if search cond no change ignore, opt.selectedRoute &&
    // NOTE: if not selectedRoute, dont search/show stops
    if (selectedStop) {
      data.selectedStop = selectedStop._id;
      data.selectedStopIds = selectedStop.ids;
    }
    if (selectedRoute) {
      data.route_id = selectedRoute.route_id;
      // data._id = selectedRoute._id;
      data.route_shape_id = selectedRoute._id;
      // delete data.bbox;
    }
    // if(opt.withId && selectedRoute){
    //   data.route_id = selectedRoute.route_id;
    //   data.route_shape_id = selectedRoute._id;
    // }
    var self = this;
    // console.log('+++++searchStops:getTransitStops',opt,data)
    requestStdFn('getTransitStops', data)
      .then(ret => {
        // if user selected Stop but map changed;
        // selectedStop = this.checkCurStopInMap(selectedStop,bbox,ret.stops);
        // let state = {selectedStop};
        let state = {};
        // stops: route.id stops
        // extras: map bbox stops
        if (opt.selectedRoute) {
          if (ret.stops.length && selectedRoute && selectedRoute.color) {
            for (let stop of ret.stops) {
              stop.color = selectedRoute.color;
              // stop.fromRoute = true #from backend
              // console.log(stop)
            }
          }
          state.curRouteStops = ret.stops;
          state.tracksViewChanges = true;
          // delete state.stops;
        }
        state.mapStops = ret.stops.concat(ret.extras || []);
        state.stopsCnt = (ret.cnt || 0) + ret.stops.length;
        // console.log('-------getTransitStops: transit stops',ret)
        // console.log('showStopModal',this.state.showStopModal,opt.selectedRoute)
        this.setState(state, () => {
          this.trackOff();
          if (opt.selectedRoute) {
            // change route search map stops, now list&map separated
            // reset slide position to top if user change route
            if (state.curRouteStops && state.curRouteStops.length && self.stopListRef) {
              // console.log('=====stopListRef scroll',self.stopListRef)
              self.stopListRef.scrollToOffset({animated: false, offset: 0});
              // setTimeout(()=>{
              //   console.log('showStopModal',this.state.showStopModal)
              // },50)
            }
          }
        });
      })
      .catch(err => {
        console.error('Error: getTransitStops ->', err);
        return;
      });
  }
  // NOTE: when map mode not select any sub type(selectedTransitTps),use region/tile features
  // if route_tp and event do search;
  searchRoutes(event, opt = {}) {
    let {alertCount, selectedRouteTps, selectedRoute, bbox, zoom} = this.state;
    if (!selectedRouteTps) {
      return;
    }
    // if already selected dont search
    // if(selectedTransitTps && selectedTransitTps.length == 0 && selectedRouteTps.indexOf('Bus')>-1){
    // when selectedRouteTps.length is 1, and no sub-type to select
    // let noOptionsAvailable = ()=>{
    //   if(selectedRouteTps.length !== 1){return false}
    //   let allRouteTps =  allRouteTpsByProv[prov];
    //   let tp = selectedRouteTps[0];
    //   let length = 1;
    //   if (allRouteTps && allRouteTps[tp]) {
    //     length = allRouteTps[tp].length;
    //   }
    //   if(length == 0) console.log('I had no choice!',tp,allRouteTps[tp])
    //   return length == 0;
    // }
    // let isBusOrTram = selectedRouteTps.length==1 && (
    //   selectedRouteTps.indexOf('Bus')>-1 ||
    //   selectedRouteTps.indexOf('Tram')>-1
    // )
    let isBusOrTramVal = isBusOrTram(selectedRouteTps.route_tp);
    let data = {
      // agency:[selectedRouteTps.agency],
      // region:this.getRegion(selectedRouteTps.region),
      route_tp: [selectedRouteTps.route_tp],
      // prov,
      bbox,
      zoom,
    };
    if (opt.region) {
      data.region = opt.region;
    } else if (opt.prov) {
      data.prov = opt.prov;
    }
    // selectedTransitTps.length || noOptionsAvailable()
    // NOTE: Sub/Rail/Ferry 显示全部线路， Bus/Tram 显示地图范围内
    if (!isBusOrTramVal) {
      // delete data.bbox;
      // if(selectedRoute && selectedRoute.route_tp){
      //   return
      // }
    }
    // console.log('++++++Search routes: getTransitRoutes data->',data,' opt->',opt,'selectedRouteTps->',selectedRouteTps,'selectedRoute->',selectedRoute)
    requestStdFn('getTransitRoutes', data)
      .then(ret => {
        var routes = ret.routes || [];
        // console.log('-------getTransitRoutes: transit routes ret',routes)
        if (!routes.length) {
          // clear map routes?
          this.setState({routes: [], mapRoutes: []});
          // return this.flashMessage(ret.msg || l10n('No routes found in this area'))
          return;
        }
        if (routes && routes.length && routes[0].coords14) {
          for (var r of routes) {
            parseCoordsToLatLng(r);
          }
        }
        // selectedRoute = this.checkCurStopInMap(selectedRoute,bbox,stops);
        // if tp1 and tp2 and > 15, return, alert handled after click
        let flashMsgOnCondition = msg => {
          if (alertCount > 0) {
            this.flashMessage(msg);
          }
        };
        let mapRoutes = [];
        if (!isBusOrTramVal) {
          mapRoutes = [].concat(routes);
        }
        // console.log('xxxxxx',opt,mapRoutes)
        if (opt.fitBnds) {
          let coordsAll = [];
          for (let r of mapRoutes) {
            if (r.coords14) {
              r.coords14.forEach(r => {
                coordsAll = coordsAll.concat(r);
              });
              // console.log('++++++coordsAll',coordsAll.length)
            }
          }
          this.map.fitToCoordinates(coordsAll);
        }
        // if(mapRoutes.length > MAX_RENDER_ROUTES){
        //   if(!selectedTransitTps.length){
        //     flashMsgOnCondition(l10n('Too many routes, please select one transit type'))
        //   } else {
        //     flashMsgOnCondition(l10n('Too many routes, please select one route'))
        //   }
        //   // mapRoutes = [] //mapRouteTps.slice(0,15)
        // }
        // console.log('++++mapRoutes is',mapRoutes)
        this.setState({routes, mapRoutes});
      })
      .catch(err => {
        console.error(err);
        return;
      });
  }
  renderFeatureNoPropDisplay() {
    return null;
    return (
      <View
        key={'schoolRetCount'}
        style={{
          flexDirection: 'row',
          justifyContent: 'flex-end',
          marginTop: 13,
          flex: 10,
          marginLeft: 10,
          marginRight: 10,
        }}>
        <Text style={{color: '#666', fontSize: 15, fontWeight: 'normal'}}>
          {(this.state.mapStops.length || 0) + ' ' + l10n('Stops')}{' '}
        </Text>
        {/* <Icon name="rmplus" size={21} color="black" style={[styles.navBarButton]}   onPress={()=>{this.createStigma()}} /> */}
      </View>
    );
  }
  onOffView() {
    // on/off setting to show on map
    return {
      icon: 'rm-transit',
      iconSize: 25,
      name: l10n('Mass Transit'),
      toggleOnOff: p => {
        this.toggleOnOff(p);
      },
      on: this.featureOn && !this.needZoomIn,
    };
  }
  toggleOnOff(onOff) {
    if (this.needZoomIn) {
      this.flashMessage(l10n('Please zoom in'));
    }
    if (onOff == 'off') {
      this.featureOn = true;
    }
    // console.log('toggleOnOff',this.featureOn)
    if ((this.featureOn = !this.featureOn)) {
      this.setShowPanel({val: true});
      // eventEmitter.emit(SYSTEM.MAP_FEATURE_PROP,{
      //   cmd:'setShowMarker',
      //   showMarker:false,
      // });
      this.map.onRegionChangeComplete();
    } else {
      this.setState({
        selectedRoute: null,
        routes: [],
        mapRoutes: [],
        curRouteStops: [],
        selectedRouteTps: {route_tp: 'Sub', v: 'Sub'},
        mapStops: [],
        selectedStop: null,
        stopsCnt: 0,
      });
      eventEmitter.emit(Constants.MapPropStop, {stop: null});
      // eventEmitter.emit(SYSTEM.MAP_FEATURE_PROP,{
      //   cmd:'setShowMarker',
      //   showMarker:true,
      // });
    }
  }
  // after map bnds change
  // filterTransitRegions(event) {
  //   if(!this.featureOn){
  //     return;
  //   }
  //   this.overlapedRegions = [];
  //   let transitTps = [];
  //   var mapRouteTps = {};
  //   // console.log('xxxxxfilter transit regions',gTransitRegions)
  //   if (gTransitRegions && gTransitRegions.rgns && gTransitRegions.rgns.length > 0) {
  //     for (let region of gTransitRegions.rgns){
  //       if ( isOverlap(region.bbox[0],region.bbox[2],event.bbox[0],event.bbox[2]) &&
  //            isOverlap(region.bbox[1],region.bbox[3],event.bbox[1],event.bbox[3]))
  //       {
  //         // console.log('region overlap',region)
  //         this.overlapedRegions.push(region);
  //         // console.log('genTransitRegionTps',mapRouteTps)
  //         genTransitRegionTps(region,mapRouteTps);
  //       }
  //     }
  //     this.setState({zoom:event.zoom,bbox:event.bbox,regions:this.overlapedRegions,transitTps,mapRouteTps,camera:event.camera},() => {
  //       this.filterSelectedRegions();
  //     });
  //   }
  // }
  doSearchRoutesAndStops(event, opt = {}) {
    if (!event) {
      let alertCount = this.state.alertCount;
      this.setState({alertCount: alertCount - 1});
    }
    let notSelected = !this.state.selectedRouteTps; //.length == 0;
    if (notSelected) {
      // console.log('xxxxxxxnot selected return')
      return;
    }
    this.searchStops(event, opt);
    // console.log('=====before search routes')
    this.searchRoutes(event, opt);
  }
  // if selectedRegions has selected transit types
  filterSelectedRegions() {
    // let {regions,selectedRouteTps,selectedTransitTps,selectedRoute,alertCount} = this.state;
    // if (selectedRouteTps.length <= 0)
    //   return;
    // let selectedRegions = [];
    // for (r of regions) {
    //   if (selectedRouteTps.indexOf(r.rttp) > -1) {
    //     if (selectedTransitTps.length > 0 && selectedTransitTps.indexOf(r.tp) > -1) {
    //       selectedRegions.push(r);
    //       if(!selectedRoute && (r.feat.length > MAX_RENDER_ROUTES)){
    //         if(alertCount>0){
    //           this.flashMessage(l10n('Too many routes, please select one'));
    //         }
    //       }
    //     } else if (selectedTransitTps.length === 0) {
    //       // if no selectedTransitTps, show all selectedRouteTps regions
    //       selectedRegions.push(r);
    //     }
    //   }
    // }
    // this.setState({alertCount:alertCount-1})
    // this.filterSelectedTiles(selectedRegions);
    // this.searchStops();
    // this.searchRoutes();
  }
  // tile is divided region
  filterSelectedTiles(selectedRegions) {
    // bbox from screen compare tilex or tiley
    // if  >= then use Small pic and / or
    // if < then use Big pic's tiless (either lat or lng)
    //   for loop all tiles, isOverlay => add to state
    // const {bbox} = this.state;
    // const screenTileWidth = Math.abs(bbox[2] - bbox[0]);
    // const screenTileHeight = Math.abs(bbox[1] - bbox[3]);
    // let selectedTiles = []
    // for (var region of selectedRegions) {
    //   region.showOnMap = false;
    //   if ((screenTileWidth >= region.tileWidth) && (screenTileHeight >= region.tileHeight)) {
    //     region.showOnMap = true;
    //   } else {
    //     for (var [i,tile] of region.regionTiles.entries()) {
    //       var selectedTile = Object.assign({},tile)
    //       if ( isOverlap(selectedTile.bbox[0],selectedTile.bbox[2],bbox[0],bbox[2]) &&
    //           isOverlap(selectedTile.bbox[1],selectedTile.bbox[3],bbox[1],bbox[3])){
    //         selectedTile.idx = i;
    //         selectedTile.urlBase = region.urlBase;
    //         selectedTile.colorCnt = region.colorCnt;
    //         selectedTile.feat = region.feat;
    //         selectedTile.isTile = true;
    //         selectedTiles.push(selectedTile);
    //       }
    //     }
    //   }
    // }
    // this.setState({selectedRegions,selectedTiles});
  }
  regionChanged(event, map) {
    // console.log('xxxxxx',event.zoom)
    super.regionChanged(event, map);
    // this.filterTransitRegions(event);
    let route_tp = '',
      selectedRoute = this.state.selectedRoute;
    if (selectedRoute) {
      route_tp = selectedRoute.route_tp;
    }
    if (event) {
      this.setState({bbox: event.bbox, zoom: event.zoom, camera: event.camera});
    }
    // && isBusOrTram(route_tp)
    if (this.featureOn) {
      this.doSearchRoutesAndStops(event);
    }
  }
  // 仅关闭浮层，保持选中
  closeStopModal() {
    // this.map.toggleBackDrop('Off')
    this.setShowPanel({val: false});
    this.setState({showStopModal: false});
  }
  renderTransitTpMenu(selectedTransitTps, transitTps) {
    return;
    let btns = [];
    // console.log('xxxxx',transitTps)
    // ["TTC", "GO", "UP"]
    transitTps.forEach(t => {
      var btn = {
        k: t.nm,
        n: l10n(t.nm),
        act: () => {
          this.setRouteType(t);
        },
        borderStyle: {
          borderWidth: 1,
          borderColor: '#e8e8e8',
          backgroundColor: 'transparent',
          paddingTop: 3,
          paddingLeft: 8,
          paddingRight: 8,
          paddingBottom: 3,
        },
        style: {
          marginRight: 10,
        },
        width: 'auto',
        // checked:vTop.val,
      };
      if (selectedTransitTps.indexOf(t) > -1) {
        btn.color = '#e03131';
        btn.textStyle = {fontWeight: 'bold'};
        btn.checked = false; //true;
        btn.borderStyle.borderColor = '#81C77E';
      }
      btns.push(btn);
    });
    if (btns.length === 0) return;
    return <View style={styles.transitTpMenu}>{this.bottomButtons([], btns)}</View>;
  }
  // closeTransitTpMenu() {
  //   this.setState({selectedRouteTps:[],selectedTransitTps:[]},() => {
  //     // this.filterSelectedRegions();
  //     this.doSearchRoutesAndStops()
  //   });
  // }
  // new popup shows html detail
  openStopDetail(stop) {
    var self = this;
    var url = '/stop?id=' + stop._id;
    var cb = val => {
      if (val == ':cancel') {
        return;
      }
      if (/^redirect|^cmd-redirect:/.test(val)) {
        var url = val.split('redirect:')[1];
        return self.closePopup(url);
      }
    };
    // console.log(url)
    var opt = {
      hide: false,
      sel: '#callBackString',
      tp: 'pageContent',
      title: l10n('RealMaster'),
      url: serverDomainIns.getFullUrl(url),
    };
    eventEmitter.emit('app.message', {msg: JSON.stringify(opt), cb: cb});
  }
  // show route list
  goBackRoutes() {
    let state = {showStopModal: false}; //selectedStop:null
    this.setState(state);
    var self = this,
      {mapStops, routes} = this.state;
    this.setShowPanel({val: true}, () => {
      // scroll to index
      setTimeout(() => {
        let selectedStop = self.state.selectedStop;
        if (selectedStop && mapStops.length && self.stopListRef) {
          let stopsList = self.state.curRouteStops || [];
          let index = stopsList
            .map(o => {
              return o._id;
            })
            .indexOf(selectedStop._id);
          // console.log('xxxxxx',index,stopsList,selectedStop)
          index = Math.max(0, index);
          // let item = selectedStop
          self.stopListRef.scrollToIndex({animated: false, index: index, viewPosition: 0.4}); //, viewPosition:0.5
          // self.stopListRef.scrollToItem({animated:true,item:item,viewPosition:0.5})
        }
        let selectedRoute = this.state.selectedRoute;
        if (selectedRoute && routes.length && self.routesListRef) {
          // let stopsList = self.state.curRouteStops||[];
          let index = routes
            .map(o => {
              return o._id;
            })
            .indexOf(selectedRoute._id);
          // console.log('xxxxxx',index,routes,selectedRoute)
          index = Math.max(0, index);
          self.routesListRef.scrollToIndex({animated: false, index: index, viewPosition: 0.5}); //, viewPosition:0.5
        }
      }, 200);
    });
    eventEmitter.emit(Constants.MapPropStop, {stop: null});
    // eventEmitter.emit(SYSTEM.MAP_FEATURE_PROP,{
    //   cmd:'setShowMarker',
    //   showMarker:false,
    // });
  }
  renderStopModal(stop = {}) {
    if (!stop.routes) {
      stop.routes = [];
    }
    let selectedRoute = this.state.selectedRoute;
    // const stopRouteTps = stop.route_tp.map((tp)=>{
    //   return (<View style={styles.stopRoute} key={tp}>
    //     <Text style={styles.stopRouteTxt}>{tp}</Text>
    //   </View>)
    // });
    // const stopRoutes = stop.routes.map((r)=>{
    //   // const circleSize = 15;
    //   return this.renderRoute({item:r})
    //   return (
    //   <TouchableOpacity style={{marginTop:5}} onPress={()=>{this.setCurRoute(r)}}>
    //     <View style={{flexDirection:'row'}} key={r._id}>
    //       <View style={[{
    //         backgroundColor:(r.color||'white'),
    //         borderRadius:circleSize,
    //         width:circleSize,
    //         height:circleSize,
    //         marginRight:10},styles.center]}>
    //         <Text style={{color:'white'}}>{(getLineName(r)).substr(0,1)}</Text>
    //       </View>
    //       <Text style={{}}>{getLineName(r)}</Text>
    //       <Text> {r.tp||''}</Text>
    //     </View>
    //   </TouchableOpacity>)
    // });
    // console.log('stopRender: ',stop)
    // TODO: make curRoute in top, if more routes, append stylus route; [.prev, ...Transfer Lines...,.next]
    let routes = [];
    let self = this;
    for (let r of stop.routes) {
      if (selectedRoute && r._id == selectedRoute._id) {
        r.isCurrent = true;
        routes.unshift(r);
      } else {
        routes.push(r);
      }
    }
    let transferPlaceHolder = null;
    if (routes.length >= 2) {
      transferPlaceHolder = {nm: l10n('Transfer Lines'), transfer: 1};
      routes[0].prev = true;
      routes[1].next = true;
      routes.splice(1, 0, transferPlaceHolder);
    }
    // console.log('+++++renderStopModal',stop,routes)
    let footer = (
      <View style={{paddingLeft: 10, paddingRight: 10, paddingBottom: 7, paddingTop: 10}}>
        <Text style={{fontSize: 9, color: '#ddd'}}>
          All the content here is from internet public sources. No guarantee is given that the information is correct,
          complete, and/or up-to-date. Any reliance you place on the information here is strictly at your own risk.
          RealMaster is not liable for the information provided here.
        </Text>
      </View>
    );
    let stopType = 'Station';
    if (isBusOrTram(stop.route_tp)) {
      stopType = 'Stop';
    }
    let showGoback = selectedRoute && selectedRoute._id && !stop.extra;
    return (
      <View
        key={'stopModal'}
        style={styles.stopModal}>
        <View style={{flexDirection: 'row', height: 44, justifyContent: 'space-between'}}>
          <View style={{flexDirection: 'row', width: 'auto'}}>
            <View
              style={{width: 44}}
              key={'goBack'}>
              {showGoback && (
                <Icon
                  name='back'
                  size={21}
                  color='black'
                  hitSlop={{top: 10, bottom: 10, left: 0, right: 0}}
                  style={styles.navBarButton}
                  onPress={() => {
                    this.goBackRoutes();
                  }}
                />
              )}
            </View>
            {/* <View style={{
              // justifyContent:'center',
              // alignItems:'center',
              // flexDirection:'row',
              // alignContent:'center',
              // textAlign: 'center',
              paddingTop:14,
              paddingRight:10,
              height:44,
              // backgroundColor:'yellow'
              }}>
              <Text style={{fontSize:14}}>{l10n(stopType)}</Text>
            </View> */}
          </View>
          <View
            style={{}}
            key={'radius'}>
            {this.renderRadius()}
          </View>
          <View
            style={{
              width: 44,
              // alignItems:'flex-start',
              // justifyContent:'flex-start',
              // backgroundColor:'red',
              // alignContent:'flex-start'
            }}
            key={'closeStopModal'}>
            <Icon
              name='rmclose'
              size={22}
              color='#ddd'
              style={[styles.navBarButton, {marginTop: -4}]}
              onPress={() => {
                this.closeStopModal();
              }}
            />
          </View>
        </View>
        <View style={{height: 190}}>
          <Text style={{fontSize: 15, paddingTop: 6, paddingBottom: 7, fontWeight: 'bold', paddingLeft: 10}}>
            {stop.nm}
          </Text>
          {/* <View style={styles.stopModalRoutes}>{stopRoutes}</View> */}
          <FlatList
            data={routes}
            renderItem={({item}) => {
              return this.renderRoute({item}, {keepStop: 1});
            }}
            keyExtractor={item => item.fnm + item.nm + item._id + item.route_id}
            style={{backgroundColor: 'white', width: '100%'}}
            ListFooterComponent={footer}
            showsVerticalScrollIndicator={false}
            showsHorizontalScrollIndicator={false}
          />
        </View>
        {/* <TouchableOpacity style={{height:190}} onPress={()=>{this.openStopDetail(stop)}}> */}
        {/* <View style={styles.stopModalTps}>{stopRouteTps}</View> */}
        {/* </TouchableOpacity> */}
      </View>
    );
    return (
      <BottomPane
        key={'stop'}
        statusBar={{hidden: true}}
        title={`${stop.tp || ''} Stop`}
        cbClose={() => {
          this.closeStopModal();
        }}>
        <TouchableOpacity
          style={styles.stopModal}
          onPress={() => {
            this.openStopDetail(stop);
          }}>
          <Text>{stop.nm}</Text>
          <View style={styles.stopModalRoutes}>{stopRoutes}</View>
          <View style={styles.stopModalTps}>{stopRouteTps}</View>
        </TouchableOpacity>
      </BottomPane>
    );
  }
  renderModal() {
    const {mapRouteTps, selectedRouteTps, selectedStop, showStopModal, mapStops, propFeatureOn} = this.state;
    if (showStopModal && selectedStop && selectedStop._id) {
      return this.renderStopModal(selectedStop);
    }
    if (!this.featureOn) return null;
    if (mapStops && !propFeatureOn){
      let count = mapStops.length || 0;
      let base = -38;
      let leftTop = 5;
      if (Platform.OS != 'ios'){
        leftTop = 3
      }
      let left = (
        <View style={{
          marginTop:base+leftTop,
          zIndex:105,
          paddingLeft:10}}>
          <Text style={{fontSize:15,paddingBottom:8,marginTop:-3}}>{count}/{this.state.stopsCnt || count} {l10n('Stops')}</Text>
        </View>)
      return (
        <ListDragPan
          id={'mapTransitList'}
          bottom={0}
          PAN_SHOW_VALUE={110}
          LIST_HEIGH={170}
        >
          <View style={{width:'100%',
            flexDirection:'row',
            justifyContent:'space-between',
            paddingLeft:5,paddingRight:5}}
          >
            {left}
          </View>
          <LayerMenu map={this.map} style={{marginTop: 0}}/>
        </ListDragPan>
      )
    }
  }
  setProvOrRegion(prov = {}, index = 0) {
    // this.setState({prov}) selectedTransitTps:[],
    const {selectedRouteTps} = this.state;
    if (this.provSelectRef) {
      // let computedIndex = 0;
      // let length = transitProvsMapping.length;
      this.provSelectRef.scrollToIndex({animated: true, index: index, viewPosition: 0.5});
    }
    // console.log('+++++setProvOrRegion',prov)
    let fitBnds = !isBusOrTram(selectedRouteTps.route_tp);
    this.setState(
      {
        showAllRouteFontWeight: 'normal',
        prov: prov.k,
        // region:prov.region,
        // selectedRouteTps:null,
        routes: [],
        mapRoutes: [],
        mapStops: [],
        selectedStop: null,
        selectedRoute: null,
        curRouteStops: [],
        stopsCnt: 0,
      },
      () => {
        eventEmitter.emit(Constants.MapPropStop, {stop: null});
        // eventEmitter.emit(SYSTEM.MAP_FEATURE_PROP,{
        //   cmd:'setShowMarker',
        //   showMarker:false,
        // });
        if (!fitBnds && prov.loc) {
          // console.log('loc=,zoom=',prov.loc,prov.zoom)
          this.map.setCenterAndZoom(prov.loc, {zoom: prov.zoom});
        }
        if (fitBnds) {
          setTimeout(() => {
            let opt = {fitBnds: true};
            if (prov.isProv) {
              opt.prov = prov.k;
            } else {
              opt.region = prov.k;
            }
            this.doSearchRoutesAndStops(null, opt);
          }, 200);
        }
        // this.filterSelectedRegions();
        // this.doSearchRoutesAndStops()
      },
    );
  }
  renderRouteTypeSelect() {
    const {routeTypes, selectedRouteTps} = this.state;
    // console.log('xxxxx',transitProvsMapping)
    const renderRouteType = ({item, index}) => {
      let style = {borderBottomColor: 'white', borderBottomWidth: 3};
      if (selectedRouteTps && selectedRouteTps.route_tp == item.route_tp) {
        style = {borderBottomColor: '#e03131', borderBottomWidth: 3};
      }
      let calcdWidth = item.v.length * 8.8;
      return (
        <TouchableOpacity
          onPress={() => {
            this.setRouteType(item, index);
          }}
          style={{}}>
          <View style={{padding: 10}}>
            <View
              style={[
                {
                  flexDirection: 'column',
                  // paddingBottom:2,
                  backgroundColor: 'white',
                  alignItems: 'center',
                  // height:22,
                  // maxWidth:30,
                  // overflow:'visible',
                  width: 'auto',
                },
                // styles.center,
                // style
              ]}>
              <Text style={[styles.title, {fontSize: 15, fontWeight: 'bold'}]}>{item.v}</Text>
              <View style={[{width: '95%', marginTop: 2, minWidth: 20, maxWidth: 30}, style]}></View>
            </View>
          </View>
        </TouchableOpacity>
      );
    };
    // const allStopSelect = stopsList.length?(<View style={{flexDirection:'row',padding:10}}>
    //   <Text style={styles.title}>All</Text>
    // </View>):null;
    return (
      <FlatList
        horizontal
        showsVerticalScrollIndicator={false}
        showsHorizontalScrollIndicator={false}
        ListHeaderComponent={null}
        data={routeTypes}
        renderItem={renderRouteType}
        keyExtractor={item => item.v}
        style={{backgroundColor: 'white', width: '100%', flexDirection: 'row', height: 40, flexGrow: 0}}
      />
    );
  }
  renderModalMenu() {
    return;
    if (!this.featureOn) return;
    const {allRouteTpsByProv, mapRouteTps, prov, selectedRouteTps, selectedTransitTps, selectedStop} = this.state;
    // console.log('selectedTransitTps: ',selectedTransitTps)
    // console.log('selectedRouteTps: ',selectedRouteTps)
    // console.log('mapRouteTps',mapRouteTps)
    let allRouteTps = allRouteTpsByProv[prov];
    let transitTps = allRouteTps;
    // console.log('allRouteTps: ',allRouteTps,'allRouteTpsByProv: ',allRouteTpsByProv)
    // if (selectedRouteTps.length > 0) {
    //   let transitTps = [];
    //   for (var tp of selectedRouteTps) {
    //     if (allRouteTps && allRouteTps[tp]) {
    //       for (var tp2 of allRouteTps[tp]) {
    //         if (transitTps.indexOf(tp2) === -1) {
    //           transitTps.push(tp2);
    //         }
    //       }
    //     }
    //   }
    // console.log('transitTps: ',transitTps)
    return this.renderTransitTpMenu(selectedTransitTps, transitTps);
    // }
  }
  toggleItemInArray(array, item) {
    let itemIdx = array.indexOf(item);
    if (itemIdx > -1) {
      array.splice(itemIdx, 1);
    } else {
      array.push(item);
    }
  }
  // transitTp in TTC/Go/Mi
  // setRouteTp(tp) {
  //   return
  //   let {selectedTransitTps} = this.state;
  //   this.toggleItemInArray(selectedTransitTps,tp);
  //   this.setState({selectedTransitTps,routes:[], mapRoutes:[],selectedRoute:null,curRouteStops:[]},() => {
  //     // this.filterSelectedRegions();
  //     this.doSearchRoutesAndStops()
  //   });
  // }
  // routeTp is {nm,route_tp,agency}//Bus/Tram/...
  // NOTE: 非单选的话改这个
  setRouteType(rttp = {}) {
    // let {selectedRouteTps} = this.state;
    // this.toggleItemInArray(selectedRouteTps,rttp);
    // console.log('setRouteType',rttp)
    let isBusOrTramVal = isBusOrTram(rttp.route_tp);
    this.setState(
      {
        showAllRouteFontWeight: 'normal',
        selectedRouteTps: rttp,
        routes: [],
        mapStops: [],
        selectedStop: null,
        mapRoutes: [],
        selectedRoute: null,
        curRouteStops: [],
        stopsCnt: 0,
      },
      () => {
        // this.filterSelectedRegions();fitBnds:!isBusOrTramVal
        eventEmitter.emit(Constants.MapPropStop, {stop: null});
        this.doSearchRoutesAndStops(null, {});
      },
    );
  }
  getIconNameByType(route_tp = '') {
    if (Array.isArray(route_tp)) {
      route_tp = route_tp.join(',');
    }
    let icon = 'ferry';
    if (/Sub/i.test(route_tp)) {
      icon = 'metro';
    } else if (/Rail/i.test(route_tp)) {
      icon = 'rm-transit';
    } else if (/Bus/i.test(route_tp)) {
      icon = 'square-bus';
    } else if (/Tram/i.test(route_tp)) {
      icon = 'tram';
    } else if (/Ferry/i.test(route_tp)) {
      icon = 'ferry';
    } else {
      console.warn('unsupported route_tp', route_tp);
    }
    return icon;
  }
  isSameRouteTpIcon(stop = {}, route = {}) {
    // if(Array.isArray(stop.routes)){
    // }
    if (route.preferred) {
      return true;
    }
    let icon = this.getIconNameByType(route.route_tp);
    let iconStop = this.getIconNameByType(stop.route_tp);
    return icon == iconStop;
  }
  getRouteIconByType(item = {}) {
    // console.log('item is:',item)
    let route_tp = item.route_tp || 'Bus';
    let style = {
      // backgroundColor:item.color || '#e03131',
      // color:'white',
      // padding:3,
    };
    let icon = this.getIconNameByType(route_tp);
    return (
      <Icon
        name={icon}
        size={14}
        color='white'
        // style={{style}}
        // hitSlop={{top: 0, bottom: 0, left: 0, right: 0}}
      />
    );
  }
  renderRoute = ({item}, opt = {}) => {
    // return null;
    // console.log(item.fnm+item.nm+item._id+item.route_id)
    // console.log(item)
    if (item.transfer) {
      return (
        <View
          style={{
            marginLeft: 18,
            borderWidth: 0.5,
            borderStyle: 'dotted',
            borderRadius: 1,
            height: 70,
            width: 0,
            marginTop: -23,
            marginBottom: -5,
            borderColor: '#bdbdbd',
            // borderTopColor:'white',
            // borderBottom:0,
            // borderRightWidth:0,
          }}>
          <View
            style={{
              flexDirection: 'row',
              paddingLeft: 10,
              paddingTop: 15,
              paddingBottom: 10,
              marginTop: 13,
              marginLeft: -19,
              width: 100,
            }}>
            <Icon
              name='route'
              size={20}
              color='#adadad'
              style={[styles.center, {marginRight: 5, backgroundColor: 'white'}]}
              // hitSlop={{top: 0, bottom: 0, left: 0, right: 0}}
            />
            <Text style={{fontSize: 13, color: '#adadad'}}>{item.nm}</Text>
          </View>
        </View>
      );
    }
    let selectedRoute = this.state.selectedRoute;
    let style = {};
    // if(selectedRoute){
    //   console.log(selectedRoute.route_id,item.route_id,item.route_id == selectedRoute.route_id)
    // }
    if (selectedRoute && item._id == selectedRoute._id) {
      style = {backgroundColor: 'white'};
    }
    const circleSize = 16;
    // let prev = {}
    // console.log('+++++num',item.num,typeof(num))
    let calcMarginLeft = 7;
    if (!item.num) {
      calcMarginLeft = 7 + 19;
    }
    return (
      <TouchableOpacity
        onPress={() => {
          this.setCurRoute(item, opt);
        }}
        style={{
          // backgroundColor:'yellow',
          height: ROUTE_ITEM_HEIGHT,
          overflow: 'hidden',
        }}
        key={item.fnm + item.nm + item._id + item.route_id}>
        <View style={[{flexDirection: 'column', padding: 10}, style]}>
          <View style={{flexDirection: 'row'}}>
            <View
              style={[
                {
                  backgroundColor: this.getLineColor(item.color),
                  borderRadius: circleSize,
                  width: circleSize,
                  height: circleSize,
                  marginRight: 10,
                },
                styles.center,
              ]}>
              {/* <Text style={{color:'white'}}>{(getLineName(item)).substr(0,1)}</Text> */}
              {this.getRouteIconByType(item)}
            </View>
            <Text
              numberOfLines={1}
              style={[styles.title, {fontSize: 15}]}>
              {getLineName(item)}
            </Text>
          </View>
          <View style={[{flexDirection: 'row', alignItems: 'center', marginTop: 5}]}>
            {!!item.num && (
              <View style={[styles.lineNumber, styles.center]}>
                <Text style={{color: '#e03131', fontSize: 12}}>{item.num}</Text>
              </View>
            )}
            <View>
              <Text style={[styles.lineCompany, {marginLeft: calcMarginLeft}]}>{item.agency}</Text>
            </View>
          </View>
        </View>
      </TouchableOpacity>
    );
  };
  renderRouteAndStopSelect() {
    let stopsList = this.state.curRouteStops || [];
    // console.log('stops',stopsList)
    let {routes, selectedRouteTps, selectedRoute, selectedStop} = this.state;
    let height = 230;
    if (!(selectedRouteTps && selectedRouteTps.route_tp)) return <View style={{height}}></View>;
    // function getStopName(nm=[]){
    //   let ret = nm[0] || '';
    //   return ret.split('-')[0]
    // }
    let routeEmptyView = (
      <View style={{padding: 10}}>
        <Text style={{color: '#666', fontSize: 9, textAlign: 'center'}}>{l10n('No routes found in this area')}</Text>
      </View>
    );

    var renderRoutesList = () => {
      return (
        <FlatList
          ref={ref => {
            this.routesListRef = ref;
          }}
          data={routes}
          // initialNumToRender={5}
          showsVerticalScrollIndicator={false}
          showsHorizontalScrollIndicator={false}
          renderItem={this.renderRoute}
          ListEmptyComponent={routeEmptyView}
          keyExtractor={item => item._id}
          getItemLayout={(data, index) => ({length: ROUTE_ITEM_HEIGHT, offset: ROUTE_ITEM_HEIGHT * index, index})}
          style={{backgroundColor: '#f1f1f1', width: '50%'}}
        />
      );
    };
    const isSelectedStop = (item, selectedStop) => {
      if (!selectedStop) {
        return false;
      }
      let haveSelected = selectedStop;
      function isIncluded() {
        if (selectedStop.ids && Array.isArray(selectedStop.ids)) {
          return selectedStop.ids.includes(item.id);
        }
        return false;
      }
      let sameOrInclusive = item._id == selectedStop._id || isIncluded();
      return haveSelected && sameOrInclusive;
    };
    var renderStopsList = () => {
      const renderStopListItem = ({item}) => {
        if (selectedStop) {
          // console.log('+++++',item,selectedStop,item._id == selectedStop._id);//selectedStop
        }
        let style = {}; //color:'blue'
        if (isSelectedStop(item, selectedStop)) {
          style.fontWeight = 'bold'; //backgroundColor:'#f1f1f1'
        }
        return (
          <TouchableOpacity
            onPress={() => {
              this.clickStop(item, {isListStop: true});
            }}
            style={{
              height: STOP_ITEM_HEIGHT,
              overflow: 'hidden',
              // backgroundColor:'pink',
            }}>
            <View style={{flexDirection: 'row', padding: 10}}>
              <Text
                numberOfLines={1}
                style={style}>
                {getStopNameShort(item)}
              </Text>
            </View>
          </TouchableOpacity>
        );
      };
      const allStopSelect = stopsList.length ? (
        <TouchableOpacity
          onPress={() => {
            this.renderCurrentRoute();
          }}
          style={{}}>
          <View style={{flexDirection: 'row', padding: 10}}>
            <Text style={[styles.title, {fontWeight: this.state.showAllRouteFontWeight}]}>All</Text>
          </View>
        </TouchableOpacity>
      ) : null;

      return (
        <FlatList
          ref={ref => {
            this.stopListRef = ref;
          }}
          ListHeaderComponent={allStopSelect}
          data={stopsList}
          // initialNumToRender={5}
          showsVerticalScrollIndicator={false}
          showsHorizontalScrollIndicator={false}
          renderItem={renderStopListItem}
          keyExtractor={item => item._id}
          // NOTE: dont calc, although may increase performance, but item height is dynamic;
          getItemLayout={(data, index) => ({length: STOP_ITEM_HEIGHT, offset: STOP_ITEM_HEIGHT * index, index})}
          onScrollToIndexFailed={({index, averageItemLength}) => {
            // console.log('onScrollToIndexFailed, index=',index,averageItemLength)
          }}
          style={{backgroundColor: 'white', width: '50%'}}
        />
      );
    };
    return (
      <View style={{flexDirection: 'row', alignItems: 'stretch', height}}>
        {renderRoutesList()}
        {renderStopsList()}
      </View>
    );
  }
  renderFeaturePanel() {
    if (!this.featureOn) return;
    if (!this.state.showPanel) return;
    if (this.state.showStopModal) return;
    // NOTE: dont do this, (setState) will cause non-stop render
    let self = this;
    let {allRouteTpsByProv, prov, selectedRouteTps} = this.state;
    let btns = [],
      route_tp;
    // return this.bottomButtons([],btns);
    if (selectedRouteTps && selectedRouteTps.route_tp) {
      route_tp = selectedRouteTps.route_tp;
    }
    let allRouteTps = allRouteTpsByProv[route_tp] || [];
    // console.log('allRouteTps=',allRouteTps,'\tallRouteTpsByProv=',allRouteTpsByProv)
    // Object.keys(allRouteTps)
    allRouteTps.forEach((t, index) => {
      let nmField = 'v';
      // if(getAppLang()=='en'){
      //   nmField = 'nm_en'
      // }
      // if(!t.region){
      //   t.region = region
      // }
      var btn = {
        k: t.k,
        n: t[nmField] || t.k,
        act: () => {
          self.setProvOrRegion(t, index);
        },
        borderStyle: {
          borderWidth: 1,
          borderColor: '#e8e8e8',
          backgroundColor: 'transparent',
          paddingTop: 3,
          paddingLeft: 8,
          paddingRight: 8,
          paddingBottom: 3,
        },
        style: {
          marginRight: 10,
        },
        width: 'auto',
        color: 'black',
      };
      if (prov && prov == t.k) {
        //selectedRouteTps && selectedRouteTps.route_tp == t.route_tp
        (btn.textStyle = {
          fontWeight: 'bold',
        }),
          (btn.color = '#e03131');
        btn.checked = false; //true;
        btn.borderStyle.borderColor = '#81C77E';
      }
      btns.push(btn);
    });
    function renderProvRegionSelect(i) {
      return i.item;
    }
    return (
      <View
        key={'featurePanel'}
        style={styles.featurePanel}>
        <View style={{flexDirection: 'row', marginRight: 40}}>{this.renderRouteTypeSelect()}</View>
        <View style={{alignItems: 'flex-end', position: 'absolute', right: 0, top: -1}}>
          <Icon
            name='rmclose'
            size={21}
            color='#ddd'
            hitSlop={{top: 10, bottom: 10, left: 0, right: 0}}
            style={styles.navBarButton}
            onPress={() => {
              this.closePanelAndSearch();
            }}
          />
        </View>
        <View
          style={styles.bottomBar}
          key={'mapBottomBar'}>
          {/* {this.bottomButtons([],btns)} */}
          <FlatList
            ref={ref => {
              this.provSelectRef = ref;
            }}
            horizontal
            ListHeaderComponent={null}
            showsVerticalScrollIndicator={false}
            showsHorizontalScrollIndicator={false}
            style={{backgroundColor: 'white', width: '100%', flexDirection: 'row', height: 27, marginTop: 0}}
            data={this.bottomButtons([], btns)}
            renderItem={renderProvRegionSelect}
            onScrollToIndexFailed={({index, averageItemLength}) => {
              console.error('onScrollToIndexFailed, index=', index, averageItemLength);
            }}
            keyExtractor={item => {
              return 'list' + item.key;
            }}
          />
        </View>
        {/* {this.renderModalMenu()} */}
        {this.renderRouteAndStopSelect()}
        {/* {this.renderFeaturePanelClose(()=>{this.closePanelAndSearch()},()=>{this.setState({showPanel:false})})} */}
        <RMBottomBar />
      </View>
    );
  }
  setShowStopModal = ({val, name}) => {
    // if(name && name!==this.name){
    //   return;
    // }
    if (val === undefined) {
      val = false;
    }
    this.setState({showStopModal: val});
  };
  // name is feature name
  setShowPanel = ({val, name}, cb) => {
    if (name && name !== this.name) {
      return;
    }
    if (val === undefined) {
      val = false;
    }
    let state = {showPanel: val};
    if (val == false && this.state.showStopModal) {
      state.showStopModal = false;
    }
    // console.log('setShowpanel in mapTransit, val:',val,' nm==this.nm?',name==this.name)
    let mapPadding = {
      top: 0,
      right: 0,
      left: 0,
      bottom: 0,
    };
    if (state.showPanel) {
      mapPadding.bottom = 250;
    }
    // NOTE: on adnroid padding works but map size not reduced
    this.map.setMapPadding({mapPadding});
    // console.log('mapPadding = ',mapPadding)
    // eventEmitter.emit('map.setMapPadding',{mapPadding})
    this.setState(state, () => {
      if (cb) cb();
    });
  };
  closePanelAndSearch() {
    // eventEmitter.emit("map.regionChange",{});
    let tmpState = {tracksViewChanges: true};
    this.setShowPanel({val: false});
    this.setState(tmpState, () => {
      // this.map.onRegionChangeComplete();
      // this.map.toggleBackDrop('Off');
      this.trackOff();
    });
  }
  renderBottomBar() {
    return null;
    if (!this.featureOn) return;
    const self = this;
    const {mapRouteTps, selectedRouteTps} = this.state;
    let btns = [];
    Object.keys(mapRouteTps).forEach(t => {
      var btn = {
        k: t,
        n: l10n(t),
        act: () => {
          self.setRouteType(t);
        },
      };
      if (selectedRouteTps.indexOf(t) > -1) {
        btn.style = {fontWeight: 'bold'};
        btn.color = '#e03131';
      }
      btns.push(btn);
    });
    return this.bottomButtons([], btns);
  }
  getStopCoordinate(stop = {}) {
    return {longitude: stop.lng || stop.geometry.coordinate[1], latitude: stop.lat || stop.geometry.coordinate[0]};
  }
  adjustRadius(isInc = false) {
    let idx = -1;
    if (isInc) {
      idx = 1;
    }
    let radiusList = this.state.radiusList;
    let currentStopRadius = this.state.currentStopRadius;
    let curIdx = radiusList.indexOf(currentStopRadius);
    curIdx += idx;
    currentStopRadius = radiusList[curIdx] || currentStopRadius;
    // console.log('currentStopRadius',currentStopRadius)
    let self = this;
    this.setState({currentStopRadius}, () => {
      // console.log('xxxxx',this.stopCircle)
      // NOTE: has 2 bug
      // 1: https://github.com/react-native-maps/react-native-maps/issues/2698
      // 2: without timeout wont work
      this.setStopCircleNativeProps();
      this.searchPropsWithinCircle();
    });
  }
  setStopCircleNativeProps() {
    var self = this;
    setTimeout(() => {
      if (self.stopCircleRef) {
        // console.log('======setStopCircle NativeProps')
        self.stopCircleRef.setNativeProps({
          fillColor: STOP_FILL_COLOR,
          strokeColor: STOP_STROKE_COLOR,
        });
      }
    }, 100);
  }
  // render currentStopRadius
  getStopRadius() {
    // return {currentStopRadius/1000}km
    let currentStopRadius = this.state.currentStopRadius;
    let dis = distanceBySmallestUnit(currentStopRadius);
    // console.log('dis: ',dis)
    // this.distanceUnit = dis.unit;
    // this.distanceUnitIndex = dis.index;
    return round(dis.distance, 2) + ' ' + l10n(dis.unit, 'distance');
  }
  renderRadius() {
    let {selectedStop} = this.state;
    if (selectedStop && selectedStop._id) {
      return (
        <View
          style={[styles.overlayRadius, styles.center]}
          key={'transit-overlay'}>
          <View key={'radius-left'}>
            <TouchableOpacity
              onPress={() => {
                this.adjustRadius(false);
              }}
              style={{paddingLeft: 9, paddingRight: 8, paddingTop: 7, paddingBottom: 8}}>
              <Icon
                name='rmminus'
                size={13}
                color='#0a0a09'
                style={[styles.center]}
                // hitSlop={{top: 0, bottom: 0, left: 0, right: 0}}
              />
            </TouchableOpacity>
          </View>
          <View
            key={'radius-center'}
            style={{
              marginLeft: 5,
              marginRight: 5,
              borderLeftColor: '#ddd',
              borderLeftWidth: 1,
              borderRightColor: '#ddd',
              height: 13,
              marginTop: 0,
              flexDirection: 'row',
              height: 18,
              minWidth: 70,
              alignContent: 'center',
              justifyContent: 'center',
              backgroundColor: 'white',
              marginLeft: -1,
              marginRight: -1,
              borderRightWidth: 1,
            }}>
            <Text style={{paddingLeft: 5, paddingRight: 5, color: 'black', fontSize: 14, textAlign: 'center'}}>
              {this.getStopRadius()}
              {/* {l10n('Scope')}: */}
            </Text>
          </View>
          <View key={'radius-right'}>
            <TouchableOpacity
              onPress={() => {
                this.adjustRadius(true);
              }}
              style={{paddingRight: 9, paddingLeft: 9, paddingTop: 8, paddingBottom: 8}}>
              <Icon
                name='rmplus'
                size={13}
                color='black'
                fontWeight={'normal'}
                style={[styles.center]}
                // hitSlop={{top: 0, bottom: 0, left: 0, right: 0}}
              />
            </TouchableOpacity>
          </View>
        </View>
      );
    }
    return null;
  }
  renderOnMap() {
    if (!this.featureOn) return;
    var views = [];
    const {zoom, mapStops, selectedStop, curStopCircleLabelTimer, currentStopRadius} = this.state;
    views = views.concat(this.renderMapRoutes());
    for (const stop of mapStops) {
      views.push(this.renderStop(stop));
    }
    // console.log('++++++++++++++++selectedStop',selectedStop,zoom)
    if (selectedStop && zoom > 12) {
      // for (var r of [500,1000,2000]) {
      const loc = this.getStopCoordinate(selectedStop);
      // let stopCircle = this.renderStopCircle(loc,currentStopRadius);
      views.push(this.renderStopCircle(loc, currentStopRadius));
      // if (curStopCircleLabelTimer > 0) {
      //   const stopCircleLabel = this.renderStopCircleLabel(loc,r);
      //   views.push(stopCircleLabel);
      // }
      // }
    }
    // for (let region of (selectedRegions || regions)) {
    //   if (region.showOnMap) {
    //     views.push(this.renderRegion(region));
    //   }
    // }
    // for (var tile of selectedTiles) {
    //   views.push(this.renderRegion(tile,tile.idx));
    // }
    return views;
  }
  // NOTE: not used
  // renderStopCircleLabel(loc,r) {
  //   const labelLoc = getPointByDistance(loc, r,this.state.camera.heading+180);
  //   return (
  //     <Marker
  //       key={loc.latitude+''+loc.longitude+'cl'+r.toString()}
  //       coordinate={labelLoc}
  //       anchor={{x:0.5,y:0.5}}
  //     >
  //       <View style={styles.stopCircleLabel}><Text style={styles.stopCircleLabelTxt}>{r/1000} KM</Text></View>
  //     </Marker>
  //   )
  // }
  renderStopCircle(loc, r) {
    let self = this;
    return (
      <Circle
        ref={ref => {
          self.stopCircleRef = ref;
        }}
        key={loc.latitude + ',' + loc.longitude + 'c' + r.toString()}
        center={loc}
        radius={r}
        lineJoin={'round'}
        strokeWidth={2}
        zIndex={15}
        fillColor={STOP_FILL_COLOR}
        strokeColor={STOP_STROKE_COLOR}
      />
    );
  }
  searchPropsWithinCircle(stop) {
    return;
    // if(!stop){
    //   stop = this.state.selectedStop || {};
    // }
    // eventEmitter.emit(SYSTEM.MAP_FEATURE_PROP_STOP,{
    //   stop:stop,
    //   lat:stop.lat,
    //   lng:stop.lng,
    //   radius:this.state.currentStopRadius
    // });
    // eventEmitter.emit(SYSTEM.MAP_FEATURE_PROP,{
    //   cmd:'search',
    //   showMarker:true,
    //   lat:stop.lat,
    //   lng:stop.lng,
    //   radius:this.state.currentStopRadius
    // });
  }
  clickStop(origStop, sOpt = {}) {
    // _id: "5e83a16f89e21d30ce86a910"
    // lat: 43.652738
    // lng: -79.379616
    // nm: (2) ["QUEEN STATION - SOUTHBOUND PLATFORM", "QUEEN STATION - NORTHBOUND PLATFORM"]
    // tp: "TTC"
    // route_tp: ["Sub"]
    var {selectedStop, bbox, selectedRouteTps, showStopModal, selectedRoute} = this.state;
    eventEmitter.emit(Constants.MapPropPreview, {val: false});
    if (selectedStop && selectedStop._id === origStop._id) {
      // selectedStop = null;
      // showStopModal = false;
      this.setState({showStopModal: true}, () => {
        this.setStopCircleNativeProps();
        this.trackOff();
        // this.searchPropsWithinCircle(origStop)
      });
      return;
    } else {
      selectedStop = origStop;
      showStopModal = true;
    }
    // var stopInMap = this.checkCurStopInMap(selectedStop,bbox,stops);
    // let hasBackDrop = this.map.getBackDropVal()
    let doSetStop = () => {
      // curStopCircleLabelTimer:5,
      this.setState({showAllRouteFontWeight: 'normal', showStopModal, selectedStop, tracksViewChanges: true}, () => {
        // console.log('++++++clickStop:',origStop)
        // user selectedRoute stops hasBackDrop
        if (sOpt.isListStop) {
          if (!origStop.lat) {
            return console.error('Stop no lat!');
          }
          // map animate to location
          var opt = {lat: origStop.lat, lng: origStop.lng, showMarker: 0};
          this.map.setCenterAndZoom(opt, {zoom: 17});
        }
        this.setStopCircleNativeProps();
        this.trackOff();
        // this.searchPropsWithinCircle(origStop)

        // if (this.stopCircleLabelTimer) {
        //   clearTimeout(this.stopCircleLabelTimer);
        //   delete this.stopCircleLabelTimer;
        // }
        //5 second to hide circle labels
        // const {curStopCircleLabelTimer} = this.state;
        // this.stopCircleLabelTimer = setTimeout(() => {
        //   this.setState({curStopCircleLabelTimer:0});
        // }, curStopCircleLabelTimer * 1000);
      });
    };
    if (!selectedStop) {
      return doSetStop();
    }
    // console.log('selectedStop+++++request getStopInfo',origStop,origStop._id)
    requestStdFn('getStopInfo', {id: origStop._id})
      .then(ret => {
        // console.log('-----getStopInfo',ret)
        if (!(ret.ok && ret.stop)) {
          return doSetStop();
        }
        if (!ret.stop.color) {
          ret.stop.color = selectedStop.color;
        }
        selectedStop = ret.stop;
        // if(origStop && origStop.extra){
        //   selectedStop.extra = true
        // }
        let routes = ret.stop.routes || [];
        if (routes && routes.length) {
          if (routes[0].coords14) {
            for (var r of routes) {
              parseCoordsToLatLng(r);
            }
          }
          // Select route from stop
          let found = false;
          routes.sort((a, b) => {
            if (a.preferred) return -1;
            return 0;
          });
          // console.log(')))))sorted',routes.forEach(element => console.log(element._id)))
          for (var r of routes) {
            // console.log(r._id,r.route_tp)
            // if route_tp matches icon tp
            // r.route_tp === selectedRouteTps.route_tp
            if (this.isSameRouteTpIcon(origStop, r)) {
              this.setCurRoute(r, {keepStop: true});
              // console.log('*****setCurRoute:',r,origStop)
              found = true;
              break;
            }
          }
          if (!found) {
            this.setCurRoute(routes[0], {keepStop: true});
          }
        }
        // console.log('-------clicked stops',ret.stop)
        doSetStop();
      })
      .catch(err => {
        console.error(err);
        return;
      });
  }
  renderStop(stop) {
    let loc = this.getStopCoordinate(stop);
    let {selectedStop, selectedRoute} = this.state;
    let isSelected = false;
    let zIndex = 10;
    if (stop && selectedStop && selectedStop._id == stop._id) {
      isSelected = true;
      zIndex = 11;
    }
    if (!selectedRoute) {
      selectedRoute = {};
    }
    let route_tp = stop.route_tp;
    if (stop.fromRoute) {
      route_tp = selectedRoute.route_tp;
      zIndex = 11;
    }
    // console.log('_______renderStop',stop,selectedRoute)
    let icon = this.getIconNameByType(route_tp);
    let color = stop.color || '';
    // getStopNameShort(stop,selectedRoute)
    // if(stop._id == '60f57fe1d6c557bab2bdcebb'){
    //   console.log('_______renderStop60f57fe1d6c557bab2bdcebb',stop.color,color)
    // }
    return (
      <Marker
        // anchor={{x:0.5,y:0.5}}
        anchor={{x: 0.5, y: 1}}
        stopPropagation={true}
        key={stop._id}
        coordinate={loc}
        style={{zIndex}}
        tracksViewChanges={this.state.tracksViewChanges}
        onPress={() => this.clickStop(stop)}>
        <StopMarker
          _id={stop._id}
          icon={icon}
          color={color}
          isSelected={isSelected}
          name={''}
        />
      </Marker>
    );
    // <Marker
    //   anchor={{x:0.5,y:0.5}}
    //   stopPropagation={true}
    //   key={stop._id}
    //   coordinate={loc}
    //   tracksViewChanges={false}
    //   onPress={()=>this.clickStop(stop)}
    //   >
    //     <View style={[styles.stopMarker,selectedStyle]}>
    //       <Text style={{color:'white'}}>
    //         {this.getStopNameShort(stop)}
    //       </Text>
    //     </View>
    // </Marker>
  }
  // buildRegionImg(region,num=null) {
  //   // OLD: /img/map-layer/
  //   // NEW: /img/transit/regions/
  //   // crash: /img/transit/regions/Canada_Ontario_Toronto_TTC_Sub_L-4-15.png
  //   // crash: /img/transit/regions/Canada_Alberta_Edmonton_Bus_L-0-4.png
  //   // return getFullURI('/img/transit/regions/Canada_Alberta_Calgary_Bus_L-0-13.png')
  //   // NOTE: rerun img alpha&optinum worked, dont know why
  //   // drawpoly 1week,map,stop connect, limit number
  //   return getFullURI('/img/transit/regions/Canada_Ontario_Toronto_TTC_Sub_L-4-5.png');
  //   if (num !== null) {
  //     console.log(getFullURI(`/img/transit/regions/${region.urlBase}_L-${region.colorCnt}-${num}.png`))
  //     return getFullURI(`/img/transit/regions/${region.urlBase}_L-${region.colorCnt}-${num}.png`);
  //   }
  //   console.log('+++',getFullURI(`/img/transit/regions/${region.urlBase}_S-${region.colorCnt}.png`))
  //   return getFullURI(`/img/transit/regions/${region.urlBase}_S-${region.colorCnt}.png`);
  // }
  getLineColor(color = '') {
    if (this.isWhite(color)) {
      return 'black';
    }
    return color || DEFAULT_LINE_COLOR;
  }
  isWhite(color = '') {
    return /^(?:white|#fff(?:fff)?|rgba?\(\s*255\s*,\s*255\s*,\s*255\s*(?:,\s*1\s*)?\))$/i.test(color);
  }
  renderMapRoutes() {
    var views = [];
    let {selectedRoute, zoom, routes, mapRoutes} = this.state;
    var field = 'coords14';
    if (zoom >= 17) {
      field = 'coords17';
    }
    // let length = 0;
    // if(Array.isArray(mapRoutes)){
    //   length = mapRoutes.length;
    // }
    // length > MAX_RENDER_ROUTES &&
    if (selectedRoute) {
      mapRoutes = [selectedRoute];
    }
    // console.log('mapRoutes is+++++',mapRoutes)
    var self = this;
    mapRoutes.forEach(f => {
      if (Array.isArray(f[field])) {
        f[field].forEach((r, idx) => {
          // console.log('less than r is: ',r.length,idx)
          views.push(
            <Polyline
              key={'mapPoly_' + field + '_' + idx + '_' + f._id}
              coordinates={r}
              strokeWidth={2}
              strokeColor={self.getLineColor(f.color)}
              style={{zIndex: 20}}
              zIndex={20}
            />,
          );
        });
      }
    });
    return views;
  }
  // renderRegion(region,num=null){
  //   // dup region not rendered, showOnMap = false
  //   let {selectedRoute,zoom} = this.state;
  //   const swLng = region.bbox[0]
  //   const swLat = region.bbox[1]
  //   const neLng = region.bbox[2]
  //   const neLat = region.bbox[3]
  //   var bounds = [
  //     [neLat,swLng],
  //     [swLat,neLng],
  //   ];
  //   var views = [];
  //   // var img = this.buildRegionImg(region,num);
  //   // console.log('renderRegion img:',img);
  //   // coords14/coords17, if zoom>=17, use 17; <=14 use 14
  //   var field = 'coords14';
  //   if (zoom >= 17) {
  //     field = 'coords17';
  //   }
  //   // return null;
  //   if(region.feat){
  //     if(!selectedRoute && (region.feat.length > MAX_RENDER_ROUTES)){
  //       let alertCount = this.state.alertCount;
  //       if(alertCount>0){
  //         return this.flashMessage(l10n('Too many routes, please select one'));
  //       }
  //     }
  //     if(region.feat.length < MAX_RENDER_ROUTES){
  //       region.feat.forEach((f)=>{
  //         if(Array.isArray(f[field])){
  //           f[field].forEach((r)=>{
  //             views.push(
  //               <Polyline
  //                 coordinates = {r}
  //                 strokeWidth = {2}
  //                 strokeColor = {f.color || '#e03131'}
  //               />
  //             )
  //           })
  //         }
  //       })
  //     }
  //     if(region.feat.length > MAX_RENDER_ROUTES && selectedRoute){
  //       let route = region.feat.find((f)=>{
  //         return f.route_id == selectedRoute.route_id
  //       }) || null;
  //       // NOTE:selectedRoute might be on map, not found in list
  //       // NOTE:found the route within region.feat
  //       if(route && Array.isArray(route[field])){
  //         route[field].forEach((r)=>{
  //           views.push(
  //             <Polyline
  //               coordinates = {r}
  //               strokeWidth = {2}
  //               strokeColor = {route.color || '#e03131'}
  //             />
  //           )
  //         })
  //       }
  //     }
  //   }
  //   return views;
  //   return (
  //     <Overlay
  //       // key={region.bbox.join(',')} {uri:img}
  //       bounds={bounds}
  //       image={{uri:img}}
  //       opacity={0.5}
  //     />
  //   );
  // }
  unmount() {
    // console.log('++++unmount')
    // gAllRouteTpsByProv = {};
    // gRouteTypes = ret.routeTypes;
    eventEmitter.removeListener('map.props.fetureOn',this.recordPropFeatureOn);
    super.unmount();
    // eventEmitter.removeListener(SYSTEM.EVENT_CLEAR_FEATURE_PANEL,this.setShowPanel);
    eventEmitter.removeListener(Constants.MapTransitStopModal, this.setShowStopModal);
  }
}

const styles = StyleSheet.create({
  stopMarker: {
    height: 26,
    width: 'auto',
    padding: 3,
    borderRadius: 18,
    borderWidth: 2.5,
    borderColor: 'white',
  },
  stopCircleLabel: {
    backgroundColor: '#e03131',
    borderColor: '#fff',
    borderWidth: 1,
    paddingVertical: 2,
    paddingHorizontal: 3,
    borderRadius: 5,
  },
  stopCircleLabelTxt: {
    color: '#fff',
  },
  transitTpMenu: {
    // padding: 15,
    // position: 'absolute',
    // bottom: 50,
    backgroundColor: 'white',
    flexDirection: 'row',
    marginLeft: 10,
    marginBottom: 10,
    // left: 0,
    // right: 0,
    // zIndex: 20,
    // borderBottomColor:'#ccc',
    // borderBottomWidth: 1,
  },
  stopModal: {
    position: 'absolute',
    bottom: 0,
    // top:74,
    backgroundColor: 'white',
    left: 0,
    right: 0,
    zIndex: 19,
    height: 280,
    // padding: 15,
    // paddingBottom: 30
  },
  stopModalTps: {
    marginTop: 15,
    flexDirection: 'row',
  },
  stopModalRoutes: {
    marginTop: 15,
    flexDirection: 'column',
  },
  stopRoute: {
    backgroundColor: '#fff',
    borderColor: '#eee',
    borderWidth: 1,
    borderStyle: 'solid',
    padding: 3,
    marginRight: 5,
  },
  stopRouteTxt: {
    // color: '#fff'
  },
  featurePanel: {
    alignItems: 'flex-start',
    position: 'absolute',
    bottom: 0,
    // top:74,
    backgroundColor: 'white',
    left: 0,
    right: 0,
    zIndex: 20,
    height: 300,
  },
  bottomBar: {
    flexDirection: 'row',
    // marginVertical: 20,
    zIndex: 15,
    height: 32,
    // flex:1,
    // backgroundColor: 'blue',
    // paddingTop:10,
    // marginTop:-10,
    paddingLeft: 10,
    // marginBottom:10,
  },
  center: {
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
  },
  overlayRadius: {
    flexDirection: 'row',
    // position: 'absolute',
    borderRadius: 3,
    borderColor: '#ddd',
    marginTop: 5,
    borderWidth: 1,
    // top: 0,
    // bottom:330,
    // left: 0,
    // right: 0,
    // width: 120,
    height: 32,
    zIndex: 5,
    // overflow: 'scroll',
    backgroundColor: 'white',
    // flex: 1,
    // justifyContent: 'left',
    // alignItems: 'center',
    // flexDirection: 'row',
  },
  lineCompany: {
    // marginLeft:7,
    backgroundColor: 'white',
    color: 'black',
    borderWidth: 0.5,
    borderColor: '#b9b9b9', //'#e8e8e8',
    paddingLeft: 4,
    paddingRight: 4,
    // paddingTop:2,
    // paddingBottom:2,
    fontSize: 12,
    height: 16,
    borderRadius: 2,
  },
  lineNumber: {
    backgroundColor: '#ffdcd7',
    paddingLeft: 2,
    paddingRight: 2,
    // paddingTop:2,
    // paddingBottom:2,
    minWidth: 16,
    height: 16,
    // marginTop:3,
    marginLeft: 24,
    borderRadius: 1,
    // alignSelf:'center',
  },
  navBarButton: {
    // flexDirection: 'row',
    // justifyContent: 'center',
    // alignItems: 'center',
    // alignSelf:'center',
    //margin: 10,
    // width: 44,
    height: 44,
    // padding: 10,
    paddingLeft: 10,
    paddingRight: 10,
    paddingTop: 12,
    // backgroundColor:'blue',
    // paddingBottom:15,
    fontSize: 20,
  },
});

export default MapTransit;
