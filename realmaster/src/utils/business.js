/**
 * 业务相关工具类
 */
import DeviceInfo from 'react-native-device-info';
import PlayServices from 'react-native-play-services';
import {Platform} from 'react-native';
import appConfigIns from '../config/appConfig';
import storageIns from '../utils/storage';
import Constants from '../config/constants';
import {urlParamToObject} from './common';
import serverDomainIns from './serverDomain';


const mapUtil = (() => {
  const stigmaRegex = /map\/webMap.*stigma/;
  const transitRegex = /map\/transit/;
  const coopRegex = /map\/coop/;

  let GoogleMapProviderAvailable = true;
  let GoogleMapNeedUpdate = false;

  const isMapSearchMode = url => {
    // China ip cant load google native map
    // console.log('######appConfig',getAppConfig('useWebMap'))
    if (appConfigIns.getAppConfig('useWebMap') === true) {
      return false;
    }
    // console.log(url, stigReg.test(url))
    if (/\/1\.5\/mapSearch\/advFilter/.test(url)) {
      return false;
    }
    if (/mapSearch.*useWebMap/.test(url)) {
      return false;
    }
    if (/mapSearch.*mode=list/.test(url)) {
      return false;
    }
    if (/grp=/.test(url)) {
      return false;
    }
    if (stigmaRegex.test(url)) {
      return true;
    }
    if (transitRegex.test(url)) {
      return true;
    }
    if (coopRegex.test(url)) {
      return true;
    }
    // vars.src == 'nativeMap' || vars.src == 'nativeAutocomplete'; already in map or autocomplete
    if (/src=native/.test(url)) {
      return false;
    }
    // if (/schoolSearch\?gps=1/.test(url)) {
    //   return true
    // }
    // console.log('++++++url',url)
    // NOTE: support native measure
    // if (/1\.5\/tools\/measure/.test(url)) {
    //   return true;
    // }
    return /\/1\.5\/mapSearch/.test(url);
  };

  const extractMapOptFromUrl = url => {
    // var opt = {
    //   tp:'mapSearch',
    // }
    var opt = urlParamToObject(url);
    opt.tp = 'mapSearch';
    // TODO: school only support incomplete
    if (/schoolSearch/.test(url)) {
      // opt.pubSchOnly = true
    }
    if (stigmaRegex.test(url)) {
      opt.stig = true;
      // console.log('++++++',this.data.jsonTp)
      // if (this.data.jsonTp && (this.data.jsonTp=='popup' || this.data.jsonTp=='pageContent')) {
      //   opt.closePopup = true
      // }
    }
    if (transitRegex.test(url)) {
      opt.transit = true;
    }
    if (coopRegex.test(url)) {
      opt.coop = true;
    }
    // if (/tools\/measure/.test(url)) {
    //   opt.measure = true
    //   // console.log('++++++',this.data.jsonTp)
    //   // if (this.data.jsonTp && (this.data.jsonTp=='popup' || this.data.jsonTp=='pageContent')) {
    //   //   opt.closePopup = true
    //   // }
    // }
    // console.log('+++++opt',url,opt)
    return opt;
  };

  const playServiceCheck = async () => {
    if (Platform.OS === 'android') {
      const result = await PlayServices.checkPlayServicesStatus();
      //debug("Google:" + result);
      
      if (result === PlayServices.GooglePlayServicesStatus.GMS_NEED_UPDATE) {
        // useBM()
        //GoogleAPIAvailability.promptGooglePlayUpdate(false);
        GoogleMapProviderAvailable = false;
        GoogleMapNeedUpdate = true;
      } else if (result === PlayServices.GooglePlayServicesStatus.AVAILABLE) {
        // useBM()
      } else if (result === PlayServices.GooglePlayServicesStatus.GMS_DISABLED) {
        // useBM()
        console.log('No Google Service ');
        GoogleMapProviderAvailable = false;
      } else if (result === PlayServices.GooglePlayServicesStatus.INVALID) {
        // useBM()
        console.log('No Google Service ');
        GoogleMapProviderAvailable = false;
      } else {
        // useBM()
        logError('Unknown Google Play Availablity Result:' + result);
        GoogleMapProviderAvailable = false;
      }

      console.log('checkPlayServicesStatus Google Service: ',GoogleMapProviderAvailable);
    }
  };

  const getGoogleMapProviderNeedUpdate = () => {
    return GoogleMapNeedUpdate;
  };
  const getGoogleMapProviderAvailable = () => {
    return GoogleMapProviderAvailable;
  };
  const goToMarket = async () => {
    return await PlayServices.goToMarket();
  };
  const goToSetting = async () => {
    return await PlayServices.goToSetting();
  };

  playServiceCheck()

  return {
    isMapSearchMode,
    extractMapOptFromUrl,
    getGoogleMapProviderNeedUpdate,
    getGoogleMapProviderAvailable,
    goToMarket,
    goToSetting,
  };
})();

const getUserAgent = async () => {
  let deviceUserAgent = '';
  const deviceUniqueId = await DeviceInfo.getUniqueId();
  const uid = `; RMuserID/${deviceUniqueId}`;

  //先从ramCache里面取，ramCache有，则AsyncStorage里面一定有
  deviceUserAgent = storageIns.getCacheItem(Constants.CacheKey.DeviceUserAgent);

  if (!deviceUserAgent) {
    let userAgent = Platform.OS == 'ios' ? 'iPhone' : 'Android';
    try {
      // NOTE: very slow, uses 5s
      userAgent = await DeviceInfo.getUserAgent();
      deviceUserAgent = `${userAgent}${uid}`;
      storageIns.setCacheItem(Constants.CacheKey.DeviceUserAgent, deviceUserAgent, true);
    } catch (error) {
      console.log(error);
      deviceUserAgent = `${userAgent}${uid}`;
    }
  }

  return deviceUserAgent;
};

/**
 * 当给定的经纬度不在加拿大范围内时，返回加拿大的默认经纬度
 * @param {number} latitude - 输入的纬度
 * @param {number} longitude - 输入的经度
 * @returns {Object} 返回一个包含默认加拿大地理位置的对象
 */
const validLocationInCa = (pos) => {
  const { latitude, longitude } = pos.coords

  //使用RMMapSearchNative.jsx第314行的initialRegion的经纬度配置
  const defaultCanadaLocation = {
    latitude: 43.6448,
    longitude: -79.3958,
  }

  function isInCanada(latitude, longitude) {
    /**
    *
    * 最小纬度: 41.676555°N （在南安大略）
    * 最大纬度: 83.113883°N（在努纳武特）
    * 最小经度: -141.000000°W（在育空和阿拉斯加边界）
    * 最大经度: -52.648098°W（在纽芬兰与拉布拉多）
    */
    const minLat = 41.676555;
    const maxLat = 83.113883;
    const minLon = -141.000000;
    const maxLon = -52.648098;

    const isLatitudeInRange = latitude >= minLat && latitude <= maxLat;
    const isLongitudeInRange = longitude >= minLon && longitude <= maxLon;

    return isLatitudeInRange && isLongitudeInRange;
}

  //不在加拿大内
  if (!isInCanada(latitude, longitude)) {
    return { ...pos, coords: {
      ...pos.coords,
      ...defaultCanadaLocation
    } }
  }

  return pos
}


const urlFix = (url = '', options = {}) => {
  // 参数验证
  if (!url || typeof url !== 'string') {
    console.warn('urlFix: 无效的URL参数', url);
    return url || '';
  }
  
  let result = url.trim();
  
  try {
    // 处理相对路径
    if (result.charAt(0) === '/') {
      result = serverDomainIns.getFullUrl(result);
    }
    
    // 确保URL包含协议
    if (!result.startsWith('http://') && !result.startsWith('https://')) {
      // 特殊域名处理 - app.test 需要使用 HTTP 协议
      if (result.includes('app.test')) {
        result = `http://${result}`;
      } else if (result.includes('.')) {
        // 包含域名但没有协议的情况，添加https协议
        result = `https://${result}`;
      }
    } else {
      // 已有协议的情况下，处理http和https
      if (result.startsWith('https://') && result.includes('app.test')) {
        // app.test域名需要使用http协议
        result = result.replace(/^https:/, 'http:');
      } else if (result.startsWith('http://') && !result.includes('app.test')) {
        // 非app.test域名升级到https
        result = result.replace(/^http:/, 'https:');
      }
    }
    
    return result;
  } catch (error) {
    console.error('urlFix: 处理URL时出错', error);
    return url;
  }
};

export {mapUtil, getUserAgent, validLocationInCa, urlFix};
