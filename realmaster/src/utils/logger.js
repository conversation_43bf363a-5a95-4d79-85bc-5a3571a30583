import { Alert } from 'react-native';
import serverDomainIns from './serverDomain'
import { mainRequest } from './request';

const MAX_ERROR_THRESHOLD = 3;
var errorCollector = {};

async function send2Server(type,msg){
  let url = serverDomainIns.getFullUrl('/cError')
  mainRequest({
    url,
    method: 'POST',
    headers: {
      'Accept': 'application/json',
      'Content-Type': 'application/json',
    },
    data: {
      m: {tp:type,msg:msg}
    }
  }).catch((err) => {
    console.log(err.toString());
  });
}

function extractKeyFromErrorObj(err){
  val = '';
  if(err && err.toString()){
    val = err.toString().replace(/[\/\#\!\@\$\*\(\)\?\"\'\;\:\,\+\=]+/g,'')
  }
  return val;
}
function logError(err,isSend){
  // TODO: dev or release
  // NOTE: console.error has bug https://github.com/facebook/react-native/issues/24382
  // throw new Error(err)
  let key = extractKeyFromErrorObj(err);
  if(errorCollector[key]){
    errorCollector[key].n += 1;
    if((errorCollector[key].n > MAX_ERROR_THRESHOLD) && isSend){
      send2Server('error',err)
    }
  } else {
    errorCollector[key] = {
      err:err,
      n:1,
    }
  }
}

function logInfo(info){
  if (!__DEV__) return;
  console.log(info)
  //send2Server('info',info)
}

function alert(info,post){
  try{
    let str = JSON.stringify(info);
    if (post){
      mainRequest({url: 'https://www.realmaster.com/alert?msg=' + str}).catch((err) => {
        console.log(err.toString());
      });
      console.log(str);
    }
    Alert.alert(str);
  }catch(e){
    Alert.alert(String(info));
  }
}
function debug(...info){
  if (!__DEV__) return;
  console.log(...info)
};
export {logError,logInfo,send2Server,alert,debug};
