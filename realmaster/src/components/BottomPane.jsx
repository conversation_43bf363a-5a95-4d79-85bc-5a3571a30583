import React,{Component} from 'react';
import PropTypes from 'prop-types';
import {
    StyleSheet,
    View,
    Text,
  } from 'react-native';
import RMNavBar from './RMNavBar';

const propTypes = {
  title: PropTypes.string,
  height: PropTypes.number,
};

const defaultProps = {
  height: 240,
};

class BottomPane extends Component {
  render(){
    //console.log(this.props.children);
    return (
      <View style={[styles.bottomPaneWrapper,{height:this.props.height}]}>
        <RMNavBar
        style={[styles.navBar]}
        title={{title: this.props.title}}
        rightButton={{cbClose: this.props.cbClose}}
        hide={this.props.hideTitleBar}
        statusBar={this.props.statusBar}
        />
        {this.props.children}
      </View>
    );
  }
};

var styles = StyleSheet.create({
  navBar: {
    backgroundColor: '#E03131',
  },
  bottomPaneWrapper:{
    position: 'absolute',
    bottom: 0,
    // top:74,
    backgroundColor: 'white',
    left: 0,
    right: 0,
    zIndex: 1000,
    height: 240,
    // elevation:5,
  },
});
export default BottomPane;
