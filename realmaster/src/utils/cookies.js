import {str2Obj, eventEmitter} from './common';
import storageIns from './storage';
import serverDomainIns from './serverDomain';
import Constants from '../config/constants';
import { getDomainByUrl } from './common';


class Cookies {
  constructor() {
    if (!Cookies.instance) {
      //存储cookie键值对，在没有获取的cookie的时候使用内存中的cookie值
      this.cookies = {};
      Cookies.instance = this;
    }
    return Cookies.instance;
  }

  /**
   * 从实例的this.cookies变量中获取cookie
   *
   * @param {string} domain - 域名
   * @returns {string|object} - 对应cookie在内存中的值
   */
  getCookie(domain) {

    // console.log('🚀 ~ file: cookies.js:26 ~ Cookies ~ getCookie ~ domain:', domain)

    if (domain.indexOf('https://') < 0) {
      domain = `https://${domain}`;
    }
    const cookieKey = `Cookie@${domain}`;
    let cookie = storageIns.getCacheItem(cookieKey);

    const cacheCookie = this.cookies[domain];

    // console.log('🚀 ~ file: cookies.js:33 ~ Cookies ~ getCookie ~ cacheCookie:', cacheCookie)

    this.cookies[domain] = cacheCookie !== cookie ? cookie : cacheCookie;
    return cookie || '';
  }

  async getCookieValueByKey(domain, key){

    // console.log('🚀 ~ file: cookies.js:44 ~ Cookies ~ getCookieValueByKey ~ getCookieValueByKey:', domain, key)


    const getCookieValue = () => {
      //获取cookie的value
      const cookieStr = this.getCookie(domain);
      //获取cookie value的键值对
      const cookieObj = cookieStr && str2Obj(cookieStr) || {};

      // console.log('🚀 ~ file: cookies.js:39 ~ Cookies ~ getCookieValue ~ cookieObj:', cookieObj)


      //返回对应的value
      return cookieObj[key] || cookieObj[key.toLowerCase()];
    };

    //如果没有传递domain，则获取默认存储的domain
    if (!domain) {
      domain = await serverDomainIns.asyncGetDomain()
      domain = getDomainByUrl(domain)

      // console.log('🚀 ~ file: cookies.js:51 ~ Cookies ~ getCookieValueByKey ~ domain:', domain)

    }

    return getCookieValue();
  };

  _parseCookie(c,cookieObj={},tmpDomain){
    let _toSetCookie = []
    c = c.trim();
    if (/cmate\.sid\=/.test(c) ||
      /locale\=/.test(c) ||
      /appmode\=/gi.test(c)
    ){
      c = c.split(';');
      for (let tmp of c){
        if(tmp){
          var kv = tmp.split('=')
          var k = kv[0].trim()
          cookieObj[k] = kv[1]
          if(/appmode/ig.test(k)){
            // notify changed to status bar
            setTimeout((val) => {
              // console.log('+++kv=',val)
              eventEmitter.emit(Constants.ChangeAppMode,{val});
            }, 100, kv[1]);
          }
        }
      }
    }
    for (let k in cookieObj){
      if (k) {
        _toSetCookie.push(k+'='+cookieObj[k])
      }
    }
    return _toSetCookie;
  }

  setCookie(tmpDomain,curCookie,setCookieValue=''){
    // console.log('🚀 ~ file: cookies.js:82 ~ Cookies ~ setCookie ~ setCookieValue:', tmpDomain,setCookieValue )
    let done = ()=>{
      if(!curCookie){
        //如果没有传值，则取cache中的cookie值
        curCookie = str2Obj(this.getCookie(tmpDomain))
      }

      let _toSetCookie;
      if (Array.isArray(setCookieValue)){
        setCookieValue = setCookieValue.join(';')
      }
      if('string' === typeof setCookieValue){
        _toSetCookie = this._parseCookie(setCookieValue,curCookie,tmpDomain)
      }
      // console.log('+++++toSet:',_toSetCookie)
      // TODO: need to check if shall retain or keep old cookies
      if (_toSetCookie.length > 0){
        _toSetCookie = _toSetCookie.join("; ");
        this.cookies[tmpDomain] = _toSetCookie;
        storageIns.setCacheItem('Cookie@'+tmpDomain,_toSetCookie,true);
        // console.log("+++SetCookie final",'"'+tmpDomain+'"',' cur=',curCookie,' new=',_toSetCookie);
      }
    }
    if(tmpDomain){
      return done()
    }

    if (!tmpDomain) {
      serverDomainIns.asyncGetDomain().then(res => {
        tmpDomain = getDomainByUrl(res)

        // console.log('🚀 ~ file: cookies.js:131 ~ Cookies ~ serverDomainIns.asyncGetDomain ~ tmpDomain:', tmpDomain)

        return done()
      })

    }
  }
}

const instance = new Cookies();
Object.freeze(instance);

export default instance;
