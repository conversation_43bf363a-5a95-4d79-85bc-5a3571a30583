#import "AppDelegate.h"

// React Native 核心
#import <React/RCTBundleURLProvider.h>
#import <React/RCTLinkingManager.h>

// 第三方服务
#import <Firebase.h>
#import <GoogleMaps/GoogleMaps.h>
#import <AuthenticationServices/AuthenticationServices.h>
#import <SafariServices/SafariServices.h>
#import <FBSDKCoreKit/FBSDKCoreKit-swift.h>

// 自定义模块
#import "RNCConfig.h"
#import "Orientation.h"
#import "RNNotifications.h"
#import "RNQuickActionManager.h"
#import <RNGoogleSignin/RNGoogleSignin.h>

@implementation AppDelegate

#pragma mark - 生命周期方法

- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions
{
    @try {
        // 1. React Native 基础配置
        self.moduleName = @"RealMaster";
        self.initialProps = @{};
        
        // 2. 环境变量初始化
        [self setupEnvironmentVariables];
        
        // 3. 第三方服务初始化
        [self initializeThirdPartyServices:application launchOptions:launchOptions];
        
        // 4. React Native 初始化
        return [super application:application didFinishLaunchingWithOptions:launchOptions];
        
    } @catch (NSException *exception) {
        NSLog(@"应用初始化过程中发生严重错误: %@", exception.reason);
        // 降级启动
        self.moduleName = @"RealMaster";
        self.initialProps = @{};
        return [super application:application didFinishLaunchingWithOptions:launchOptions];
    }
}

#pragma mark - 私有方法

- (void)setupEnvironmentVariables {
    NSDictionary *env = [RNCConfig env];
    #ifdef DEBUG
        NSLog(@"\n========== RNCConfig 环境变量 ==========\n%@\n=======================================", env);
    #endif
}

- (void)initializeThirdPartyServices:(UIApplication *)application launchOptions:(NSDictionary *)launchOptions {
    // 初始化 Google Maps
    NSString *mapsApiKey = [RNCConfig envFor:@"IOS_MAPS_API_KEY"];
    if (mapsApiKey) {
        [GMSServices provideAPIKey:mapsApiKey];
    } else {
        NSLog(@"警告: 地图 API Key 未设置");
    }
    
    // 初始化通知服务
    @try {
        [RNNotifications startMonitorNotifications];
    } @catch (NSException *exception) {
        NSLog(@"通知服务初始化失败: %@", exception.reason);
    }
    
    // 初始化 Firebase
    @try {
        [FIRApp configure];
    } @catch (NSException *exception) {
        NSLog(@"Firebase 初始化失败: %@", exception.reason);
    }
    
    // 初始化 Facebook SDK
    @try {
        [[FBSDKApplicationDelegate sharedInstance] application:application
                                    didFinishLaunchingWithOptions:launchOptions];
    } @catch (NSException *exception) {
        NSLog(@"Facebook SDK 初始化失败: %@", exception.reason);
    }
}

#pragma mark - Bundle URL 配置

- (NSURL *)sourceURLForBridge:(RCTBridge *)bridge {
#if DEBUG
    return [[RCTBundleURLProvider sharedSettings] jsBundleURLForBundleRoot:@"index"];
#else
    return [[NSBundle mainBundle] URLForResource:@"main" withExtension:@"jsbundle"];
#endif
}

- (NSURL *)bundleURL {
#if DEBUG
    return [[RCTBundleURLProvider sharedSettings] jsBundleURLForBundleRoot:@"index"];
#else
    return [[NSBundle mainBundle] URLForResource:@"main" withExtension:@"jsbundle"];
#endif
}

#pragma mark - 应用程序事件处理

- (BOOL)application:(UIApplication *)application openURL:(NSURL *)url options:(NSDictionary<UIApplicationOpenURLOptionsKey,id> *)options {
    // 处理 Google 登录
    if ([RNGoogleSignin application:application openURL:url options:options]) {
        return YES;
    }
    
    // 处理 Facebook 登录
    if ([[FBSDKApplicationDelegate sharedInstance] application:application openURL:url options:options]) {
        return YES;
    }
    
    // 处理深度链接
    if ([RCTLinkingManager application:application openURL:url options:options]) {
        return YES;
    }
    
    // 处理微信登录
    return [WXApi handleOpenURL:url delegate:self];
}

- (BOOL)application:(UIApplication *)application handleOpenURL:(NSURL *)url {
    return [WXApi handleOpenURL:url delegate:self];
}

- (BOOL)application:(UIApplication *)application continueUserActivity:(NSUserActivity *)userActivity restorationHandler:(void(^)(NSArray<id<UIUserActivityRestoring>> * __nullable restorableObjects))restorationHandler {
    [RCTLinkingManager application:application continueUserActivity:userActivity restorationHandler:restorationHandler];
    return [WXApi handleOpenUniversalLink:userActivity delegate:self];
}

#pragma mark - 快捷操作处理

- (void)application:(UIApplication *)application performActionForShortcutItem:(UIApplicationShortcutItem *)shortcutItem completionHandler:(void (^)(BOOL succeeded))completionHandler {
    [RNQuickActionManager onQuickActionPress:shortcutItem completionHandler:completionHandler];
}

#pragma mark - 屏幕方向控制

- (UIInterfaceOrientationMask)application:(UIApplication *)application supportedInterfaceOrientationsForWindow:(UIWindow *)window {
    return [Orientation getOrientation];
}

#pragma mark - 推送通知处理

- (void)application:(UIApplication *)application didRegisterForRemoteNotificationsWithDeviceToken:(NSData *)deviceToken {
    [RNNotifications didRegisterForRemoteNotificationsWithDeviceToken:deviceToken];
}

- (void)application:(UIApplication *)application didFailToRegisterForRemoteNotificationsWithError:(NSError *)error {
    [RNNotifications didFailToRegisterForRemoteNotificationsWithError:error];
}

- (void)application:(UIApplication *)application didReceiveRemoteNotification:(NSDictionary *)userInfo fetchCompletionHandler:(void (^)(UIBackgroundFetchResult result))completionHandler {
    [RNNotifications didReceiveBackgroundNotification:userInfo withCompletionHandler:completionHandler];
}

@end

