<?xml version="1.0" encoding="UTF-8"?>
<Scheme
   LastUpgradeVersion = "1620"
   version = "1.7">
   <BuildAction
      parallelizeBuildables = "YES"
      buildImplicitDependencies = "YES"
      buildArchitectures = "Automatic">
      <PreActions>
         <ExecutionAction
            ActionType = "Xcode.IDEStandardExecutionActionsCore.ExecutionActionType.ShellScriptAction">
            <ActionContent
               title = "Run Script"
               scriptText = "# Type a script or drag a script file from your workspace to insert its path.&#10;rm &quot;${CONFIGURATION_BUILD_DIR}/${INFOPLIST_PATH}&quot;&#10;echo &quot;.env.development&quot; &gt; /tmp/envfile&#10;">
               <EnvironmentBuildable>
                  <BuildableReference
                     BuildableIdentifier = "primary"
                     BlueprintIdentifier = "13B07F861A680F5B00A75B9A"
                     BuildableName = "RealMaster.app"
                     BlueprintName = "RealMaster"
                     ReferencedContainer = "container:RealMaster.xcodeproj">
                  </BuildableReference>
               </EnvironmentBuildable>
            </ActionContent>
         </ExecutionAction>
         <ExecutionAction
            ActionType = "Xcode.IDEStandardExecutionActionsCore.ExecutionActionType.ShellScriptAction">
            <ActionContent
               title = "Run Script"
               scriptText = "# Type a script or drag a script file from your workspace to insert its path.&#10;&quot;${SRCROOT}/../node_modules/react-native-config/ios/ReactNativeConfig/BuildXCConfig.rb&quot; &quot;${SRCROOT}/..&quot; &quot;${SRCROOT}/tmp.xcconfig&quot;&#10;">
               <EnvironmentBuildable>
                  <BuildableReference
                     BuildableIdentifier = "primary"
                     BlueprintIdentifier = "13B07F861A680F5B00A75B9A"
                     BuildableName = "RealMaster.app"
                     BlueprintName = "RealMaster"
                     ReferencedContainer = "container:RealMaster.xcodeproj">
                  </BuildableReference>
               </EnvironmentBuildable>
            </ActionContent>
         </ExecutionAction>
         <ExecutionAction
            ActionType = "Xcode.IDEStandardExecutionActionsCore.ExecutionActionType.ShellScriptAction">
            <ActionContent
               title = "Run Script"
               scriptText = "# Type a script or drag a script file from your workspace to insert its path.&#10;# &#x5207;&#x6362;&#x5230;&#x9879;&#x76ee;&#x6839;&#x76ee;&#x5f55;&#10;cd &quot;${SRCROOT}/..&quot;&#10;&#10;# &#x4ece; appversion.json &#x63d0;&#x53d6;&#x7248;&#x672c;&#x53f7;&#x548c;&#x6784;&#x5efa;&#x53f7;&#10;VERSION=$(grep &apos;&quot;version&quot;:&apos; appversion.json | sed &apos;s/.*: &quot;\(.*\)&quot;,/\1/&apos;)&#10;VERSION_BUILD=$(grep &apos;&quot;versionBuild&quot;:&apos; appversion.json | sed &apos;s/.*: \(.*\),*/\1/&apos;)&#10;&#10;# &#x4e34;&#x65f6;&#x6587;&#x4ef6;&#10;TEMP_FILE=&quot;ios/tmp.xcconfig.tmp&quot;&#10;&#10;# &#x5982;&#x679c; tmp.xcconfig &#x5b58;&#x5728;&#xff0c;&#x5148;&#x5907;&#x4efd;&#x5185;&#x5bb9;&#10;if [ -f &quot;ios/tmp.xcconfig&quot; ]; then&#10;    # &#x5220;&#x9664;&#x65e7;&#x7684; APP_VERSION &#x548c; APP_VERSIONBUILD &#x884c;&#xff08;&#x5982;&#x679c;&#x5b58;&#x5728;&#xff09;&#10;    grep -v &quot;^APP_VERSION=&quot; &quot;ios/tmp.xcconfig&quot; | grep -v &quot;^APP_VERSIONBUILD=&quot; &gt; &quot;$TEMP_FILE&quot;&#10;    # &#x6dfb;&#x52a0;&#x65b0;&#x7684;&#x7248;&#x672c;&#x4fe1;&#x606f;&#10;    echo &quot;APP_VERSION=$VERSION&quot; &gt;&gt; &quot;$TEMP_FILE&quot;&#10;    echo &quot;APP_VERSIONBUILD=$VERSION_BUILD&quot; &gt;&gt; &quot;$TEMP_FILE&quot;&#10;    # &#x66ff;&#x6362;&#x539f;&#x6587;&#x4ef6;&#10;    mv &quot;$TEMP_FILE&quot; &quot;ios/tmp.xcconfig&quot;&#10;else&#10;    # &#x5982;&#x679c;&#x6587;&#x4ef6;&#x4e0d;&#x5b58;&#x5728;&#xff0c;&#x521b;&#x5efa;&#x65b0;&#x6587;&#x4ef6;&#10;    echo &quot;APP_VERSION=$VERSION&quot; &gt; &quot;ios/tmp.xcconfig&quot;&#10;    echo &quot;APP_VERSIONBUILD=$VERSION_BUILD&quot; &gt;&gt; &quot;ios/tmp.xcconfig&quot;&#10;fi&#10;&#10;echo &quot;&#x2705; &#x7248;&#x672c;&#x540c;&#x6b65;&#x6210;&#x529f;&#xff1a;&quot;&#10;echo &quot;APP_VERSION = $VERSION&quot;&#10;echo &quot;APP_VERSIONBUILD = $VERSION_BUILD&quot;&#10;">
               <EnvironmentBuildable>
                  <BuildableReference
                     BuildableIdentifier = "primary"
                     BlueprintIdentifier = "13B07F861A680F5B00A75B9A"
                     BuildableName = "RealMaster.app"
                     BlueprintName = "RealMaster"
                     ReferencedContainer = "container:RealMaster.xcodeproj">
                  </BuildableReference>
               </EnvironmentBuildable>
            </ActionContent>
         </ExecutionAction>
      </PreActions>
      <BuildActionEntries>
         <BuildActionEntry
            buildForTesting = "YES"
            buildForRunning = "YES"
            buildForProfiling = "YES"
            buildForArchiving = "YES"
            buildForAnalyzing = "YES">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "13B07F861A680F5B00A75B9A"
               BuildableName = "RealMaster.app"
               BlueprintName = "RealMaster"
               ReferencedContainer = "container:RealMaster.xcodeproj">
            </BuildableReference>
         </BuildActionEntry>
      </BuildActionEntries>
   </BuildAction>
   <TestAction
      buildConfiguration = "Debug"
      selectedDebuggerIdentifier = "Xcode.DebuggerFoundation.Debugger.LLDB"
      selectedLauncherIdentifier = "Xcode.DebuggerFoundation.Launcher.LLDB"
      shouldUseLaunchSchemeArgsEnv = "YES"
      shouldAutocreateTestPlan = "YES">
   </TestAction>
   <LaunchAction
      buildConfiguration = "Debug"
      selectedDebuggerIdentifier = "Xcode.DebuggerFoundation.Debugger.LLDB"
      selectedLauncherIdentifier = "Xcode.DebuggerFoundation.Launcher.LLDB"
      launchStyle = "0"
      useCustomWorkingDirectory = "NO"
      ignoresPersistentStateOnLaunch = "NO"
      debugDocumentVersioning = "YES"
      debugServiceExtension = "internal"
      allowLocationSimulation = "YES">
      <BuildableProductRunnable
         runnableDebuggingMode = "0">
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "13B07F861A680F5B00A75B9A"
            BuildableName = "RealMaster.app"
            BlueprintName = "RealMaster"
            ReferencedContainer = "container:RealMaster.xcodeproj">
         </BuildableReference>
      </BuildableProductRunnable>
   </LaunchAction>
   <ProfileAction
      buildConfiguration = "Release"
      shouldUseLaunchSchemeArgsEnv = "YES"
      savedToolIdentifier = ""
      useCustomWorkingDirectory = "NO"
      debugDocumentVersioning = "YES">
      <BuildableProductRunnable
         runnableDebuggingMode = "0">
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "13B07F861A680F5B00A75B9A"
            BuildableName = "RealMaster.app"
            BlueprintName = "RealMaster"
            ReferencedContainer = "container:RealMaster.xcodeproj">
         </BuildableReference>
      </BuildableProductRunnable>
   </ProfileAction>
   <AnalyzeAction
      buildConfiguration = "Debug">
   </AnalyzeAction>
   <ArchiveAction
      buildConfiguration = "Release"
      revealArchiveInOrganizer = "YES">
   </ArchiveAction>
</Scheme>
