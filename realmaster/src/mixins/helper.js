
import convertArea from '../es/convertArea';
import convertDistance from '../es/convertDistance';
import computeDestinationPoint from '../es/computeDestinationPoint';

function callOnce(fnOrig) {
    return function(...argv) {
        var cb;
        if (cb = fnOrig) {
        fnOrig = null;
        return cb(...argv);
        }
    };
};

function isDate(input) {
  if ( Object.prototype.toString.call(input) === "[object Date]" )
    return true;
  return false;
};

function deepCloneArray(obj){
  let ret = [];
  for (v of obj){
    if ('object' == typeof(v)){
      if (Array.isArray(v)){
        ret.push(deepCloneArray(v));
      }else if(isDate(v)){
        ret.push(new Date(v));
      }else{
        ret.push(deepAssign({},v));
      }
    }else{
      ret.push(v);
    }
  }
  return ret;
}

function deepAssign(objOrig,obj2){
  if (!obj2){
    obj2 = objOrig;
    objOrig = {};
  }
  for (k in obj2){
    let v = obj2[k];
    if ('object' == typeof(v)){
      if (Array.isArray(v)){
        objOrig[k] = deepCloneArray(v);
      }else if(isDate(v)){
        ret.push(new Date(v));
      }else{
        objOrig[k] = deepAssign({},v);
      }
    }else{
      objOrig[k] = obj2[k];
    }
  }
  return objOrig;
}



const DistanceUnits = ['mm','cm','in','ft','yd','m','km','mi','sm'];
const AreaUnits = ['in2','ft2','yd2','m2','a','ha','km2'];

function distanceBySmallestUnit(m){
  let i = 0;
  while (i<DistanceUnits.length){
    un = DistanceUnits[i];
    d = convertDistance(m,un);
    dabs = Math.abs(d);
    if (dabs < 100 ){
      break;
    }
    i++;
  }
  return {distance:d,unit:un,index:i};
}
function distanceByUnit(m,un){
  return convertDistance(m,un);
}
function areaBySmallestUnit(mm){
  let i = 0;
  while (i<AreaUnits.length){
    un = AreaUnits[i];
    d = convertArea(mm,un);
    dabs = Math.abs(d);
    if (dabs < 100 ){
      break;
    }
    i++;
  }
  let suffix = '';
  if(un.indexOf(2)>0){suffix='²';un.replace('2','')}
  return {area:d,unit:un,index:i,suffix};
}
function areaByUnit(mm,un){
  return convertArea(mm,un);
}
function roundZero(num=0,digit){
  num = ''+num;
  let ret = '';
  let count = 0;
  for(let i of num){
    if(count > digit) break;
    if(!(i == '0' || i == '.')){
      count++;
    }
    ret = ret + i;
  }
  return ret
}
// old has bug 0.0005006556241219013 -> 0
function round(num, digit){
  let n = 10 ** digit;
  let rounded = Math.round(num * n);
  if(rounded == 0){
    return roundZero(num,digit);
  }
  return  rounded / n;
}
function getPointByDistance(loc, distance, bearing = 180) {
  return computeDestinationPoint(loc, distance, bearing)
}

// a2 > a1, b2 > b1
// calculate if a1--a2 overlap with b1--b2
function isOverlap(a1,a2,b1,b2){
  if (a1>b1){
    if (a1<=b2) return true;
    return false;
  }else if (a1<b1){
    if (a2>=b1) return true;
    return false;
  }
  return true;
}
export {
  callOnce,
  deepAssign,
  deepCloneArray,
  isDate,
  DistanceUnits,
  AreaUnits,
  distanceBySmallestUnit,
  distanceByUnit,
  areaBySmallestUnit,
  areaByUnit,
  round,
  isOverlap,
  getPointByDistance};
