import React,{ Component } from 'react';
import {
  StatusBar,
  View,
  Platform,
} from 'react-native';
import PropTypes from 'prop-types';
import DeviceInfo from 'react-native-device-info';
import { getColor } from '../utils/RMstyle';
import appConfigIns from '../config/appConfig'
import { eventEmitter } from "../utils/common";
// import { getCookieValueByKey } from './helpers/cookies';

var hasNotch = DeviceInfo.hasNotch();
// DeviceInfo.hasNotch().then(tmpHasNotch => {
// console.log('++++++hasNotch: ',hasNotch)
//   hasNotch = tmpHasNotch;
// });
// const ModelIphoneX = DeviceInfo.getModel() === 'Simulator'?'Simulator':'iPhone X';
var deviceId = DeviceInfo.getDeviceId();
// .then(deviceId => {
// console.log('deviceId: '+deviceId);
// })

var iosModels;
// let Dimensions = {get:()=>{return 100}}
// NOTE: used SafeAreaView so no need this
// NOTE2: need full screen, use cust statusbar
var STATUS_BAR_HEIGHT_IOS;
// console.log("Statusbar currentHeight: ", StatusBar.currentHeight);
var BOTTOM_BAR_HEIGHT;

function getIphonestatusBarHeight() {
  let model = iosModels[deviceId]
  if (model) {
    return model.notch
  }
  return 48;
}
function getIphoneBottomHeight(){
  let model = iosModels[deviceId]
  if (model && model.foot != null) {
    return model.foot
  }
  return 15
}
function initValues(){
  iosModels = appConfigIns.getAppConfig('iosModelsAndNotch');
  STATUS_BAR_HEIGHT_IOS = getIphonestatusBarHeight();
  BOTTOM_BAR_HEIGHT = getIphoneBottomHeight()
}

export const StatusBarShape = {
  style: PropTypes.oneOf(['light-content', 'default']),
  hidden: PropTypes.bool,
  tintColor: PropTypes.string,
  hideAnimation: PropTypes.oneOf(['fade', 'slide', 'none']),
  showAnimation: PropTypes.oneOf(['fade', 'slide', 'none']),
};

initValues()



export function getBottomBarHeight(){
  if (Platform.OS === 'ios'){
    return BOTTOM_BAR_HEIGHT;
  } else {
    // return 20;
    if (hasNotch) {
      // NOTE: StatusBar.currentHeight; android only
      return 0;//StatusBar.currentHeight;
    }
    return 0;
    // console.log('++++++',StatusBar.currentHeight)
  }
}
export function getStatusBarHeight(){
  // console.log('++++status bar height',STATUS_BAR_HEIGHT_IOS);
  if (Platform.OS === 'ios'){
    return STATUS_BAR_HEIGHT_IOS;
  } else {
    if (hasNotch) {
      // NOTE: StatusBar.currentHeight; android only
      return 0;//StatusBar.currentHeight;
    }
    return 0;
    // console.log('++++++',StatusBar.currentHeight)
  }
  return 0;
}
// console.log('++++++getBottomBarHeight'+getBottomBarHeight())
// export const bottomBarHeight = getBottomBarHeight();
// export var statusBarHeight = getStatusBarHeight();
export class RMBottomBar extends Component {
  constructor(props) {
    super(props);
    this.styles = {
      height: getBottomBarHeight(),
      backgroundColor:  '#fff', // test#fff
    }
    this.state = {}
  }
  render() {
    // TODO: support android?
    if (Platform.OS === 'ios') {
      return (
        <View key={'appBottomBarForIphoneX'} style={[this.styles]} />
      );
    }
    return null;
  }
}
export class RMStatusBar extends Component {
  constructor(props) {
    super(props);
    this.styles = {
      height: getStatusBarHeight(),
      // backgroundColor: 'blue',//getColor('mainTheme'), // '#e03131', // test
      zIndex:10,
      // paddingTop:-10,
      // color:'red',
    }
    this.state = {
      backgroundColor: 'transparent'//await getColor('mainTheme')
    }
    // console.log('tint='+this.props.tintColor)
    initValues()
    // console.log('++++++',getBottomBarHeight(),getStatusBarHeight())
  }
  static getMarginOffset(){
    return 0;
  }
  async componentDidMount() {
    this.customizeStatusBar();
    let state = {
      backgroundColor:await getColor('mainTheme'),//transparent
    }
    this.setState(state)
    // console.log('SYSTEM.CHANGE_APP_MODE mounted!!')
    // eventEmitter.on(SYSTEM.CHANGE_APP_MODE,this.changeAppMode.bind(this));
    eventEmitter.on('navbar.setBarColor', this.changeBarColor.bind(this));
  }
  unmount(){
    // eventEmitter.removeListener(SYSTEM.CHANGE_APP_MODE,this.changeAppMode.bind(this));
    eventEmitter.removeListener('navbar.setBarColor', this.changeBarColor.bind(this));
  }
  async changeBarColor(opt={}){
    this.setState({backgroundColor:opt.barColor},()=>{
      // console.log('bg='+this.state.backgroundColor,
      //   ' tint='+this.props.tintColor,
      //   ' getColor='+getColor('mainTheme'),
      //   ' getCookieValueByKey='+getCookieValueByKey(null,'appmode')
      // )
    })
  }
  async changeAppMode(opt={}){
    // console.log('+++changeAppMode',opt)
    let appmode = null
    if(opt.val){
      appmode = opt.val
    }
    this.setState({backgroundColor:await getColor('mainTheme',appmode)},()=>{
      // console.log('bg='+this.state.backgroundColor,
      //   ' tint='+this.props.tintColor,
      //   ' getColor='+getColor('mainTheme'),
      //   ' getCookieValueByKey='+getCookieValueByKey(null,'appmode')
      // )
    })
    // }
  }
  // componentWillReceiveProps() {
  //   this.customizeStatusBar();
  // }
  customizeStatusBar() {
    if (Platform.OS === 'ios') {
      // if (this.props.barStyle) {
      //   StatusBar.setBarStyle(this.props.barStyle);
      // }

      const animation = this.props.hidden ?
        this.props.hideAnimation : this.props.showAnimation;

      StatusBar.showHideTransition = animation;
      StatusBar.hidden = this.props.hidden;
      // StatusBar.setBarStyle('light-content');
    }
  }
  render() {
    // StatusBar.setBarStyle('dark-content');
    // NOTE: https://developer.apple.com/documentation/uikit/uiscrollview/1619421-scrollstotop scrollsToTop is native property,
    // no listener in js;
    // if (Platform.OS === 'ios' || true) {
    const customStatusBarTintColor = this.props.tintColor ?
      { backgroundColor: this.props.tintColor } : null;
    let androidBGColor = this.state.backgroundColor;
    if(customStatusBarTintColor){
      androidBGColor = customStatusBarTintColor.backgroundColor
    }
    return (
      // force statusbar to be dark content, otherwise in white bar nothing to show
      <View key={'statusBarWrapper'}
        style={[this.styles, {backgroundColor:this.state.backgroundColor},customStatusBarTintColor]}>
        <StatusBar barStyle="light-content" backgroundColor={androidBGColor}/>
      </View>
      // <TouchableOpacity style={[this.styles, customStatusBarTintColor]} onPress={(e)=>{console.log('+++++++')}}>
      // </TouchableOpacity>
    );
    // }
    return null;
  }
}
RMStatusBar.propTypes = {
  // style: PropTypes.oneOf(['light-content', 'default']),
  hidden: PropTypes.bool,
  tintColor: PropTypes.string,
  hideAnimation: PropTypes.oneOf(['fade', 'slide', 'none']),
  showAnimation: PropTypes.oneOf(['fade', 'slide', 'none']),
};
RMStatusBar.defaultProps = {
  barStyle: 'light-content',
  hidden: false,
  hideAnimation: 'slide',
  showAnimation: 'slide',
}
