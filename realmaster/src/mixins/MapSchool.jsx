/*
schs:[] {
  _id: 1020861, nm: "St. Monica Catholic School", addr: "14 Broadway Avenue", city: "Toronto",…}
  addr: "14 Broadway Avenue"
  bnsc: 1
  city: "Toronto"
  loc: [43.7097, -79.3986]
  0: 43.7097
  1: -79.3986
  nm: "St. Monica Catholic School"
  _id: 1020861}

state:
  schs:[] all schools
  schList: [] home school list
  sch: current school. sch.cBnd: current Boundaries on map
  showPane: bool, show current school pane
  showPanel: bool, show school filter panel
  bns:[] show bns pane
  bn:  current selected bn
  loc: pointer on map
  searchSchool: show search home school dialog
*/
import React, {Component} from 'react';
import {
  StyleSheet,
  View,
  Text,
  Image,
  Alert,
  Platform,
  ScrollView,
  TouchableOpacity,
  TouchableHighlight,
  Dimensions,
  TouchableWithoutFeedback,
  FlatList,
} from 'react-native';
// import Icon from 'react-native-vector-icons/Ionicons';
import {Icon, eventEmitter} from '../utils/common';
import MapFeature from './MapFeature';
import {<PERSON>er, Polygon} from 'react-native-maps';
import schoolRed from '../assets/images/schoolRed.png';
import schoolGreen from '../assets/images/schoolGreen.png';
import schoolOrange from '../assets/images/schoolOrange.png';
import schoolRed0 from '../assets/images/schoolRed0.png';
import schoolGreen0 from '../assets/images/schoolGreen0.png';
import schoolOrange0 from '../assets/images/schoolOrange0.png';
import pSchoolRed from '../assets/images/pSchoolRed.png';
import pSchoolGreen from '../assets/images/pSchoolGreen.png';
import pSchoolOrange from '../assets/images/pSchoolOrange.png';
import pSchoolRed0 from '../assets/images/pSchoolRed0.png';
import pSchoolGreen0 from '../assets/images/pSchoolGreen0.png';
import pSchoolOrange0 from '../assets/images/pSchoolOrange0.png';
// import PriceMarker from '../views/PriceMarker';
import IconText from '../components/IconText';
// import BottomPane from '../views/BottomPane';
import BottomPaneWhite from '../components/BottomPaneWhite';
import {requestStdFn} from '../utils/request';
import serverDomainIns from '../utils/serverDomain';
import {l10n} from '../utils/i18n';
import {RMBottomBar} from '../components/RMStatusBar';
import {sprintf} from '../lib/sprintf';
import {urlParamToObject} from './mapSearch';
import {deepAssign} from './helper';
import {distance4display} from '../lib/valueDisplay';
import Constants from '../config/constants';
import appConfigIns from '../config/appConfig';
import ListDragPan from './mapListDragPanResponder';
import RmIcon from '../components/RmIcon'

const {width, height} = Dimensions.get('window');
const schTypeMapping = {
  ele: {nm: 'Elementary', color: '#F1F8EC', textColor: '#5cb85c'},
  mid: {nm: 'Middle', color: '#F1F8EC', textColor: '#5cb85c'},
  hgh: {nm: 'Secondary', color: '#F1F8EC', textColor: '#5cb85c'},
  fi: {nm: 'French Immersion', color: '#DAE3F3', textColor: '#007aff'},
  ef: {nm: 'Extended French', color: '#DAE3F3', textColor: '#007aff'},
  eng: {nm: 'English', color: '#DAE3F3', textColor: '#007aff'},
  catholic: {nm: 'Catholic', color: '#FDF1E8', textColor: '#e03131'},
  hot: {nm: 'Top', color: '#FDF1E8', textColor: '#e03131'}, //Popular/Top/Hot?
  boarding: {nm: 'Boarding', color: '#DAE3F3', textColor: '#007aff'},
  admIntvw: {nm: 'Request Interview', color: '#DAE3F3', textColor: '#007aff'},
  admSSAT: {nm: 'Request SSAT', color: '#DAE3F3', textColor: '#007aff'},
};

function filterSchoolName(sch) {
  return sch.nm
    .toUpperCase()
    .replace(' Junior and Senior Public School', '')
    .replace('Junior Public School', 'JP')
    .replace('Senior Public School', 'SP')
    .replace('Public School', 'PS')
    .replace('Catholic School', 'CS')
    .replace(' Community School', '');
}
function clearSchoolName(sch) {
  return sch.nm.replace(/\s(Public|School|Elementory|Middle|Secondary|Catholic)/gi, '');
}

function gfConvert(gf) {
  if (gf < 0) {
    return l10n('KinderGarden');
  }
  return gf;
}
function fiConvert(fi) {
  if (fi && fi != 0) {
    return l10n('French Immersion');
  }
  return '';
}

// NOTE: checked != val, case: All, checked = false, val = true
// checked: UI check mark and green boarder
// val: real value, t/f
// clickable: UI for clickable, black/grey
const gFeatureSetting = {
  _main: {
    // For this whole feature
    nm: l10n('Schools'),
    m: l10n('Only show in certain zoom level.'),
    val: true,
    _segments: [
      'all',
      'school',
      // 'catholic',
      'privateSchool',
      'university',
      'college',
    ],
  },
  school: {
    nm: 'Public Schools',
    val: false,
    checked: false,
    clickable: true,
    //_segments:[['public','catholic'],['eng','fi','ef'],['ele','mid','hgh'],['search']],
    _segments: [
      {nm: 'Board', l: ['public', 'catholic'], clickable: false},
      {nm: 'Grade', l: ['ele', 'mid', 'hgh'], clickable: false},
      {nm: 'Language', l: ['eng', 'fi', 'ef'], clickable: false},
      {nm: 'Programs', l: ['ib', 'ap', 'gif', 'art', 'sport', 'allOther'], clickable: false},
    ],
  },
  public: {nm: 'Public', val: false},
  catholic: {
    clickable: false,
    nm: 'Catholic',
    val: false,
  },
  eng: {nm: 'English', val: true, clickable: false},
  fi: {nm: 'French Immersion', val: true, clickable: false},
  ef: {nm: 'Extended French', val: true, clickable: false},

  ele: {nm: 'Elementary', val: true, clickable: false},
  mid: {nm: 'Middle', val: true, clickable: false},
  hgh: {nm: 'Secondary', val: true, clickable: false},

  ib: {nm: 'IB', val: true, clickable: false},
  ap: {nm: 'AP', val: true, clickable: false},
  gif: {nm: 'Gifted', val: true, clickable: false},
  art: {nm: 'Art', val: true, clickable: false},
  sport: {nm: 'Sports', val: true, clickable: false},
  allOther: {nm: 'All Others', val: true, clickable: false},

  search: {nm: 'On Map Search', val: true},
  privateSchool: {
    nm: l10n('Private Schools'),
    val: false,
    checked: false,
    clickable: true,
    m: l10n('Only show 30 schools at one time.'),
    _segments: [
      {nm: 'Rank', l: ['top']},
      {nm: 'Gender', l: ['coedu', 'girls', 'boys', 'noGenderRestrict']},
    ],
  },
  university: {
    nm: l10n('University'),
    val: false,
    checked: false,
    clickable: true,
  },
  college: {
    nm: l10n('College'),
    val: false,
    checked: false,
    clickable: true,
  },
  top: {nm: 'Top', val: false},
  all: {
    nm: l10n('All'),
    val: true,
    checked: true,
    clickable: true,
    _segments: [
      {nm: 'Board', l: ['public', 'catholic'], clickable: false},
      {nm: 'Grade', l: ['ele', 'mid', 'hgh'], clickable: false},
      {nm: 'Language', l: ['eng', 'fi', 'ef'], clickable: false},
      {nm: 'Programs', l: ['ib', 'ap', 'gif', 'art', 'sport', 'allOther'], clickable: false},
    ],
  },
  coedu: {nm: 'Co-Education', val: true},
  girls: {nm: 'Girls', val: true},
  boys: {nm: 'Boys', val: true},
  noGenderRestrict: {nm: 'No gender restriction', val: true},
};
var feature = {school: true, privateSchool: true};
function getCoordinates(bn) {
  if (!bn) return null;
  let coordinates = [];
  for (let i = 0; i < bn.length; i += 2) {
    coordinates.push({longitude: bn[i + 1], latitude: bn[i]});
  }
  return coordinates;
}
function getMultipleCoordinates(bns) {
  let ary = [];
  bns.forEach(bn => {
    ary.push(getCoordinates(bn));
  });
  return ary;
}
function assignArray(aryTarget, ary1) {
  ary1.forEach(v => {
    aryTarget.push(v);
  });
}
class MapSchool extends MapFeature {
  constructor(map, props, onOff) {
    super(map, 'Schools', props, onOff);
    this.state = {
      //latlng: [{longitude:-79.40,latitude:43.63}],
      schs: {},
      tracksViewChanges: true,
      showPanel: false,
      menuItem: 'all',
      propFeatureOn: true,
      featureSetting: deepAssign(gFeatureSetting),
      schCnt: 0,
    };
    if (props.propFeatureOn != null) {
      this.state.propFeatureOn = props.propFeatureOn;
    }
    this.zoomAlertCount = 0;
    this.listDragPan = React.createRef();
    this.regOnPress();
    this.setFeature.bind(this);
    // this.featureSetting = Object.assign({},featureSetting);
    // this.featureOn = true;
    eventEmitter.on(Constants.EventGetHomeSchools, this.getHomeSchools);
    eventEmitter.on(Constants.EventSet, this.setFeature);
    // eventEmitter.on(SYSTEM.EVENT_CLEAR_FEATURE_PANEL,this.setShowPanel);
    eventEmitter.on('map.props.fetureOn', this.recordPropFeatureOn.bind(this));
  }
  // NOTE: component never mounted
  // componentDidMount(){
  //   console.error('-----school component mount')
  //   this.regOnPress();
  // }
  recordPropFeatureOn(val) {
    // console.log('event',val)
    this.setState({propFeatureOn: val});
  }
  gotUniversity(ret, tp = 'university') {
    let schools = this.state.schs || {};
    // console.log('gotUniversity ret=',ret,' tp=',tp,'schools=',schools)
    for (k in schools) {
      // console.log('k==',k,'schools[k][tp]=',schools[k][tp])
      if (schools[k][tp]) {
        // only clear old univ schools, do not touch other schools
        delete schools[k];
      }
    }
    // console.log('univ ret',tp,ret)
    let schs = ret;
    if (schs) {
      schs.forEach(sch => {
        sch[tp] = true;
        //sch.loc = [sch.lat,sch.lng];
        schools[sch._id] = sch;
      });
    }
    // console.log('+++++',schools,Object.keys(schools).length)
    this.setState({schs: schools, tracksViewChanges: true}, () => {
      // console.log('tp=',tp,' len=',Object.keys(this.state.schs).length)
    });
    this.trackOff();
  }
  gotSchoolsResult(features, ret) {
    var schools = {}; //this.state.schs || {};
    // NOTE: delete this type of school from old list
    for (let f of features) {
      var tp = f[0];
      // for (k in schools){
      //   if (schools[k][tp] == true){ // only clear old non-private schools, do not touch private schools
      //     delete schools[k];
      //   }
      // }
      let schs = ret[tp];
      if (schs) {
        schs.forEach(sch => {
          sch[tp] = true;
          //sch.loc = [sch.lat,sch.lng];
          schools[sch._id] = sch;
        });
      }
    }
    this.setState({schs: schools, tracksViewChanges: true, schCnt: ret.schCnt || 0}, () => {
      // console.log('tp=',tp,' len=',Object.keys(this.state.schs).length)
    });
    this.trackOff();
  }
  gotSchools(schs) {
    //console.log('gotSchools',schs);
    let schools = this.state.schs || {};
    if (this.state.menuItem !== 'all') {
      for (k in schools) {
        // use.pub?
        if (!schools[k].private) {
          // only clear old non-private schools, do not touch private schools
          delete schools[k];
        }
      }
    }
    schs.forEach(sch => {
      // sch.snm = filterSchoolName(sch);
      // sch.ssnm = clearSchoolName(sch);
      // sch._id = '' + sch._id;
      schools[sch._id] = sch;
    });
    this.setState({schs: schools, tracksViewChanges: true}, () => {
      // console.log(Object.keys(this.state.schs).length)
    });
    this.trackOff();
  }
  gotPrivateSchools(ret) {
    //console.log('gotPrivateSchools',schs);
    let schools = this.state.schs || {};
    for (k in schools) {
      if (schools[k].private) {
        // only clear old private schools, do not touch other schools
        delete schools[k];
      }
    }
    let schs = ret;
    if (schs) {
      schs.forEach(sch => {
        sch.private = true;
        //sch.loc = [sch.lat,sch.lng];
        schools[sch._id] = sch;
      });
    }
    this.setState({schs: schools, tracksViewChanges: true});
    this.trackOff();
  }
  clearSchools() {
    this.setState({schs: null, pSchs: null, bns: null, sch: null, showPane: null, loc: null, searchDialog: null});
  }
  closeModal() {
    this.setState({showPane: null, bns: null, loc: null, searchDialog: null});
  }
  getSchoolQuery(q, featureLists = [], featureKey) {
    let filter = [];
    let norFilter = [];
    // featureList = {nm:'xxx',l:['a','b']}
    // console.log('featureLists: ',featureLists,'featureKey:',featureKey,'featureSetting',this.state.featureSetting)
    let serviceName = featureKey || this.state.menuItem;
    if (serviceName == 'university' || serviceName == 'college') {
      q.showOnMap = true;
      q.tp = serviceName;
    }
    for (let featureList of featureLists) {
      let subFilter = {};
      for (let k of featureList.l) {
        if (this.state.featureSetting[k] && this.state.featureSetting[k].val) {
          subFilter[k] = 1;
        }
      }
      let notAllSubFilterSelected = Object.keys(subFilter).length != featureList.l.length;
      // NOTE:? 都选相当于都不选？除了private->top
      if (subFilter.top || notAllSubFilterSelected) {
        if (subFilter.allOther) {
          // ib/ap/.../allOther line. when has allOther, use Nor
          // i.e. when selected ib+allOther, Nor:[{ap:1},{gif:1},{art:1},{sport:1}]
          for (let k of featureList.l) {
            if (this.state.featureSetting[k] && !this.state.featureSetting[k].val) {
              let obj = {};
              obj[k] = 1;
              norFilter.push(obj);
            }
          }
        } else if (subFilter.public) {
          norFilter.push({catholic: 1});
        } else {
          filter.push(subFilter);
        }
      }
      // console.log('subFilter:',subFilter,'!=length',Object.keys(subFilter).length != featureList.l.length)
    }
    if (norFilter.length > 0) {
      filter.push({Nor: norFilter});
    }
    if (filter.length > 0) {
      q.filter = filter;
    }
    return q;
  }
  // getUniversityData({q,menuItem,cbFnName,featureKey}){
  //   RMPost('/1.5/school/university',q,(err,ret)=>{
  //     if(err){
  //       return;
  //     }
  //     if (ret.err || ret.e) {
  //       if(menuItem == 'university' || menuItem == 'college'){
  //         return this.flashMessage(ret.err);
  //       }
  //       // return Alert.alert(ret.err || ret.e);
  //       return;
  //     }
  //     this[cbFnName](ret,featureKey);
  //   })
  //   return;
  // }
  doSearchNew({event}) {
    // featureKey,serviceName,cbFnName
    let features = [
      ['university', 'findUniversities', 'gotUniversity'],
      ['college', 'findUniversities', 'gotUniversity'],
      ['privateSchool', 'findPrivateSchools', 'gotPrivateSchools'],
      ['school', 'findSchools', 'gotSchools'],
    ];
    let isFeatureKeyActive = featureKey => {
      if (this.state.menuItem == 'all') {
        return true;
      }
      return (
        this.state.featureSetting && this.state.featureSetting[featureKey] && this.state.featureSetting[featureKey].val
      );
    };
    let data = {};
    for (let f of features) {
      let featureKey = f[0];
      let q = {bbox: event.bbox, zoom: event.zoom};
      if (isFeatureKeyActive(featureKey)) {
        data[featureKey] = {};
        let tmp = this.getSchoolQuery(q, this.state.featureSetting[featureKey]._segments, featureKey);
        data[featureKey].q = tmp;
      }
    }
    // console.log(`featureKey:${featureKey}; serviceName:${serviceName}; menuItem:${this.state.menuItem}; ${this.state.featureSetting[featureKey].val}`);
    requestStdFn('findMapSchools', data)
      .then(ret => {
        this.gotSchoolsResult(features, ret);
      })
      .catch(err => {
        console.log(err);
        return;
      });
  }
  doSearch({event, featureKey, serviceName, cbFnName}) {
    let isFeatureKeyActive = featureKey => {
      if (this.state.menuItem == 'all') {
        return true;
      }
      return (
        this.state.featureSetting && this.state.featureSetting[featureKey] && this.state.featureSetting[featureKey].val
      );
    };
    let menuItem = this.state.menuItem;
    if (isFeatureKeyActive(featureKey)) {
      // console.log(`featureKey:${featureKey}; serviceName:${serviceName}; menuItem:${this.state.menuItem}; ${this.state.featureSetting[featureKey].val}`);
      let q = {bbox: event.bbox};
      q = this.getSchoolQuery(q, this.state.featureSetting[featureKey]._segments, featureKey);
      // console.log('query:',serviceName,q,this.state.featureSetting[featureKey]._segments,featureKey);
      // NOTE: univ/colege use rmpost insted of stdfunc
      // if(serviceName == 'university' || serviceName == 'college'){
      //   return this.getUniversityData({q,menuItem,cbFnName,featureKey});
      // } else {
      requestStdFn(serviceName, q)
        .then(ret => {
          this[cbFnName](ret);
        })
        .catch(err => {
          console.log(err);
          return;
        });

      // }
    } else {
      this[cbFnName]([]);
    }
  }
  flashZoomIn() {
    this.zoomAlertCount++;
    if (this.zoomAlertCount > 4) {
      return;
    }
    clearTimeout(this.zoomTimer);
    this.zoomTimer = setTimeout(() => {
      this.flashMessage('Please zoom in to view school');
    }, 800);
  }
  regionChanged(event, map) {
    //console.log(feature);
    // console.log("regionChanged zoom:" + event.zoom);
    super.regionChanged(event, map);
    if (!this.featureOn || !feature.school) return;
    // clear off map view schools
    if (event && event.bbox) {
      let schs = this.state.schs;
      let bbox = event.bbox;
      for (let k in schs) {
        if (schs[k]) {
          // let sch = schs[k];
          if (!schs[k].loc) {
            schs[k].loc = [];
          }
          // console.log('xxxxx',schs[k])
          let lat = schs[k].lat || schs[k].loc[0];
          let lng = schs[k].lng || schs[k].loc[1];
          if (bbox[0] <= lng && lng <= bbox[2] && bbox[1] <= lat && lat <= bbox[3]) {
            // console.log('sch is in bbox',bbox,schs[k].lat,schs[k].lng)
          } else {
            // console.log('sch is not in bbox',bbox,schs[k].lat,schs[k].lng,schs[k])
            delete schs[k];
          }
        }
      }
      this.setState({schs});
    }
    // if (event.zoom < 15){
    //   this.needZoomIn = true;
    //   // if (!this.needZoomIn){
    //   this.setState({schs:null});
    //   // }
    //   this.flashZoomIn()
    //   return;
    // }
    // this.setState({schs:null});
    // event,featureKey,serviceName,cbFnName
    // this.needZoomIn = false;
    this.doSearchSchools({event});
    this.trackOff();
  }
  doSearchSchools({event}) {
    this.doSearchNew({event});
    // this.doSearch({event,
    //   featureKey:'university',
    //   serviceName:'findUniversities',
    //   cbFnName:'gotUniversity'});
    // this.doSearch({event,
    //   featureKey:'college',
    //   serviceName:'findUniversities',
    //   cbFnName:'gotUniversity'});
    // this.doSearch({event,
    //   featureKey:'privateSchool',
    //   serviceName:'findPrivateSchools',
    //   cbFnName:'gotPrivateSchools'});
    // this.doSearch({event,
    //   featureKey:'school',
    //   serviceName:'findSchools',
    //   cbFnName:'gotSchools'});
  }
  onOffView() {
    // on/off setting to show on map
    return {
      icon: 'rm-school', //'graduation-cap',//'rmcat-school',
      iconSize: 24,
      name: l10n('School'),
      toggleOnOff: (p, silent) => {
        this.toggleOnOff(p, silent);
      },
      on: feature.school && this.featureOn, //&& !this.needZoomIn
    };
  }
  // silent means dont show panel, but feature is on
  // TODO: remove onpress
  toggleOnOff(onOff, silent = false) {
    // if (this.needZoomIn){
    //   this.flashMessage("Please zoom in")
    // }
    // will set to !featureOn
    let realOnOff = !this.featureOn;
    if (onOff !== null) {
      if (onOff == 'off') {
        realOnOff = false;
      } else {
        realOnOff = true;
      }
    }
    // console.log('++++++toggleOnOff in mapSchool',realOnOff)
    this.featureOn = realOnOff;
    if (realOnOff) {
      this.regOnPress();
    } else {
      // removeOnPressFeature
      this.map.removeOnPressFeature(this);
    }
    if (this.featureOn) {
      this.setState({showPanel: !silent});
      // TODO: this will cause other featue reload too,
      this.map.onRegionChangeComplete();
    } else {
      this.setState({sch: null});
    }
  }
  resetFilterToAll() {
    // console.log('reset filter',gFeatureSetting)
    let tmpFeatureSetting = deepAssign(gFeatureSetting);
    this.setState({featureSetting: tmpFeatureSetting});
  }
  // kTop in ['all','school','catholic','privateSchool']
  setMainValue(kTop) {
    let menuItem = this.state.menuItem;
    let stateFeatureSetting = this.state.featureSetting;
    // console.log(kTop,menuItem)
    if (kTop == menuItem) {
      return;
    }
    if (kTop == 'all') {
      // all clickable = false
      return this.setState({menuItem: 'all'}, () => {
        this.resetFilterToAll();
      });
    } else {
      // set clickable to true
      let _segments = stateFeatureSetting[kTop]._segments || [];
      _segments.forEach(obj => {
        obj.clickable = true;
        obj.val = false;
        if (obj.l && obj.l.length) {
          obj.l.forEach(subType => {
            // console.log(subType)
            stateFeatureSetting[subType].clickable = true;
            stateFeatureSetting[subType].val = false;
          });
        }
      });
    }
    // TODO: set clickable
    stateFeatureSetting[kTop].checked = true;
    stateFeatureSetting[kTop].val = true;
    stateFeatureSetting[menuItem].checked = false;
    stateFeatureSetting[menuItem].val = false;
    this.setState({menuItem: kTop, featureSetting: stateFeatureSetting}, () => {
      // console.log(this.state.featureSetting)
    });
  }
  renderFeaturePanel() {
    if (!this.featureOn) return;
    if (!this.state.showPanel) return;
    let self = this;
    var btns = [];
    // console.log(this.state.featureSetting)
    // return
    this.state.featureSetting._main._segments.forEach((kTop, idx) => {
      let vTop = self.state.featureSetting[kTop];
      // console.log("Sch vTop:"+vTop.nm);
      btns.push({
        clickable: vTop.clickable,
        k: kTop,
        n: vTop.nm,
        color: vTop.checked ? 'black' : '#777',
        act: () => {
          // NOTE: all vs pub/cath/pri
          if (idx == 1 || idx == 0) {
            self.flatListRef.scrollToOffset({offset: 0});
          } else {
            self.flatListRef.scrollToIndex({animated: true, index: idx});
          }
          self.setMainValue(kTop);
        },
        borderStyle: {
          borderWidth: 1,
          borderColor: vTop.checked ? '#81C77E' : '#e8e8e8',
          // backgroundColor: 'blue',
          paddingTop: 5,
          paddingLeft: 8,
          paddingRight: 8,
          paddingBottom: 5,
          minWidth: 50,
        },
        textStyle: {
          fontSize: 13,
        },
        style: {
          marginRight: 10,
          // backgroundColor:'green'
        },
        width: 'auto',
        checked: false, //vTop.checked,
      });
    });
    // if (this.state.menuItem && this.featureSetting[this.state.menuItem]){
    //   return this.renderModelMenu();
    // }
    // return this.bottomButtons([],btns);
    // TODO: use <FlatList
    //   data={DATA}
    //   renderItem={bottomButton}
    //   keyExtractor={item => item.id}
    // />
    return (
      <View
        key={'featurePanel'}
        style={styles.featurePanel}>
        <FlatList
          ref={ref => {
            this.flatListRef = ref;
          }}
          // style={styles.mainSelector}
          style={{flex: 1}}
          contentContainerStyle={styles.mainSelector}
          bounces={false}
          showsVerticalScrollIndicator={false}
          showsHorizontalScrollIndicator={false}
          data={btns}
          contentInset={{right: 0, top: 0, left: 0, bottom: 3}}
          horizontal={true}
          renderItem={({item, index, separators}) => {
            return this.bottomButton(item);
          }}
          keyExtractor={btn => {
            return btn.k;
          }}></FlatList>
        {/* <View style={styles.bottomBar} key={'mapBottomBar'}>
        {this.bottomButtons([],btns)}
      </View> */}
        <View style={{flex: 5}}>{this.renderModelMenu()}</View>
        <View
          style={{
            marginBottom: 15,
            marginTop: 20,
            marginLeft: 10,
            marginRight: 10,
            paddingTop: 5,
            paddingLeft: 5,
            paddingBottom: 5,
            flexDirection: 'row',
            borderRadius: 2,
            backgroundColor: 'rgb(255, 255, 221)',
          }}>
          <Icon
            name='lightbulb-o'
            size={13}
            color='#e03131'
            style={[]}
            hitSlop={{top: 0, bottom: 0, left: 0, right: 0}}
            onPress={() => {}}
          />
          <Text style={{paddingLeft: 10, fontSize: 11, color: '#666'}}>
            {l10n('Long press empty space on map to show nearby schools')}
          </Text>
        </View>
        {this.renderFeaturePanelClose(
          () => {
            this.closePanelAndSearch();
          },
          () => {
            this.setState({showPanel: false, showPane: false});
            this.map.toggleBackDrop('off');
          },
        )}
        <RMBottomBar />
      </View>
    );
  }
  closePanelAndSearch() {
    // eventEmitter.emit("map.regionChange",{});
    let tmpState = {showPanel: false, showPane: false, schs: {}, tracksViewChanges: true};
    this.setState(tmpState, () => {
      this.map.onRegionChangeComplete();
    });
    this.map.toggleBackDrop('off');
    this.trackOff();
  }
  renderFeatureNoPropDisplay() {
    return null;
    return (
      <View
        key={'schoolRetCount'}
        style={{
          flexDirection: 'row',
          justifyContent: 'flex-end',
          marginTop: 13,
          flex: 10,
          marginLeft: 10,
          marginRight: 10,
        }}>
        <Text style={{color: '#666', fontSize: 15, fontWeight: 'normal'}}>
          {(Object.keys(this.state.schs).length || 0) + ' ' + l10n('Schools')}{' '}
        </Text>
        {/* <Icon name="rmplus" size={21} color="black" style={[styles.navBarButton]}   onPress={()=>{this.createStigma()}} /> */}
      </View>
    );
  }
  renderBottomBar() {
    return null;
    if (!this.featureOn) return;
    let self = this;
    let btns = [];
    featureSetting._main._segments.forEach(kTop => {
      //console.log("Sch:"+kTop);
      let vTop = self.featureSetting[kTop];
      btns.push({
        k: kTop,
        n: vTop.nm,
        color: vTop.val ? 'black' : '#777',
        icn: 'rmcat-school',
        act: () => {
          self.setState({menuItem: kTop});
        },
      });
    });
    return this.bottomButtons([], btns);
  }
  toggleFilterOnOff(k) {
    // console.log(k);
    let stateFeatureSetting = this.state.featureSetting;
    // console.log(k,'before: ',stateFeatureSetting[k])
    stateFeatureSetting[k].val = !stateFeatureSetting[k].val;
    stateFeatureSetting[k].checked = !stateFeatureSetting[k].checked;
    this.setState({featureSetting: stateFeatureSetting}, () => {
      // console.log('after: ',stateFeatureSetting[k])
    });
  }
  // featureChanged(){
  //   this.setState({menuItem:null});
  //   this.map.onRegionChangeComplete();
  // }
  renderBound(block, bnid) {
    // holes https://github.com/react-native-community/react-native-maps/blob/master/docs/polygon.md
    let outer = getCoordinates(block.bn);
    let holes = getMultipleCoordinates(block.holes);
    // console.log(bnid);
    // console.log(outer);
    // console.log(holes);
    return (
      <Polygon
        key={'schBndId' + bnid}
        coordinates={outer}
        holes={holes}
        strokeWidth={2}
        strokeColor='#FF3333'
        fillColor='rgba(255,100,100,0.2)'
      />
    );
  }
  renderBounds(bns, view) {
    if (bns._view) {
      return assignArray(view, bns._view);
    }
    // build polygon block with outside and inside boundaries
    let myView = [];
    let block = {}; // bn._id as key
    bns.forEach(bn => {
      if (bn.exclude == 1) {
        let b = block[bn.exbnid];
        if (!b) {
          block[bn.exbinid] = b = {holes: []};
        }
        b.holes.push(bn.bn);
      } else {
        let b = block[bn.bnid];
        if (!b) {
          block[bn.bnid] = b = {holes: []};
        }
        b.bn = bn.bn;
      }
    });
    // console.log(block);
    for (let bnid in block) {
      myView.push(this.renderBound(block[bnid], bnid));
    }
    bns._view = myView;
    assignArray(view, myView);
  }
  isValidSchoolLoc(sch = {}) {
    let hasLatLng = (sch.lat || sch.lng) != null;
    let hasLoc = sch.loc && sch.loc[0];
    return hasLatLng || hasLoc;
    // (sch.lat || sch.loc)
  }
  renderMarker(sch = {}) {
    if (!this.isValidSchoolLoc(sch)) {
      console.log('Bad School, can not render:', sch);
      return;
    }
    let loc = {longitude: sch.lng || sch.loc[1], latitude: sch.lat || sch.loc[0]};
    let schImg = schoolRed;
    let bkcolor = '#303030';
    let marker = null;
    let zIndex = 10;
    if (this.state.sch && sch._id == this.state.sch._id) {
      // current school
      schImg = sch.private ? pSchoolOrange0 : schoolOrange0;
      marker = Platform.OS == 'ios';
      // marker = true;
      bkcolor = '#5d5d5d';
      zIndex = 11;
    } else if (sch.private) {
      schImg = sch.hot ? pSchoolRed : pSchoolGreen;
    } else {
      schImg = schoolGreen;
    }
    //  {/* {marker} */}
    return (
      <Marker
        stopPropagation={true}
        tracksViewChanges={this.state.tracksViewChanges}
        style={{zIndex}}
        key={'' + sch._id + sch.nm}
        onPress={this.fnSelSch(sch)}
        coordinate={loc}
        //icon={schImg}
      >
        {marker ? (
          <IconText
            icon={schImg}
            text={sch.ssnm || sch.snm || sch.nm}
            backgroundColor={bkcolor}
          />
        ) : (
          // <PriceMarker
          //   isTopUp={false}
          //   offMarket={false}
          //   isSelected={false}
          //   amount={sch.snm||sch.nm}
          //   />
          <Image
            resizeMode='stretch'
            style={styles.marker}
            source={schImg}
          />
        )}
      </Marker>
    );
  }
  renderOnMap() {
    if (!this.featureOn) return null;
    let self = this;
    let view = [];
    if (this.state.loc) {
      view.push(
        <Marker
          tracksViewChanges={this.state.tracksViewChanges}
          key={'SchSearchMarker'}
          coordinate={{longitude: this.state.loc.lng, latitude: this.state.loc.lat}}></Marker>,
      );
    }
    // console.log('render school on map')
    let curSch = null;
    if (this.state.sch) {
      curSch = this.state.sch;
      view.push(this.renderMarker(curSch));
    }
    if (this.state.schs) {
      for (let schId in this.state.schs) {
        if (!curSch || schId != curSch._id) {
          let sch = this.state.schs[schId];
          view.push(this.renderMarker(sch));
        }
      }
    }
    if (this.state.sch && this.state.sch.cBns) {
      this.renderBounds(this.state.sch.cBns, view);
    }
    if (view.length > 0) {
      return view;
    }
    return null;
  }
  renderGrade(bn = {}, i = 0, opt = {}, sch) {
    // TODO: use backend Str generate
    if (sch && sch.private) {
      return (
        <View style={{flexDirection: 'row'}}>
          <Text
            style={opt.style}
            key={'schGrade' + i}>
            {sch.grd} {l10n('Grades')}
          </Text>
        </View>
      );
    }
    if (sch && Object.keys(bn) == 0) {
      bn = sch;
    }
    return (
      <View style={{flexDirection: 'row'}}>
        <Text
          style={opt.style}
          key={'schGrade' + i}>
          {gfConvert(bn.gf)}-{bn.gt} {l10n('Grades')}
        </Text>
        {opt.showFI && !!bn.fi && (
          <Text
            key={'schFi' + i}
            style={opt.style}>
            {' '}
            {l10n('French Immersion')}
          </Text>
        )}
        {!!bn.extStr && (
          <Text
            key={'schBnExtra'}
            style={opt.style}>
            {' '}
            ({bn.extStr})
          </Text>
        )}
        {!!bn.i && bn.i.length > 1 && (
          <Text
            key={'schBnI'}
            style={opt.style}>
            {' '}
            ({bn.i.length})
          </Text>
        )}
      </View>
    );
  }
  renderSchoolTypes(sch = {}, grades = []) {
    function singleViewObj(key, text, style, textStyle) {
      return {key, text, style, textStyle};
    }
    function singleView(opt = {}) {
      let {key, text, style, textStyle} = opt;
      return (
        <View
          style={[styles.schGrades, style]}
          key={key}>
          <Text style={[{fontSize: 11}, textStyle]}>{text}</Text>
        </View>
      );
    }
    if (sch.tags && sch.tags.length) {
      sch.tags.forEach(tag => {
        let style = {backgroundColor: tag.color || 'rgb(241, 248, 236)'};
        if (grades.length) {
          style.marginLeft = 6;
        }
        let textStyle = {color: tag.textColor || 'rgb(92, 184, 92)'};
        grades.push(singleViewObj('sch' + sch._id + tag.nm, tag.nm, style, textStyle));
      });
      // grades.push(singleViewObj('sch1234','test1',{},{}))
      // grades.push(singleViewObj('sch1235','test2',{},{}))
      // grades.push(singleViewObj('sch1236','test3',{},{}))
      // return grades;
    } else {
      for (let i in schTypeMapping) {
        if (sch[i]) {
          let style = {backgroundColor: schTypeMapping[i].color};
          if (grades.length) {
            style.marginLeft = 6;
          }
          let textStyle = {color: schTypeMapping[i].textColor};
          grades.push(singleViewObj('sch' + sch._id + 'i' + i, l10n(schTypeMapping[i].nm), style, textStyle));
        }
      }
    }
    // console.log(grades)
    return (
      <FlatList
        ref={ref => {
          this.schoolTagsRef = ref;
        }}
        // style={styles.mainSelector}
        style={{flex: 1}}
        contentContainerStyle={{}}
        bounces={false}
        showsVerticalScrollIndicator={false}
        showsHorizontalScrollIndicator={false}
        data={grades}
        contentInset={{right: 0, top: 0, left: 0, bottom: 0}}
        horizontal={true}
        renderItem={({item, index, separators}) => {
          return singleView(item);
        }}
        keyExtractor={btn => {
          return btn.key;
        }}></FlatList>
    );
    // return grades;
  }
  renderSchoolItem(sch, bnIdx, bn, view, opt = {}) {
    // console.log('+++sch is:',sch)
    let onPressSchoolListItem = this.fnSelSchBoundary(sch, bnIdx).bind(this);
    let keyFacts = this.getSchoolKeyFacts(sch);
    if (opt.marker) {
      onPressSchoolListItem = this.fnSelSch(sch).bind(this);
    }
    view.push(
      <View
        key={'schList' + bn.bnid + ':' + sch._id}
        style={{
          // paddingLeft:10,
          borderBottomColor: '#f1f1f1',
          borderBottomWidth: 1,
        }}>
        <TouchableOpacity onPress={onPressSchoolListItem}>
          <View style={[styles.schoolNameBar, styles.schoolNameWrapper]}>
            <Text
              numberOfLines={1}
              style={styles.schoolName}>
              {clearSchoolName(sch)}
            </Text>
            {/* <View style={{position:'absolute',right:0,top:0}}>
            <Icon name="rmclose"
              size={20}
              color="#bbb"
              style={[styles.center]}
              hitSlop={{top: 10, bottom: 10, left: 0, right: 0}}
              // onPress={this.fnSelSchBoundary(sch,bnIdx)}
              />
          </View> */}
          </View>
          <View style={[styles.row, {justifyContent: 'space-between', paddingRight: 10}]}>
            <View style={styles.row}>
              <Text style={styles.schoolAddr}>{sch.addr}</Text>
              {sch.dist && (
                <Text style={{color: '#F0951C', fontSize: 10, paddingLeft: 10}}>{distance4display(sch.dist)}</Text>
              )}
            </View>
            {/* {this.renderGrade(sch.bns[bnIdx],sch._id,{style:styles.schoolAddr})} */}
          </View>
          <View style={[styles.gradesWrapper, {marginTop: -3, paddingBottom: 10, paddingLeft: 10}]}>
            {this.renderSchoolTypes(bn.useSch ? sch : bn)}
          </View>
        </TouchableOpacity>
        {keyFacts && (
          <ScrollView 
            horizontal={true}
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.keyFactsScrollView}
            bounces={false}>
            <TouchableOpacity onPress={onPressSchoolListItem} key={'keyFactsItem'}>
              <View style={[styles.keyFactsContainer, {paddingBottom: 10}]} key={'kfInList'}>
                {keyFacts}
              </View>
            </TouchableOpacity>
        </ScrollView>
        )}
      </View>,
    );
  }
  /**
   * 获取学校的关键信息数据
   * @param {Object} sch - 学校对象
   * @returns {Array} 返回处理后的关键信息数组
   */
  getSchoolKeyFacts(sch) {
    if(sch.university || sch.college){
      return null;
    }
    let keyFacts = [];
    if (sch.keyFacts && Array.isArray(sch.keyFacts) && sch.keyFacts.length > 0) {
      let totalCount = sch.keyFacts.length;
      keyFacts = sch.keyFacts.map((item, index) => this.keyFact(item, index, totalCount));
    } else if (!sch.private) {
      keyFacts.push(this.keyFact({'key': l10n('Rank'), 'val': l10n('N/A')}, 'norank', 1));
    } else {
      keyFacts.push(this.keyFact({'key': l10n('No Result'), 'val': ''}, 'noneKeyFact', 1));
    }
    return keyFacts;
  }

  keyFact(item, index, totalCount = 1) {
    const handleAlert = (alertType) => {
      Alert.alert(l10n('AI Rating & Ranking'), l10n("RealMaster uses Al to estimate a school's rating and ranking. This is a reference point only. Contact an agent for better insight into a full report of the school."));
    };
    const containerWidth = width - 20;
    // 统一计算宽度和最小宽度
    const getKeyFactDimensions = () => {
      if (totalCount > 2) {
        const isLastItem = index === totalCount - 1;
        if (isLastItem) {
          // 最后一个项目
          return [
            styles.keyFactStart,
            {
              maxWidth: 110,
              minWidth: containerWidth * 0.14,
            }
          ];
        } else {
          // 其余项目
          const normalWidth = containerWidth * 0.43;
          return [
            styles.keyFactStart,
            {
              width: normalWidth,
              minWidth: normalWidth,
            }
          ];
        }
      }
      // 只有一个时占满宽度
      return [
        styles.keyFactCenter,
        {
          width: containerWidth,
          minWidth: containerWidth,
        },
      ];
    };

    const keyFactStyle = getKeyFactDimensions();

    return (
      <View key={index} style={keyFactStyle}>
        <View style={styles.keyFactHeader}>
          <Text 
            style={styles.boldValue}
            numberOfLines={1}
            ellipsizeMode="tail"
          >
            {item.val}
          </Text>
          {(item.valTotal != null && item.valTotal != 0) && (
            <View style={styles.totalContainer}>
              <Text style={styles.totalText}>/{item.valTotal}</Text>
              {(item.diffRank != null && item.diffRank != 0) && (
                <RmIcon 
                  name={item.diffRank > 0 ? 'long-arrow-down' : 'long-arrow-up'} 
                  color={item.diffRank > 0 ? "#E03131" : "#2fa800"}
                  style={styles.arrowIcon}
                />
              )}
            </View>
          )}
          {item.isStyle2 && item.rating && (
            <View style={styles.ratingContainer}>
              <Text style={styles.separatorText}> | </Text>
              <Text style={styles.boldValue}>{item.rating}</Text>
            </View>
          )}
        </View>
        <View style={styles.keyFactDetail}>
          <Text style={styles.keyText}>
            {item.key}{item.grade ? `/${item.grade}` : ''}
          </Text>
          {item.alert && (
            <TouchableOpacity 
              onPress={() => handleAlert(item.alert)}
              style={styles.alertButton}
            >
              <RmIcon name="question-circle-o" size={14} color="#777" />
            </TouchableOpacity>
          )}
        </View>
      </View>
    );
  }
  featureName() {
    return l10n('School');
  }
  onPressMenu() {
    return l10n('Search Home Schools');
  }
  renderModelMenu() {
    let self = this;
    let menuSegments = this.state.featureSetting[this.state.menuItem]._segments || [];
    let menuView = [];
    // when this feature is on
    menuSegments.forEach((menuOpt, i) => {
      let btns = [];
      menuOpt.l.forEach(k => {
        // access first level obj
        v = self.state.featureSetting[k];
        // console.log("Sch v:"+v.nm);
        btns.push({
          k: k,
          n: v.nm,
          color: v.clickable ? '#777' : '#f1f1f1',
          disabled: !v.clickable,
          borderStyle: {
            borderWidth: 1,
            borderColor: v.checked ? '#81C77E' : '#e8e8e8',
            backgroundColor: 'transparent',
            paddingTop: 5,
            paddingLeft: 8,
            paddingRight: 8,
            paddingBottom: 5,
          },
          style: {
            marginRight: 10,
          },
          textStyle: {
            fontSize: 13,
          },
          width: 'auto',
          checked: false, //v.checked,
          act: () => {
            if (v.clickable) {
              self.toggleFilterOnOff(k);
            } else {
              console.log('ignore unclickable');
            }
          },
        });
      });
      menuView.push(
        <View
          key={'schMenu' + i}
          style={{
            paddingLeft: 10,
            marginBottom: 10,
            // marginTop:5,
            position: 'relative',
          }}>
          <View style={{}}>
            <Text
              style={{
                fontSize: 15,
                fontWeight: 'bold',
                color: menuOpt.clickable ? 'black' : '#777',
              }}>
              {l10n(menuOpt.nm)}
            </Text>
          </View>
          <View style={styles.modelMenuRow}>{self.bottomButtons([], btns)}</View>
        </View>,
      );
    });
    return menuView;
    let v = self.featureSetting[this.state.menuItem];
    let btns = [
      {
        k: this.state.menuItem,
        n: v.nm,
        color: v.val ? '#e33' : '#777',
        icn: 'rmcat-school',
        act: () => {
          self.toggleFilterOnOff(this.state.menuItem);
        },
      },
      {
        k: 'ok',
        n: 'OK',
        color: '#333',
        act: () => {
          self.featureChanged();
        },
      },
    ];
    menuView.push(
      <View
        key={'schMenu_b'}
        style={styles.modelMenuRow}>
        {self.bottomButtons([], btns)}
      </View>,
    );
    return (
      <View
        key={'schMenu'}
        style={styles.modelMenu}>
        {menuView}
        <RMBottomBar />
      </View>
    );
  }
  renderModal() {
    if (!this.featureOn) return null;
    // show home schools
    if (this.state.schList) {
      // return;
      let view = [];
      this.state.schList.forEach(sch => {
        sch.bns.forEach((bn, i) => {
          this.renderSchoolItem(sch, i, bn, view);
        });
      });
      let count = this.state.schList.length;

      return (
        <BottomPaneWhite
          key={'schoolList'}
          height={400}
          title={sprintf(l10n('%d home schools found'), count)}
          title1b={l10n('Long press on map to set location')}
          cbClose={() => {
            this.setState({schList: null});
          }}>
          <ScrollView style={{marginBottom: 20}}>{view}</ScrollView>
        </BottomPaneWhite>
      );
    }
    // show menu?
    // if (this.state.menuItem && this.featureSetting[this.state.menuItem]){
    //   return this.renderModelMenu();
    // }
    // show school pane(detail)
    if (this.state.showPane) {
      let sch = this.state.sch;
      if (!sch) {
        return null;
      }
      let grades = this.renderSchoolTypes(sch, []);
      let keyFacts = this.getSchoolKeyFacts(sch);
      let keyFactLabel = l10n('Key Facts');
      let kfExtra = [];
      if (sch.private) kfExtra.push(l10n('Private School'));
      if (!this.state.sch.cBns) kfExtra.push(l10n('No boundary data'));
      if (kfExtra.length > 0) {
        kfExtra = '(' + kfExtra.join(', ') + ')';
      }
      let height = 230;
      if (sch.university || sch.college) {
        height = 125;
      }
      return (
        <BottomPaneWhite
          key={'schoolDetailModal'}
          height={height}
          title={sch.nm}
          title1b={(sch.addr || 'No addr') + ', ' + (sch.city || '')}
          onPress={this.fnSelSchDetail(sch)}
          cbClose={this.fnClose()}>
          {/* <View key={'SchoolPane'} style={[styles.bottomPaneWrapper,{height:height}]}> */}
          {/* <TouchableOpacity onPress={this.fnSelSchDetail(sch)}> */}
          {/* <View style={[styles.schoolNameBar]}>
            <View style={styles.schoolNameWrapper}>
              <Text numberOfLines={2} key={'schNm'} style={styles.schoolName}>{sch.nm}</Text>
            </View>
            <TouchableOpacity
              onPress={this.fnClose()}
              style={[styles.center,styles.closeButtonBns,{height:36,width:36,top:2,right:2}]}
              >
              <Icon name="rmclose"
                size={20}
                color="#ddd"
                style={[styles.center]}
                hitSlop={{top: 10, bottom: 10, left: 0, right: 0}}
                />
            </TouchableOpacity>
          </View> */}
          <View
            key={'schDetail'}
            style={{marginTop: 10}}>
            {/* <View>
              <Text key={'schAddr'} style={styles.schoolAddr}>{sch.addr||'No addr'} {sch.city||''}</Text>
              <Text key={'schG'}>{gfConvert(sch.gf)}-{sch.gt} {l10n('Grades')}</Text>
            </View>          */}
            {/* <TouchableWithoutFeedback> */}
            <View
              key={'schGrades'}
              style={{flexDirection: 'row', paddingLeft: 10, width: '100%'}}>
              {/* <View style={[styles.gradesWrapper,{backgroundColor:'red'}]}> */}
              {grades}
              {/* </View> */}
            </View>
            {/* </TouchableWithoutFeedback> */}
            {!(sch.university || sch.college) && (
              <View style={{borderTopColor: '#f1f1f1', borderTopWidth: 1, marginTop: 15}}>
                <TouchableOpacity
                  onPress={this.fnSelSchDetail(sch)}
                  key={'schKeyFacts'}>
                  <View
                    style={styles.schoolNameWrapper}
                    key={'kfWrapper'}>
                    <Text
                      key={'kf'}
                      style={{fontSize:15,fontWeight: 'bold'}}>
                      {keyFactLabel}
                    </Text>
                    {!!kfExtra.length && (
                      <Text
                        key={'kf2'}
                        style={[styles.schoolName, {paddingLeft: 5, fontSize: 12, color: '#777'}]}>
                        {kfExtra}
                      </Text>
                    )}
                  </View>
                </TouchableOpacity>
                <ScrollView 
                  horizontal={true}
                  showsHorizontalScrollIndicator={false}
                  contentContainerStyle={styles.keyFactsScrollView}
                  bounces={false}>
                  <TouchableOpacity onPress={this.fnSelSchDetail(sch)} key={'keyFactsCont'}>
                    <View style={styles.keyFactsContainer} key={'kfInDetail'}>
                      {keyFacts}
                    </View>
                  </TouchableOpacity>
                </ScrollView>
              </View>
            )}
          </View>
          {/* </TouchableOpacity> */}
          {/* </View> */}
        </BottomPaneWhite>
      );
    }
    // show bns select
    if (this.state.bns) {
      let bnsView = [];
      this.state.bns.forEach((bn, i) => {
        bnsView.push(
          <View
            key={'schBnd' + i}
            style={styles.boundaryItem}>
            <TouchableOpacity onPress={this.fnSelBoundary(i)}>
              <View style={{flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between'}}>
                {/* <View style={{width:width/2, overflow:'hidden'}}>
                  <Text key={'schNm'+i} style={{}}>{this.state.sch.snm}</Text>
                </View> */}
                {this.renderGrade(bn.useSch ? sch : bn, i, {showFI: true})}
              </View>
            </TouchableOpacity>
          </View>,
        );
      });
      return (
        <View
          key={'SchoolBoundaries'}
          style={[styles.bottomPaneWrapper, {height: 200}]}>
          <View style={[styles.schoolNameBar]}>
            <View style={styles.schoolNameWrapper}>
              <Text
                key={'schSelBnd'}
                style={styles.schoolName}>
                {l10n('Select Boundary')}
              </Text>
            </View>
            <Icon
              name='rmclose'
              size={20}
              color='#ddd'
              style={[styles.center, styles.closeButtonBns]}
              hitSlop={{top: 10, bottom: 10, left: 0, right: 0}}
              onPress={this.fnClose()}
            />
          </View>
          <View
            style={{
              borderBottomColor: '#f1f1f1',
              borderBottomWidth: 1,
              paddingLeft: 10,
              paddingBottom: 10,
              marginTop: -7,
            }}>
            <Text style={{fontSize: 12}}>{this.state.sch.snm}</Text>
          </View>
          <ScrollView
            contentContainerStyle={styles.contentContainer}
            key={'schBnds'}>
            {bnsView}
          </ScrollView>
        </View>
      );
    }
    // show map school list
    if (this.state.schs && !this.state.propFeatureOn) {
      //Object.keys(this.state.schs).length &&
      let view = [];
      Object.values(this.state.schs).forEach(sch => {
        // console.log(sch)
        if (!sch.bns) {
          sch.bns = [];
        }
        if (sch.bns.length) {
          sch.bns.forEach((bn, i) => {
            this.renderSchoolItem(sch, i, bn, view, {marker: 1});
          });
        } else {
          this.renderSchoolItem(sch, 0, {useSch: true}, view, {marker: 1});
        }
      });
      let count = Object.keys(this.state.schs).length;
      let panShowValue= 110;
      if (Platform.OS === 'android') {
        panShowValue = 125;
      }
      // console.log('state schs:',this.state.schs,' view:',view)
      let base = -42;
      let left = (
        <View
          style={{
            zIndex: 105,
            //backgroundColor:'blue',
            marginTop: base + 5,
          }}>
          <View
            style={{
              // backgroundColor:'blue',
              paddingLeft: 15,
              width: 150,
              overflow: 'visible',
            }}>
            {/* sprintf(l10n("%d schools in map"),count) */}
            <Text
              numberOfLines={1}
              style={{fontSize: 15, marginTop: 0}}>
              {count}/{this.state.schCnt || count} {l10n('Results')}
            </Text>
          </View>
          <View style={{paddingLeft: 15, paddingBottom: 7}}>
            <Text style={{fontSize: 11, color: '#777'}}>{l10n('Long press on map to set location')}</Text>
          </View>
        </View>
      );
      let right = (
        <TouchableOpacity
          style={{
            height: 40,
            zIndex: 120,
            paddingLeft: 3,
            paddingRight: 15,
            paddingTop: 3,
            marginTop: base - 8,
            paddingBottom: 3,
            // backgroundColor:'blue',
            // alignItems:'flex-end'
          }}
          onPress={e => {
            // console.log(this.listDragPan.current)
            this.listDragPan.current.toggleListView(e);
          }}>
          <View>
            <Text style={{marginTop: 10, color: '#3899EC', fontSize: 16, fontWeight: 'bold'}}>{l10n('LIST')}</Text>
          </View>
        </TouchableOpacity>
      );
      return (
        <ListDragPan
          // left={left}
          // right={right}
          // list={[]}
          ref={this.listDragPan}
          id={'schoolList'}
          bottom={0}
          PAN_SHOW_VALUE={panShowValue}
          enableLayerMenuControl={true}
          layerMenuMap={this.map}
          layerMenuStyle={{marginTop: 10, marginBottom:25}}
          renderTopContent={() => (
            <View
              key="schoolTopContent"
              style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                zIndex: 101,
                width: '100%',
              }}>
              {left}
              {right}
            </View>
          )}
          onGrant={e => {
            this.setState({scroll: false});
            // console.log('onGrant',e)
          }}
          onRelease={e => {
            this.setState({scroll: true});
          }}
          closeListView={() => {
            this.setState({schList: null});
          }}>
          <ScrollView
            showsVerticalScrollIndicator={false}
            scrollEnabled={this.state.scroll}
            style={{
              // zIndex:105,
              // paddingLeft:10,
              // backgroundColor:'lightblue',
              // height:300,
              width: '100%',
            }}>
            {/* <View style={{height:100}}></View> */}
            {view}
            <View style={{height: 100}}></View>
          </ScrollView>
        </ListDragPan>
      );
    }
    return null;
  }
  fnClose() {
    let self = this;
    return () => {
      self.setState({bns: null, showPane: null, tracksViewChanges: true});
      this.trackOff();
    };
  }
  fnSelSchBoundary(sch, bnIndex) {
    return () => {
      // console.log(sch._id + ":" + bnIndex);
      this.closeOtherModals();
      this.getBound(sch, bnIndex);
      this.setState({bns: null, schList: null});
      return;
    };
  }
  fnSelBoundary(bnIndex) {
    return () => {
      if (this.state.sch) {
        // console.log(this.state.sch._id + ":" + bnIndex);
        this.getBound(this.state.sch, bnIndex);
        this.setState({bns: null});
        return;
      }
      console.log('No sch for bnd');
    };
  }
  fnSelSchDetail(sch) {
    // console.log('fnSelSchDetail',sch._id)
    return () => {
      var self = this;
      let isLoggedIn = appConfigIns.getAppConfig('isLoggedIn');
      if (!isLoggedIn) {
        return eventEmitter.emit('app.closeAndRedirectRoot', {
          url: serverDomainIns.getFullUrl('/1.5/user/login'),
        });
      }
      var url = '/1.5/school/inapp/detail?id=' + sch._id;
      if (sch.private) url = '/privateSchools/detail/' + sch._id + '?src=nativeMap';
      if (sch.university || sch.college) url = '/1.5/school/university/detail/' + sch._id + '?src=nativeMap';
      var cb = val => {
        // console.log('mapsearch school cb val: ',val);
        if (val == ':cancel') {
          // console.log('canceled');
          return;
        }
        if (/^redirect|^cmd-redirect:/.test(val)) {
          // return window.location = val.split('redirect:')[1]
          var url = val.split('redirect:')[1];
          // console.log('close and redirect root: '+url)
          return self.closePopup(url);
          // return window.location = url;
        }
        try {
          // var val = 'loc=43.5723199141038,-79.5785565078259&zoom=15&saletp=lease';
          // console.log('school cb val:',val)
          var d = urlParamToObject(val);
          // Alert.alert(JSON.stringify(d));
          // window.bus.$emit('school-prop', d);
          // self.searchSchoolProp(d);
          var data = {lat: d.loc[0], lng: d.loc[1], zoom: d.zoom, saletp: d.saletp};
          eventEmitter.emit('map.setFilterVal', data);
          self.map.setCenterAndZoom(data);
        } catch (e) {
          // console.error(e);
        }
      };
      var opt = {
        hide: false,
        sel: '#callBackString',
        tp: 'pageContent',
        title: l10n('RealMaster'),
        // toolbar:false,
        url: serverDomainIns.getFullUrl(url),
      };
      eventEmitter.emit('app.message', {msg: JSON.stringify(opt), cb: cb});
    };
  }
  // click on map marker
  fnSelSch(sch) {
    return e => {
      if (e.persist) {
        e.persist(); // Avoids warnings relating to https://fb.me/react-event-pooling
      }
      var self = this;
      if (!sch) return false;
      // always reshow the school detail
      // if (this.state.sch && (this.state.sch._id == sch._id) && ((!this.state.sch.bns) || (this.state.sch.bns.length < 2))){
      //   return;
      // }
      this.closeOtherModals();
      // console.log("School Clicked:",sch);
      let state = {sch: sch, bn: null, tracksViewChanges: true, loc: null, searchDialog: null, schList: null};
      this.setState(state);
      if (sch.private) {
        state.bns = null;
        state.showPane = true;
        this.getPrivateSchoolDetail(sch);
      } else if (sch.bns && sch.bns.length > 0) {
        // has bns object
        if (sch.bns.length > 1) {
          // has more than one bns
          state.bns = sch.bns;
          state.showPane = false;
        } else {
          // has only one bns
          // console.log('only one bns')
          state.bns = null;
          state.showPane = true;
          this.getBound(sch, 0);
        }
      } else {
        // no bns
        state.bns = null;
        state.showPane = true;
        this.getBound(sch, -1);
      }
      // console.log(state);
      this.setState(state);
      this.trackOff();
      return false;
    };
  }
  getPrivateSchoolDetail(sch) {
    if (sch) {
      requestStdFn('getPrivateSchool', {schId: sch._id})
        .then(ret => {
          this.gotPrivateSchoolDetail(ret);
        })
        .catch(err => {
          console.log(err);
          return;
        });
    }
  }
  gotPrivateSchoolDetail(sch) {
    if (sch.sch) sch = sch.sch;
    //console.log(sch)
    sch.private = true;
    for (k of ['boarding', 'admIntvw', 'admSSAT']) {
      //console.log(k,sch[k])
      if (sch[k] && sch[k] != 'Yes') {
        delete sch[k];
      }
    }
    this.setState({sch: sch, showPane: true, bns: null, bn: null, tracksViewChanges: true});
    this.trackOff();
  }
  getBound(sch, bnIndex) {
    if (sch && sch._id && !(sch.university || sch.college)) {
      let bn = null;
      if (sch.bns && sch.bns[bnIndex]) {
        bn = sch.bns[bnIndex];
      }
      // console.log('getSchBnd:' , sch._id , bnIndex, bn);
      requestStdFn('getSchBnd', {schId: sch._id, bn: bn, showCurBnTxt: true})
        .then(ret => {
          this.gotBound(ret, bn);
        })
        .catch(err => {
          console.log(err);
          return;
        });


      return;
    }
    // console.log("No school or bnds" + schId + ":" + bnIndex);
    // console.log(sch);
  }
  gotBound(sch, bn) {
    // console.log(sch,bn);
    sch._id = '' + sch._id;
    sch.snm = filterSchoolName(sch);
    sch.ssnm = clearSchoolName(sch);
    this.setState({sch: sch, showPane: true, bns: null, bn: bn, tracksViewChanges: true});
    this.trackOff();
  }
  unmount() {
    super.unmount();
    eventEmitter.removeListener(Constants.EventGetHomeSchools, this.getHomeSchools);
    eventEmitter.removeListener(Constants.EventSet, this.setFeature);
    // eventEmitter.removeListener(SYSTEM.EVENT_CLEAR_FEATURE_PANEL,this.setShowPanel);
    eventEmitter.removeListener('map.props.fetureOn', this.recordPropFeatureOn);
  }
  // name is feature name
  setShowPanel = ({val, name}) => {
    if (name && name !== this.name) {
      return;
    }
    let showPanel = val;
    if (val === undefined) {
      showPanel = false;
    }
    let state = {showPanel, showPane: showPanel};
    if (val === false) {
      state.schList = null;
      // state.bns
      // state.schList
    }
    // console.log('setShowPanel:',val,name,state);
    this.setState(state);
  };
  // commercial props can setFeature to false, not show school when commercial prop
  // @Allen removed
  setFeature(set) {
    // set = {school:true/false}
    // if (set.hasOwnProperty('school')){
    //   feature.school = set.school;
    // }
  }
  onPress(e) {
    var self = this;
    if (!self.featureOn) {
      return;
    }
    this.getHomeSchools({lat: e.coordinate.latitude, lng: e.coordinate.longitude});
  }
  getHomeSchools = loc => {
    // console.log(prop);
    let data = {loc: [loc.lat, loc.lng], mode: 'bnd'};
    requestStdFn('getHomeSchools', data)
      .then(ret => {
        this.gotHomeSchools(ret, loc);
      }).catch(err => {
        return this.flashMessage(err);
      });
  };
  gotHomeSchools(schList, loc) {
    if (!schList || schList.length == 0) {
      this.flashMessage(l10n('No Result'));
      schList = null;
    }
    this.setState({schList: schList, searchDialog: null, loc: loc});
  }
}

const styles = StyleSheet.create({
  marker: {
    //flex:1,
    width: 24,
    height: 24,
    zIndex: 1,
  },
  text: {
    color: '#ff0000',
  },
  contentContainer: {
    // paddingVertical: 10
  },
  boundaryItem: {
    paddingTop: 10,
    paddingBottom: 10,
    paddingLeft: 10,
    paddingRight: 10,
    // borderBottomWidth: 1,
    // borderBottomColor: '#D0D0D0',
  },
  gradesWrapper: {
    // justifyContent:'flex-start',
    // alignItems:'center',
    flexDirection: 'row',
    // paddingLeft:10,
    // width:width/2,
    // overflow:'hidden',
  },
  gradesToWrapper: {
    justifyContent: 'flex-end',
    alignItems: 'center',
    flexDirection: 'row',
    width: width / 2 - 1,
    overflow: 'hidden',
  },
  buttonStyle: {
    // paddingRight: 10,
    // paddingLeft: 10,
    // paddingTop:4,
    // paddingBottom:4,
    // borderRadius: 0,
    // alignItems:'center',
    // justifyContent:'center',
    // backgroundColor: '#5cb85c',
    // width:width/2,
    // height:50,
  },
  schType: {
    // color:'white',
  },
  schGrades: {
    // backgroundColor: '#6FCE1B',
    paddingRight: 6,
    paddingLeft: 6,
    paddingTop: 3,
    paddingBottom: 3,
    borderRadius: 0,
    // marginLeft:10,
    // borderColor: 'transparent',
    // borderWidth: 1,
  },
  bottomPaneWrapper: {
    position: 'absolute',
    bottom: 0,
    // top:74,
    backgroundColor: 'white',
    left: 0,
    right: 0,
    // flexDirection:'row',
    zIndex: 15,
    height: 240,
  },
  // modelMenu:{
  //   position: 'absolute',
  //   bottom: 0,
  //   // top:74,
  //   backgroundColor: 'white',
  //   left: 0,
  //   right: 0,
  //   // flexDirection:'row',
  //   zIndex: 20,
  // },
  modelMenuRow: {
    flexDirection: 'row',
    // marginVertical: 20,
    zIndex: 15,
    height: 40,
    backgroundColor: 'white',
    // borderWidth: 1,
    // borderBottomColor: "#eee",
    //borderColor: '#eee',
  },
  // closeButton:{
  //   flexDirection: 'row',
  //   justifyContent: 'center',
  //   alignItems: 'center',
  //   //margin: 10,
  //   padding: 10,
  // },
  closeButtonBns: {
    position: 'absolute',
    top: 10,
    right: 10,
  },
  center: {
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
  },
  schoolNameBar: {
    // backgroundColor:'#e03131',
    // alignItems:'center',
    // justifyContent:'flex-start',
    paddingTop: 4,
    minHeight: 44,
  },
  schoolName: {
    color: 'black',
    fontSize: 17,
    // backgroundColor:'red',
    fontWeight: 'bold',
    // alignSelf:'center',
    alignItems: 'center',
    // height:44,
  },
  schoolNameWrapper: {
    flexDirection: 'row',
    // backgroundColor:'blue',
    height: 44,
    alignItems: 'center',
    justifyContent: 'flex-start',
    paddingLeft: 10,
  },
  keyFactStart: {
    alignContent: 'flex-start',
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
  },
  keyFactCenter: {
    alignContent: 'center',
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'column',
  },
  keyFactDesc: {
    color: '#888',
    fontSize: 11,
    paddingTop: 10,
  },
  keyFactVal: {
    fontSize: 15,
    // flex:1,
    color: 'black',
    // fontWeight:'500',
    // paddingBottom:10,
  },
  schoolAddr: {
    color: '#777',
    fontSize: 12,
    paddingLeft: 10,
    paddingBottom: 15,
  },
  row: {
    flexDirection: 'row',
  },
  featurePanel: {
    flexDirection: 'column',
    justifyContent: 'flex-start',
    alignContent: 'flex-start',
    position: 'absolute',
    bottom: 0,
    // top:74,
    backgroundColor: 'white',
    left: 0,
    right: 0,
    zIndex: 1000, //TODO:
    height: 420,
    paddingTop: 10,
  },
  mainSelector: {
    // flexDirection: 'row',
    // marginVertical: 20,
    // alignItems:'stretch',
    // zIndex:15,
    // width:'100%',
    height: 44,
    // backgroundColor: 'blue',
    paddingTop: 7,
    paddingRight: 0,
    // marginTop:13,
    paddingLeft: 10,
    marginBottom: 3,
    // marginRight:-10
  },
  keyFactHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    flexWrap: 'nowrap',
  },
  keyFactDetail: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    marginTop: 3,
  },
  boldValue: {
    fontWeight: 'bold',
    fontSize: 17,
    color: '#000',
  },
  totalContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  totalText: {
    color: '#999',
    fontSize: 12,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  separatorText: {
    color: '#000',
    fontSize: 17,
  },
  keyText: {
    fontSize: 12,
    color: '#6f6f6f',
  },
  alertButton: {
    marginLeft: 5,
  },
  arrowIcon: {
    paddingTop: 0,
    paddingBottom: 0,
    paddingLeft: 3,
    paddingRight: 3,
    fontSize: 11,
  },
  keyFactsContainer: {
    marginTop: 5,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    paddingLeft: 10,
    paddingRight: 10,
    width: '100%',
  },
  keyFactsScrollView: {
    flexDirection: 'row',
  },
});

export default MapSchool;
