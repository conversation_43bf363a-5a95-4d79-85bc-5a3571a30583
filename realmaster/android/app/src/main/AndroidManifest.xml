<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <uses-feature android:name="android.hardware.camera" />
    <uses-feature android:name="android.hardware.camera.autofocus" />

    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.FLASHLIGHT" />
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_MEDIA_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.READ_CALENDAR" />
    <uses-permission android:name="android.permission.WRITE_CALENDAR" />
    <uses-permission android:name="android.permission.CALL_PHONE" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.DOWNLOAD_WITHOUT_NOTIFICATION" />
    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />

    <!-- <uses-permission android:name="android.permission.SEND_SMS" /> -->
    <!--  -->
    <queries>
        <!-- <package android:name="com.example.store" /> -->
        <!-- <package android:name="com.example.services" /> -->
        <package android:name="com.tencent.mm" />
        <package android:name="com.google.android.gm" />
        <package android:name="com.google.android.apps.maps" />
        <package android:name="com.google.android.gms.maps" />

        <intent>
            <action android:name="android.intent.action.SEND_MULTIPLE" />
            <data android:mimeType="*/*" />
        </intent>
    </queries>
    <application
        android:hardwareAccelerated="true"
        android:name=".MainApplication"
        android:label="@string/app_name"
        android:icon="@mipmap/ic_launcher"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:allowBackup="false"
        android:networkSecurityConfig="@xml/network_security_config"
        android:requestLegacyExternalStorage="true"
        android:theme="@style/AppTheme"
        android:launchMode="singleTop">
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@mipmap/ic_notification" />
        <!-- Set custom default icon. This is used when no icon is set for incoming notification messages.
            See README(https://goo.gl/l4GJaQ) for more. -->
        <!-- <meta-data
        android:name="com.google.firebase.messaging.default_notification_icon"
        android:resource="@mimap/ic_notification" /> -->
        <!-- Set color used with incoming notification messages. This is used when no color is set for the incoming
                  notification message. See README(https://goo.gl/6BKBk7) for more. -->
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_color"
            android:resource="@color/rm_red" />
        <meta-data
            android:name="com.facebook.sdk.ApplicationId"
            android:value="@string/facebook_app_id" />
        <meta-data
            android:name="com.facebook.sdk.ClientToken"
            android:value="@string/facebook_client_token" />
        <!-- AIzaSyDgi2mHkM6kdJhpxc3ubEbvid9w7GQ4-fY -->
        <!-- android:value="AIzaSyBOslrM1NF12A_SX_SnhFXKoBxmfdeD7Gc"  -->
        <meta-data
            android:name="com.google.android.geo.API_KEY"
            android:value="@string/MAPS_API_KEY"
            />

        <meta-data
            android:name="com.google.firebase.messaging.default_notification_channel_id"
            android:value="RealMaster-android-pn-channel-id" />
        <!-- Change the value to true to enable pop-up for in foreground (remote-only, for local use ignoreInForeground) -->

        <provider
            android:authorities="com.facebook.app.FacebookContentProvider357776481094717"
            android:name="com.facebook.FacebookContentProvider" />

        <activity
            android:name=".wxapi.WXEntryActivity"
            android:label="@string/app_name"
            android:exported="true"
            android:taskAffinity="com.realmaster"
            android:launchMode="singleTask"></activity>
        <activity
            android:exported="true"
            android:name=".MainActivity"
            android:launchMode="singleTask"
            android:label="@string/app_name"
            android:screenOrientation="portrait"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
            android:windowSoftInputMode="adjustPan">
            <!-- <intent-filter>
            <action android:name="android.intent.action.MAIN" />
            <category android:name="android.intent.category.LAUNCHER" />
        </intent-filter> -->
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:scheme="https"
                    android:host="www.realmaster.cn"
                    android:pathPrefix="/1.5/prop/detail" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:scheme="https"
                    android:host="realmaster.com"
                    android:pathPrefix="/1.5/prop/detail" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:scheme="https"
                    android:host="realmaster.com"
                    android:pathPrefix="/s" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:scheme="https"
                    android:host="www.realmaster.cn"
                    android:pathPrefix="/s" />
            </intent-filter>
        </activity>
        <activity android:name="com.facebook.react.devsupport.DevSettingsActivity" />
    </application>

</manifest>
