## 1.universal link 配置
服务器配置https://realmaster.com/apple-app-site-association
```json
{
  "applinks":{
    "apps":[],
    "details":[
      {
        "appID":"8A2DLASB37.com.realmaster",
        "paths":[
          "/app/*",
          "/sdksample/*",
          "*"
        ],
        "components":[
          {
            "#":"no_universal_links",
            "exclude":true,
            "comment":"Matches any URL whose fragment equals no_universal_links and instructs the system not to open it as a universal link"
          },
          {
            "/":"/getapp/*",
            "comment":"Matches any URL whose path starts with /buy/"
          },
          {
            "/":"/1.5/prop/detail",
            "comment":"open prop detail page in app"
          },
          {
            "/":"/s/*detail*",
            "comment":"open prop detail page in app using short url"
          },
          {
            "/":"/zh-cn/*",
            "exclude":true,
            "comment":"Matches any URL whose path starts with /help/website/ and instructs the system not to open it as a universal link"
          },
          {
            "/":"/1.5/topics/*",
            "?":{
              "id":"????????????????????????"
            },
            "comment":"Matches any URL whose path starts with /help/ and which has a query item with name 'articleNumber' and a value of exactly 4 characters"
          }
        ]
      }
    ]
  }
}
```
Xcode 配置 Associated Domains

<img src='./mdResource/assoiatedDomain.png' width=100% />
