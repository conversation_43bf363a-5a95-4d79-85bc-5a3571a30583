import { useEffect, useState } from "react";
import { Text, StyleSheet } from "react-native";
/**
 * CountDown component that displays a countdown timer.
 *
 * @param {Object} props - The component props.
 * @param {number} [props.seconds=5] - The number of seconds to count down from.
 * @param {Function} props.onFinish - The callback function to call when the countdown finishes.
 * @returns {JSX.Element} - The rendered CountDown component.
 */
const CountDown = ({seconds, onFinish, syncDuration}) => {
    const [count, setCount] = useState(seconds);

    useEffect(() => {
        // global.rmLog(`[CountDown.jsx:15~useEffect]`, seconds);
        setCount(seconds);
    }, [seconds]);

    useEffect(() => {
        // global.rmLog(`[CountDown.jsx:20~useEffect]`, count);
        if (count > 0) {
            const timer = setInterval(() => {
              // global.rmLog(`[CountDown.jsx:22~useEffect]`, count);
              setCount((prevCount) => prevCount - 1);
            }, 1000);
            return () => clearInterval(timer);
        } else {
            onFinish();
        }
    }, [count, onFinish]);

    // useEffect(() => {
    //     syncDuration(count);
    // }, [count, syncDuration]);


    // global.rmLog(`[CountDown.jsx:35~render]`, count);

    return count > 0 ? <Text style={styles.countText}>{count}</Text> : null;
}

const styles = StyleSheet.create({
    countText: {
        color: 'rgba(255,255,255,1)',
        fontSize: 14,
        paddingLeft: 4
    }
})

export default CountDown;
