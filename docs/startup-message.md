# RealMaster App 启动消息处理文档

本文档描述了 RealMaster App 中 React Native 与 WebView 之间的消息处理机制。

## 一、RN 自主处理类消息

这些消息可以由 RN 端独立完成处理，无需与 Web 页面进行数据交互。

### 1. 设备信息类

| 消息类型 | 说明 | 处理方式 |
|---------|------|---------|
| `getMemoryDetail` | 获取设备内存信息 | 通过 DeviceInfo 获取总内存、已用内存、最大内存 |
| `getSystemVersion` | 获取系统版本 | 通过 DeviceInfo 获取系统版本号 |
| `getDeviceId` | 获取设备ID | 通过 DeviceInfo 获取设备唯一标识 |
| `getUniqueId` | 获取唯一标识 | 通过 DeviceInfo 获取应用唯一标识 |
| `ver` | 获取应用版本 | 获取应用构建号和ID |
| `coreVer` | 获取核心版本 | 获取应用版本号 |

### 2. 系统功能类

| 消息类型 | 说明 | 处理方式 |
|---------|------|---------|
| `clipboard.get` | 获取剪贴板内容 | 读取系统剪贴板 |
| `clipboard.set` | 设置剪贴板内容 | 写入系统剪贴板 |
| `exitApp` | 退出应用 | 调用退出确认 |
| `keyboard.dismiss` | 关闭键盘 | 收起系统键盘 |
| `vibrate` | 设备震动 | 触发设备震动 |
| `permissions.openSettings` | 打开系统设置 | 跳转系统设置页面 |

### 3. 存储管理类

| 消息类型 | 说明 | 处理方式 |
|---------|------|---------|
| `storage.getItemObj` | 获取存储项 | 从本地存储读取数据 |
| `storage.setItemObj` | 设置存储项 | 写入数据到本地存储 |
| `storage.removeItemObj` | 删除存储项 | 从本地存储删除数据 |
| `setCookie` | 设置Cookie | 设置域名相关Cookie |
| `setSystemValue` | 设置系统配置 | 更新系统配置值 |
| `showSystemValue` | 显示系统配置 | 显示当前系统配置 |
| `refreshSystemValue` | 刷新系统配置 | 重新加载系统配置 |

### 4. 权限检查类

| 消息类型 | 说明 | 处理方式 |
|---------|------|---------|
| `permissions.check.location` | 检查位置权限 | 检查位置服务权限状态 |
| `permissions.check.locationaccuracy` | 检查位置精度 | 检查位置精度权限状态 |
| `permissions.check.notification` | 检查通知权限 | 检查通知权限状态 |
| `AppInstalledChecker` | 检查应用安装 | 检查特定应用是否安装 |
| `canOpenURL` | 检查URL可用性 | 检查是否可以打开特定URL |

### 5. 路由管理类

| 消息类型 | 说明 | 处理方式 |
|---------|------|---------|
| `getRoutesCounts` | 获取路由数量 | 获取当前路由堆栈数量 |
| `getNativeRouteStack` | 获取路由堆栈 | 获取原生路由堆栈信息 |
| `closePopup` | 关闭弹窗 | 关闭当前弹窗 |
| `closeAndRedirect` | 关闭并重定向 | 关闭当前页面并重定向 |
| `closeAndRedirectRoot` | 返回根路由 | 返回到根路由页面 |

## 二、需要与 Web 页面通信类消息

这些消息需要与 Web 页面进行数据交互或返回结果。

### 1. 权限请求类

| 消息类型 | 说明 | 处理方式 |
|---------|------|---------|
| `permissions.request.notification` | 请求通知权限 | 请求系统通知权限 |
| `permissions.request.location` | 请求位置权限 | 请求位置服务权限 |
| `permissions.request.locationaccuracy` | 请求位置精度 | 请求位置精度权限 |
| `permissions.request.requestMultiple` | 请求多个权限 | 批量请求多个权限 |

### 2. 第三方服务类

| 消息类型 | 说明 | 处理方式 |
|---------|------|---------|
| `hasGoogleService` | 检查谷歌服务 | 检查谷歌服务可用性 |
| `hasPlayServices` | 检查商店服务 | 检查应用商店服务 |
| `facebook.share` | Facebook分享 | 分享内容到Facebook |
| `facebook.auth` | Facebook认证 | Facebook登录认证 |
| `wechat.share` | 微信分享 | 分享内容到微信 |
| `wechat.auth` | 微信认证 | 微信登录认证 |
| `google.auth` | 谷歌认证 | 谷歌账号认证 |
| `apple.auth` | 苹果认证 | 苹果账号认证 |

### 3. 功能交互类

| 消息类型 | 说明 | 处理方式 |
|---------|------|---------|
| `calendar.authorizationStatus` | 日历权限状态 | 检查日历权限状态 |
| `calendar.saveEvent` | 保存日历事件 | 创建日历事件 |
| `calendar.findCalendars` | 查找日历 | 获取可用日历列表 |
| `geoPosition` | 获取地理位置 | 获取当前位置信息 |
| `pageContent` | 页面内容 | 获取页面内容信息 |
| `map` | 地图操作 | 处理地图相关操作 |
| `mapSearch` | 地图搜索 | 执行地图搜索功能 |
| `qrcode` | 二维码扫描 | 处理二维码扫描 |
| `autocomplete` | 自动完成 | 处理自动完成功能 |

### 4. 网络请求类

| 消息类型 | 说明 | 处理方式 |
|---------|------|---------|
| `fetch` | 数据获取 | 执行网络请求 |
| `downloadImage` | 图片下载 | 下载并保存图片 |

### 5. 状态同步类

| 消息类型 | 说明 | 处理方式 |
|---------|------|---------|
| `webview.checkAlive` | 检查存活状态 | 检查WebView状态 |
| `pushToken` | 推送令牌 | 获取推送通知令牌 |
| `isCommandAvailable` | 命令可用性 | 检查命令是否可用 |
| `setAppLang` | 设置语言 | 设置应用语言 |

## 注意事项

1. RN 自主处理类消息应优先在本地完成处理，减少不必要的网络通信
2. 与 Web 通信类消息需要考虑：
   - 网络状态处理
   - 错误处理机制
   - 超时处理策略
   - 数据同步机制
3. 权限相关操作需要：
   - 完整的权限检查流程
   - 清晰的用户提示信息
   - 合理的降级处理方案 