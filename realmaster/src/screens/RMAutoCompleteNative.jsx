import React, {Component} from 'react';
import {
  ActivityIndicator,
  Platform,
  Animated,
  Easing,
  StyleSheet,
  View,
  TextInput,
  ScrollView,
  Keyboard,
  KeyboardAvoidingView,
  TouchableOpacity,
  FlatList,
  Text,
  Dimensions,
  Alert,
} from 'react-native';

import storageIns from '../utils/storage';
import Constants from '../config/constants';

import ResList from '../components/search/ResList';
import HistList from '../components/search/HistList';
import Ads from '../components/search/Ads';
import RmIcon from '../components/RmIcon';
// import Address from './components/search/Address';

import {l10n} from '../utils/i18n';
import {getWxIsInstalled} from '../utils/wechat';
// import {requestStdFn,getServerDomain,RMPost} from './RMNetwork';
import {mainRequest, requestStdFn} from '../utils/request';
import {eventEmitter} from '../utils/common';
import {RMStatusBar, RMBottomBar} from '../components/RMStatusBar';
import {gotoUrl, gotoMap} from '../components/search/helper';
import colorTheme from '../utils/colors';
import {getColor} from '../utils/RMstyle';
// import AnimatedWave from './components/animated/animatedWave'

const {width, height} = Dimensions.get('window');
var styles = StyleSheet.create({
  container: {
    backgroundColor: 'rgb(227,227,227)',
    flex: 1,
  },
  currentTab: {
    borderBottomWidth: 2,
    borderStyle: 'solid',
    color: colorTheme['main_red'],
    borderColor: colorTheme['main_red'],
    // borderColor:'rgb(255,36,36)'
  },
  searchWrapper: {
    padding: 7,
    height: 44,
    // backgroundColor:this.state.backgroundColor,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
  },
  back: {
    width: 30,
    alignContent: 'center',
  },
  search: {
    borderRadius: 5,
    width: width - 70,
    flexDirection: 'row',
    backgroundColor: '#fff',
    height: 30,
    overflow: 'hidden',
  },
  searchInput: {
    fontSize: 14,
    borderTopLeftRadius: 5,
    borderBottomLeftRadius: 5,
    color: 'black',
    paddingLeft: 6,
    paddingRight: 15,
    paddingTop: 0,
    paddingBottom: 0,
    width: width - 150,
  },
  searchIcons: {
    width: 80,
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    borderTopRightRadius: 5,
    borderBottomRightRadius: 5,
    // paddingTop:7,
    // paddingBottom:7,
    paddingLeft: 10,
    paddingRight: 10,
  },
  searchDvd: {
    width: 1,
    height: 10,
    backgroundColor: 'rgb(230,230,230)',
    marginRight: 10,
    marginLeft: 10,
  },
  tags: {
    paddingLeft: 5,
    backgroundColor: '#fff',
    paddingBottom: 5,
  },
  tagContainer: {
    marginRight: 10,
    marginTop: 10,
  },
  tag: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: 'rgb(231,231,231)',
  },
  tagIcon: {
    marginRight: 5,
  },
  tagTxt: {
    fontSize: 14,
    color: 'rgb(109,109,109)',
  },
  tabs: {
    paddingLeft: 10,
    paddingRight: 10,
    backgroundColor: '#fff',
    flexDirection: 'row',
  },
  tabWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 10,
    paddingBottom: 10,
  },
  tab: {
    color: 'rgb(142,142,142)',
    fontSize: 15,
    fontWeight: 'bold',
    textTransform: 'capitalize',
    // ??????????
  },
  indicator: {
    marginLeft: -5,
    // width:30,
    // height:30,
    // alignSelf:'center',
    transform: [{scaleX: 0.8}, {scaleY: 0.8}],
    marginRight: 8,
  },
  tabNumWrapper: {
    marginLeft: 3,
    width: 25,
    textAlign: 'left',
    textAlignVertical: 'bottom',
    // alignItems:'flex-end',
    // justifyContent:'flex-start',
    height: 20,
  },
  tabNum: {
    height: 20,
    fontSize: 9,
    textAlignVertical: 'center',
    // alignSelf:'center',
    color: 'rgb(142,142,142)',
  },
  histsView: {
    justifyContent: 'center',
    alignItems: 'center',
    height: height - 400,
  },
  noResultTxt: {
    color: 'rgb(161,161,161)',
    fontSize: 16,
  },
  clearBtn: {height: 32, width: 30, justifyContent: 'center', alignItems: 'center'},
});

class RMAutoCompleteNative extends Component {
  constructor(props) {
    super(props);
    (this.strings = {
      NEED_LOGIN: 'Need Login',
      NO_RESULT: 'No Result',
      TIME_OUT: 'Timeout',
      NOT_FOUND: 'Not found',
    }),
      // this.spinValue = new Animated.Value(0)
      (this.datas = [
        // 'isPaytop',
        'isCip',
        // 'isVipUser',
        // 'isVipRealtor',
        // 'isRealtor',
        // 'isVisitor',
        // 'lang',
        // 'allowedShareSignProp',
        // 'propPtypes',
        // 'ptpTypes',
        // 'propSortMethods',//need this
        // 'projSortMethods',
        // 'isApp',
        // 'isLoggedIn',
        // 'shareUID',
        // 'allowedPromoteProp',
        // 'hasFollowedRealtor',
        // // 'propCounts',
        // 'reqHost',
        // 'shareAvt',
        // 'sessionUser',
        // 'domFilterVals',
        // 'domFilterValsShort',
        // // 'bsmtFilterVals',
        // 'allowedEditGrpName',
        // 'shareLinks',
        // // 'autocomplete',
        // // 'placeApi',
        // 'coreVer',
        // 'showSoldPriceBtn',
        'isDevGroup',
        // 'isAdmin',
        'userCity',
      ]),
      (this.state = {
        adTp: 'tl',
        dispVar: {userCity: {}},
        currentTab: 'listing',
        userClickTab: '',
        hists: [],
        searchStr: '',
        props: [],
        ref: null,
        projs: [],
        schs: [],
        georets: [],
        topListingAds: [],
        communities: [],
        // show addr of first match prop in props list
        firstMatchedAddr: null,
        searchingProps: false,
        searchingProjs: false,
        searchingSchs: false,
        searchingCmtys: false,
        srchTxtIdx: 0,
        srchTxt: ' Input address, name, ID',
        srchTxtArray: [' Input address, name, ID', 'Separate multi IDs by comma'],
      });
    this.search = this.search.bind(this);
  }
  async componentDidMount() {
    this.data = {
      ...this.props,
      ...this.props.route.params,
    };
    // this.data = {};
    // this.data.referer = this.props.referer;
    // this.data.closePopup = this.props.closePopup;
    // var thisProps = this.props;
    // this.thisProps = this.props;
    this.setSearchPlaceHolder();
    this.setHistoryData();
    this.getPageData();
    var state = {
      backgroundColor: await getColor('commonBarColor'),
      textColor: await getColor('commonBarText'),
      searchPlaceHolderColor: await getColor('commonMainColor'),
      searchColor: await getColor('commonMinorColor'),
    };
    // console.log('state',state)
    this.setState(state);
    setTimeout(() => {
      if (this.ref) {
        this.ref.focus();
      }
    }, 600);
  }
  spin() {
    // this.spinValue.setValue(0)
    // Animated.timing(
    //   this.spinValue,
    //   {
    //     toValue: 1,
    //     duration: 4000,
    //     easing: Easing.linear
    //   }
    // ).start(() => this.spin())
  }
  flashMessage(msg) {
    eventEmitter.emit('flash-message', {msg: msg});
    // Alert.alert(msg);
  }
  getPageData() {
    var dispVar = {};
    // console.log('autocomplete pageData')

    mainRequest({
      url: '/1.5/pageData',
      method: 'post',
      data: {
        datas: this.datas,
        page: 'autocomplete',
      },
    })
      .then(ret => {
        if (ret.err || ret.e) {
          return this.flashMessage(ret.err || ret.e);
        }
        if (ret.datas) {
          dispVar = ret.datas;
        }
        this.setState({dispVar}, () => {
          // console.log(this.state.dispVar);
        });
      })
      .catch(err => {
        if ('TIME_OUT' == err) {
          this.flashMessage(this.strings[err]);
        }
      });
  }
  async getAds(adTp) {
    let tp = adTp;
    if (tp == 'proj') {
      tp = 'tl';
    } else {
      tp = 'proj';
    }
    const hasWechat = getWxIsInstalled();
    try {
      const ret = await requestStdFn('getAds', {tp, hasWechat});
      if (ret.ok) {
        this.setState({topListingAds: ret.result});
        storageIns.setItem(Constants.AcAdtp, tp);
      } else {
        console.warn('get ads failed', ret);
      }
    } catch (error) {
      console.log('get ads error', error);
    }
  }

  // getTopListings() {
  //   requestStdFn('getTopListings',{status:'A',ptype:'Residential',city:this.props.city},(err,ret)=>{
  //     if (ret.ok) {
  //       this.setState({topListingAds:ret.tls});
  //     }
  //   });
  // }
  componentWillUnmount() {
    clearTimeout(this.addrTimer);
    clearTimeout(this.projTimer);
    clearTimeout(this.schTimer);
  }
  setHistoryData = async () => {
    try {
      // const hists = await AsyncStorage.getItem('hists');
      // const currentTab = await AsyncStorage.getItem('currentTab')
      // let adTp = await AsyncStorage.getItem('adTp')
      // TODO: use getCacheItem
      var hists = await storageIns.getItem(Constants.AcHists);
      if ('string' == typeof hists) {
        hists = JSON.parse(hists);
      }
      // var currentTab = await RMStorage.syncGetItem(SYSTEM.AC_CURTAB)
      // currentTab = JSON.parse(currentTab)
      var adTp = await storageIns.getItem(Constants.AcAdtp);
      // adTp = JSON.parse(adTp)
      // console.log('hists',hists)
      if (hists !== null) {
        this.setState({hists: hists});
      }
      // if (currentTab !== null) {
      //   this.setState({currentTab});
      // }
      if (adTp == null) {
        adTp = 'tl';
      }
      this.setState({adTp}, () => {
        this.getAds(adTp);
      });
    } catch (e) {
      // error reading value
      console.error('Error setHistoryData: ', e);
    }
  };
  hasSearched(nm) {
    if (nm == 'pre-construction') {
      nm = 'preconstruction';
    }
    return this['hasSearched' + nm];
  }
  // NOTE: use standard function to search at same time
  searchResultsAtSameTime(ms, val) {
    this.setState({
      searchingProps: true,
      searchingProjs: true,
      searchingSchs: true,
      searchingCmtys: true,
    });
    this.addrSearchTs = Date.now();
    let handleAddrSearchResult = ret => {
      // console.log('cb1 called')
      // console.log(err,ret)
      var props = [];
      var firstMatchedAddr = null;
      this.hasSearchedlisting = true;
      if (ret && ret.ok) {
        if (ret.ts !== this.addrSearchTs) {
          return;
        }
        props = ret.l;
        // for (let i of props){
        //   console.log(`${i._id}\tmt:${i.mt}\tspcts:${i.spcts}\tts:${i.ts}`)
        // }
        if (ret.l.length == 0) {
          this.notFoundProp = true;
        }
        if (props && props.length) {
          firstMatchedAddr = props[0];
        }
      } else {
        this.notFoundProp = true;
        if (ret && ret.err) {
          console.error(err);
        }
      }
      this.setState({firstMatchedAddr, props: props, searchingProps: false});
    };
    let handleSearchProjectsResult = ret => {
      // console.log('cb2 called',err,ret)
      let stateProj = {searchingProjs: false};
      if (ret.ok) {
        this.hasSearchedpreconstruction = true;
        stateProj.projs = ret.projs;
        if (ret.projs.length == 0) {
          this.notFoundProj = true;
        } else {
          // if(this.notFoundProp){
          //   stateProj.currentTab = 'pre-construction'
          // }
        }
      }
      this.setState(stateProj);
    };
    // let cb = (err,ret)=>{
    //   console.log('++++err,ret',err,ret)
    // }
    var schs = [];
    var state = {searchingSchs: false};
    // seq: 2,4,1,3
    let handleFindSchoolsResult = ret => {
      // console.log('cb3 called',err,ret)
      this.hasSearchedschool = true;
      if (ret.length > 0) {
        schs = schs.concat(ret);
      } else {
      }
    };
    let handleFindPrioritySchoolsResult = ret => {
      if (ret.items && ret.items.length > 0) {
        schs = schs.concat(ret.items);
      }
      setTimeout(() => {
        cbMergeSchool();
      }, 50);
    };
    let handleSearchCmtysByNameResult = ret => {
      // console.log(ret)
      this.hasSearchedcmtys = true;
      let stateCmty = {searchingCmtys: false};
      if (ret && ret.length > 0) {
        stateCmty.communities = ret;
      } else {
      }
      this.setState(stateCmty);
    };
    let cbMergeSchool = () => {
      let projs = this.state.projs;
      if (!schs.length) {
        this.notFoundSch = true;
      }
      state.currentTab = 'listing';
      if (this.notFoundProp && projs.length) {
        state.currentTab = 'pre-construction';
      }
      if (this.notFoundProp && this.notFoundProj) {
        if (schs.length) {
          state.currentTab = 'school';
        } else {
          // @fred 地址这个，如果前面有结果了，就不需要提前检索，用户点了tab再去检索。如果前面没有结果，自动检索，并且跳到这个tab。
          // not found anything
          this.searchGeos(0, val);
        }
      } else {
        if (this.state.currentTab == 'geo') {
          this.searchGeos(0, val);
        }
      }
      if (this.state.userClickTab && this.state.currentTab == this.state.userClickTab) {
        delete state.currentTab;
      }
      state.schs = schs;
      this.setState(state);
    };
    clearTimeout(this.allSearchTimer);
    this.allSearchTimer = setTimeout(async () => {
      requestStdFn('addrSearch', {s: val, limit: 10, loc: 1, ts: this.addrSearchTs}).then(ret => {
        handleAddrSearchResult(ret);
      }).catch(err => {
        // handle error, ））9 特殊指令处理，服务端不接受，但是需要返回其他方法结果
        this.notFoundProp = true;
        this.hasSearchedlisting = true;
        this.setState({firstMatchedAddr: null, props: [],searchingProps:false});
        console.warn('error addrSearch', err);
      })

      requestStdFn('searchProjects', {s: val}).then(ret => {
        handleSearchProjectsResult(ret);
      }).catch(err => {
        console.warn('error searchProjects', err);
      })
      requestStdFn('findSchools', {nm: val}).then(ret => {
        handleFindSchoolsResult(ret);
      }).catch(err => {
        console.warn('error findSchools', err);
      })
      requestStdFn('findPrivateSchools', {nm: val}).then(ret => {
        handleFindPrioritySchoolsResult(ret);
      }).catch(err => {
        console.warn('error findPrivateSchools', err);
      })
      requestStdFn('searchCmtysByName', {name: val}).then(ret => {
        handleSearchCmtysByNameResult(ret);
      }).catch(err => {
        console.warn('error searchCmtysByName', err);
      })
    }, ms);
  }
  // searchAddr(ms=500,val){
  //   var projTimeout = 1000;
  //   var props = [];
  //   this.addrTimer = setTimeout(()=>{
  //     this.setState({searchingProps:true});
  //     this.addrSearchTs = Date.now();
  //     requestStdFn('addrSearch',{s:val,limit:10,loc:1,ts:this.addrSearchTs},(err,ret)=>{
  //       // console.log(ret)
  //       if (ret && ret.ok) {
  //         this.hasSearchedlisting = true;
  //         // console.log('addrSearch:  ',ret.l);
  //         if(ret.ts !== this.addrSearchTs){
  //           // console.log('dumping previous ret1')
  //           return;
  //         }
  //         props = ret.l;
  //         if (ret.l.length == 0) {
  //           this.notFoundProp = true;
  //           projTimeout = 0;
  //           // currentTab = 'pre-construction';
  //         }
  //       } else {
  //         //handle error
  //         if(ret && ret.err){err=ret.err}
  //         console.error(err)
  //       }
  //       this.setState({props:props,searchingProps:false});
  //       // TODO: timeout search proj if length
  //       this.searchProj(projTimeout,val)
  //     });
  //   },ms);
  // }
  // searchProj(ms=1000,val){
  //   var projs = [];
  //   var schTimeout = 1000;
  //   // var currentTab = this.state.currentTab;
  //   var state = {projs,searchingProjs:false}
  //   this.projTimer = setTimeout(() => {
  //     this.setState({searchingProjs:true});
  //     requestStdFn('searchProjects',{s:val},(err,ret)=>{
  //       if (ret.ok) {
  //         this.hasSearchedpreconstruction = true;
  //         state.projs = ret.projs;
  //         // console.log('projects:',ret.projs);
  //         // && props.length == 0
  //         if (ret.projs.length == 0) {
  //           this.notFoundProj = true;
  //           schTimeout = 0;
  //           // currentTab = 'school';
  //         } else {
  //           if(this.notFoundProp){
  //             state.currentTab = 'pre-construction'
  //           }
  //         }
  //       } else {
  //         console.warn('error searchProjects',err);
  //       }
  //       this.setState(state);
  //       this.searchSchool(schTimeout,val);
  //     });
  //   }, ms);
  // }
  // searchSchool(ms=1000,val){
  //   // var currentTab = this.state.currentTab;
  //   var addrTimeout = 1000;
  //   var schs = [];
  //   var state = {searchingSchs:false}
  //   this.schTimer = setTimeout(()=>{
  //     this.setState({searchingSchs:true});
  //     // TODO: request at once
  //     requestStdFn('findSchools',{nm:val},(err,ret)=>{
  //       this.hasSearchedschool = true;
  //       // console.log('schools:',ret);
  //       if (ret.length > 0) {
  //         // const schools = ret.schs;
  //         // const schs = schools.schs;
  //         // const bnds = schools.bnds;
  //         // if (bnds.length == 0 && projs.length == 0) {
  //         //   currentTab = 'listing';
  //         // }
  //         // let bounds = []
  //         // for(let i in bnds) {
  //         //   let sch = schs[bnds[i]._id]
  //         //   let r = Object.assign(bnds[i], sch)
  //         //   if ((r.gf == 0) || (r.gf == -1) || (typeof r.gf == 'undefined')) {
  //         //     r.gf = 'K'
  //         //   }
  //         //   bounds.push(r)
  //         // }
  //         // console.log(bounds);
  //         schs = ret;
  //       } else {
  //         // console.log('error findSchoools length');
  //       }
  //       requestStdFn('findPrivateSchools',{nm:val},(err,ret)=>{
  //         if (err) {
  //           console.warn(err);
  //         }
  //         // console.log(ret);
  //         if (!err && ret.cnt > 0) {
  //           schs = schs.concat(ret.items);
  //         }
  //         if(!schs.length){
  //           addrTimeout = 0
  //           this.notFoundSch = true;
  //         }
  //         // if (this.notFoundProp && this.projs.length) {
  //         //   currentTab = 'pre-construction'
  //         // }
  //         if (this.notFoundProp && this.notFoundProj) {
  //           if(schs.length){
  //             state.currentTab = 'school'
  //           } else {
  //             // @fred 地址这个，如果前面有结果了，就不需要提前检索，用户点了tab再去检索。如果前面没有结果，自动检索，并且跳到这个tab。
  //             this.searchGeos(addrTimeout,val)
  //           }
  //         } else {
  //           if(this.state.currentTab == 'geo'){
  //             this.searchGeos(0,val)
  //           }
  //         }
  //         state.schs = schs;
  //         this.setState(state);
  //       });
  //     });
  //   },ms);
  // }
  searchGeos(ms = 1000, val) {
    // let georets = [{addr:'no addr?',city:'Markham',prov:'ON'}];
    var georets = [];
    var schTimeout = 1000;
    // var currentTab = this.state.currentTab;
    var state = {georets, searchingGeo: false};
    // this.setState({georets:georets,searchingGeo:false});
    this.geoTimer = setTimeout(async () => {
      this.setState({searchingGeo: true});
      try {
        const ret = await requestStdFn('addrSuggest', {s: val});
        if (ret && ret.ok) {
          this.hasSearchedgeo = true;
          // projs = ret.projs;
          // console.log('geos:',ret);
          if (ret.suggest.length == 0) {
            this.notFoundGeo = true;
            // schTimeout = 0;
          } else {
            if (this.notFoundSch && this.notFoundProp && this.notFoundProj) {
              // console.log(this.notFoundSch , this.notFoundProp , this.notFoundProj)
              state.currentTab = 'geo';
            }
            for (let i of ret.suggest) {
              if (!i._id) {
                i._id = i.fullAddr || new Date();
              }
              // console.log(i._id)
            }
            state.georets = ret.suggest;
          }
          this.setState(state);
        }
      } catch (error) {
        console.warn('error searchGeo', error);
      }
    }, ms);
  }
  onChangeText(val) {
    this.setState({searchStr: val});
    if (val && val.length < 3) {
      return;
    }
    if (val.trim() == '' || val.includes(')') || val.includes('*')) {
      return;
    }
    if (this.addrTimer || this.geoTimer) {
      this.setState({props: [], projs: [], schs: [], firstMatchedAddr: null});
      clearTimeout(this.addrTimer);
      clearTimeout(this.projTimer);
      clearTimeout(this.schTimer);
      clearTimeout(this.geoTimer);
      // delete this.addrTimer;
      // delete this.projTimer;
      // delete this.schTimer;
    }
    this.hasSearchedpreconstruction = false;
    this.hasSearchedlisting = false;
    this.hasSearchedschool = false;
    this.hasSearchedgeo = false;
    this.hasSearchedcmtys = false;

    this.notFoundProp = false;
    this.notFoundProj = false;
    this.notFoundSch = false;
    this.notFoundGeo = false;
    this.notFoundCommunities = false;
    // TODO: make another 3 func
    // func1()-> if result.length = 0 , call func2 immediatly, else wait 1s
    //   func2()-> same
    //     func3()->
    if (val.length > 100) {
      val = val.substr(0, 100);
    }
    this.searchResultsAtSameTime(600, val);
  }
  setSearchPlaceHolder() {
    var idx = Math.round(Math.random());
    let {srchTxtArray} = this.state;
    let srchTxt = srchTxtArray[idx];
    srchTxt = l10n(srchTxt);
    this.setState({srchTxt});
    // setInterval(()=>{
    // },3000)
  }
  clrSearch() {
    this.setState({firstMatchedAddr: null, searchStr: '', props: [], schs: [], projs: []});
  }
  search(icon, val) {
    if (val.cmd == '') {
      return;
    }
    const id = val._id;
    const hist = {
      k: id,
      icon,
      val,
      ts: new Date().toISOString(),
    };
    let {hists} = this.state;
    // console.log('++++++',hist,hists)
    // NOTE: remove duplicated
    for (let i in hists) {
      if (hists[i].k == id) {
        hists.splice(i, 1);
      }
    }
    if (hists.length >= 50) {
      hists.splice(hists.length - 1, 1);
    }
    let newHists = hists;
    // if result need login, do not add to history
    if (val && !val.login) {
      newHists = [hist].concat(hists);
    }
    this.setState({hists: newHists});
    // AsyncStorage.setItem('hists', JSON.stringify(newHists));
    // TODO: if val has lat lng ignore;
    storageIns.setItem(Constants.AcHists, JSON.stringify(newHists));
    if (icon == 'addr') {
      if (val && (val.loc || val.lat)) {
        // console.log(val)
        val.tp = icon;
        if (!val.lat && val.loc) {
          val.lat = val.loc[1];
          val.lng = val.loc[0];
        }
        val.saletp_en = ['Sale'];
        val.status_en = 'A';
        gotoMap(val, this.data.closePopup, this.data.referer);
        return;
      }
      if (!val.prov) {
        val.prov = val.pr_en || val.pr;
      }
      if (!val.cnty) {
        val.cnty = val.country || 'Canada';
      }
      // TODO: if alread have lag lng goto map directly
      let addr = `${val.addr}, ${val.city}, ${val.prov}, ${val.cnty}`;

      mainRequest({
        url: '/1.5/geocoding',
        method: 'post',
        data: {
          addr,
        },
      }).then(ret => {
        if (ret.ok) {
          val.tp = icon;
          val.lat = ret.result.lat;
          val.lng = ret.result.lng;
          val.saletp_en = ['Sale'];
          val.status_en = 'A';
          gotoMap(val, this.data.closePopup, this.data.referer);
        } else if (/Denied/.test(ret.e)) {
          // {"e": "Access Denied"}
          // Alert.alert('login')
          // console.log(ret.e)
          // this.data.closePopup('/1.5/user/login')
        }
      });
      // fetch(getServerDomain()+'/1.5/geocoding',{
      //   method:'POST',
      //   headers: {'Content-Type':'application/json'},
      //   body:JSON.stringify({addr:val.addr+', '+val.city})
      // })
      // .then(res=>res.json())
      // .then(ret=>{
      // })
    }
    if (icon == 'command') {
      mainRequest({
        url: '/1.5/search/prop/list',
        method: 'post',
        data: {
          id,
        },
      }).then(ret => {
        if (ret.redirect) {
          this.goBack(ret.redirect);
        } else {
          if (val.cmd.trim() == '') {
            return;
          }
          gotoUrl('/1.5/search/prop?d=/1.5/index&id=' + val.cmd, this.data.closePopup);
        }
      });
      // fetch(getServerDomain()+'/1.5/search/prop/list',{
      //   method:'POST',
      //   headers: {'Content-Type':'application/json'},
      //   body:JSON.stringify({id})
      // })
      // .then(res=>res.json())
      // .then(ret=>{
      // })
    }
  }
  storeHistorySearchData = async hists => {
    try {
      // await AsyncStorage.setItem('hists', JSON.stringify(hists))
      await storageIns.setItem(Constants.AcHists, JSON.stringify(hists));
    } catch (e) {
      // saving error
    }
  };
  removeHist(id) {
    let {hists} = this.state;
    for (let i in hists) {
      if (hists[i].k == id) {
        hists.splice(i, 1);
      }
    }
    this.setState({hists});
    storageIns.setItem(Constants.AcHists, JSON.stringify(hists));
  }
  changeTab(tab, index = 0) {
    this.setState({currentTab: tab, userClickTab: tab});
    if (this.tabSelectList) {
      this.tabSelectList.scrollToIndex({animated: true, index: index, viewPosition: 0.5});
    }
    // search geo anyway when uer click
    // console.log('change tab',this.notFoundSch , this.notFoundProp , this.notFoundProj)
    if (tab == 'geo') {
      //&& !this.state.georets.length
      this.searchGeos(0, this.state.searchStr);
    }
    storageIns.setItem(Constants.AcCurrTab, JSON.stringify(tab));
  }
  schoolAction = url => {
    // console.log('schoolAction: '+url, this.data.referer)
    if (this.data.referer == 'index') {
      this.data.closePopup(url);
    } else {
      // from mapSearch
      // console.log('xxxxx',url)
      eventEmitter.emit('map.searchSchoolProp', url);
      this.data.closePopup();
    }
  };
  goBack(opt) {
    if (this.data && this.data.closePopup) {
      if (!opt && this.data && this.data.d) {
        opt = this.data.d;
        if (/\/index/.test(opt)) {
          var char = '?';
          if (opt.indexOf('?') > 1) {
            char = '&';
          }
          opt = opt + char + 'rand=' + Math.random();
        }
      }
      Keyboard.dismiss();
      this.data.closePopup(opt);
    }
  }
  render() {
    const {
      srchTxt,
      props,
      projs,
      schs,
      searchStr,
      hists,
      georets,
      topListingAds,
      currentTab,
      searchingProps,
      searchingProjs,
      searchingSchs,
      dispVar,
      searchingGeo,
      communities,
      searchingCmtys,
    } = this.state;

    // console.log(hists);
    // console.log(encodeURIComponent(dispVar.userCity.n));
    this.tags = [
      {
        icon: 'map-marker',
        txt: 'Current Location',
        url: '/1.5/mapSearch?d=/1.5/index&gps=1&tp=mapSearch',
      },
      {
        icon: 'rmcat-sold',
        txt: 'Sold',
        url:
          '/1.5/mapSearch?d=/1.5/index&mode=list&city=' +
          encodeURIComponent(dispVar.userCity.o) +
          '&cityName=' +
          encodeURIComponent(dispVar.userCity.n) +
          '&prov=' +
          dispVar.userCity.p +
          '&dom=-90&mapmode=sold',
      },
      {
        icon: 'rmcat-openhouse',
        txt: 'Open House',
        url:
          '/1.5/mapSearch?d=/1.5/index&mode=list&city=' +
          encodeURIComponent(dispVar.userCity.o) +
          '&cityName=' +
          encodeURIComponent(dispVar.userCity.n) +
          '&prov=' +
          dispVar.userCity.p +
          '&oh=true',
      },
      {
        icon: 'rm-warning',
        txt: 'Stigmatized',
        url: '/1.5/map/webMap?ss=1&zoom=15&tab=stigma&gps=0&referer=nativeAutocomplete',
        // url:'/stigma/house?gps=1&d=/1.5/index&referer=nativeAutocomplete&city='+encodeURIComponent(dispVar.userCity.o)
        needLoginIn: true, //TODO: not supported
      },
    ];
    if (dispVar.isDevGroup) {
      this.tags.push({
        icon: 'gear',
        txt: 'Admin page',
        url: 'admin',
      });
      this.tags.push({
        icon: 'gear',
        txt: 'Video',
        url: 'video',
      });
    }
    // console.log(this.tags[1].url)
    // const spin = this.spinValue.interpolate({
    //   inputRange: [0, 1],
    //   outputRange: ['0deg', '360deg']
    // })
    var tabs = [
      {nm: 'listing', list: props, loading: searchingProps, nmVal: 'Listing', index: 0},
      {nm: 'pre-construction', list: projs, loading: searchingProjs, nmVal: 'PreCon', index: 1},
      {nm: 'school', list: schs, loading: searchingSchs, nmVal: 'School', index: 2},
      {nm: 'cmtys', list: communities, loading: searchingCmtys, nmVal: 'Community', index: 3},
      {nm: 'geo', list: georets, loading: searchingGeo, nmVal: 'Address', index: 4},
    ];
    // <Animated.View style={{transform: [{rotate: spin}]}}>
    // <RmIcon size={10} name={'spinner'} color={'#ccc'}/>
    // </Animated.View>
    let getSearchRetCount = item => {
      if (item.nm == 'geo' && !this.hasSearched(item.nm)) {
        return <Text style={[styles.tabNum]}>{'?'}</Text>;
      }
      return (
        <Text style={[styles.tabNum, this.hasSearched(item.nm) ? {} : {color: 'white'}]}>
          {item.list.length > 9 ? item.list.length + '+' : item.list.length}
        </Text>
      );
    };
    let renderTabItem = ({item}) => {
      let currentTabStyle = null;
      if (currentTab == item.nm) {
        currentTabStyle = styles.currentTab;
      }
      return (
        <TouchableOpacity onPress={() => this.changeTab(item.nm, item.index)}>
          <View style={[styles.tabWrapper]}>
            <Text style={[styles.tab, currentTabStyle]}>{l10n(item.nmVal)}</Text>
            <View style={styles.tabNumWrapper}>
              {item.loading ? (
                <ActivityIndicator
                  size='small'
                  color='#b9b9b9'
                  style={styles.indicator}
                />
              ) : (
                getSearchRetCount(item)
              )}
            </View>
          </View>
        </TouchableOpacity>
      );
    };
    let tabsView = (
      <View style={styles.tabs}>
        <FlatList
          ref={ref => {
            this.tabSelectList = ref;
          }}
          keyboardShouldPersistTaps='always'
          showsHorizontalScrollIndicator={false}
          horizontal
          data={tabs}
          keyExtractor={item => item.nm}
          renderItem={renderTabItem}
        />
      </View>
    );
    let histsView = (
      <View style={styles.histsView}>
        <RmIcon
          name={'history'}
          color={'rgb(161,161,161)'}
          size={36}
        />
        <Text style={styles.noResultTxt}>{l10n('No search history')}</Text>
      </View>
    );
    if (hists.length > 0) {
      histsView = (
        <HistList
          referer={this.data.referer}
          lang={this.data.lang}
          schoolAction={this.schoolAction}
          closePopup={this.data.closePopup}
          histList={hists}
          search={this.search}
          removeHist={this.removeHist.bind(this)}
        />
      );
    }
    let result = (
      <View style={{flex: 1}}>
        <View style={styles.tags}>
          <FlatList
            keyboardShouldPersistTaps='always'
            showsHorizontalScrollIndicator={false}
            horizontal
            data={this.tags}
            keyExtractor={(item, idx) => idx.toString()}
            renderItem={({item}) => (
              <TouchableOpacity
                style={styles.tagContainer}
                onPress={() => gotoUrl(item.url, this.data.closePopup, this.data)}>
                <View style={styles.tag}>
                  <RmIcon
                    style={styles.tagIcon}
                    name={item.icon}
                    color={colorTheme['main_red']}
                  />
                  <Text style={styles.tagTxt}>{l10n(item.txt)}</Text>
                </View>
              </TouchableOpacity>
            )}
          />
        </View>
        
        {topListingAds.length > 0 && (
          <Ads
            ads={topListingAds}
            lang={this.data.lang}
            closePopup={this.data.closePopup}
          />
        )}
        
        <View style={{flex: 1}}>
          {histsView}
        </View>
      </View>
    );
    if (searchStr && searchStr.length > 0) {
      let reslist,
        tp,
        tl,
        lst = [],
        more;
      switch (currentTab) {
        case 'listing':
          tp = 'prop';
          tl = l10n('Listing');
          lst = props;
          more = '/1.5/search/prop?d=/1.5/index&id=' + searchStr;
          break;
        case 'pre-construction':
          tp = 'proj';
          tl = l10n('Pre-Construction');
          lst = projs;
          more = '/1.5/prop/projects?d=/1.5/index&nm=' + searchStr;
          break;
        case 'school':
          tp = 'sch';
          tl = l10n('School');
          lst = schs;
          more = '/1.5/school/schoolList?d=/1.5/index&nm=' + searchStr;
          break;
        case 'geo':
          tp = 'geo';
          tl = l10n('Addresses');
          lst = georets;
          more = '/1.5/search/prop?d=/1.5/index&id=' + searchStr;
          break;
        case 'cmtys':
          tp = 'cmtys';
          tl = l10n('Community');
          lst = communities;
          more = '/1.5/search/prop?d=/1.5/index&id=' + searchStr;
          break;
        default:
          break;
      }
      // if (currentTab == 'listing') {
      //   reslist = (
      //     <View>
      //       <ResList referer={this.data.referer} schoolAction={this.schoolAction}
      //       lang={this.data.lang} closePopup={this.data.closePopup} search={this.search}
      //       searchStr={this.state.searchStr} lst={lst} tl={tl} tp={tp} more={more}/>
      //       {/* {!dispVar.isCip &&
      //         <Address closePopup={this.data.closePopup} searchTxt={search} search={this.search}/>
      //       } */}
      //     </View>
      //   );
      // } else
      // console.log('+++++',lst)
      reslist = (
        <ResList
          referer={this.data.referer}
          schoolAction={this.schoolAction}
          lang={this.data.lang}
          closePopup={this.data.closePopup}
          search={this.search}
          searchStr={this.state.searchStr}
          lst={lst}
          tl={tl}
          tp={tp}
          more={more}
        />
      );
      result = (
        <View style={{flex: 1}}>
          {tabsView}
          <View style={{flex: 1}}>
            {reslist}
          </View>
        </View>
      );
    }
    let close;
    if (searchStr != '') {
      close = (
        <TouchableOpacity
          onPress={() => this.clrSearch()}
          style={styles.clearBtn}>
          <RmIcon
            size={16}
            name={'rmclose'}
            color={'rgb(185,185,185)'}
          />
        </TouchableOpacity>
      );
    }
    let textColor = this.state.textColor || 'black';
    let searchColor = this.state.searchColor || 'white';
    let searchPlaceHolderColor = this.state.searchPlaceHolderColor;
    // console.log('++++++',textColor) 'rgb(185,185,185)'
    let icon = (
      <RmIcon
        size={14}
        name={'rmsearch'}
        color={searchColor}
      />
    );
    return (
      <View
        behavior={'padding'}
        style={styles.container}>
        <RMStatusBar tintColor={this.state.backgroundColor} />
        <View style={[styles.searchWrapper, {backgroundColor: this.state.backgroundColor}]}>
          <TouchableOpacity
            onPress={() => this.goBack()}
            style={styles.back}>
            <RmIcon
              name={'back'}
              color={textColor}
              size={21}
            />
          </TouchableOpacity>
          <View style={styles.search}>
            <TextInput
              autoFocus={false}
              ref={ref => {
                this.ref = ref;
              }}
              placeholderTextColor={'#777'}
              style={[styles.searchInput, {backgroundColor: searchPlaceHolderColor}]}
              value={searchStr}
              placeholder={srchTxt}
              onChangeText={this.onChangeText.bind(this)}
            />
            <View style={[styles.searchIcons, {backgroundColor: searchPlaceHolderColor}]}>
              {close}
              <View style={styles.searchDvd}></View>
              <TouchableOpacity
                onPress={() => {
                  this.search('command', {_id: searchStr, cmd: searchStr});
                }}
                style={styles.clearBtn}>
                {icon}
                {/* <AnimatedWave
                  sizeOvan={28}
                  icon={icon}
                  // onPress={() => alert("Hello")}
                  colorOvan={'#e03131'}
                  zoom={2}
                /> */}
              </TouchableOpacity>
            </View>
          </View>
        </View>
        {result}
      </View>
    );
  }
}

export default RMAutoCompleteNative;
