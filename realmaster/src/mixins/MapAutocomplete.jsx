/*
*/
import React, {Component} from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableHighlight,
  TextInput,
  Platform,
//   Image,
//   ScrollView,
} from 'react-native';
import MapFeature from './MapFeature';
import {gotoAutocomplete} from '../components/search/helper'
import { Icon } from '../utils/common';

// import { Marker } from 'react-native-maps';
//import PriceMarker from '../views/PriceMarker';
// import IconText from '../views/IconText';
// import BottomPane from '../views/BottomPane';
// import {RMPost,getServerDomain} from '../RMNetwork';
import {l10n} from '../utils/i18n';
import { eventEmitter } from '../utils/common';

class MapAutocomplete extends MapFeature{
  constructor(map,props){
    super(map,"MapAutocomplete",props);
    this.state = {
      tracksViewChanges:false,
    }
  }
  regionChanged(event,map){
    // console.log("regionChanged zoom:" + event.zoom);
    super.regionChanged(event,map);
  }
  renderOnMap(){
    return null;
  }
  renderModal(){
    return null;
  }
  setSearchString(str){
    // console.log(str);
  }
  showListView(opt={}){
    // console.log('+++++++',opt)
    eventEmitter.emit("map.showListView",opt);
  }
  renderButton(id){
    return null;
    //  backgroundColor: 'blue'
    var paddingAndroid = {};
    if(Platform.OS === 'android'){
      paddingAndroid = {paddingBottom:1,paddingTop:0}
    }
    return (
      <View
        key={'autocomplete'}
        style={{flex:1,
          // width:44,
          flexDirection: 'row',
          justifyContent: 'center',
          alignItems: 'center',
          // backgroundColor: 'yellow'
          }}>
        <TextInput
          style={[styles.titleSearchInput,paddingAndroid]}
          onChangeText={(text) => this.setSearchString(text)}
          placeholder={l10n('Input Addr/Prop ID')}
          placeholderTextColor={'#777'}
          value={this.state.text}
          underlineColorAndroid='rgba(0,0,0,0)'
          onFocus={(e)=>{gotoAutocomplete({referer:'mapSearch'})}}
        />
        <View style={[styles.center,{
          height:32,
          // backgroundColor:'green',
          padding:0,
          paddingRight:7,
          // flex:0,
          marginLeft:-20,
          zIndex:10,
          marginRight:2,
          borderRadius:4,
        }]} >
          <Icon name="rmsearch"
            size={14}
            color="#888"
            hitSlop={{top: 10, bottom: 10, left: 0, right: 0}}
          />
        </View>
      </View>
    )
  }
}

const styles = StyleSheet.create({
  center:{
    alignContent:'center',
    alignItems:'center',
    justifyContent:'center',
  },
  container: {
    flexDirection: 'column',
    alignSelf: 'flex-start',
  },
  titleSearchInput: {
    borderColor: 'white',
    borderWidth: 1,
    fontSize:13,
    // position: 'absolute',
    // left: 90,
    // right: 110,
    // width:180,
    flex:1,
    // alignSelf: 'center',
    paddingLeft: 6,
    marginRight:0,
    marginLeft: 10,
    // bottom: 6,
    // top: 6,
    // zIndex:9,
    height: 32,
    borderRadius: 4,
    backgroundColor:'white',
    // color:'#ddd',
    alignItems: 'center',
  },
});

export default MapAutocomplete;
