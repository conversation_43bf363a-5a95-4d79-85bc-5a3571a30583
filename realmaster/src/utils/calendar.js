import RNCalendarEvents from 'react-native-calendar-events';
import {
  Alert,
  Platform
} from 'react-native';
import {
  l10n
} from './i18n';
import {
  request,
  PERMISSIONS
} from 'react-native-permissions';
var StrPermissionCalendar;
if (Platform.OS === 'ios') {
  StrPermissionCalendar = PERMISSIONS.IOS.CALENDARS;
} else {
  StrPermissionCalendar = PERMISSIONS.ANDROID.WRITE_CALENDAR;
}
var RMCalendarEvents = {
  authorizationStatus(postCallback) {
    RNCalendarEvents.checkPermissions().then((CalendarAuthStatus) => {
      // CalendarAuthStatus = status;
      // RMStorage.setItemObj(CAL_AUTH_STATUS, CalendarAuthStatus);
      // console.log('+++++++', CalendarAuthStatus);
      postCallback(CalendarAuthStatus);
    }).catch((err) => {
      console.error(err)
      postCallback('error');
    })
  },
  authorizeEventStore(postCallback) {
    RNCalendarEvents.requestPermissions().then((CalendarAuthStatus) => {
      // CalendarAuthStatus = status;
      // RMStorage.setItemObj(CAL_AUTH_STATUS, CalendarAuthStatus);
      // console.log('xxxxxxx',CalendarAuthStatus);
      postCallback(CalendarAuthStatus);
    }).catch((err) => {
      console.error(err)
      postCallback('error');
    })
  },
  findCalendars(postCallback) {
    RNCalendarEvents.getCalendars().then((list) => {
      // console.log('+++++++',list);
      postCallback(list);
    }).catch((err) => {
      console.log(err)
      postCallback('error');
    })
  },
  saveEvent(json, postCallback) {
    function doSaveEvent() {
      var details = json.details || {};
      var title = json.title || details.title;
      var opt = {};
      if (details.exceptionDate) {
        opt.exceptionDate = details.exceptionDate;
      }
      if (details.futureEvents) {
        opt.futureEvents = details.futureEvents ? true : false;
      }
      // To update an event, the event id must be defined. - wiki guide
      // console.log(json)
      // var values = ['id', 'startDate', 'endDate', 'calendarId', 'location', 'notes', 'allDay', 'recurrence', 'description']
      // for (let index = 0; index < values.length; index++) {
      //   let elem = values[index];
      //   if (json[elem]) {
      //     details[elem] = json[elem];
      //   }
      // }
      // console.log('save event:',title, details);
      RNCalendarEvents.saveEvent(title, details, opt).then((evtid) => {
        // console.log('+++saveEvent: ',evtid);
        postCallback(evtid);
      }).catch((err) => {
        if (/t have permissions/.test(err.toString())) {
          doAlert()
          return;
        }
        console.log(err)
        postCallback('Error: ' + err.toString());
      })
    }

    function doAlert(msg='') {
      Alert.alert(msg+l10n('Calendar permission is required to save events!'));
    }

    function doGetAuth() {
      // console.log('do get auth')
      // using alternative
      // request(StrPermissionCalendar).then(CalendarAuthStatus => {
      //     console.log('doGetAuth ret: '+CalendarAuthStatus)
      //     if (CalendarAuthStatus == 'undetermined') {
      //         doGetAuth()
      //     } else if (CalendarAuthStatus == 'denied') {
      //         doAlert()
      //     } else {
      //         doSaveEvent()
      //     }
      //   }).catch((err) => {
      //     console.error(err)
      //     postCallback('error');
      //   });
      // NOTE: has bug https://github.com/wmcmahan/react-native-calendar-events/issues/241
      RNCalendarEvents.requestPermissions().then((CalendarAuthStatus) => {
        // Alert.alert('requestResult:'+CalendarAuthStatus)
        if (CalendarAuthStatus == 'authorized') {
          doSaveEvent()
        } else {
          doAlert()
          postCallback('unauthorized');
        }
      }).catch((err) => {
        // doAlert(err.toString())
        // Alert.alert('requestError:'+err.toString())
        postCallback('error');
      })
      // setTimeout(() => {
      //   doSaveEvent()
      // }, 4000);
      // .then((CalendarAuthStatus) => {
      //     console.log('doGetAuth ret: '+CalendarAuthStatus)
      //     if (CalendarAuthStatus == 'undetermined') {
      //         doGetAuth()
      //     } else if (CalendarAuthStatus == 'denied'){
      //         doAlert()
      //     } else {
      //         doSaveEvent()
      //     }
      //   }).catch((err) => {
      //     console.error(err)
      //     postCallback('error');
      //   })
    }
    // 1. check permission
    // 2. request if none
    // 3. save event
    RNCalendarEvents.checkPermissions().then((CalendarAuthStatus) => {
      // Alert.alert('xxxxx'+CalendarAuthStatus+/undetermined/i.test(CalendarAuthStatus))
      if (/undetermined/i.test(CalendarAuthStatus)) {
        doGetAuth()
      } else if (CalendarAuthStatus == 'authorized') {
        doSaveEvent()
      } else {
        doGetAuth();
        // doAlert()
      }
    }).catch((err) => {
      console.error(err)
      postCallback('error');
    })
  },
  saveCalendar(json, postCallback) {
    var cal = json.details || {};
    // https://github.com/wmcmahan/react-native-calendar-events#Calendar-options
    // var values = ['title', 'color', 'entityType', 'name', 'accessLevel', 'ownerAccount', 'source'];
    // for (let index = 0; index < values.length; index++) {
    //   let elem = values[index];
    //   if (json[elem]) {
    //     cal[elem] = json[elem]
    //   }
    // }
    RNCalendarEvents.saveCalendar(cal).then((calid) => {
      // console.log('+++++++',calid);
      postCallback(calid);
    }).catch((err) => {
      console.log(err)
      postCallback('error');
    })
  },
  findEventById(json, postCallback) {
    var details = json.details || {};
    var id = json.id || details.id;
    // console.log('++++++',id)
    RNCalendarEvents.getEventById(id).then((evt) => {
      // console.log('+++++++',evt);
      postCallback(evt);
    }).catch((err) => {
      console.log(err)
      postCallback('error finding event');
    })
  },
  removeEvent(json, postCallback) {
    let opt = {};
    var details = json.details;
    var id;
    if('string' == typeof(details)){
      id = details;
    } else {
      id = json.id || details.id;
    }
    if (details.exceptionDate) {
      opt.exceptionDate = details.exceptionDate;
    }
    if (details.futureEvents) {
      opt.futureEvents = details.futureEvents;
    }
    RNCalendarEvents.removeEvent(id, opt).then((succeded) => {
      // console.log('+++++++',succeded);
      postCallback(succeded);
    }).catch((err) => {
      console.log(err)
      postCallback('error');
    })
  },
  fetchAllEvents(json, postCallback) {
    var details = json.details;
    var startDate = details.startDate;
    var endDate = details.endDate;
    var calendars = details.calendars || null;
    RNCalendarEvents.fetchAllEvents(startDate, endDate, calendars).then((evt) => {
      // console.log('+++++++',evt);
      postCallback(evt);
    }).catch((err) => {
      console.log(err)
      postCallback('error');
    })
  }
}
export {
  RMCalendarEvents
}
