import React, {Component} from 'react';
import {Platform, StyleSheet, Text,View} from 'react-native';
import RmIcon from '../RmIcon'

export default Rooms = (props) => {
  const {prop,page} = props;
  let bdrms,bthrms,gr;
  let color = '#6D6D6D';
  if (page == 'ad') {
    color = '#fff';
  }
  if (prop.bdrms || prop.rmbdrm) {
    let bdrmStr = '';
    if(prop.rmbdrm){
      bdrmStr = prop.rmbdrm;
    } else {
      bdrmStr = prop.bdrms+(prop.br_plus?'+'+prop.br_plus:'');
    }
    bdrms = (
      <View style={styles.room}>
        <RmIcon name={"rmbed"} color={color} size={14} />
        <Text style={[styles.roomNum,{color:color}]}>{bdrmStr}</Text>
      </View>
    )
  }
  if (prop.bthrms || prop.rmbthrm) {
    bthrms = (
      <View style={styles.room}>
        <RmIcon name={"rmbath"} color={color} size={14} />
        <Text style={[styles.roomNum,{color:color}]}>{prop.rmbthrm || prop.bthrms}</Text>
      </View>
    )
  }
  if (prop.gr || prop.rmgr) {
    gr = (
      <View style={styles.room}>
        <RmIcon name={"rmcar"} color={color} size={14} />
        <Text style={[styles.roomNum,{color:color}]}>{prop.rmgr || parseInt(prop.gr)}</Text>
      </View>
    )
  }
  return (
    <View style={styles.rooms}>
      {bdrms}
      {bthrms}
      {gr}
    </View>
  );
}
const styles = StyleSheet.create({
  rooms:{
    flexDirection:'row',
    alignItems:'center',
  },
  room:{
    flexDirection:'row',
    alignItems:'center',
    marginRight:5
  },
  roomNum:{
    marginLeft:3,
    fontSize:14
  },
})