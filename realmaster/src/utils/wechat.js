import * as WeChat from 'react-native-wechat-lib';
import {logError,alert,debug} from './logger';
import { Platform, Alert } from 'react-native';
import Constants from '../config/constants';
import appConfigIns from '../config/appConfig'
import { l10n } from './i18n';

let _wxIsInstalled = null;
let wechat = null;

function getWxIsInstalled(){
  return appConfigIns.getAppConfig('hasWechat') //_wxIsInstalled
}
//
// WeChat.addListener('SendMessageToWX.Resp',(info) => {
//   alert("SendMessageToWX.Resp:" + JSON.stringify(info));
// })
// WeChat.addListener('SendAuth.Resp',(info) => {
//   alert("SendAuth.Resp:" + JSON.stringify(info));
// })


function wxRegister (appId, universalLink) {
  //wechat = await WeChat.registerApp('wxcf33ce325754da25');
  // if (Platform.OS === 'ios'){ // ios universalLink
    // wechat = WeChat.registerAppWithDescription('wxcf33ce325754da25', "RealMaster");
  if (!appId){
    appId = 'wxcf33ce325754da25'
  }

  // console.log(appId,domain)
  wechat = WeChat.registerApp(appId, 'https://realmaster.com/').then((r)=>{
    // console.log('wxRegister result',r)
  })
  // }else{
  //   wechat = WeChat.registerApp('wxcf33ce325754da25'); // for debug mode ('wxc5bda304af7f72c0');//
  // }
  // console.log('wechat = ',wechat);


  // WeChat.isWXAppInstalled()
  // .then( result => {
  //   _wxIsInstalled = result;
  //   appConfigIns.setAppConfig({hasWechat:_wxIsInstalled})
  //   // console.log('Wechat Installed:' + result);
  // })
  // .catch( e => {
  //   _wxIsInstalled = 0;
  //   logError('WX Register Error:' + e,true);
  // });
  // return WeChat;
}
/*   new share method

*/
/**
 * 分享出去的标题不能太长
 * @param {string} titleString
 * @param {number} maxStrLength
 * @param isAddSuffix
 */
const cutTitleShort = (titleString, maxStrLength=30) => {
  if (!titleString) {
    return Constants.AppName;
  }
  let newStr;
  if (titleString.length > maxStrLength - 7) {
    newStr = `${titleString.substring(0, maxStrLength - 7)}...【${Constants.AppName}】`;
  } else {
    newStr = `${titleString}【${Constants.AppName}】`;
  }
  return newStr;
};
function wxShare(obj,cb){
  var method = 'shareWebpage';
  var defaultShareData = {
    title: obj.title,
    description: obj.description,
    // 'https://cdnparap130.paragonrels.com/ParagonImages/Property/p13/BCRES/262608273/0/640/480/f644ecdd50564fc042fba9272f847a76/16/ad361d32c1160c1bb0eaa9bf623169ae/262608273.JPG'
    thumbImageUrl: (obj.thumb || obj.thumbData), // 缩略图
    webpageUrl: (obj.url || obj.media.webpageUrl),
    scene: 0, // 分享到, 0:会话 1:朋友圈 2:收藏
    isRemoveSuffix: true,
  };
  if (obj.tp === 1 || obj.tp === 'moment' || obj.tp === 'timeline'){
    defaultShareData.scene = 1;
  } else if (obj.tp === 2 || obj.tp === 'fav' || obj.tp === 'favourite'){
    defaultShareData.scene = 2;
  }

  if (/imageUrl|imageFile|imageResource/.test(obj.type)) {
    defaultShareData.imageUrl = defaultShareData.thumbImageUrl; //'https://img.realmaster.com/mls/1/442/W4841442.jpg?5565395';
    delete defaultShareData.thumbImageUrl;
    method = 'shareImage';
    if (/^data:image/.test(defaultShareData.imageUrl)) {
      defaultShareData.type = 'imageResource';
      
      // 计算base64图片大小
      const base64Length = defaultShareData.imageUrl.length - (defaultShareData.imageUrl.indexOf(',') + 1);
      const base64Size = (base64Length * 3 / 4) / 1024; // 转换为KB
      global.rmLog(`wechat.js:101~~~base64Size`, base64Size);


      //这里已经分别尝试过lib的其他所有分享方法，都会有底层包报错，只能加大小限制
      // 如果是Android平台且base64图片大于200KB则不分享
      if (Platform.OS === 'android' && base64Size > 200) {
        return cb({err: l10n('The image is too large. Please download it and share locally.')});
      }
      
      // TypeError: nativeShareLocalImage is not a function
      // defaultShareData.imageUrl = '/sdcard/test.png';
      // method = 'shareLocalImage';
    }
  }
  // console.log(Object.keys(defaultShareData));
  // const isInstalled = await WeChat.isWXAppInstalled();
  // if (!isInstalled) {
  //   return cb('Not installed');
  // }
  //if (!_wxIsInstalled) return cb({err:'ERR_WECHAT_NOT_INSTALLED'});
  // var metadata = {...defaultShareData, ...shareMetadata};
  if (!defaultShareData.isRemoveSuffix) {
    defaultShareData.title = cutTitleShort(defaultShareData.title, 30);
  }
  // console.log('>> invoking ',method,defaultShareData);
  // defaultShareData.thumbImageUrl=
  // 'https://www.realmaster.com/wp-content/uploads/2023/04/202-5.jpg';
  // 'https://www.realmaster.com/wp-content/uploads/2023/04/111.jpg'
  WeChat[method](defaultShareData).then(function(res){
    // console.log('<< after invoke',res)
    if (res.errCode == -2){
      return cb({cancelled:1});
    }
    // if (res.errCode === 0) {
    // 在分享中取消 也正常返回
    return cb({ok:1,detail:res});
    // }
  }).catch(e=>{
    console.error(e);
    if(e == -2){
      return console.log('cancelled');
    }
    if (e instanceof WeChat.WechatError) {
      console.warn(e.stack);
    } else {
      console.log(e);
    }
    cb({err:e.toString()})
  })
}
function wxShare2 (obj,cb) {
  // We compromised with callback for the react-native-wechat inability to deal with Promise then/catch
  //if (!_wxIsInstalled) return cb({err:'ERR_WECHAT_NOT_INSTALLED'});
  // type{Strign} -> news|text|imageUrl|imageFile|imageResource|video|audio|file
  let method = 'shareToSession',opt = {
    type:       (obj.type || 'news'),
    thumbImage: (obj.thumb || obj.thumbData),
    title:      obj.title,
    description: obj.description,
    webpageUrl: (obj.url || obj.media.webpageUrl),
  };
  if (obj.tp === 1 || obj.tp === 'moment' || obj.tp === 'timeline'){
    method = 'shareToTimeline';
  }
  if (/imageUrl|imageFile|imageResource/.test(obj.type)) {
    opt.imageUrl = opt.thumbImage;
    delete opt.thumbImage;
    if (/^data:image/.test(opt.imageUrl)) {
      opt.type = 'imageResource'
    }
  }
  // console.log('+++++',method,opt);
  // try {
  //   WeChat[method](opt)
  //   .then(function(result) {
  //     debug("Share Result:" + JSON.stringify(result))
  //     console.error(result);
  //     if (result.errCode == -2){
  //       return cb({cancelled:1});
  //     }else{ //} if(result.errCode == 0){
  //       return cb({ok:1});
  //     }
  //   },function(reject){
  //     console.log('rejected');
  //     console.log(reject);
  //   })
  //   // console.log(result);
  //   // .catch(e => {
  //   //   console.log('cought error',e);
  //   //   if (e == -2){
  //   //     return cb({cancelled:1})
  //   //   }
  //   //   cb({err:e})
  //   // })
  // } catch (e) {
  //   console.log('cought error',e);
  //   if (e == -2){
  //     return cb({cancelled:1})
  //   }
  //   cb({err:e})
  // }
  // WeChat.shareToSession({
  //
  // })
  WeChat[method](opt)
  .then(function(result) {
    // debug("Share Result:" + JSON.stringify(result))
    // console.error(result);
    if (result.errCode == -2){
      return cb({cancelled:1});
    }else{ //} if(result.errCode == 0){
      return cb({ok:1});
    }
  })
  .catch(e => {
    // console.log('wechat cought error',e);
    if (e == -2){
      return cb({cancelled:1})
    }
    cb({err:e})
  })

  // { //Timeline({
  //   type: 'news',
  //   thumbImage: 'https://realmaster.com/img/logo.png',
  //   title: 'Share URL',
  //   description: 'share web image to session',
  //   mediaTagName: 'email signature',
  //   messageAction: undefined,
  //   messageExt: undefined,
  //   webpageUrl: 'https://www.realmaster.com/'
  // });
  // Alert.alert("success:" + JSON.stringify(result));
}

function wxAuth(cb) {
  // console.log('>> sending auth request');
  WeChat.sendAuthRequest("snsapi_userinfo")
  .then( result => {
    if (result.errStr){
      if(result.errCode == -2){
        return cb({cancelled:1});
      }
      Alert.alert('Error: '+JSON.stringify(result));
      return cb(result)
    } else { //} if(result.errCode == 0){
      result.ok = 1;
      return cb(result);
    }
  })
  .catch( e => {
    // Alert.alert('wxAuth Error:',e.toString())
    if (e == -2){
      return cb({cancelled:1})
    }
    return cb({err:e});
  })
}




export {wxShare,wxAuth,wxRegister,getWxIsInstalled};
