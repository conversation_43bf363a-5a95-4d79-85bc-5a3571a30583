/**
 * @format
 */

import {AppRegistry} from 'react-native';
import App from './src/App';
import {name as appName} from './app.json';

// 全局log，用于调试，不需要import，直接使用global.rmLog，配合.vscode/_log.code-snippets，配置vscode项目全局快捷键打印文件和行数使用
global.rmLog = function(...args) {
    if (!__DEV__) { return; }
    
    // Check if the last argument is a log level
    const logLevel = typeof args[args.length - 1] === 'string' && (args[args.length - 1] === 'error' || args[args.length - 1] === 'log') 
        ? args.pop() 
        : 'log';    
    const logFunction = logLevel === 'error' ? console.error : console.log;
     // 第一个参数直接跟在emoji后面，之后的参数前面加换行
     logFunction(`🐳 ${args[0]}`, ...args.slice(1));
}

AppRegistry.registerComponent(appName, () => App);
