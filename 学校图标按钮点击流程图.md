# 地图学校图标按钮点击流程图

## 概述
本文档描述了React Native地图组件中，用户点击屏幕右侧学校图标按钮后，底部面板渲染筛选按钮的完整函数调用流程。

## 主要组件关系
- **RMMapSearchNative.jsx**: 主地图组件
- **MapSchool.jsx**: 学校功能混入类，继承自MapFeature
- **MapFeature.jsx**: 地图功能基类

## 流程图

```mermaid
flowchart TD
    A[用户点击右侧学校图标按钮] --> B[触发toggleLayerSelect方法]
    B --> C{判断当前layerSelect状态}
    C -->|显示| D[设置showLayerSelect=true<br/>showBackdrop=true]
    C -->|隐藏| E[设置showLayerSelect=false<br/>showBackdrop=false]
    
    D --> F[渲染底部图层选择面板layerModel]
    F --> G[遍历onOffMenuData数组]
    G --> H[FlatList渲染每个图层选项]
    H --> I[用户点击学校图层选项]
    
    I --> J[调用selectLayer方法]
    J --> K{判断点击的图层类型}
    K -->|学校图层Schools| L[调用item.onOff.toggleOnOff方法]
    L --> M[MapSchool.toggleOnOff方法被调用]
    M --> N{判断开关状态}
    N -->|开启| O[设置featureOn=true<br/>setState showPanel=true]
    N -->|关闭| P[设置featureOn=false<br/>清除学校数据]
    
    O --> Q[调用renderFeaturePanel方法]
    Q --> R[获取featureSetting._main._segments]
    R --> S[生成5个顶部筛选按钮配置]
    S --> T[FlatList渲染水平滚动按钮列表]
    
    T --> U[用户点击某个筛选按钮]
    U --> V[触发按钮的act回调函数]
    V --> W[调用setMainValue方法]
    W --> X[更新menuItem状态]
    X --> Y[调用renderModelMenu方法]
    Y --> Z[渲染底部详细筛选选项]
    
    Z --> AA[用户点击底部筛选选项]
    AA --> BB[调用toggleFilterOnOff方法]
    BB --> CC[更新featureSetting状态]
    CC --> DD[重新渲染界面]
```

## 详细函数调用链

### 1. 初始点击处理
```javascript
// RMMapSearchNative.jsx
toggleLayerSelect() {
    let showLayerSelect = this.state.showLayerSelect;
    let showBackdrop = this.state.showBackdrop;
    let state = {
        showLayerSelect: !showLayerSelect,
        showBackdrop: !showBackdrop,
    }
    this.setState(state, () => {
        if(state.showLayerSelect) {
            this.clearFeaturesPanels({msg:'clear',val:false})
        }
    })
}
```

### 2. 图层选择处理
```javascript
// RMMapSearchNative.jsx
selectLayer(item, opt={}) {
    let activeFeatureName = this.state.activeFeatureName;
    
    if(activeFeatureName != item.feature.name) {
        this.switchOffAllLayers(['MapProps','MapDistance'])
        item.onOff.toggleOnOff(); // 调用MapSchool的toggleOnOff
    } else {
        this.clearFeaturesPanels({name:activeFeatureName,msg:'clear',val:true})
    }
    
    if(['MapTransit','Schools'].indexOf(item.feature.name) > -1) {
        this.clearLayerSelect();
    }
    
    this.setState({
        activeFeatureName: item.feature.name
    })
}
```

### 3. 学校功能开启
```javascript
// MapSchool.jsx
toggleOnOff(onOff, silent = false) {
    let realOnOff = !this.featureOn;
    
    this.featureOn = realOnOff;
    
    if (realOnOff) {
        this.regOnPress();
    } else {
        this.map.removeOnPressFeature(this);
    }
    
    if (this.featureOn) {
        this.setState({showPanel: !silent});
        this.map.onRegionChangeComplete();
    } else {
        this.setState({sch: null});
    }
}
```

### 4. 渲染顶部5个筛选按钮
```javascript
// MapSchool.jsx
renderFeaturePanel() {
    if (!this.featureOn || !this.state.showPanel) return;
    
    let self = this;
    var btns = [];
    
    // 遍历主要筛选选项 (通常为5个: All, Public, Catholic, Private, University)
    this.state.featureSetting._main._segments.forEach((kTop, idx) => {
        let vTop = self.state.featureSetting[kTop];
        btns.push({
            clickable: vTop.clickable,
            k: kTop,
            n: vTop.nm, // 按钮显示名称
            color: vTop.checked ? 'black' : '#777',
            act: () => {
                // 点击按钮的回调函数
                if (idx == 1 || idx == 0) {
                    self.flatListRef.scrollToOffset({offset: 0});
                } else {
                    self.flatListRef.scrollToIndex({animated: true, index: idx});
                }
                self.setMainValue(kTop); // 设置主要筛选值
            },
            // 样式配置...
        });
    });
    
    return (
        <View key={'featurePanel'} style={styles.featurePanel}>
            <FlatList
                data={btns}
                horizontal={true}
                renderItem={({item}) => this.bottomButton(item)}
                keyExtractor={btn => btn.k}
            />
            <View style={{flex: 5}}>{this.renderModelMenu()}</View>
            {/* 关闭按钮等其他UI */}
        </View>
    );
}
```

### 5. 处理按钮点击
```javascript
// MapSchool.jsx
setMainValue(kTop) {
    let menuItem = this.state.menuItem;
    let stateFeatureSetting = this.state.featureSetting;
    
    if (kTop == menuItem) {
        return; // 如果是相同按钮，不处理
    }
    
    if (kTop == 'all') {
        return this.setState({menuItem: 'all'}, () => {
            this.resetFilterToAll();
        });
    } else {
        // 设置子选项为可点击状态
        let _segments = stateFeatureSetting[kTop]._segments || [];
        _segments.forEach(obj => {
            obj.clickable = true;
            obj.val = false;
            if (obj.l && obj.l.length) {
                obj.l.forEach(subType => {
                    stateFeatureSetting[subType].clickable = true;
                    stateFeatureSetting[subType].val = false;
                });
            }
        });
    }
    
    // 更新选中状态
    stateFeatureSetting[kTop].checked = true;
    stateFeatureSetting[kTop].val = true;
    stateFeatureSetting[menuItem].checked = false;
    stateFeatureSetting[menuItem].val = false;
    
    this.setState({
        menuItem: kTop, 
        featureSetting: stateFeatureSetting
    });
}
```

### 6. 渲染底部详细筛选菜单
```javascript
// MapSchool.jsx
renderModelMenu() {
    let self = this;
    let menuSegments = this.state.featureSetting[this.state.menuItem]._segments || [];
    let menuView = [];
    
    menuSegments.forEach((menuOpt, i) => {
        let btns = [];
        menuOpt.l.forEach(k => {
            v = self.state.featureSetting[k];
            btns.push({
                k: k,
                n: v.nm,
                color: v.clickable ? '#777' : '#f1f1f1',
                disabled: !v.clickable,
                act: () => {
                    if (v.clickable) {
                        self.toggleFilterOnOff(k); // 切换筛选选项
                    }
                },
                // 样式配置...
            });
        });
        
        menuView.push(
            <View key={'schMenu' + i}>
                <Text style={{fontSize: 15, fontWeight: 'bold'}}>
                    {l10n(menuOpt.nm)}
                </Text>
                <View style={styles.modelMenuRow}>
                    {self.bottomButtons([], btns)}
                </View>
            </View>
        );
    });
    
    return menuView;
}
```

### 7. 处理筛选选项切换
```javascript
// MapSchool.jsx
toggleFilterOnOff(k) {
    let stateFeatureSetting = this.state.featureSetting;
    
    // 切换选项状态
    stateFeatureSetting[k].val = !stateFeatureSetting[k].val;
    stateFeatureSetting[k].checked = !stateFeatureSetting[k].checked;
    
    this.setState({featureSetting: stateFeatureSetting});
}
```

## 5个主要筛选按钮说明

通常情况下，学校筛选面板顶部的5个按钮对应：

1. **All (全部)** - 显示所有类型学校
2. **Public (公立)** - 显示公立学校
3. **Catholic (天主教)** - 显示天主教学校  
4. **Private (私立)** - 显示私立学校
5. **University (大学)** - 显示大学院校

每个按钮点击后会：
- 更新UI状态（选中/未选中）
- 切换底部详细筛选选项的可用性
- 根据选择的类型重新获取和显示学校数据
- 滚动到对应的筛选区域

## 状态管理

整个流程中涉及的主要状态：
- `showLayerSelect`: 控制图层选择面板显示
- `showPanel`: 控制学校筛选面板显示
- `activeFeatureName`: 当前激活的功能层名称
- `menuItem`: 当前选中的筛选类型
- `featureSetting`: 详细的筛选配置对象
- `schs`: 学校数据对象

## 数据流向

```
用户交互 → UI状态更新 → 筛选条件变化 → 数据重新获取 → 地图重新渲染
```

整个流程确保了用户可以通过简单的点击操作来筛选和查看不同类型的学校信息，同时保持良好的用户体验和性能。 