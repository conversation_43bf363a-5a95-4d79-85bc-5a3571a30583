import { logError,debug } from './RMLogger'
import { Platform } from 'react-native'
// TODO: use play services
// import PlayServices from 'react-native-play-services';
import PlayServices from 'react-native-play-services';

// var NM = require('./RMMapNative')
// var RMMapNative = NM.RMMapNative;

// // RMMapNative = {}
// var RMMap = exports.RMMap = RMMapNative; //RMMapNative;
var GoogleMapProviderAvailable = true;
var GoogleMapNeedUpdate = false;
if (Platform.OS === 'android'){
  (async () => {
    const result = await PlayServices.checkPlayServicesStatus();
    //debug("Google:" + result);
    console.log("checkPlayServicesStatus Google Service: " + result);
    if(result === PlayServices.GooglePlayServicesStatus.GMS_NEED_UPDATE) {
      // useBM()
      //GoogleAPIAvailability.promptGooglePlayUpdate(false);
      GoogleMapProviderAvailable = false;
      GoogleMapNeedUpdate = true;
    } else if (result === PlayServices.GooglePlayServicesStatus.AVAILABLE){
      // useBM()
    } else if (result === PlayServices.GooglePlayServicesStatus.GMS_DISABLED){
      // useBM()
      console.log('No Google Service ');
      GoogleMapProviderAvailable = false;
    } else if (result === PlayServices.GooglePlayServicesStatus.INVALID){
      // useBM()
      console.log('No Google Service ');
      GoogleMapProviderAvailable = false;
    } else {
      // useBM()
      logError("Unknown Google Play Availablity Result:" + result)
      GoogleMapProviderAvailable = false;
    }
  })();
}
function getGoogleMapProviderNeedUpdate(){
  return GoogleMapNeedUpdate;
}
function getGoogleMapProviderAvailable(){
  return GoogleMapProviderAvailable;
}
async function goToMarket(){
  return await PlayServices.goToMarket();
}
async function goToSetting(){
  return await PlayServices.goToSetting();
}
export {getGoogleMapProviderNeedUpdate,getGoogleMapProviderAvailable,goToMarket,goToSetting}
