// NOTE: no longer used, uses google autocomplete and geocode,
// which costs a lot and force user to login and button
// new one use mapbox and own geocode

import React, {Component} from 'react';
import {
  Platform,
  Alert,
  StyleSheet,
  View,
  TextInput,
  Switch,
  TouchableHighlight,
  FlatList,
  Text,
  Dimensions,
} from 'react-native';
import RmIcon from '../RmIcon';
// import {RMStorage} from '../../RMStorage';
import storageIns from '../../utils/storage';
// import {SYSTEM,getAppConfig} from '../../RMSystem';
import Constants from '../../config/constants';

import {l10n} from '../../utils/i18n';
// import { RMPost,RMGet } from '../../RMNetwork';
import {mainRequest} from '../../utils/request';
// AIzaSyClYqxKKg2WdhFE9UDKA9ekFYHDIMLfdLA || AIzaSyDg3M9-IWfpswsto_nXAIX9ESHt987-SvY
// const ADDR_AUTOCOMPLETE_API = 'https://maps.googleapis.com/maps/api/place/autocomplete/json?key=AIzaSyClYqxKKg2WdhFE9UDKA9ekFYHDIMLfdLA&language=en&components=country:ca';
const ADDR_AUTOCOMPLETE_API = '/1.5/props/streetautocomplete';
export default class Address extends Component {
  constructor(props) {
    super(props);
    this.state = {
      shwGglAddr: 'off',
      addrs: [],
      searchTxt: props.searchTxt,
    };
    this.toggle = this.toggle.bind(this);
  }
  componentDidMount() {
    this.getData();
  }
  getData = async () => {
    try {
      // let shwGglAddr = await AsyncStorage.getItem('shwGglAddr');
      // let expireTime = await AsyncStorage.getItem('expireTime');
      let shwGglAddr = await storageIns.getItem(Constants.AcShowGlAddr);
      let expireTime = await storageIns.getItem(Constants.AcGlExpire);
      // console.log('+++++',shwGglAddr,expireTime)
      if (shwGglAddr == null) {
        shwGglAddr = 'off';
      } else {
        // DONT use string compare, Number.MAX_SAFE_INTEGER
        // console.log(new Date().getTime().toString(), expireTime, new Date().getTime().toString() >= expireTime)
        if (parseInt(new Date().getTime().toString()) >= parseInt(expireTime)) {
          shwGglAddr = 'off';
        }
      }
      let addrs = storageIns.getCacheItem(Constants.AcAddr);
      let state = {shwGglAddr};
      if (addrs) {
        // console.log('-------',addrs);
        state.addrs = addrs;
      }
      this.setState(state);
    } catch (e) {
      // error reading value
    }
  };
  toggle() {
    let {shwGglAddr} = this.state;
    shwGglAddr = shwGglAddr == 'on' ? 'off' : 'on';
    if (shwGglAddr == 'on') {
      // TODO: mechanism to update sysValues
      // let isLoggedIn = getAppConfig('isLoggedIn')
      // // console.log('super toggleOnOff:',onOff,needLogin,isLoggedIn)
      // if(!isLoggedIn){
      //   console.error('Need Login')
      //   return EventEmitter.emit("app.closeAndRedirectRoot",{url:'/1.5/user/login'});
      // }
      this.getAddress(this.props.searchTxt);
    } else {
      this.setState({addrs: []});
    }
    this.setState({shwGglAddr});
    storageIns.setItem('shwGglAddr', shwGglAddr);
    // RMStorage.setItemObj(SYSTEM.AC_SHOW_GL_ADDR,shwGglAddr);
    //remember use addr for 7 days
    var expireTime = new Date().getTime() + 7 * 24 * 60 * 60 * 1000;
    storageIns.setItem('expireTime', expireTime.toString());
    // RMStorage.setItemObj(SYSTEM.AC_GL_EXP,expireTime.toString());
  }
  static getDerivedStateFromProps(nextProps, prevState) {
    if (nextProps.searchTxt != prevState.searchTxt) {
      return {searchTxt: nextProps.searchTxt};
    } else {
      return null;
    }
  }
  componentWillUnmount() {
    // console.log('-------addr unmouted')
    storageIns.setCacheItem(Constants.AcAddr, this.state.addrs);
    clearTimeout(this.timer);
  }
  componentDidUpdate(prevProps, prevState) {
    const {shwGglAddr} = this.state;
    const {searchTxt} = this.props;
    // console.log('+++++did update',searchTxt,prevProps.searchTxt)
    if (prevProps.searchTxt !== searchTxt && shwGglAddr) {
      if (this.timer) {
        clearTimeout(this.timer);
      }
      this.timer = setTimeout(() => {
        if (shwGglAddr == 'on') {
          this.getAddress(searchTxt);
        }
      }, 1500);
      this.setState({searchTxt: searchTxt});
    }
  }
  getAddress(s) {
    // TODO: use rm geoaddr lib?
    // console.log('++++++do get address',s)
    // RMGet(`${ADDR_AUTOCOMPLETE_API}&input=${s}`,{},(err,responseJson={})=>{
    mainRequest({
      url: ADDR_AUTOCOMPLETE_API,
      method: 'post',
      data: {
        input: s,
      },
    })
      .then(responseJson => {
        if (responseJson.status == 'OK') {
          let addrs = [];
          const ps = responseJson.predictions;
          for (let i in ps) {
            let addr = ps[i];
            addrs.push({
              _id: addr.place_id,
              addr: addr.structured_formatting.main_text,
              city: addr.structured_formatting.secondary_text,
            });
          }
          this.setState({addrs});
        }
      })
      .catch(err => {
        console.error(err.toString());
      });
  }
  render() {
    let {shwGglAddr, addrs} = this.state;
    return (
      <View>
        <View style={styles.header}>
          <Text>{l10n('Address Results')}</Text>
          <Switch
            onValueChange={() => this.toggle()}
            value={shwGglAddr == 'on' ? true : false}
          />
        </View>
        <FlatList
          data={addrs}
          keyExtractor={item => item._id}
          renderItem={({item}) => (
            <TouchableHighlight
              onPress={() => {
                this.props.search('addr', item);
              }}>
              <View style={styles.addr}>
                <RmIcon
                  style={styles.icon}
                  color={'rgb(130,130,130)'}
                  name={'map-marker'}
                  size={20}
                />
                <View>
                  <Text>{item.addr}</Text>
                  <Text>{item.city}</Text>
                </View>
              </View>
            </TouchableHighlight>
          )}
        />
      </View>
    );
  }
}
const styles = StyleSheet.create({
  header: {
    padding: 10,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  addr: {
    padding: 10,
    backgroundColor: '#fff',
    flexDirection: 'row',
    alignItems: 'center',
  },
  icon: {
    marginRight: 10,
  },
});
