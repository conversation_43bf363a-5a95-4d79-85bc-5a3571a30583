import 'react-native-gesture-handler';
import React, { useState, useEffect, useRef } from 'react';
import { BackHandler, Vibration, Keyboard, Alert, Linking, Platform, Text, AppState, NativeModules, View } from 'react-native';
import { NavigationContainer, StackActions, CommonActions } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import Orientation from 'react-native-orientation-locker';
import Clipboard from '@react-native-clipboard/clipboard';
import DeviceInfo from 'react-native-device-info';
import Permissions from 'react-native-permissions';
import { Notifications } from 'react-native-notifications';
import { AppInstalledChecker, CheckPackageInstallation } from 'react-native-check-app-install';

import RMWebview from './screens/RMWebview';
import RMAdmin from './screens/RMAdmin';
import QRCodeScreen from './screens/QRCodeScreen';
import RMAutoCompleteNative from './screens/RMAutoCompleteNative';
import RMMapSearchNative from './screens/RMMapSearchNative';
import RMMapNative from './screens/RMMapNative';
import RMNetwork from './screens/RMNetwork';

import { bootup } from './utils/bootup';
import { eventEmitter, getDomainByUrl, showDialog, isPopupPushMessageUrl } from './utils/common';
import { getColor } from './utils/RMstyle';
import appConfigIns from './config/appConfig';
import storageIns from './utils/storage';
import networkIns from './utils/network';
import { setAppLang, l10n, initL10n } from './utils/i18n';
import { mapUtil, urlFix } from './utils/business';
import { FBLogin, FBLogout, FBshare } from './utils/facebook';
import { GoogleLogin, hasPlayServices } from './utils/google';
import { AppleLogin, AppleRevoke } from './utils/apple';
import { wxRegister, wxShare, wxAuth, getWxIsInstalled } from './utils/wechat';
import { singleSocialShare, socialShare } from './utils/share';
import Constants from './config/constants';
import notificationService from './services/NotificaitonService';
import { mainRequest, fetchSystemVals } from './utils/request';
import { RMCalendarEvents } from './utils/calendar';
import { readGeoPosition, watchGeoPosition, getGeoPosition, getCacheGeoPos, clearWatch } from './utils/location';
import { downloadImage } from './utils/files';
import cookiesIns from './utils/cookies';
import eventHandler from './utils/event';
import { logError, alert } from './utils/logger';
import SplashScreen from './screens/SplashScreen';
import { rmPermissions, handleLocationPermission } from './utils/permissions';
import env from './config/env';

const App = () => {
  let StrPermissionLocation;

  global.rmLog(`App.jsx:51~~~App`, env.enviroment);

  if (Platform.OS === 'ios') {
    StrPermissionLocation = Permissions.PERMISSIONS.IOS.LOCATION_WHEN_IN_USE;
  } else {
    StrPermissionLocation = Permissions.PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION;
  }

  const Stack = Platform.OS === 'ios' ? createStackNavigator() : createNativeStackNavigator();
  //对应RMwebview.jsx中的useImperativeHandle(ref, 内部定义的方法带出来
  const rmwebviewRef = useRef(null);
  const navigatorRef = useRef();
  const routeStack = useRef([]);
  const pageLoadCallstack = useRef([]);
  const appLoadCallstack = useRef([]);
  const webRouteData = useRef({});
  const appState = useRef(AppState.currentState);
  const qrcodeCallback = useRef();

  const showSplashFromurl = useRef(true);
  const [splashRefresh, setSplashRefresh] = useState('');

  const linkingEventTimer = useRef(null);


  const hideSplash = () => {
    setSplashRefresh('splash');
  }



  const clearCallstack = (stack) => {
    while (stack.length > 0) {
      let func = stack.shift();
      if (typeof func === 'function') {
        func();
      }
    }
  };


  const whenAppReady = fn => {
    appLoadCallstack.current.push(fn);
  };


  const disableScroll = (p, rmweb) => {
    // if (Platform.OS === 'android') {
    //   return;
    // }
    // p = p ? true : false; //cast
    // // console.log('setDisableScroll: '+p);
    // // Tested no effect
    // // this.setState({scrollEnabled:p});
    // // Tested work but not as expected
    // let target;
    // if ((target = currentRoute.current.rmweb || rmweb)) {
    //   // console.log(target.index,rmweb);
    //   if (target.setDisableScroll) {
    //     target.setDisableScroll(p);
    //   }
    // }
    // EventEmitter.emit("setDisableScroll",p);
  };

  // handle backbutton click in android
  const askExit = () => {
    Alert.alert(l10n('Quit?'), l10n('Close RealMaster APP?'), [
      {
        text: l10n('Quit'),
        onPress: () => {
          BackHandler.exitApp();
        },
      },
      { text: l10n('Cancel'), onPress: () => { } },
    ]);
  };

  const handleAndroidBackButton = () => {
    const currentRoute = routeStack.current[routeStack.current.length - 1];
    if (currentRoute.index > 0) {
      routePop();
    } else {
      if (currentRoute && currentRoute.rmweb && !currentRoute.rmweb.goBack()) {
        askExit();
      }
    }
    return true;
  };

  const openInBrowser = url => {
    Linking.openURL(urlFix(url));
  };

  const gotQrcode = result => {
    if (qrcodeCallback.current) {
      setTimeout(() => {
        qrcodeCallback.current(result);
        qrcodeCallback.current = null;
      }, 50);
    }
    routePop();
  };

  const routePush = (route, opt = {}) => {
    //要添加到路由栈index为最大
    route.index = routeStack.current.slice(-1)[0].index + 1
    routeStack.current.push(route);
    //要添加的路由赋值给当前操作的路由
    // currentRoute.current = route;
    opt = {
      ...route,
      ...opt,
      index: opt.index ?? route.index, //opt.index可能为0
      uniqueRouteId: Date.now(),
    };

    // 判断是 web 页面还是其他页面来决定传递参数的方式
    let pushAction
    if (route.tp === 'web') {
      // Web 页面参数通过 appPropsData 传递
      webRouteData.current = opt;
      pushAction = StackActions.push(route.tp);
    } else {
      // 其他页面传递参数到路由, 不影响未完全迁移代码
      pushAction = StackActions.push(route.tp, opt);
    }
    navigatorRef.current && navigatorRef.current.dispatch(pushAction);
  };




  const popup = json => {
    const route = {
      url: urlFix(json.url),
      tp: 'web',
      jsonTp: 'popup',
      barColor: json.barColor,
      textColor: json.textColor,
      init: false,
    };
    if (json.cfg && json.cfg.title) {
      route.title = json.cfg.title;
    }

    global.rmLog(`[App.jsx:204~~~~~~~~~~popup]`, route);

    routePush(route, webDefaultParamObj(route));
  };

  const routePop = url => {
    global.rmLog(`App.jsx:210~~~routePop`, url);
    if (routeStack.current.length > 1) {
      navigatorRef.current && navigatorRef.current.dispatch(StackActions.pop(1));
      routeStack.current.pop();
      // currentRoute.current = routeStack.current[routeStack.current.length - 1];
      if (url && url.checkAlive) {
        eventEmitter.emit('webview.checkAlive', { tp: 'bootup', msg: 'app come to foreground' });
      }
    }
    if ('object' == typeof url) {
      url = url.url;
    }
    if (url) {
      setTimeout(() => {
        const currentRoute = routeStack.current[routeStack.current.length - 1];
        currentRoute.url = urlFix(url);
        global.rmLog(`[App.jsx:routePop] emit navigation.replace`, currentRoute);
        eventEmitter.emit('navigation.replace', currentRoute);
      }, 150);
    }
  };


  const routePopToTop = (route) => {
    if (!route || !navigatorRef.current) return;
  
    // Reset route stack to only contain first route
    routeStack.current.length = 1;
    webRouteData.current = {
      ...route,
      forceUpdate: Date.now() // 添加一个强制更新标记
    };

    global.rmLog(`App.jsx:239~~~routePopToTop`, route);
    try {
        const currentState = navigatorRef.current.getState();
        
        if (!currentState || currentState.index === 0) {
            global.rmLog(`[App.jsx:routePopToTop] already at root, emit navigation.replace`, route.url);
            // 直接触发更新
            eventEmitter.emit('webview.forceUpdate', {
                url: route.url,
                init: route.init || false,
                index: 0
            });
            return;
        }

        const unsubscribe = navigatorRef.current.addListener('state', (e) => {
            global.rmLog(`App.jsx:249~~~state`, e);
            if (e.data.state.index === 0) {
                unsubscribe();
                global.rmLog(`[App.jsx:routePopToTop] emit navigation.replace`, route.url);
                // 先触发强制更新
                eventEmitter.emit('webview.forceUpdate', {
                    url: route.url,
                    init: route.init || false,
                    index: 0
                });
                // 然后再触发导航替换
                setTimeout(() => {
                    eventEmitter.emit('navigation.replace', {
                        url: route.url,
                        init: route.init || false,
                        index: 0
                    });
                }, 100);
            }
        });

        navigatorRef.current.dispatch(StackActions.popToTop());
    } catch (error) {
        console.error('Navigation failed:', error);
    }
};

  const routeResetAndPush = (route, opt = {}) => {
    //要添加到路由栈index为最大
    route.index = routeStack.current.length;
    // currentRoute.current = route;

    opt = {
      ...route,
      ...opt,
      index: opt.index ?? route.index,
      uniqueRouteId: Date.now(),
    };

    const resetName = route.tp == 'network' ? 'network' : 'mapSearch';

    let pushAction = StackActions.push(route.tp, opt);

    navigatorRef.current &&
      navigatorRef.current.dispatch(state => {
        // TODO: gRoutes remove mapSearch
        const routes = state.routes.filter(r => {
          return r.name !== resetName;
        });
        // NOTE: if lose sync, this.rmweb will = null
        routeStack.current = routeStack.current.filter(r => {
          // console.log('++++gRoute name',r.name,r.tp)
          return r.tp !== resetName;
        });
        return CommonActions.reset({
          ...state,
          routes,
          index: routes.length - 1,
        });
      });
    routeStack.current.push(route);
    //再push
    navigatorRef.current && navigatorRef.current.dispatch(pushAction);
  };

  const closeAndRedirectRoot = ({ url, init }) => {
    if (!url) {
      return;
    }

    routePopToTop({
      url: urlFix(url),
      toolbar: false,
      tp: 'web',
      init,
    });
  };

  const createCallback = (cb, index) => {
    return (ret) => {

      if (typeof cb === 'function') {
        cb(ret);
      } else {
        postMessage(ret, cb, null, index);
      }
    };
  };

  const postCallback = (json, index) => {

    if (json.cb) {
      return createCallback(json.cb, index);
    }
    return (arg1, arg2, arg3) => postMessage(arg1, arg2, arg3, index)
  };

  const postMessage = (p, cb, tp, index) => {
    global.rmLog(`App.jsx:342~~~postMessage`, routeStack.current, p, cb, tp, index);
    let message = {
      p
    }
    if (tp) message.tp = tp;
    if (cb) message.cb = cb;
    message = JSON.stringify(message);
    const targetWebview = routeStack.current.find(route => route.index === index)?.rmweb
    if (targetWebview) {
      targetWebview.postMessage(message);
    } else {
      pageLoadCallstack.current.push(() => postMessage(p, cb, tp, index))
    }
  };

  const onMessage = (body, index) => {
    let isNativeMessage = false, message;
    if (typeof body === 'object') {
      isNativeMessage = true;
      message = body.msg;
    } else {
      message = body;
    }
    try {
      const json = JSON.parse(message);

      if (isNativeMessage && body.cb) {
        json.cb = body.cb;
      }
      handleMessage(json, index)
    } catch (error) {
      if (typeof message === 'string' && message[0] === ':') {
        return;
      } else if (/Unexpected\stoken/.test(error.toString())) {
        return;
      } else {
        logError(`Unknown command: ${error}`);
      }
    }
  }

  const handleMessage = async (json, index) => {
    global.rmLog(`App.jsx:384~~~handleMessage`, json);
    switch (json.tp) {
      case 'webview.checkAlive': {
        eventEmitter.emit('webview.checkAlive', { tp: 'api', msg: 'checkalive req from web' });
        break;
      }
      case 'clipboard.get': {
        const text = await Clipboard.getString();
        postCallback(json, index)(text || '');
        break;
      }
      case 'clipboard.set': {
        Clipboard.setString(json.p || '');
        break;
      }
      case 'getMemoryDetail': {
        const callback = postCallback(json, index);
        const result = {};
        result.totalMemory = await DeviceInfo.getTotalMemory();
        result.usedMemory = await DeviceInfo.getUsedMemory();
        result.maxMemory = await DeviceInfo.getMaxMemory();
        callback(result);
        break;
      }
      case 'exitApp': {
        askExit();
        break;
      }
      case 'getSystemVersion': {
        postCallback(json, index)(DeviceInfo.getSystemVersion());
        break;
      }
      case 'getDeviceId': {
        postCallback(json, index)(DeviceInfo.getDeviceId());
        break;
      }
      case 'getUniqueId': {
        postCallback(json, index)(await DeviceInfo.getUniqueId());
        break;
      }
      case 'hasGoogleService': {
        postCallback(json, index)(mapUtil.getGoogleMapProviderAvailable());
        break;
      }
      case 'hasPlayServices': {
        hasPlayServices(postCallback(json, index));
        break;
      }
      case 'err': {
        if (!/RMSrv/.test(json.err)) {
          console.warn(json.err);
        }
        break;
      }
      case 'log': {
        console.log(json.p);
        break;
      }
      case 'ver': {
        const buildNumber = DeviceInfo.getBuildNumber();
        const buildId = await DeviceInfo.getBuildId();
        const rmVersion = buildId + '-' + buildNumber;
        postCallback(json, index)(rmVersion);
        break;
      }
      case 'coreVer': {
        postCallback(json, index)(DeviceInfo.getVersion());
        break;
      }
      case 'setBarColor':
        break;
      case 'pushToken': {
        const callback = postCallback(json, index);
        storageIns.getItemObj(Constants.PnToken, (err, token) => {
          if (!token) return
          global.rmLog(`[App.jsx:454~pushToken]`, token);
          callback(token, null, 'pushToken', 0);
        });
        break;
      }
      case 'fetch': {
        const callback = postCallback(json, index);
        const { url, opt = {} } = json;
        const { method = 'GET', body: data = {} } = opt;
        const requestOptions = {
          url,
          method,
          data,
          headers: {
            Accept: 'application/json',
            'Content-Type': 'application/json',
          },
        };

        mainRequest(requestOptions)
          .then(response => callback(typeof response === 'object' ? JSON.stringify(response) : response))
          .catch(err => callback(`Error: ${err.toString()}`));
        break;
      }
      case 'permissions.request.notification': {
        const callback = postCallback(json, index);
        Permissions.requestNotifications(['alert', 'sound']).then(({ status, settings }) => {
          storageIns.setItem(Constants.PermNotifResult, JSON.stringify(status));
          registerPnToken(status)
          callback(status);
        });
        break;
      }
      case 'calendar.authorizationStatus': {
        RMCalendarEvents.authorizationStatus(postCallback(json, index));
        break;
      }
      case 'calendar.authorizeEventStore': {
        RMCalendarEvents.authorizeEventStore(postCallback(json, index));
        break;
      }
      case 'calendar.saveEvent': {
        RMCalendarEvents.saveEvent(json, postCallback(json, index));
        break;
      }
      case 'calendar.findCalendars': {
        RMCalendarEvents.findCalendars(postCallback(json, index));
        break;
      }
      case 'calendar.saveCalendar': {
        RMCalendarEvents.saveCalendar(json, postCallback(json, index));
        break;
      }
      case 'calendar.findEventById': {
        RMCalendarEvents.findEventById(json, postCallback(json, index));
        break;
      }
      case 'calendar.removeEvent': {
        RMCalendarEvents.removeEvent(json, postCallback(json, index));
        break;
      }
      case 'calendar.fetchAllEvents': {
        RMCalendarEvents.fetchAllEvents(json, postCallback(json, index));
        break;
      }
      case 'permissions.request.requestMultiple': {
        const callback = postCallback(json, index);
        const permissionKeys = json.p?.map(key => Permissions[key]) || [];
        if (!permissionKeys.length) return callback('error');

        Permissions.requestMultiple(permissionKeys).then(statuses => { callback(statuses) });
        break;
      }
      case 'permissions.request.requestMultipleLocAndNotificationAndroid': {
        const callback = postCallback(json, index);
        Permissions.requestNotifications(['alert', 'sound']).then(({ status, settings }) => {
          storageIns.setItem(Constants.PermNotifResult, JSON.stringify(status));
          storageIns.setItem(Constants.PermNotifResultSettings, JSON.stringify(settings));
          registerPnToken(status);
          Permissions.request(StrPermissionLocation).then(response => {
            if (response === Permissions.RESULTS.GRANTED) {
              getGeoPosition();
              watchGeoPosition();
            }
            callback({ noti: status, loca: response });
          });
        });
        break;
      }
      case 'permissions.request.location': {
        const platform = Platform.OS;
        const systemVersion = platform === 'ios' ? DeviceInfo.getSystemVersion().split('.')[0] : null;
        const hasPreciseLocation = platform === 'ios' && systemVersion >= '14';
        const callback = postCallback(json, index);
        const requestNormalLocation = () => {
          Permissions.request(StrPermissionLocation).then(response => {
            if (response === Permissions.RESULTS.GRANTED) {
              getGeoPosition();
              watchGeoPosition();
            }
            callback(response);
          });
        };

        if (!hasPreciseLocation) {
          return requestNormalLocation();
        }

        Permissions.requestLocationAccuracy({ purposeKey: 'YOUR-PURPOSE-KEY' })
          .then(accuracy => {
            if (/full|reduced/.test(accuracy)) {
              getGeoPosition();
              watchGeoPosition();
            }
            callback(accuracy);
          })
          .catch(() => requestNormalLocation());
        break;
      }
      case 'permissions.request.locationaccuracy': {
        const callback = postCallback(json, index);
        Permissions.requestLocationAccuracy({ purposeKey: 'YOUR-PURPOSE-KEY' })
          .then(accuracy => { callback(accuracy) })
          .catch(err => { callback(err.toString()) });
        break;
      }

      case 'permissions.check.locationaccuracy': {
        const callback = postCallback(json, index);
        Permissions.checkLocationAccuracy()
          .then(accuracy => { callback(accuracy) })
          .catch(err => { callback(err.toString()) });
        break;
      }
      case 'permissions.check.location': {
        const callback = postCallback(json, index);
        Permissions.check(StrPermissionLocation).then(response => { callback(response) });
        break;
      }
      case 'downloadImage': {
        const { url, opt } = json;
        const callback = postCallback(json, index);
        downloadImage({ ...opt, url }, (err, result) => {
          if (err) logError(`downloadImage${err}`);
          callback(err ? err : 'Saved');
        });
        break;
      }
      case 'permissions.check.notification': {
        //从设置打开或关闭通知权限
        const callback = postCallback(json, index);
        const { status } = await Permissions.checkNotifications()
        callback(status);
        registerPnToken(status)
        break;
      }
      case 'permissions.openSettings': {
        Permissions.openSettings();
        break;
      }
      case 'permissions.openSettings.notification': {
        Linking.openURL('App-Prefs:NOTIFICATIONS_ID&path=com.realmaster');
        break;
      }
      case 'permissions.openSettings.location': {
        Linking.openURL('App-Prefs:LOCATION_SERVICES&path=com.realmaster');
        break;
      }
      case 'storage.getItemObj': {
        const callback = postCallback(json, index);
        const { key, readCache = false } = json;
        storageIns.getItemObj(key, { readCache }, (err, result) => {
          callback(err ? `@Error:${err.toString()}` : JSON.stringify(result));
        });
        break;
      }
      case 'storage.setItemObj': {
        const callback = postCallback(json, index);
        const { key, value, stringify = false, store = false } = json;
        storageIns.setCacheItem(key, stringify ? JSON.stringify(value) : value, store, (err) => {
          callback(err);
        });
        break;
      }
      //TODO:web端需要判断对应逻辑来控制地图层级
      case 'getNativeRouteStack': {
        // const canBackToMap = routeStack.current.some(route => route.tp === 'mapSearch');
        const callback = postCallback(json, index);
        callback(routeStack.current, null, 'getNativeRouteStack')
        break;
      }
      case 'storage.removeItemObj': {
        storageIns.removeItem(json.key);
        break;
      }
      case 'refreshSystemValue': {
        const callback = postCallback(json, index);
        fetchSystemVals({}, (ret) => { callback(ret) });
        break;
      }

      case 'canOpenURL': {
        const callback = postCallback(json, index);
        const { url } = json;
        if (!url) return callback(false);

        Linking.canOpenURL(url)
          .then(isInstalled => callback(isInstalled))
          .catch(err => callback(err.toString()));
        break;
      }
      case 'AppInstalledChecker': {
        const callback = postCallback(json, index);
        const { name } = json;
        if (!name) return callback(false);

        if (Platform.OS !== 'ios') {
          let packageName = name;
          if (!/^com/.test(packageName)) {
            const tryGetPackageName = AppInstalledChecker.getPackageName?.(packageName);
            if (!/^com/.test(tryGetPackageName)) return callback('Error: use package name');
            packageName = tryGetPackageName;
          }
          AppInstalledChecker.isAppInstalledAndroid(packageName).then(isInstalled => { callback(isInstalled) });
        } else {
          AppInstalledChecker.isAppInstalled(name)
            .then(isInstalled => {
              callback(isInstalled)
            })
            .catch(err => callback(err.toString()));
        }
        break;
      }
      case 'editSysConfig':
        break;
      case 'setSystemValue': {
        const { key, value } = json;
        const parsedValue = ['maximumZ'].includes(key) ? parseInt(value) || 0 : value;
        appConfigIns.setAppConfig({ [key]: parsedValue });
        break;
      }
      case 'setCookie': {
        const { p, domain } = json;
        if (p && domain) {
          cookiesIns.setCookie(getDomainByUrl(domain), null, p);
        }
        break;
      }
      case 'showSystemValue': {
        Alert.alert('appConfig', JSON.stringify(appConfigIns.appConfig()));
        break;
      }
      case 'vibrate': {
        Vibration.vibrate([1000]);
        break;
      }
      case 'facebook.share': {
        FBshare(json.content, postCallback(json, index));
        break;
      }
      case 'wechat.share': {
        wxShare(json.p, postCallback(json, index));
        break;
      }
      case 'wechat.auth': {
        wxAuth(postCallback(json, index));
        break;
      }
      case 'facebook.auth': {
        FBLogout({}, () => FBLogin({ permissions: json.permissions }, postCallback(json, index)));
        break;
      }
      case 'facebook.logout': {
        FBLogout({}, result => postCallback(json, index)(result));
        break;
      }
      case 'google.auth': {
        GoogleLogin({ silent: json.silent }, postCallback(json, index));
        break;
      }
      case 'apple.auth': {
        AppleLogin({ silent: json.silent }, postCallback(json, index));
        break;
      }
      case 'wechat.has': {
        postCallback(json, index)(getWxIsInstalled());
        break;
      }
      case 'share': {
        socialShare(json.p, postCallback(json, index));
        break;
      }
      case 'singleShare': {
        singleSocialShare(json.p, postCallback(json, index));
        break;
      }
      case 'linking': {
        const { dest, url } = json.p || {};
        const callback = postCallback(json, index);
        if (['sms', 'mailto', 'tel'].includes(dest)) {
          Linking.openURL(url)
            .then(() => callback({ ok: 1 }))
            .catch(e => callback({ ok: 0, err: e }));
        }
        break;
      }
      case 'getRoutesCounts': {
        const callback = postCallback(json, index);
        callback({ ok: 1, cnt: routeStack.current.length });
        break;
      }
      case 'keyboard.dismiss': {
        Keyboard.dismiss();
        break;
      }
      case 'disableScroll': {
        disableScroll(json.p);
        break;
      }
      case 'sendSMS': {
        console.warn('no longer used');
        break;
      }
      case 'openInBrowser': {
        openInBrowser(json.url || json.p);
        break;
      }
      case 'pageContent': {
        const route = {
          url: urlFix(json.url),
          tp: 'web',
          selector: json.sel,
          toolbar: true,
          title: json.title,
          wait: json.wait,
          hide: json.hide,
          barColor: json.barColor,
          textColor: json.textColor,
          noClose: json.noClose,
          bold: json.bold,
          indicator: json.text,
          cancelable: json.cancelable,
          jsonTp: 'pageContent',
          init: false,
          forVerticalIOS: json.forVerticalIOS,
          callback: postCallback(json, index)
        };
        if (!route.barColor) {
          route.barColor = await getColor('commonBarColor')
        }
        if (!route.textColor) {
          route.textColor = await getColor('commonBarText')
        }
        if (json.colorName) {
          route.barColor = await getColor(json.colorName)
        }
        if (json.toolbar != null) {
          route.toolbar = json.toolbar ? true : false;
        }

        routePush(route, webDefaultParamObj(route));
        break;
      }
      case 'closePopup': {
        const opt = json.checkAlive ? { checkAlive: true } : {};
        routePop(opt);
        break;
      }
      case 'closeAndRedirect': {
        routePop(json.url);
        break;
      }
      case 'closeAndRedirectRoot': {
        routePopToTop({ url: urlFix(json.url), toolbar: false, tp: 'web' });
        break;
      }
      case 'popup': {
        if (!json.barColor) {
          json.barColor = await getColor('commonBarColor');
        }
        if (!json.textColor) {
          json.textColor = await getColor('commonBarText');
        }
        popup(json);
        break;
      }
      case 'admin': {
        if (json.cb) delete json.cb;
        const route = { tp: 'admin', ...json };
        routePush(route, route);
        break;
      }
      case 'qrcode': {
        //需要先请求相机权限，否责直接运行，安卓第一次会黑屏https://github.com/teslamotors/react-native-camera-kit/issues/579

        const routePushToQRcode = () => {
          const route = {
            tp: 'qrcode',
            cancelButtonVisible: true,
            cancelButtonTitle: 'Close',
          };
          qrcodeCallback.current = postCallback(json, index);
          routePush(route, route);
        }

        const cameraPermisson = Permissions.PERMISSIONS[Platform.OS.toUpperCase()].CAMERA;
        Permissions.check(cameraPermisson).then(response => {
          if (response === Permissions.RESULTS.GRANTED) {
            routePushToQRcode()
          } else if (response === Permissions.RESULTS.DENIED) {
            Permissions.request(cameraPermisson).then(response => {
              if (response === Permissions.RESULTS.GRANTED) {
                routePushToQRcode()
              } else {
                Alert.alert(l10n('Alert'), l10n('Camera permission denied!'));
              }
            })
          } else {
            Alert.alert(l10n('Alert'), l10n('Camera permission bloacked!'));
          }
        })

        break;
      }
      case 'autocomplete': {
        if (json.cb) delete json.cb;
        const route = { tp: 'autocomplete', ...json, city: json.city };
        routePush(route, route);
        break;
      }
      case 'confirm': {
        const callback = postCallback(json, index);
        Alert.alert(
          json.title,
          json.msg,
          json.btns.map((name, index) => ({
            text: name,
            onPress: () => callback(index + 1),
          }))
        );
        break;
      }
      case 'map': {
        const route = {
          tp: 'map',
          ...json,
          coords: { latitude: json.lat, longitude: json.lng },
        };
        routePush(route, route);
        break;
      }
      case 'mapSearch': {
        if (json.cb) delete json.cb;
        if (json.loc && !json.lat) {
          json.lat = json.loc[0];
          json.lng = json.loc[1];
        }
        if (json.zoom && !json.delta) {
          json.delta = 0.0025;
        }
        const route = {
          tp: 'mapSearch',
          ...json,
          coords: { latitude: json.lat, longitude: json.lng, delta: json.delta },
        };
        json.resetmap ? routeResetAndPush(route, route) : routePush(route, route);
        break;
      }
      case 'setApplicationIconBadgeNumber': {
        Notifications.ios.setBadgeCount(parseInt(json.number) || 0);
        break;
      }
      case 'getApplicationIconBadgeNumber': {
        const num = Notifications.ios.getBadgeCount();
        callback(num);
        break;
      }
      case 'alert': {
        let message = json.msg || json.p;
        message = message + '';
        const title = json.title || (message.length > 35 ? l10n('Alert') : '');
        //暂时隐藏load failed提示
        if (message.includes('Load failed')) return
        try {
          message = JSON.parse(message);
        } catch (error) {
          console.warn('alert error:', error.toString());
        }
        Alert.alert(title, message);
        break;
      }
      case 'geoPosition': {
        //首先获取系统层面是否打开定位
        const isSystemLocation = await DeviceInfo.isLocationEnabled();
        if (!isSystemLocation) {
          return showDialog({
            message: 'System location permission not granted!',
            confirmText: 'Open Settings',
            handleConfirm: () => {
              rmPermissions.openSettings()
            }
          })
        }

        const handleGranted = () => {
          const geoPosition = getCacheGeoPos();
          if (geoPosition && geoPosition.coords) {
            getGeoPosition({ noAlert: true });
            return postCallback(json, index)(geoPosition);
          }
          getGeoPosition(postCallback(json, index));
        }

        const handleBlocked = () => {
          showDialog({
            message: 'GEO_ERROR: User Denied access to location service',
            confirmText: 'Open Settings',
            handleConfirm: () => {
              rmPermissions.openSettings()
            }
          })
        }
        const getPermisson = handleLocationPermission({
          granted: handleGranted,
          blocked: handleBlocked,
        })

        //然后获取应用层面是否打开定位
        getPermisson.check()
        break;
      }
      case 'setAppLang': {
        setAppLang(json.lang);
        break;
      }
      case 'isCommandAvailable': {
        postCallback(json, index)(Constants.CommandMap[json.cmd]);
        break;
      }
      default:
        if (!eventHandler.fireEvent(json.tp, json.p, this, json)) {
          if (!json.tp) {
            console.warn('unknown msg', json);
            return;
          }
          postCallback(json, index)(`Unknown command: ${JSON.stringify(json)}`);
        }
    }
  };

  const handleWebviewLoadEnd = (index) => {
    //处理不依赖页面postMessage，而是应用直接postMessage的情况
    if (index === 0) {
      clearCallstack(appLoadCallstack.current)
    }
  };

  const handleOnLoadStart = (index) => {
    global.rmLog(`App.jsx:1019~~~handleOnLoadStart`, routeStack.current, index);
    //webview中的URL开始加载时，RMWebview就已经被创建完成了，此时可以获取到rmwebviewRef。即rmwebviewRef.postMessage也可以调用了
    //此时routeStack中的路由堆栈可以挂载rmweb，且一个挂载过的，不用再次挂载
    routeStack.current.map(route => {
      if (route.index === index && !route.rmweb) {
        route.rmweb = rmwebviewRef.current
      }
    })

    if (Platform.OS === 'ios') {
      webViewRMSrvReady({ index })
    }
  }


  const webViewRMSrvReady = ({ index }) => {
    const message = JSON.stringify({ p: 'ready', next: 1 });
    const webRouteRmweb = routeStack.current.find(route => route.index === index)?.rmweb
    if (webRouteRmweb) {
      webRouteRmweb.postMessage(message); // let's sync to 1
    }
    //之前没有post出去的消息依次调用
    clearCallstack(pageLoadCallstack.current)
  };

  const computeShowSplashScreen = () => {
    const backgroundDuration = Date.now() - appConfigIns.getAppConfig('appStateTs');
    if (backgroundDuration > appConfigIns.getAppConfig('backgroundTs') * 1000) {
      // if (backgroundDuration > 5 * 1000) {
      //通过通知和universelink打开的应用，热启动，不展示splash
      if (!showSplashFromurl.current) {
        showSplashFromurl.current = true
      } else {
        //展示splash
        setSplashRefresh(Math.random().toString())
      }

    }
  }

  const handleAppStateChange = nextAppState => {
    //进入后台和前台都需要调用
    notificationService.handleBadge();
    // 应用从后台或非活动状态切换到活动状态
    if (appState.current.match(/inactive|background/) && nextAppState === 'active') {
      // 计算是否需要显示启动屏幕
      computeShowSplashScreen();
      
      // 通知WebView应用状态已改变
      eventEmitter.emit('webview.appStateChange', { 
        nextAppState, 
        appState: appState.current 
      });
      
      // 检查WebView是否存活
      eventEmitter.emit('webview.checkAlive', { 
        tp: 'bootup', 
        msg: 'app come to foreground' 
      });
      
      return;
    }
    
    // 更新当前应用状态
    appState.current = nextAppState;
    
    // 应用进入后台或非活动状态时记录时间戳
    if (nextAppState.match(/inactive|background/)) {
      appConfigIns.setAppConfig({ appStateTs: Date.now() });
    }
  };

  const networkOnline = () => {
    if (routeStack.current[routeStack.current.length - 1].tp == 'network') {
      return routePop();
    }
  };

  const networkDown = (opt = {}) => {
    const networkConnected = networkIns.isConnected();
    if (routeStack.current[routeStack.current.length - 1].tp != 'network') {
      routePush({
        tp: 'network',
        networkConnected: opt.networkConnected || networkConnected,
      });
    }
  };

  const _handleOpenURL = url => {
    if (url) {
      if ('object' === typeof url && url.url) {
        url = url.url;
      }
      // Alert.alert("handleOpenURL:"+JSON.stringify(url)+/realmaster|app\.test/.test(url)); // TO Remove
      if (/realmaster|app\.test/.test(url)) {
        postMessage(url, null, 'openURL', 0)
      }
    }
  };

  const eventCenter = {
    events: {
      'webview.goReady': webViewRMSrvReady,
      'app.message': onMessage,
      checkNetwork: networkIns.doCheckNetwork,
      // 'adscreen.ready': _adScreenReadyHelper,
      // 'startup.jump': startupJump,
      // 'adScreen.timeout': adScreenTimeout,
      openInBrowser: openInBrowser,
      'network.offline': networkDown,
      'network.online': networkOnline,
      'app.closeAndRedirectRoot': closeAndRedirectRoot,
    },
    handleEmitter(action) {
      Object.keys(this.events).map(key => {
        eventEmitter[action](key, this.events[key]);
      });
    },
    on() {
      this.handleEmitter('on');
    },
    remove() {
      this.handleEmitter('removeListener');
    },
  };

  //WebviewScreen初始化路由参数
  const webDefaultParamObj = (extra = {}) => {
    const defVal = {
      tp: 'web',
      scrollEnabled: true,
      toolbar: true, //!(this.route.index == 0) || this.route.toolbar,
      hide: false,
    };
    return { ...defVal, ...extra };
  };

  webRouteData.current = webDefaultParamObj({ index: 0, toolbar: false, init: true });

  const initalApp = async () => {
    //会清除通知列表
    // Notifications.ios.setBadgeCount(0)
    // currentRoute.current = appPropsData.current;
    routeStack.current.push(webRouteData.current);
    //NOTE: 需要配置和初始化
    // await analytics().logEvent('app_mount', {ts: new Date()});
    //this will lock the view to Portrait
    Orientation.lockToPortrait();
    //set Text can't scale
    Text.defaultProps = Text.defaultProps || {};
    Text.defaultProps.allowFontScaling = false;
    eventCenter.on();

    const { lastLoadUrl: lastLoadUrlFromStorage } = await storageIns.syncStorage2Cache();
    if (lastLoadUrlFromStorage) {
      closeAndRedirectRoot({ url: lastLoadUrlFromStorage, init: true });
    } else {
      //第一次安装app
      hideSplash()
    }

    bootup().then((res) => {
      if (!lastLoadUrlFromStorage && res.lastLoadUrl) {
        closeAndRedirectRoot({ url: res.lastLoadUrl, init: true });
      }
    })

    //检查网络是否正常
    networkIns.checkIsConnected();
    //初始化多语言
    await initL10n();
    readGeoPosition((loc = geoPosition) => {
      var opt = {};
      // let geoPosition = await RMStorage.syncGetItem('Cookie@'+tmpDomain)
      if (loc) {
        // console.log('++++geoloc',loc)
        opt.loc = loc.coords;
      }
      // NOTE: already in index
      // console.log('++++fetchSystemVals called',loc)
      fetchSystemVals(opt, (datas) => {
        global.rmLog(`App.jsx:1169~~~fetchSystemVals`, datas);
        wxRegister(
          datas.nativeWechatAppId,
          // datas.nativeWechatDomain
        );
      });
    });


    //deepLink处理
    Linking.getInitialURL()
      .then((url) => {
        if (!url) return
        //应用通过universelink冷启动，获取url的时间大概在启动后2秒多，splash会展示2s后隐藏
        hideSplash()
        whenAppReady(() => _handleOpenURL(url))
      })
      .catch(err => alert('An error occurred', err));

    Linking.addEventListener('url', (url) => {
      //应用从后台切换到前台会多次触发监听，保证只触发一次，避免重复postMessge
      clearTimeout(linkingEventTimer.current)
      linkingEventTimer.current = setTimeout(() => {
        showSplashFromurl.current = false
        _handleOpenURL(url)
      }, 50)
    });

    // if (Platform.OS === 'ios' || Platform.OS === 'android') {
    //   Permissions.checkNotifications().then(({status, settings}) => {
    //     // type PermissionStatus = 'unavailable' | 'denied' | 'blocked' | 'granted';
    //     // console.log('check notify&&&&&&&',status,settings)
    //     if (status == Permissions.RESULTS.GRANTED) {
    //       pnConfig({postMessage, whenAppReady, debounceHandlePushMessage, hideSplash});
    //     }
    //   });
    // }
    Permissions.check(StrPermissionLocation).then(response => {
      // console.log('MSG: permissions: location: '+response);
      if (response == Permissions.RESULTS.GRANTED) {
        getGeoPosition({ noAlert: true });
        watchGeoPosition();
      }
    });
  };


  //发送注册token给web
  const handlePnToken = (token) => {
    const params = {
      token,
      os: Platform.OS
    }
    if (params.os === 'android') {
      params.tp = 'fcm'
    }

    storageIns.setItem(Constants.PnToken, JSON.stringify(params))
    global.rmLog(`App.jsx:1251~~~handlePnToken`, params);
    postMessage(params, null, 'pushToken', 0)
  }

  //处理收到的notificationå
  const handleNotify = (notification) => {
    global.rmLog(`App.jsx:1234~~~handleNotify`, notification);
    if (typeof notification !== 'object') {
      return;
    } else if (notification == null) {
      notification = {};
    }
    Vibration.vibrate([1000]);
    

    let url = '';
    let title = '';
    let message = '';

    ['message', 'data', 'payload'].forEach((key) => {
      const content = notification[key];
      if (content) {
        url = content.url || url;
        title = content.title || title;
        message = content.body || message;
      }
    });
    if (notification.url) {
      url = notification.url
    }
    if (notification.title) {
      title = notification.title
    }

    
    //是否点击了通知
    if (['open', 'initial'].includes(notification.notiType)) {
      global.rmLog(`[App.jsx:1259~~~~~~~~~~handleNotify]`, url);
      if (!url) return
      if (notification.notiType === 'initial') {
        //通过通知冷启动，获取url的时间大概在启动后300ms，splash会展示300ms后隐藏
        hideSplash()
      } else {
        showSplashFromurl.current = false
      }
      if (isPopupPushMessageUrl(url)) {
        global.rmLog(`App.jsx:1270~~~handleNotify`, );
        const popupFun = () => {
          popup({
            url,
            toolbar: true,
          });
        }
        if (notification.notiType === 'open') {
          popupFun()
        } else {
          whenAppReady(popupFun)
        }

      } else {
        if (/rmpntest/.test(url)) {
          return;
        }
        global.rmLog(`App.jsx:1270~~~handleNotify`, );
        const routePopToTopFun = () => {
          routePopToTop({
            url: urlFix(url),
            toolbar: false,
            tp: 'web',
          });
        }
        if (notification.notiType === 'open') {
          routePopToTopFun()
        } else {
          whenAppReady(routePopToTopFun)
        }
      }
    }

    //前台收到通知
    if (notification.notiType === 'foreground') {
      // ios & android handle differently
      if (Platform.OS == 'ios' && !/rmpntest/.test(url)) {
        return
      }
      const webMessage = {
        message,
        title,
        url,
        foreground: true,
      }
      postMessage(webMessage, null, 'pushNotice', 0);
    }
  }


  //注册pnToken
  const registerPnToken = async (status) => {
    //检查应用是否有通知权限
    if (!status) {
      const res = await Permissions.checkNotifications()
      status = res.status
    }

    if (status !== Permissions.RESULTS.GRANTED) return
    const handleFunctions = {
      handlePnToken,
      handleNotify
    }
    notificationService.initial(handleFunctions)
  }


  useEffect(() => {
    initalApp();
    registerPnToken();
    //订阅网络变化
    networkIns.subNetworkStateChange();

    const appStateSub = AppState.addEventListener('change', handleAppStateChange);
    let androidBackHandler = null;

    //安卓返回按钮
    if (Platform.OS === 'android') {
      androidBackHandler = BackHandler.addEventListener('hardwareBackPress', handleAndroidBackButton);
    }
    
    return () => {
      if (Platform.OS === 'android') {
        androidBackHandler.remove();
      }
      if (Linking.removeEventListener) {
        Linking.removeEventListener('url', _handleOpenURL);
      }
      //清除location监听
      clearWatch();

      networkIns.unsubNetworkStateChange();
      appStateSub.remove();
      eventCenter.remove();
    };
  }, []);

  return (
    <>
      <SplashScreen refresh={splashRefresh} />
      <NavigationContainer ref={navigatorRef}>
        <Stack.Navigator
          initialRouteName='web'
          screenOptions={{
            presentation: 'card',
            headerShown: false,
            gestureEnabled: false,
          }}>
          <Stack.Screen
            name='web'
            options={{
              animation: 'fade_from_bottom',
              gestureEnabled: true,
              detachPreviousScreen: false, // 保持前一个页面不被分离
              unmountOnBlur: false, // 页面失焦时不卸载
            }}>
            {props => (
              <RMWebview
                ref={rmwebviewRef}
                {...props}
                routeData={webRouteData.current}
                onLoadStart={handleOnLoadStart}
                onMessage={onMessage}
                closePopup={routePop}
                onLoadEnd={handleWebviewLoadEnd}
              />
            )}
          </Stack.Screen>
          <Stack.Screen name='admin'>
            {props => (
              <RMAdmin
                {...props}
                closePopup={routePop}
              />
            )}
          </Stack.Screen>
          <Stack.Screen name='qrcode'>
            {props => (
              <QRCodeScreen
                {...props}
                onCancel={routePop}
                onSucess={gotQrcode}
              />
            )}
          </Stack.Screen>
          <Stack.Screen name='autocomplete'>
            {props => (
              <RMAutoCompleteNative
                {...props}
                closePopup={routePop}
              />
            )}
          </Stack.Screen>
          <Stack.Screen name='mapSearch'>
            {props => (
              <RMMapSearchNative
                {...props}
                closePopup={routePop}
              />
            )}
          </Stack.Screen>
          <Stack.Screen name='map'>
            {props => (
              <RMMapNative
                {...props}
                closePopup={routePop}
              />
            )}
          </Stack.Screen>
          <Stack.Screen
            name='network'
            component={RMNetwork}
            options={() => ({
              //TODO:
              // cardStyleInterpolator: forFade
            })}
          />
        </Stack.Navigator>
      </NavigationContainer>
    </>
  );
};

export default App;
