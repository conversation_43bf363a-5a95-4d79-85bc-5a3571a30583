// 09/22 22:01
function time (t) {
  var t = new Date(t);
  return (t.getMonth() + 1) + '/' + t.getDate() + ' ' + t.getHours() + ':' + (t.getMinutes()<10?'0':'') + t.getMinutes();
}
function day (t) {
  if (!t) {
    return;
  }
  var t = new Date(t);
  return t.getUTCDate();
}
//2018.2
function yearMonth(t) {
  if (!t) {
    return;
  }
  t = new Date(t)
  return t.getFullYear() + '.' + (t.getUTCMonth()+1);
}
function number (number, d) {
  if (number != null) {
    d = parseInt(d);
    return isNaN(number) ? 0 : parseFloat(number.toFixed(d))
  } else {
    return number;
  }
}
//1.2M
function propPrice (number, toFixed) {
  // console.log(number);
  if (number != null) {
    number = parseInt(number);
    if (isNaN(number) || number == 0) {
      return '';
    }
    // 9000 -> 9k, 800000 -> 800k
    if (number < 1000){
      // number
      number += '';
    } else if (number < 10000){
      number = (number/1000).toFixed(1)+'K';
    } else if (number < 999500) {
      number = Math.round(number/1000).toFixed(0)+'K';
    } else {//if (number >= 1000000) {
      toFixed = toFixed || 1
      number = (number/1000000).toFixed(toFixed)+'M';
    }
    // number = number.replace('.0','');
    return number;
  } else {
    return '';
  }
}
function percentage (number, d) {
  if (number != null) {
    number = parseFloat(number);
    if (isNaN(number)) {
      return 0;
    }
    return (number * 100).toFixed(2);
  } else {
    return number;
  }
}
// yyyy.MM.dd
function dotdate (d, isCh) {
  // console.log(d);
  if (!d) {
    return '';
  }
  if ('number' == typeof d) {
    d += '';
    d = d.slice(0,4)+'/'+d.slice(4,6)+'/'+d.slice(6,8)
  }
  //not supported by ios
  // var isIOS = /iPad|iPhone|iPod|Safari/.test(navigator.userAgent) && !window.MSStream;
  if ('string' == typeof d && !/\d+Z/.test(d)) {
    d += ' EST';
  }
  var split1 = isCh?'年':'.';
  var split2 = isCh?'月':'.';
  var split3 = isCh?'日':'';
  if (/^2\d{3}-\d{1,2}-\d{1,2}/.test(d) && !/\d+Z/.test(d)) {
    var d2 = d.split(' ')[0].split('-');
    return d2[0]+split1+d2[1]+split2+d2[2]+split3;
  }
  var t = new Date(d);
  if (!t || isNaN( t.getTime() )) {
    // console.log('isIos: '+isIOS+' ivalid date: '+d);
    return d;
  }
  return t.getFullYear() + split1 + (t.getMonth()+1) + split2 + t.getDate() + split3;
}
// MM/dd/yyyy HH:mm
function datetime (t) {
  var t = new Date(t);
  return (t.getMonth() + 1) + '/' + t.getDate() + '/' + t.getFullYear() + ' ' + t.getHours() + ':' + (t.getMinutes()<10?'0':'') + t.getMinutes();
}
function monthNameAndDate (t){
  // var monthNames = ["January", "February", "March", "April", "May", "June",
  //   "July", "August", "September", "October", "November", "December"
  // ];
  if (!t) {
    return '';
  }
  var monthNames = ["Jan", "Feb", "Mar", "Apr", "May", "Jun",
    "Jul", "Aug", "Sept", "Oct", "Nov", "Dec"
  ];
  var d = new Date(t);
  return monthNames[d.getMonth()]+'.'+d.getDate();
}
//https://stackoverflow.com/questions/2901102/how-to-print-a-number-with-commas-as-thousands-separators-in-javascript
function currency (x,sign='$',dig){
  try {
    var tmp = parseInt(x);
    if (isNaN(tmp)) {
      return null;
    }
    var parts = x.toString().split(".");
    parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    if (dig == 0) {
      parts[1] = undefined;
    } else if (dig > 0) {
      if (parts[1]) {
        parts[1] = parts[1].substr(0,dig)
      }
    }
    return sign + parts.filter(function (val) {return val;}).join(".");
  } catch (e) {
    // console.error(e);
    return null;
  }
}
function arrayValue(v){
  if (Array.isArray(v)) {
    return v.join(' ')
  }
  return v
}
// var filters = {
//   time:time,
//   day:day,
//   number:number,
//   dotdate:dotdate,
//   datetime:datetime,
//   propPrice:propPrice,
//   percentage:percentage,
//   yearMonth:yearMonth,
//   monthNameAndDate:monthNameAndDate,
//   currency:currency,
//   arrayValue:arrayValue
// }
// export default filters;
export {propPrice,currency,dotdate};
