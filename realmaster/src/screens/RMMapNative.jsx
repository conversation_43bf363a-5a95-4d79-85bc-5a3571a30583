import { View,Alert,StyleSheet,TouchableOpacity,Text,Image,Platform,Dimensions} from 'react-native';
import React, { Component } from 'react';
import MapView, { PROVIDER_GOOGLE } from 'react-native-maps';
import RMNavBar from "../components/RMNavBar";
// import PropTypes from 'prop-types';

const styles = StyleSheet.create({
  container: {
   ...StyleSheet.absoluteFillObject,
   //height: 400,
   //width: 400,
   //justifyContent: 'flex-end',
   //alignItems: 'center',
   flexDirection: 'column',
   flex: 1,
  },
  navBar: {
    backgroundColor: '#F00',
  },
  map: {
    // ...StyleSheet.absoluteFillObject,
    flex: 1,
    position:'relative',
    zIndex:1
  },
  buttonText: {
    textAlign: 'center',
  },
  button: {
    width: 100,
    paddingHorizontal: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 5,
  },
  bubble: {
    backgroundColor: 'rgba(255,255,255,0.7)',
    paddingHorizontal: 18,
    paddingVertical: 12,
    borderRadius: 20,
  },
  buttonOverlay:{
    flex: 1,
    position: 'absolute',
    right: 3,
    top: 110,
    // opacity: 0.5,
    width: 40,
    height: 40,
    backgroundColor:'transparent',
    zIndex:5
  },
  overLayCircle:{
    borderRadius:40,
    width: 40,
    height: 40,
    padding: 10,
    flex:1,
    backgroundColor: 'white',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center'
  },
  buttonImage:{
    flex:1,
    width: 20,
    height: 20
  },
  tipText:{
    height:30,
    // flex:1,
    padding:5,
    position:'relative',
    backgroundColor:'white',
    bottom:0
  }
});
const { width, height } = Dimensions.get('window');
const ASPECT_RATIO = width / (height-44);
var LATITUDE = 37.78825;
var LONGITUDE = -122.4324;
const LATITUDE_DELTA = 0.015;
const LONGITUDE_DELTA = LATITUDE_DELTA * ASPECT_RATIO;
class RMMapNative extends Component {
  constructor(props) {
    super(props);
    this.state = {
      mapTypeId:this.props.mapTypeId,
      lat:null,
      lng:null,
      region: {
        latitude: LATITUDE,
        longitude: LONGITUDE,
        latitudeDelta: LATITUDE_DELTA,
        longitudeDelta: LONGITUDE_DELTA,
      },
    };
  }
  componentDidMount(){
    this.props = {
      ...this.props,
      ...this.props.route.params
    }
    this.data = {};
    this.data.closePopup = this.props.closePopup;
    LATITUDE = parseFloat(this.props.coords.latitude) || 43.6448;
    LONGITUDE = parseFloat(this.props.coords.longitude) || -79.3958;
    let state = {
      region:{
        latitude:LATITUDE,
        longitude:LONGITUDE,
        latitudeDelta: LATITUDE_DELTA,
        longitudeDelta: LONGITUDE_DELTA,
      }
    }
    if (this.props && this.props.coords.latitude) {
      state.lat = LATITUDE;
      state.lng = LONGITUDE;
      state.coords = this.props.coords;
    }
    this.setState(state);
  }
  render() {
    //  const { region } = this.props;
    //  console.log(region);
     var mapTypeIcon = this.state.mapTypeId == 'standard' ? require('../assets/images/btns/fa-globe.png') : require('../assets/images/btns/fa-map-o.png');
     return (
       <View style ={styles.container}>
         <RMNavBar
         style={[styles.navBar]}
         title={{title: (this.props.title || 'RealMaster Map')}}
         rightButton={{cbClose:this.closePopup}}
         />
         <View style={styles.buttonOverlay}>
           <TouchableOpacity
             onPress={() => this.switchMapType()}
             style={[styles.overLayCircle]}
             >
             {/* <Text style={styles.buttonText}>Animate (Bearing)</Text> */}
             <Image style={styles.buttonImage} source={mapTypeIcon} />
           </TouchableOpacity>
         </View>
         <MapView
           provider={PROVIDER_GOOGLE}
           style={styles.map}
           //cacheEnabled={true}
           showsScale={true}
           mapType={this.state.mapTypeId}
           ref={ref => { this.map = ref; }}
           initialRegion={this.state.region}
           region={this.state.region}
           onRegionChange={region => this.onRegionChange(region)}
         >
           { this.state.lat != null &&
             (<MapView.Marker
              coordinate={this.state.coords}
              title={'Me'}
              description={'Location'}
              />)
           }
         </MapView>
         { Platform.OS == 'ios' && this.state.mapTypeId == 'standard' &&
            <Text style={styles.tipText}>Use 2 fingers push screen up to view 3d map</Text>
         }
         {/* <View style={styles.buttonContainer}>
           <TouchableOpacity
             onPress={() => this.animateToRandomBearing()}
             style={[styles.bubble, styles.button]}
             >
             <Text style={styles.buttonText}>Animate (Bearing)</Text>
           </TouchableOpacity>
           <TouchableOpacity
             onPress={() => this.animateToRandomViewingAngle()}
             style={[styles.bubble, styles.button]}
             >
             <Text style={styles.buttonText}>Animate (View Angle)</Text>
           </TouchableOpacity>
         </View> */}
       </View>
     );
   }
   closePopup = () => {
     this.data.closePopup()
   }
   switchMapType(){
     var type = 'standard';
     if (this.state.mapTypeId == 'standard') {
       type = 'hybrid';//satellite
     }
     this.setState({
       mapTypeId:type
     })
   }
   onRegionChange(region) {
    // console.log('+++'+JSON.stringify(region));
    // this.setState({ region });
   }
  //  getRandomFloat(min, max) {
  //    return (Math.random() * (max - min)) + min;
  //  }
  //  animateToRandomBearing() {
  //    this.map.animateToBearing(this.getRandomFloat(-360, 360));
  //  }
  //  animateToRandomViewingAngle() {
  //    this.map.animateToViewingAngle(this.getRandomFloat(0, 90));
  //  }
};
RMMapNative.defaultProps = {
  mapTypeId: 'standard'
};
export default RMMapNative;
