import { Platform, PermissionsAndroid, Alert, NativeModules } from 'react-native';
import ReactNativeBlobUtil from 'react-native-blob-util';
import { CameraRoll } from '@react-native-camera-roll/camera-roll';
import { check, request, PERMISSIONS, RESULTS } from 'react-native-permissions';
import { mainRequest } from './request';
import appConfigIns from '../config/appConfig';
import storageIns from './storage';
import Constants from '../config/constants';

let fs = ReactNativeBlobUtil.fs;
let dirs = fs.dirs;
const deviceLanguage =
  Platform.OS === 'ios'
    ? NativeModules.SettingsManager.settings.AppleLocale || NativeModules.SettingsManager.settings.AppleLanguages[0] // iOS 13
    : NativeModules.I18nManager.localeIdentifier;
const defaultAdsConfig = {
  baseUrl: appConfigIns.getAppConfig('splashJsonUrlBase'),
  path: '/1.5/ads/splash',
  localAdFileName: 'splash.json',
  defaultAdBackgroundImg: require('../assets/images/screenEn.jpg'),
};

//splash.json在手机中的存储路径
const adsJsonPath = dirs.DocumentDir + '/' + defaultAdsConfig.localAdFileName;

/**
 * 异步函数用于检查Android权限
 * 根据Android平台的版本，检查并请求相应的权限
 * 在Android 13（API级别33）及以上版本中，使用READ_MEDIA_IMAGES和READ_MEDIA_VIDEO权限
 * 在Android 13以下版本中，使用READ_EXTERNAL_STORAGE权限
 *
 * @returns {Promise<boolean>} 返回一个Promise，解析为布尔值，表示是否拥有必要的权限
 */
async function hasAndroidPermission() {
  const getCheckPermissionPromise = async () => {
    if (Platform.Version >= 33) {
      const [hasReadMediaImagesPermission, hasReadMediaVideoPermission] = await Promise.all([
        PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.READ_MEDIA_IMAGES),
        PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.READ_MEDIA_VIDEO),
      ]);
      return hasReadMediaImagesPermission && hasReadMediaVideoPermission;
    } else {
      return await PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE);
    }
  };

  const hasPermission = await getCheckPermissionPromise();
  if (hasPermission) {
    return true;
  }
  const getRequestPermissionPromise = async () => {
    if (Platform.Version >= 33) {
      const statuses = await PermissionsAndroid.requestMultiple([
        PermissionsAndroid.PERMISSIONS.READ_MEDIA_IMAGES,
        PermissionsAndroid.PERMISSIONS.READ_MEDIA_VIDEO,
      ]);

      return (
        statuses[PermissionsAndroid.PERMISSIONS.READ_MEDIA_IMAGES] === PermissionsAndroid.RESULTS.GRANTED &&
        statuses[PermissionsAndroid.PERMISSIONS.READ_MEDIA_VIDEO] === PermissionsAndroid.RESULTS.GRANTED
      );
    } else {
      const status = await PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE);
      return status === PermissionsAndroid.RESULTS.GRANTED;
    }
  };

  return await getRequestPermissionPromise();
}

/**
 * 检查iOS相册权限
 * @returns {Promise<boolean>} 返回一个Promise，解析为布尔值，表示是否拥有必要的权限
 */
async function hasIosPermission() {
  try {
    const permission = PERMISSIONS.IOS.PHOTO_LIBRARY;
    const result = await check(permission);
    global.rmLog(`files.js:79~~~hasIosPermission`, result);
    if (result === RESULTS.GRANTED || result === RESULTS.LIMITED) {
      return true;
    }

    if (result === RESULTS.DENIED) {
      const requestResult = await request(permission);
      return requestResult === RESULTS.GRANTED;
    }

    return false;
  } catch (error) {
    console.error('检查iOS相册权限失败:', error);
    return false;
  }
}

/**
 * 下载并保存图片或视频到相机胶卷
 * @param {Object} opt - 选项对象，包含url和noExt属性
 * @param {Function} cb - 回调函数，保存结果或错误信息
 */
const doDownloadImage = (opt, cb) => {

  const accessDenied = () => {
    const permDeniedMsgiOS = appConfigIns.getAppConfig('isCip') ?
      '访问相册权限被拒绝，请去应用设置中授权.' :
      'Access to photo library was denied. Please enable it in Settings.';
    const permDeniedMsgAndroid = appConfigIns.getAppConfig('isCip') ?
      '访问相册权限受限，请去应用设置中授权或选择授权的相册内容.' :
      'Access to photo library is restricted. Please go to app settings to grant permission or select authorized photo content.';
    return cb(Platform.select({ ios: permDeniedMsgiOS, android: permDeniedMsgAndroid }));
  }
  
  //处理base64图片数据存储
  async function handleDataUrl() {
    const isPng = /^data:image\/png/.test(opt.url);
    const ext = isPng ? 'png' : 'jpeg';
    const randName = Date.now();
    const filePath = Platform.OS === 'android' ? dirs.MainBundleDir : dirs.DocumentDir;
    const fullFilePath = `${filePath}/${randName}.${ext}`;

    const imageData = opt.url.split(`data:image/${ext};base64,`)[1];

    try {
      await ReactNativeBlobUtil.fs.writeFile(fullFilePath, imageData, 'base64');

      if (Platform.OS === 'android' && !(await hasAndroidPermission())) {
        return accessDenied();
      } else if (Platform.OS === 'ios' && !(await hasIosPermission())) {
        return accessDenied();
      }

      const ret = await CameraRoll.saveAsset(fullFilePath, { type: 'photo', album: 'RealMaster' });
      cb(null, ret);
    } catch (err) {
      cb(err.toString());
    }
  }

  //处理URL图片存储
  async function handleRegularUrl() {
    const match = opt.url.match(/(jpg|jpeg|png|gif|mp4)$/i);
    //match可能为null，取[0]会报错
    const ext = match ? match[0] : 'jpg';
    const cfg = {
      fileCache: true,
      appendExt: ext,
    };

    if (opt.noExt) {
      delete cfg.appendExt;
    }

    try {
      const res = await ReactNativeBlobUtil.config(cfg).fetch('GET', opt.url);

      if (Platform.OS === 'android' && !(await hasAndroidPermission())) {
        return accessDenied();
      } else if (Platform.OS === 'ios' && !(await hasIosPermission())) {
        return accessDenied();
      }

      const ret = await CameraRoll.saveAsset(res.path(), { type: 'photo', album: 'RealMaster' });
      Alert.alert('Success', 'Saved to camera roll!');
      res.flush();
      cb(null, ret);
    } catch (err) {
      cb(err.toString());
    }
  }

  //判断是base64还是url
  if (/^data:image/.test(opt.url)) {
    handleDataUrl();
  } else {
    handleRegularUrl();
  }
};

/**
 * 根据提供的选项下载图片，并使用回调函数返回结果
 *
 * @param {Object} opt - 包含下载图片所需信息的对象
 * @param {string} opt.url - 图片的URL地址
 * @param {Function} cb - 回调函数，用于处理下载过程中的成功或错误结果
 * @returns {void}
 */
const downloadImage = (opt, cb) => {
  if (!opt.url) {
    return cb('Not specified img uri');
  }

  // const checkAndRequestPermissions = () => {
  //   check(Permissions.PERMISSIONS.ANDROID.WRITE_EXTERNAL_STORAGE)
  //     .then(response => {
  //       if (response !== 'authorized') {
  //         Permissions.request(Permissions.PERMISSIONS.ANDROID.WRITE_EXTERNAL_STORAGE)
  //           .then(response => {
  //             if (response === 'authorized') {
  //               doDownloadImage(opt, cb);
  //             } else {
  //               cb('Permission denied');
  //             }
  //           })
  //           .catch(error => {
  //             cb(`Permission request failed: ${error}`);
  //           });
  //       } else {
  //         doDownloadImage(opt, cb);
  //       }
  //     })
  //     .catch(error => {
  //       cb(`Permission check failed: ${error}`);
  //     });
  // };

  // if (Platform.OS === 'android') {
  //   checkAndRequestPermissions();
  // } else {
    doDownloadImage(opt, cb);
  // }
};

/**
 * 根据应用或设备语言设置广告的默认图片。
 * 如果语言是中文，则设置特定的图片作为默认广告背景。
 */
const setDefaultImage = async () => {
  const chineseImg = require('../assets/images/screen.jpg');
  const lang = await storageIns.getItem(Constants.AppLang);

  if ((lang && ['zh', 'zh-cn'].includes(lang)) || /^zh(_[a-zA-Z]+)?$/.test(deviceLanguage)) {
    defaultAdsConfig.defaultAdBackgroundImg = chineseImg;
  }
};

/**
 * 获取默认广告的JSON对象。
 * 首先根据语言设置设置默认图片。
 * @returns {Object} 包含本地源图片的默认广告JSON对象。
 */
const getDefaultAdsJson = async () => {
  await setDefaultImage();
  return {
    _id: 'defaultAds',
    localSrc: defaultAdsConfig.defaultAdBackgroundImg,
  };
};

/**
 * 从本地文件系统加载并解析JSON文件。
 * @param {string} filePath - JSON文件的路径。
 * @param {string} [encoding='utf8'] - 文件编码。
 * @returns {Object} 解析后的JSON对象。
 * @throws 如果文件无法读取或解析，将抛出错误。
 */
const loadAdsJsonFromLocal = async (filePath, encoding = 'utf8') => {
  try {
    const res = await fs.readFile(filePath, encoding);
    return JSON.parse(res);
  } catch (error) {
    console.error('加载本地广告JSON失败:', error);
    throw error;
  }
};

/**
 * 从远程源获取更新的广告，并在必要时更新本地广告JSON。
 * 它按开始日期对广告进行排序，如果最新广告与本地广告不同，则进行更新。
 * @param {Object} localAdsJson - 当前本地广告JSON对象。
 * @returns {Object} 更新后的广告JSON对象。
 */
const getUpdatedAds = async (localAdsJson) => {
  const { baseUrl, path } = defaultAdsConfig;
  const url = path;
  const data = {};
  let remoteAdsJson = [];
  try {
    // global.rmLog(`files.js:278~~~getUpdatedAds`, url);
    remoteAdsJson = await mainRequest({ url, data, method: 'post' });
    global.rmLog(`files.js:279~~~remoteAdsJson`, remoteAdsJson);
  } catch (error) {
    global.rmLog(`[files.js:247~getUpdatedAds]`, error, 'error');
  }

  // const remoteAdsJson = [{
  //   src: "https://f.realmaster.cn/P/GNF/FPM.jpeg",
  //   tgt: "https://www.realmaster.com/en/new-condos/Markham/Victory-Green?id=66c243f357e26db65ad0b284",
  //   lang: ["zh", "en", "zh-cn", "kr"],
  //   grp: "all",
  //   area: ["china", "nochina"],
  //   st: 20240828,
  //   et: 20240906,
  //   _id: "fNomLHnwyz"
  // }];

  // const remoteAdsJson = []

  //如果远程广告为空，则更新本地广告为默认红屏幕
  if (remoteAdsJson.length === 0) {
    const defaultAds = await getDefaultAdsJson();
    fs.writeFile(adsJsonPath, JSON.stringify(defaultAds), 'utf8');
    return
  }

  const sortedAds = remoteAdsJson.sort((a, b) => b.st - a.st);
  if (sortedAds.length > 0 && sortedAds[0]._id !== localAdsJson?._id) {
    let tempFile = null;
    try {
      const response = await ReactNativeBlobUtil.config({ fileCache: true }).fetch('GET', sortedAds[0].src);
      tempFile = response.path();
      const base64Res = await fs.readFile(response.path(), 'base64');
      sortedAds[0].base64 = `data:image/png;base64,${base64Res}`;
      await fs.writeFile(adsJsonPath, JSON.stringify(sortedAds[0]), 'utf8');
    } catch (error) {
      global.rmLog('[files.js:getUpdatedAds]', error, 'error');
      throw error;
    } finally {
      if (tempFile) {
        await fs.unlink(tempFile).catch(console.error);
      }
    }
  }
};

/**
 * 获取广告JSON对象，可以从本地存储中获取，也可以创建一个默认的。
 * 如果本地广告JSON文件不存在或发生错误，则返回默认广告JSON。
 * @returns {Object} 广告JSON对象。
 */
const getAds = async () => {
  try {
    const isExist = await fs.exists(adsJsonPath);
    if (isExist) {
      const localAdsJson = await loadAdsJsonFromLocal(adsJsonPath);
      if (localAdsJson._id === 'defaultAds') {
        return await getDefaultAdsJson();
      }
      return localAdsJson;
    } else {
      const defaultAds = await getDefaultAdsJson();
      await fs.writeFile(adsJsonPath, JSON.stringify(defaultAds), 'utf8');
      return defaultAds;
    }
  } catch (error) {
    console.error('获取广告时出错:', error);
    return await getDefaultAdsJson();
  }
};

export { downloadImage, getAds, getUpdatedAds };
