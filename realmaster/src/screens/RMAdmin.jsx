// 'use strict';

import {l10n} from '../utils/i18n';
import appConfigIns from '../config/appConfig';
import React, {Component} from 'react';
import PropTypes from 'prop-types';
import RmIcon from '../components/RmIcon';
import {RMStatusBar, RMBottomBar} from '../components/RMStatusBar';

import {
  TextInput,
  Button,
  StyleSheet,
  View,
  Text,
  Switch,
  TouchableOpacity,
  //VibrationIOS,
  KeyboardAvoidingView,
} from 'react-native';

// var RMAdmin = React.createClass({
class RMAdmin extends Component {
  constructor(props) {
    super(props);
    //var state = {
    // timeout: null,
    // appConfig: Object.assign({},appConfig)
    //}
    var state = Object.assign({}, appConfigIns.appConfig());
    state.isPublish = false;
    state.publishBtnTitle = 'Start Publish';
    this.state = state;
    this.datas = {};
  }
  componentDidMount() {
    this.count = 0;
    this.props = {
      ...this.props,
      ...this.props.route.params,
    };
    this.datas.rightButton = {cbClose: this.props.closePopup};
    this.setState();
    // console.log('####did mount');
    // console.log(this.state.backgroundTs);
  }
  componentWillUnmount() {
    // clearTimeout(this.timeout)
  }
  onChangeText(text) {
    let tmp = {tileUrl: text};
    this.setState(tmp, () => {
      // console.log(this.state)
    });
  }
  onApplySettings() {
    // console.log('save values, state:',this.state)
    let tmp = {};
    let object = this.state;
    for (const key in object) {
      if (object.hasOwnProperty(key)) {
        tmp[key] = object[key];
      }
    }
    appConfigIns.setAppConfig(tmp);
  }
  toggle(k, v) {
    let tmp = {};
    tmp[k] = v;
    // console.log('++++',tmp)
    this.setState(tmp, () => {
      console.log(this.state);
    });
  }
  onChangeNumber(val) {
    // console.log('++++',typeof val,val)
    // return
    let tmp = {backgroundTs: parseInt(val)};
    this.setState(tmp, () => {
      // console.log(this.state)
    });
  }
  goBack() {
    this.datas.rightButton.cbClose();
    // this.props.closePopup()
  }
  render() {
    this.datas.barCodeFlag = true;
    return (
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'} // iOS 和 Android 不同的处理方式
        style={{flex: 1}}
        >
        <View style={styles.container}>
          <RMStatusBar />
          {/* <RMNavBar
          style={{}}
          title={{title:'Native settings'}}
          rightButton={this.datas.rightButton}
          hide={false}
         />; */}
          <TouchableOpacity
            onPress={() => this.goBack()}
            style={styles.back}>
            <RmIcon
              name={'back'}
              color={'white'}
              size={21}
            />
          </TouchableOpacity>
          <TextInput
            style={styles.input}
            onChangeText={text => this.onChangeText(text)}
            value={this.state.tileUrl}
          />
          <View style={styles.row}>
            <Text>useTileOverlay?</Text>
            <Switch
              // disabled={true}
              onValueChange={v => this.toggle('useTileOverlay', v)}
              value={this.state.useTileOverlay ? true : false}
            />
          </View>
          <View style={styles.row}>
            <Text>useWebMap?</Text>
            <Switch
              // disabled={true}
              onValueChange={v => this.toggle('useWebMap', v)}
              value={this.state.useWebMap ? true : false}
            />
          </View>
          <View style={styles.row}>
            <Text>mapSowStig?</Text>
            <Switch
              // disabled={true}
              onValueChange={v => this.toggle('mapShowStigmatized', v)}
              value={this.state.mapShowStigmatized ? true : false}
            />
          </View>
          <View style={styles.row}>
            <Text>mapShowCoop?</Text>
            <Switch
              // disabled={true}
              onValueChange={v => this.toggle('mapShowCoop', v)}
              value={this.state.mapShowCoop ? true : false}
            />
          </View>
          <View style={styles.row}>
            <Text>debug mode?</Text>
            <Switch
              // disabled={true}
              onValueChange={v => this.toggle('debugMode', v)}
              value={this.state.debugMode ? true : false}
            />
          </View>
          <View style={styles.row}>
            <Text>hasWechat?</Text>
            <Switch
              // disabled={true}
              onValueChange={v => this.toggle('hasWechat', v)}
              value={this.state.hasWechat ? true : false}
            />
          </View>
          <View style={styles.row}>
            <Text>useDefaultMap?</Text>
            <Switch
              // disabled={true}
              onValueChange={v => this.toggle('useDefaultMap', v)}
              value={this.state.useDefaultMap ? true : false}
            />
          </View>
          <View style={styles.row}>
            <Text>splashTimeout value(in seconds)</Text>
          </View>
          <View>
            <TextInput
              style={{backgroundColor: '#ddd', padding: 10}}
              onChangeText={this.onChangeNumber.bind(this)}
              // value={this.state.backgroundTs+''}
              defaultValue={this.state.backgroundTs + ''}
              placeholder='backgroundTs in seconds'
              // keyboardType='numeric'
            />
          </View>
          <TouchableOpacity
            onPress={() => {
              this.onApplySettings();
            }}
            style={styles.cancelButton}>
            <Text style={styles.cancelButtonText}>Save</Text>
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    );
  }
}
class CancelButton extends Component {
  // var CancelButton = React.createClass({
  render() {
    return (
      <View style={styles.cancelButton}>
        <TouchableOpacity onPress={this.props.onPress}>
          <Text style={styles.cancelButtonText}>{this.props.title}</Text>
        </TouchableOpacity>
      </View>
    );
  }
}

var styles = StyleSheet.create({
  camera: {
    //height: 568,
    flex: 1,
    alignItems: 'center',
  },

  rectangleContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'transparent',
  },
  container: {
    ...StyleSheet.absoluteFillObject,
    flexDirection: 'column',
    flex: 1,
  },
  rectangle: {
    height: 250,
    width: 250,
    borderWidth: 2,
    borderColor: '#00FF00',
    backgroundColor: 'transparent',
  },
  row: {
    padding: 10,
    flexDirection: 'row',
  },
  back: {
    // flex: 1,
    height: 40,
    padding: 9,
    backgroundColor: '#e03131',
  },
  cancelButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    backgroundColor: 'white',
    marginTop: 20,
    // width: 100,
    // bottom: 10,
  },
  cancelButtonText: {
    borderRadius: 5,
    paddingLeft: 12,
    paddingRight: 12,
    paddingTop: 6,
    paddingBottom: 6,
    fontSize: 17,
    fontWeight: '500',
    color: 'white',
    backgroundColor: '#6fce1b',
  },
  input: {
    height: 40,
    borderColor: 'gray',
    borderWidth: 1,
    color: 'black',
  },
  backgroundVideo: {
    position: 'absolute',
    top: 100,
    left: 0,
    bottom: 0,
    right: 0,
  },
});
RMAdmin.propTypes = {
  cancelButtonVisible: PropTypes.bool,
  cancelButtonTitle: PropTypes.string,
  onSucess: PropTypes.func,
  onCancel: PropTypes.func,
};
RMAdmin.defaultProps = {
  cancelButtonVisible: true,
  cancelButtonTitle: l10n('Cancel'),
};
export default RMAdmin;
