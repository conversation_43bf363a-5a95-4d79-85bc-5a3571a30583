/**
 * 应用启动执行的操作
 */
import cookieIns from "./cookies"
import { getFastestDomain } from "./common";
import DeviceInfo from 'react-native-device-info'
import storageIns from './storage'
import Constants from "../config/constants";
import serverDomainIns from './serverDomain';


/**
 * Calculates the fastest domain based on the request and application mode.
 *
 * @param {Object} request - The request object containing httpProtocol, protocol, and domain.
 * @param {string} appMode - The application mode.
 * @returns {Object} - An object with lastLoadDomain and lastLoadUrl.
 */
export const handleFastestDomain = (request, appMode) => {
    const { protocol, dm } = request;
    const lastLoadDomain = `${protocol}://${dm}`;
    const lastLoadUrl = `${lastLoadDomain}/app?sv=${DeviceInfo.getVersion()}&appmode=${appMode}`;
    return {
        lastLoadDomain,
        lastLoadUrl,
    };
}


/**
 * Performs application startup operations.
 *
 * @returns {Promise<Object|null>} Returns an object with lastLoadDomain and lastLoadUrl if the fastest domain is not found,
 * otherwise returns null.
 */
export const bootup = async () => {

  const appmode = await cookieIns.getCookieValueByKey('', 'appmode') || 'mls';
    // Get the fastest domain and its parameters
    //{"params": {"dm": "ch.realmaster.cn", "f": 2, "protocol": "https", "sts": 1724761345497, "url": "https://ch.realmaster.cn/appdm?appmode=mls"}, "result": {"dm": false, "useWebMap": true}}
    const { req, res } = await getFastestDomain(appmode);

    global.rmLog(`bootup.js:43~~~res`, res);

    if (!res.dm) {
        const { lastLoadDomain, lastLoadUrl } = handleFastestDomain(req, appmode);
        global.rmLog(`bootup.js:45~~~bootup`, lastLoadDomain, lastLoadUrl);
        storageIns.setCacheItem(Constants.CacheKey.LastLoadDomain, lastLoadDomain,  true, ()=>{
          //同步设置Domain
          serverDomainIns.asyncGetDomain()
        })
        storageIns.setCacheItem(Constants.CacheKey.LastLoadUrl, lastLoadUrl,  true)
        return {
          lastLoadDomain,
          lastLoadUrl
        }
    }

    return {}
}
