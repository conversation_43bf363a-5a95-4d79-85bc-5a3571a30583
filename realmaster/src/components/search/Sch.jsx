import React, {Component} from 'react';
import {Platform, StyleSheet, Text, View,Dimensions,TouchableOpacity} from 'react-native';
import {gotoSch} from './helper'
import RmIcon from '../RmIcon'
import HistRight from './HistRight';
import {l10n} from '../../utils/i18n';
import colorTheme from '../../utils/colors';

const {width} = Dimensions.get('window');
export default Sch = (props) => {
  var styles = StyleSheet.create({
    sch:{
      backgroundColor:'#fff',
      padding:10,
      flexDirection:'row',
      justifyContent:'space-between',
      alignItems:'center',
      borderColor:'rgb(240,240,240)',
      borderBottomWidth:0.5,
      borderStyle:'solid'
    },
    left:{
      flexDirection:'row',
      alignItems:'center',
    },
    schIcon:{
      marginRight:10,
    },
    nm:{
      color:'rgb(80,80,80)',
      fontSize:14,
      maxWidth:width - 100,
      fontWeight:'bold'
    },
    addr:{
      color:'rgb(80,80,80)',
      fontSize:12
    },
    tp:{
      marginRight:5,
      color:'#fff',
      // paddingTop:1,
      // paddingBottom:1,
      paddingLeft:3,
      paddingRight:3,
      fontSize:10
    },
    public:{
      backgroundColor: colorTheme['secondary_red'], //'rgb(255,90,90)'
    },
    private:{
      backgroundColor:'rgb(146,104,152)'
    },
    info:{
      marginTop:5,
      color:'rgb(177,177,177)',
      fontSize:12
    },
    right:{
      flexDirection:'row',
      justifyContent:'space-between',
      // width:50
    }
  })
  const {sch,page,closePopup,schoolAction} = props;
  let tp = styles.public;
  let info = '';
  let icon = 'rm-school'//'rmcat-school-pr';
  let schTp = l10n('Public');
  if ((sch.gf == 0) || (sch.gf == -1) || (typeof sch.gf == 'undefined')) {
    sch.gf = 'K'
  }
  let grades = l10n(' Grades')

  //优先判断私立，因为公立的schTp比较多
  if (sch.private) {
    schTp = l10n('Private')
    tp = styles.private;
    const grd = sch.grd || sch.gf;
    if (grd) {
      info = grd + grades;
    } else {
      info = grades+' '+l10n('Unavailable')
    }
  } else {
    if (sch.catholic) {
      schTp = l10n('Catholic')
    }
    if (sch.gt) {
      info = sch.gf + '-' + sch.gt + grades;
    } else {
      info = sch.gf + grades;
    }
  }


  if (sch.fraser && sch.fraser.length > 0) {
    var frs = sch.fraser[0];
    sch.firate = frs.firate
    sch.firank = frs.firank + '/' + frs.fitotal
    if (frs.filast5rank && frs.filast5total){
      sch.filast5rank = frs.filast5rank + '/' + frs.filast5total
    }
    if (sch.filast5rank) {
      info += ' | Rank: ' + sch.filast5rank;
    }
  }
  let rightSection;
  if (page == 'hist') {
    rightSection = <HistRight item={sch} removeHist={props.removeHist} />;
  } else {
    rightSection = (
      <View style={styles.right}>
        <RmIcon size={20} name={'angle-right'} color={'rgb(153,153,153)'} />
        {/* <RmIcon size={20} name={'map-marker'} color={'rgb(130,130,130)'} /> */}
      </View>
    );
  }

  return(
    <TouchableOpacity onPress={()=>{props.search('sch',sch);gotoSch(sch,closePopup,schoolAction)}}>
      <View style={styles.sch}>
        <View style={styles.left}>
          <RmIcon style={styles.schIcon} name={icon} size={20} color={'rgb(130,130,130)'} />
          <View>
            <Text numberOfLines={2} style={styles.nm}>{sch.nm}</Text>
            <View style={styles.left}>
              <Text style={[styles.tp,tp]}>{schTp}</Text>
              <Text style={styles.addr}>{sch.city}</Text>
            </View>
            <Text style={styles.info}>{info}</Text>
          </View>
        </View>
        {rightSection}
      </View>
    </TouchableOpacity>
  );
}
