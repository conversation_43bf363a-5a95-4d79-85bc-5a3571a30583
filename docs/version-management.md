# RealMaster 版本管理使用文档

## 目录

1. 版本号管理概述
2. 版本号结构说明
3. Android 版本管理
4. iOS 版本管理 
5. 环境配置
6. 版本发布流程
7. 最佳实践

## 1. 版本号管理概述

RealMaster App 使用统一的版本管理系统，通过 `appversion.json` 作为主配置文件来管理应用版本。该系统支持 Android 和 iOS 两个平台的版本同步管理。

### 核心配置文件

- `appversion.json`: 主版本配置文件
- `android/app/build.gradle`: Android 构建配置
- iOS Schemes: 
  - `RealMaster-development.xcscheme`
  - `RealMaster-production.xcscheme`
- 环境配置:
  - `.env.development`
  - `.env.production`

## 2. 版本号结构说明

### 2.1 版本号组成

在 `appversion.json` 中定义了三个关键的版本号字段：

```json
{
    "version": "6.6.2",      // 版本号
    "versionCode": 66027,    // Android 内部版本号
    "versionBuild": 2        // iOS 构建版本号
}
```

- `version`: 用户可见的版本号，采用语义化版本号格式 (MAJOR.MINOR.PATCH)
- `versionCode`: Android 平台的内部版本号，用于 Play Store 版本控制
- `versionBuild`: iOS 平台的构建版本号，用于 App Store 版本控制

### 2.2 版本号规则

1. 版本号格式 (version)
   - 主版本号 (MAJOR): 重大功能变更或不兼容的 API 修改时递增
   - 次版本号 (MINOR): 新功能添加但向下兼容时递增
   - 修订号 (PATCH): Bug 修复和小改动时递增

2. Android versionCode 规则
   - 格式：MMNNPB (MM=主版本，NN=次版本，P=修订版本，B=构建号)
   - 示例：66027 = 6.6.2 版本的第 7 次构建

3. iOS Build Number (versionBuild)
   - 每次构建时自动递增
   - 在同一版本号下持续递增
   - 新版本发布时可以选择重置为 1

## 3. Android 版本管理

### 3.1 构建配置

Android 版本管理主要在 `android/app/build.gradle` 文件中配置：

```gradle
def getVersionInfo() {
    def appVersionFile = file("${rootDir}/../appversion.json")
    def versionInfo = new JsonSlurper().parseText(appVersionFile.text)
    return versionInfo
}

android {
    defaultConfig {
        def versionInfo = getVersionInfo()
        versionCode versionInfo.versionCode
        versionName versionInfo.version
    }
}
```

### 3.2 自动版本更新

在 Release 构建时会自动更新版本号：

```gradle
gradle.taskGraph.whenReady { taskGraph ->
    if (taskGraph.hasTask(':app:assembleRelease')) {
        def versionInfo = getVersionInfo()
        def newVersionCode = versionInfo.versionCode
        newVersionCode++
        updateVersionCode(newVersionCode)
    }
}
```

### 3.3 环境配置

Android 构建支持开发和生产两种环境：

```gradle
project.ext.envConfigFiles = [
    debug: ".env.development",
    release: ".env.production"
]
```

### 3.4 版本号查看

在构建过程中会打印版本信息：

```
=== Build Version Info ===
Version Name: 6.6.2
Version Code: 66027
=====================
```

## 4. iOS 版本管理

### 4.1 构建配置

iOS 版本管理通过 Xcode Scheme 和预构建脚本实现自动化管理：

#### 开发环境 (RealMaster-development.xcscheme)

```shell
# 设置环境文件
echo ".env.development" > /tmp/envfile

# 读取版本信息
VERSION=$(grep '"version":' appversion.json | sed 's/.*: "\(.*\)",/\1/')
VERSION_BUILD=$(grep '"versionBuild":' appversion.json | sed 's/.*: \(.*\),*/\1/')

# 更新配置
echo "APP_VERSION=$VERSION" > "ios/tmp.xcconfig"
echo "APP_VERSIONBUILD=$VERSION_BUILD" >> "ios/tmp.xcconfig"
```

#### 生产环境 (RealMaster-production.xcscheme)

```shell
# 设置环境文件
echo ".env.production" > /tmp/envfile

# 读取并更新版本信息
VERSION=$(grep '"version":' appversion.json | sed 's/.*: "\(.*\)",/\1/')
NEW_VERSION_BUILD=$((VERSION_BUILD + 1))

# 更新 appversion.json
sed -i '' "s/\"versionBuild\": $VERSION_BUILD/\"versionBuild\": $NEW_VERSION_BUILD/" appversion.json
```

### 4.2 版本号自动化

iOS 构建过程中的版本号处理：

1. 版本号同步
   - 从 `appversion.json` 读取版本信息
   - 生成临时配置文件 `tmp.xcconfig`
   - 更新 Info.plist 中的版本信息

2. 构建号管理
   - 开发环境：保持当前构建号
   - 生产环境：自动递增构建号
   - 版本号变更时可选择重置构建号

### 4.3 环境切换

通过 Xcode Scheme 实现环境切换：

1. 开发环境 (Development)
   - 使用 `.env.development` 配置
   - 保持当前构建号
   - 用于日常开发和测试

2. 生产环境 (Production)
   - 使用 `.env.production` 配置
   - 自动递增构建号
   - 用于 App Store 发布

### 4.4 版本信息查看

构建过程中会打印版本同步信息：

```
✅ 版本同步成功：
APP_VERSION = 6.6.2
APP_VERSIONBUILD = 3 (原值: 2)
```

## 5. 环境配置

### 5.1 环境文件

项目使用两个环境配置文件：

1. `.env.development` - 开发环境
```
ENV=development
IOS_MAPS_API_KEY=xxx
ANDROID_MAPS_API_KEY=xxx
API_BASE_URL=https://realmaster.com
```

2. `.env.production` - 生产环境
```
ENV=production
IOS_MAPS_API_KEY=xxx
ANDROID_MAPS_API_KEY=xxx
API_BASE_URL=https://realmaster.com
```

### 5.2 环境切换

#### Android
- Debug 构建使用 development 环境
- Release 构建使用 production 环境

#### iOS
- 通过 Scheme 选择环境：
  - `RealMaster-development`
  - `RealMaster-production`

## 6. 版本发布流程

### 6.1 版本号更新

1. 更新 `appversion.json` 中的版本信息：
```json
{
    "version": "x.y.z",      // 新版本号
    "versionCode": nnnnn,    // Android 版本号
    "versionBuild": n        // iOS 构建号
}
```

2. 提交版本更新：
```bash
git add appversion.json
git commit -m "chore: bump version to x.y.z"
```

### 6.2 Android 发布流程

1. 确保环境配置正确
   - 检查 `.env.production` 配置
   - 验证 API 密钥和服务器地址

2. 构建 Release 版本
   ```bash
   cd android
   ./gradlew assembleRelease
   ```

3. 版本号会自动更新
   - `versionCode` 自动递增
   - 更新同步到 `appversion.json`

### 6.3 iOS 发布流程

1. 选择 Production Scheme
   - 在 Xcode 中选择 `RealMaster-production`
   - 确认环境配置正确

2. 构建 Archive
   - Product > Archive
   - 构建号自动递增
   - 版本信息自动同步

3. 提交 App Store
   - 使用 Xcode Organizer
   - 选择最新的 Archive
   - 上传到 App Store Connect

### 6.4 发布后操作

1. 提交版本更新
   ```bash
   git add appversion.json
   git commit -m "chore: update version after release"
   git push
   ```

2. 创建版本标签
   ```bash
   git tag -a v6.6.2 -m "Release version 6.6.2"
   git push origin v6.6.2
   ```

## 7. 最佳实践

### 7.1 版本号管理

1. 遵循语义化版本
   - 主版本号：重大更新
   - 次版本号：新功能
   - 修订号：问题修复

2. 保持版本同步
   - 确保 `appversion.json` 是唯一真实来源
   - 及时提交版本更新

### 7.2 环境管理

1. 开发环境
   - 使用 development 配置
   - 避免修改构建号
   - 用于本地测试

2. 生产环境
   - 使用 production 配置
   - 构建号自动管理
   - 仅用于正式发布

### 7.3 注意事项

1. 版本更新
   - 每次发布前更新版本号
   - 确保版本号递增
   - 提交所有版本相关更改

2. 环境配置
   - 不要混用环境配置
   - 保护生产环境密钥
   - 定期检查配置有效性

3. 发布流程
   - 遵循标准发布流程
   - 创建版本标签
   - 保存构建记录 