/**
* 通知权限获取过程
*/


 应用安装完成

 ~ file: App.jsx:1502 ~ Permissions.checkNotifications ~ status: denied


 LOG  🚀 ~ file: RMWebview.jsx:394 ~ onMessage ~ str: %7B%22tp%22%3A%22pushToken%22%7D
 LOG  🚀 ~ file: App.jsx:460 ~ onMessage ~ body: {"tp":"pushToken"}
 LOG  🚀 ~ file: App.jsx:341 ~ postCallback ~ json: {"tp": "pushToken"}

 //点击setup
  LOG  🚀 ~ file: RMWebview.jsx:394 ~ onMessage ~ str: %7B%22tp%22%3A%22permissions.request.notification%22%2C%22cb%22%3A1733387030856%7D
 LOG  🚀 ~ file: App.jsx:460 ~ onMessage ~ body: {"tp":"permissions.request.notification","cb":1733387030856}
 LOG  🚀 ~ file: App.jsx:341 ~ postCallback ~ json: {"cb": 1733387030856, "tp": "permissions.request.notification"}
   🚀 ~ file: App.jsx:328 ~ return ~ ret: blocked 1733387030856


/**
*定位权限获取过程
*/

