import React, {Component} from 'react';
import {Platform, StyleSheet, Text,View,TouchableHighlight} from 'react-native';
import RmIcon from '../RmIcon'
import { gotoUrl } from './helper';
import {l10n} from '../../utils/i18n';

export default SectionMore = (props) => {
  return (
    <TouchableHighlight onPress={()=>gotoUrl(props.more,props.closePopup)}>
      <View style={styles.container}>
        <Text style={styles.title}>{l10n('See all results')}</Text>
        <RmIcon size={20} name={'angle-right'} color={'rgb(130,130,130)'} />
      </View>
    </TouchableHighlight>
    );
}
const styles = StyleSheet.create({
  container:{
    flexDirection:'row',
    justifyContent:'space-between',
    alignItems:'center',
    padding:10,
    backgroundColor:'#fff'
  },
  title:{
    color:'rgb(80,80,80)',
    fontSize:12,
  }
})
