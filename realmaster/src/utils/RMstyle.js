import appConfigIns from '../config/appConfig';
import cookieIns from './cookies';
import theme from './colors';
// TODO: use rmcolors.js
const PADDING = {
  paddingLeft: 10,
};

// import {RMPost} from './RMNetwork';
const darkRed = '#b30808';

const rmRed = theme.main_red;
const redLightBackground = '#FBEDEB';
const redText = theme.main_red;

const brightGreen = '#2FA800';
// const darkGreen =
const greenLightBackground = theme.secondary_green_3;
const greenText = '#2B9C00';

const darkGrey = '#51514F';
const greyButton = '#525151';

const commonBarColor = rmRed; //
const commonBarText = '#FFF'; //white

const BASE = {
  greyButton: greyButton,
  darkGrey: darkGrey,
  commonBarColor: commonBarColor,
  commonBarText: commonBarText,
  commonMainColor: '#f1f1f1',
  commonMinorColor: 'black',
};
const COLORS_BEFORE = Object.assign({}, BASE, {
  mainTheme: rmRed,
  highlightText: redText,
  highlightBg: redLightBackground,
  commonBarColor: rmRed,
  commonBarText: 'white',
  commonMainColor: 'white',
  commonMinorColor: 'rgb(185,185,185)',
});
const COLORS = Object.assign({}, BASE, {
  mainTheme: rmRed,
  highlightText: redText,
  highlightBg: redLightBackground,
  // 'commonBarColor':
});
// TODO: dark mode
const COLORS_DARK = {
  mainTheme: darkRed,
};
const COLORS_RM = Object.assign({}, BASE, {
  mainTheme: brightGreen,
  highlightText: greenText,
  highlightBg: greenLightBackground,
  // 'commonBarColor':brightGreen
});
async function _getTheme(appmode) {
  if (!appmode) {
    appmode = await cookieIns.getCookieValueByKey(null, 'appmode') || 'mls';
  }


  // console.log('----appmode',appmode)
  let theme = COLORS;
  if (appmode == 'rm') {
    theme = COLORS_RM;
  }
  return theme;
}
async function getColor(component = 'mainTheme', appmode = null) {
  let theme = await _getTheme(appmode);
  let dualHomepage = appConfigIns.getAppConfig('dualHomepage') || false;
  // console.log('dualHomepage=',dualHomepage)
  if (!dualHomepage) {
    theme = COLORS_BEFORE;
  }
  return theme[component] || rmRed;
}
async function setColor({appmode, component, color}) {
  let theme = await _getTheme(appmode);
  theme[component] = color;
  return true;
}
function getColorStatic(component, appmode) {
  let theme = COLORS;
  return theme[component] || rmRed;
}
export {getColor, setColor, getColorStatic};
