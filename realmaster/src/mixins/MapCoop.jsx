/*

*/
import React, {Component} from 'react';
import {
  StyleSheet,
  View,
  Text,
  Linking,
  Image,
  Alert,
  Platform,
  ScrollView,
  TouchableOpacity,
  TouchableHighlight,
  Dimensions,
} from 'react-native';
// import Icon from 'react-native-vector-icons/Ionicons';
import { Icon, eventEmitter } from '../utils/common';
import MapFeature from './MapFeature';
import { Marker } from 'react-native-maps';
import { requestStdFn } from '../utils/request'
import serverDomainIns from '../utils/serverDomain'
import {l10n} from '../utils/i18n';
import BottomPaneWhite from '../components/BottomPaneWhite';
import ListDragPan from './mapListDragPanResponder';

const MAX_TITLE_LEN = 15;

class MapCoop extends MapFeature{
  constructor(map,props,onOff){
    super(map,"MapCoop",props,onOff);
    this.state = {
      coops: [],
      tracksViewChanges:true,
      curCoop:null,
      propFeatureOn: true,
      coopCnt: 0,
    }
    // this.featureOn = true;
    this.needLogin = true;
    this.listDragPan = React.createRef();
    this.needZoomIn = false;
    // console.log('++++props.propFeatureOn',props.propFeatureOn)
    if(props.propFeatureOn != null){
      this.state.propFeatureOn = props.propFeatureOn
    }
    eventEmitter.on("map.props.fetureOn",this.recordPropFeatureOn.bind(this))
    // this.regFeature(feature);
    // eventEmitter.on(SYSTEM.EVENT_GET_HOME_SCHOOL,this.getHomeSchools);
    // eventEmitter.on(SYSTEM.EVENT_SET_FEATURE,this.setFeature);
  }
  recordPropFeatureOn(val){
    // console.log('event',val)
    this.setState({propFeatureOn:val})
  }
  onOffView(){
    // on/off setting to show on map
    return {
      icon:'rm-coophouse',//'rmcat-rentbuy',
      name:l10n('Co-op housing'),
      iconSize:24,
      iconStyle:{marginTop:-1},
      iconListStyle:{marginTop:1},
      toggleOnOff:(p)=>{this.toggleOnOff(p)},on:(this.featureOn && !this.needZoomIn)
    }
  }
  toggleOnOff(onOff){
    if (this.needZoomIn){
      this.flashMessage(l10n('Please zoom in'))
    }
    // NOTE: will change later#57
    if(onOff == 'off'){
      this.featureOn = true;
    }
    if (this.featureOn = !this.featureOn){
      super.toggleOnOff(this.featureOn,this.needLogin)
      this.map.onRegionChangeComplete();
    }else{
      this.setState({curCoop:null})
    }
  }
  regionChanged(event,map){
    //console.log("regionChanged zoom:" + event.zoom);
    super.regionChanged(event,map);
    if (event.zoom < 8){
      if (!this.needZoomIn){
        this.setState({coops:[],curCoop:null});
      }
      this.needZoomIn = true;
      return;
    }
    this.needZoomIn = false;
    if (!this.featureOn) return;
    var ne = [event.bbox[3],event.bbox[2]];
    var sw = [event.bbox[1],event.bbox[0]];

    requestStdFn('getCoop',{ne,sw}).then(ret => {
      if (ret.cnt) {
        // console.log('xxxxxgetCoop',ret.items)
        this.setState({coops:ret.items, coopCnt: ret.cnt || 0});
      } else {
        this.setState({coops:[], coopCnt: 0});
      }
    }).catch(err=> {
      this.flashMessage(err)
      console.log('coop house err: ',err);
      return;
    })
  }
  closeModal(){
    this.setState({curCoop:null});
  }
  openModel(house) {
    return()=>{
      // console.log('+++openModel',house)
      this.closeOtherModals();
      this.setState({curCoop:house});
    }
    return;
  }
  openCoopDetail(house) {
    return;
    return ()=>{
      // TODO: @Rain show school detail webview
      var self = this;
      var url = '/coophouse?id='+house._id;
      var cb = (val)=>{
        // console.log('mapsearch cb val: ',val);
        if (val == ':cancel') {
          // console.log('canceled');
          return;
        }
        if (/^redirect|^cmd-redirect:/.test(val)) {
          // return window.location = val.split('redirect:')[1]
          var url = val.split('redirect:')[1];
          // console.log('close and redirect root: '+url)
          return self.closePopup(url);
          // return window.location = url;
        }
      }
      var opt = {
        hide:false,
        sel:'#callBackString',
        tp:'pageContent',
        title:l10n('RealMaster'),
        // toolbar:false,
        url: serverDomainIns.getFullUrl(url),
      }
      eventEmitter.emit("app.message",{msg:JSON.stringify(opt),cb:cb});
    }
  }
  renderFeatureNoPropDisplay(){
    return null
    let resultCount = (this.state.coops.length||0)
    if(resultCount >= 30){
      resultCount = resultCount+'+'
    }
    return(
      <View key={'schoolRetCount'}
        style={{flexDirection:'row',
        justifyContent:'flex-end',
        marginTop:13,
        flex:10,marginLeft:10,marginRight:10}}>
        <Text style={{color:'#666',fontSize:15,fontWeight:'normal'}}>{resultCount+' '+l10n('Results')} </Text>
        {/* <Icon name="rmplus" size={21} color="black" style={[styles.navBarButton]}   onPress={()=>{this.createStigma()}} /> */}
      </View>
    )
  }
  renderCoopListItem(h={}){
    return (<View style={{padding:10, borderBottomColor:'#f1f1f1',borderBottomWidth:1}}>
      <TouchableOpacity onPress={this.openModel(h)}>
        <Text style={styles.title}>{h.title} </Text>
        <Text style={[styles.ptp,{fontWeight:'200'}]}>{h.addr}</Text>
        <Text style={styles.ptp}>{h.displayContents1}</Text>
      </TouchableOpacity>
    </View>)
  }
  setShowPanel(){
    this.setState({curCoop:null})
  }
  renderModal() {
    if (!this.featureOn) return;
    let curCoop = this.state.curCoop
    if (curCoop){
      return (
        <BottomPaneWhite key={'coopHouseModal'}
          height={250}
          title={(curCoop.title||' ')}
          title1b={(curCoop.addr||' ')}
          cbClose={()=>{
            this.setState({curCoop:null});
          }}>
          <View style={styles.block}>
            {!!curCoop.displayContents1 &&
              <Text style={styles.addr}>{curCoop.displayContents1}</Text>
            }
            {!!curCoop.displayContents2 &&
              <Text style={styles.addr}>{curCoop.displayContents2}</Text>
            }
            {!!curCoop.displayContents3 &&
              <Text style={styles.addr}>{curCoop.displayContents3}</Text>
            }
            {!!curCoop.displayContents4 &&
              <Text style={styles.addr}>{curCoop.displayContents4}</Text>
            }
            {!!curCoop.url &&
              <TouchableOpacity onPress={()=>Linking.openURL(curCoop.url)} style={{marginTop:20}}>
                <Text style={styles.ref}>{l10n('Details')}</Text>
              </TouchableOpacity>
            }
          </View>
        </BottomPaneWhite>);
      // return (
      //   <BottomPane key={'coop'} hideTitleBar={true} statusBar={{hidden:true}}>
      //     <View onPress={this.openCoopDetail(curCoop)}>
      //       <View style={[styles.schoolNameBar]}>
      //         <View style={styles.schoolNameWrapper}>
      //           <Text key={'schSelBnd'} style={styles.schoolName}>{curCoop.title}</Text>
      //         </View>
      //         <TouchableOpacity style={[styles.previewIconWrapper,
      //         {
      //           right:10,
      //           width:32,
      //           padding:4,
      //         }]} onPress={()=>this.setState({curCoop:null})}>
      //           <View style={{}}>
      //             <Icon name="rmclose" size={22} color="#ddd" style={{}}/>
      //           </View>
      //         </TouchableOpacity>
      //         {/* <Icon name="rmclose"
      //           size={20}
      //           color="#bbb"
      //           style={[styles.center, styles.closeButtonBns]}
      //           hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}
      //           onPress={()=>this.setState({curCoop:null})} /> */}
      //       </View>
      //       <View style={styles.block}>
      //         <Text style={styles.addr}>{curCoop.address}</Text>
      //         {/* <Text style={styles.addr}>{curCoop.primary_sponsor}</Text> */}
      //         <Text style={styles.addr}>{curCoop.displayContents1}</Text>
      //         <Text style={styles.addr}>{curCoop.displayContents2}</Text>
      //         <Text style={styles.addr}>{curCoop.displayContents3}</Text>
      //         {/* <Text style={styles.addr}>{curCoop.property_type}</Text> */}
      //         {/* <Text style={styles.addr}>{curCoop.type}</Text> */}
      //         {/* <Text style={styles.addr}>{curCoop.tbl_units}</Text> */}
      //         {/* <Text style={styles.addr}>{curCoop.tbl_client_ele}</Text> */}
      //         {/* <Text style={styles.addr}>{curCoop.how_to_apply}</Text> */}
      //         {/* <Text style={styles.addr}>{curCoop.phone}</Text> */}
      //         {/* <Text style={styles.addr}>{curCoop.article_body}</Text> */}
      //       </View>
      //     </View>
      //   </BottomPane>
      // );
    }
    const {coops} = this.state;
    // console.log(this.state.propFeatureOn)
    if (coops && !this.state.propFeatureOn){//&& coops.length
      let view = [];
      for(let h of coops){
        view.push(this.renderCoopListItem(h))
      }
      let count = coops.length;
      // console.log('state houses:',this.state.houses)
      let base = -40;
      let leftTop = 5;
      // console.log('state houses:',this.state.houses)
      if (Platform.OS != 'ios'){
        leftTop = 3
      }
      let left = (
        <View style={{
          marginTop:base+leftTop,
          zIndex:120,
          paddingLeft:10,paddingBottom:7}}>
          {/* <Text>{sprintf(l10n("%d co-ops found"),count)}</Text> */}
          <Text style={{fontSize:15}}>{count}/{this.state.coopCnt || count} {l10n('Results')}</Text>
        </View>)
      let right = (<TouchableOpacity style={{
        height:40,
        // width:40,
        zIndex:120,
        paddingLeft:3,
        paddingRight:15,
        // paddingTop:3,
        marginTop:base-8,
        paddingBottom:3,
        // backgroundColor:'blue',
        // alignItems:'flex-end'
        }}
        onPress={(e)=>{
          // console.log(this.listDragPan.current)
          this.listDragPan.current.toggleListView(e)
        }}>
        <View>
          <Text style={{marginTop:10,color:'#3899EC', fontSize:16, fontWeight:'bold'}}>{l10n('LIST')}</Text>
        </View>
      </TouchableOpacity>)
      // (
      //   <View style={{paddingLeft:10}}>
      //     {/* <Text style={{fontSize:11,color:'#777'}}>{l10n('Long press on map to set location')}</Text> */}
      //   </View>
      // )
      return (
        <ListDragPan
          ref={this.listDragPan}
          id={'mapCoopList'}
          bottom={0}
          PAN_SHOW_VALUE={110}
          enableLayerMenuControl={true}
          layerMenuMap={this.map}
          layerMenuStyle={{marginTop: 0, marginBottom: 40}}
          renderTopContent={() => (
            <View 
              key="coopTopContent" 
              style={{width:'100%',
                flexDirection:'row',
                justifyContent:'space-between',
                paddingLeft:5,paddingRight:5}}
            >
              {left}
              {right}
            </View>
          )}
          closeListView={()=>{
            this.setState({coops:null});
          }}
        >
          <ScrollView
            showsVerticalScrollIndicator={false}
            style={{
              // paddingLeft:10,
              paddingTop:10,
              // backgroundColor:'lightblue',
              width:'100%'}}>
            {view}
            <View style={{height:100}}></View>
          </ScrollView>
        </ListDragPan>
      )
    }
    return null;
  }
  renderOnMap() {
    if (!this.featureOn) return;
    var view = [];
    const {coops} = this.state;
    const self = this;
    for (let coop of coops) {
      view.push(self.renderMarker(coop));
    }
    return view;
  }
  renderMarker(coop){
    if (!(coop.lat || coop.loc)){
      console.log("Bad coop, can not render:",coop);
    }
    let isSelected = false, zIndex = 10;

    let curCoop = this.state.curCoop;
    if(coop && curCoop && curCoop._id == coop._id){
      isSelected = true;
      zIndex = 11;
    }
    // console.log('xxxxx',isSelected)
    function getShortTitle(c){
      let tmp = coop.sTitle||coop.title||'';
      let end = ''
      if(tmp.length>MAX_TITLE_LEN){
        end = '..'
      }
      return (tmp).slice(0,MAX_TITLE_LEN)+end
    }
    let loc = {longitude:(coop.lng || coop.loc[1]),latitude:(coop.lat || coop.loc[0])};
    return (
      <Marker
        stopPropagation={true}
        tracksViewChanges={ this.state.tracksViewChanges }
        key={coop._id}
        style={{zIndex}}
        onPress={this.openModel(coop)}
        coordinate={loc}
      >
        {/* <IconText
          icon={schoolOrange0}
          text={coop.title}
          borderColor={'#4688f2'}
          backgroundColor={'#4688f2'}
        /> */}
        <View style={[styles.stigmatizedMarker,{backgroundColor:isSelected?'#003fcc':'#4688f2'}]}>
        </View>
        {/* <View style={styles.coopMarker}>
          <Text style={styles.coopMarkerText}>{getShortTitle(coop)}</Text>
        </View> */}
      </Marker>
    );
  }


  unmount(){
    super.unmount();
    eventEmitter.removeListener('map.props.fetureOn',this.recordPropFeatureOn);
    // eventEmitter.removeListener(SYSTEM.EVENT_GET_HOME_SCHOOL,this.getHomeSchools);
    // eventEmitter.removeListener(SYSTEM.EVENT_SET_FEATURE,this.setFeature);
  }

}

const styles = StyleSheet.create({
  stigmatizedMarker:{
    backgroundColor:'#000',
    // paddingTop: 3,
    // paddingBottom: 3,
    // paddingRight: 8,
    // paddingLeft: 8,
    // borderRadius:11,
    zIndex:10,
    height: 19,
    width: 19,
    padding:3,
    borderRadius:18,
    borderWidth: 2.5,
    borderColor: 'white',
  },
  marker:{
    width: 24,
    height: 24,
    zIndex: 1,
  },
  previewIconBg:{
    backgroundColor:'rgba(0,0,0,0.7)',
    width:24,
    height:24,
    borderRadius:12,
    alignItems:'center',
    justifyContent:'center',
  },
  previewIconWrapper:{
    position:'absolute',
    top:7,
    zIndex:10,
    alignItems:'center',
    justifyContent:'center',
    height:32,
    flexDirection:'row',
  },
  bottomPaneWrapper:{
    width:'100%',
    position: 'absolute',
    bottom: 0,
    backgroundColor: 'white',
    left: 0,
    right: 0,
    zIndex: 20,
    height: 260,
  },
  ref:{
    color:'rgb(66, 139, 202)',
    fontSize:15,
  },
  block:{
    // borderTopColor:'#ddd',
    // borderTopWidth:1,
    padding:10,
    marginTop:7,
    fontSize:14
  },
  addr:{
    // marginBottom:5
    fontSize:14,
  },
  ptp:{
    color:'rgb(119,119,119)',
    fontSize:14,
    paddingTop:7,
    // paddingBottom:3,
  },
  title:{
    fontSize:16,
    fontWeight:'bold',
  },
  closeButtonBns:{
    position: 'absolute',
    top:10,
    right:10,
    width:24,
    height:24,
  },
  center:{
    justifyContent:'center',
    alignItems:'center',
    alignSelf:'center',
  },
  schoolNameBar:{
    // backgroundColor:'#e03131',
    // alignItems:'center',
    // justifyContent:'flex-start',
    // height:88,
  },
  schoolName:{
    color:'black',
    fontSize:17,
    // backgroundColor:'red',
    fontWeight:'bold',
    // alignSelf:'center',
    alignItems:'center',
    // height:44,
  },
  schoolNameWrapper:{
    flexDirection:'row',
    // backgroundColor:'blue',
    // height:44,
    paddingTop:10,
    alignItems:'center',
    justifyContent:'flex-start',
    paddingLeft:10
  },
  coopMarker:{
    backgroundColor:'#4688f2',
    paddingTop: 3,
    paddingBottom: 3,
    paddingRight: 8,
    paddingLeft: 8,
    borderRadius:11,
    borderWidth: 1,
    borderColor: '#245bd6',
    zIndex:10,
  },
  coopMarkerText:{
    color:'#fff',
    fontSize:11
  }
});

export default MapCoop;
