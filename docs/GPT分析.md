以下是根据你的需求整理的完整 Markdown 文档，适用于项目文档或团队协作说明：

⸻

🧩 React Native 学校卡片组件设计文档

本设计文档说明了如何将学校信息卡片拆分为通用组件模块，并提供了组件结构、职责、样式建议和示例 Props。

⸻

📦 组件结构总览

<SchoolCard>
├── <HeaderSection>
│   ├── <SchoolName />
│   ├── <SchoolAddress />
│   └── <TagList>
│       ├── <Tag /> (可复用)
│
├── <KeyFactsSection>
│   ├── <KeyFactItem />
│   ├── <KeyFactItem />
│   └── <KeyFactItem />


⸻

1. <SchoolCard /> — 主卡片组件
	•	职责：封装所有子组件，控制卡片整体样式。
	•	样式：圆角、白底、阴影、内边距。
	•	Props 示例：

<SchoolCard
  name="Oriole Park Junior Public School"
  address="80 Braemar Ave, Toronto, ON"
  tags={['Elementary', 'English']}
  keyFacts={[
    { title: 'AI Ranking/G3', rank: '15/2994', score: 9.5, isDown: true },
    { title: 'AI Ranking/G6', rank: '412/2819', score: 9.5, isDown: true },
    { title: 'Grade', score: 'K–8' }
  ]}
/>


⸻

2. <HeaderSection /> — 卡片头部信息
	•	包含：
	•	<SchoolName />：学校名称
	•	<SchoolAddress />：地址
	•	<TagList />：标签列表

⸻

🏷 <Tag /> — 标签组件（可复用）
	•	职责：展示如“Elementary”、“English”这样的标签。
	•	样式：圆角背景色 + 小字体。
	•	Props 示例：

<Tag text="Elementary" />
<Tag text="English" />


⸻

3. <KeyFactsSection /> — 关键信息展示区
	•	职责：横向排列多个 <KeyFactItem />
	•	布局：flex-row 均分布局

⸻

📊 <KeyFactItem /> — 单项关键信息项
	•	展示内容：
	•	排名（如 15/2994）
	•	上/下降箭头（lucide-react-native 图标）
	•	分数（如 9.5）
	•	标题（如“AI Ranking/G3”）
	•	Props 示例：

<KeyFactItem title="AI Ranking/G3" rank="15/2994" score={9.5} isDown />
<KeyFactItem title="Grade" score="K–8" />


⸻

🎨 样式建议（Tailwind 风格）
	•	所有组件建议使用 Tailwind CSS-in-JS 库（如 tailwind-rn 或 nativewind）
	•	主卡片使用：
	•	rounded-2xl
	•	shadow
	•	bg-white
	•	p-4
	•	子组件中使用：
	•	text-lg font-bold（标题）
	•	text-sm text-gray-500（辅助信息）
	•	bg-green-100 text-green-800（标签）

⸻

✅ 可扩展点
	•	加入 <InfoTooltip /> 用于解释 AI Ranking 等字段
	•	支持点击 <KeyFactItem /> 跳转详情页
	•	响应式布局支持（适配不同屏幕尺寸）

⸻

如需项目初始化代码或使用演示，欢迎继续提出！我也可以帮你生成 Storybook 文档或测试数据。