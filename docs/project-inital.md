# Project Inital

[toc]

## ssh authentication and clone
根据 https://docs.github.com/en/authentication 说明，确保本地ssh key在github已生效（命令行测试显示successful）。

`<NAME_EMAIL>:real-rm/rmRN.git` 到本地

## 检查本地react native环境
### iOS
#### 1. Xcode安装与配置

App Store安装Xcode。

打开Xcode， **Settings > Locations** Command Line Tools 选择最新的版本。

在 **Settings > Accounts** 中设置属于开发团队的Apple ID。

#### 2. 安装 macOS包管理工具homebrew
```bash
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
```
 安装完成后，将brew的环境变量加入`.zshrc`

 ```bash
 export PATH=/opt/homebrew/bin:$PATH
 ```

> ps: 如果使用翻墙代理后，google可以在浏览器访问，但是在terminal中，`curl https://www.google.com/`没有反应。此时应将代理命令行copy到`.zshrc`中。否则在terminal中拉任何外网资源都将timeout。

#### 3.  安装包管理工具nvm及node

```bash
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.40.1/install.sh | bash
```
然后将
```bash
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"  # This loads nvm
```
添加到 `.zshrc`。

重新打开terminal，使用`nvm --version`查看是否安装成功。成功后，安装最新的LTS版本的node.
```bash
nvm install 22.11.0
```
#### 4. 升级ruby，安装cocoapods，安装watchman
```bash
brew install ruby
brew install cocoapods
brew install watchman
```

### Android
#### 1. 安装Android Studio
https://developer.android.google.cn/studio 下载页面下载，国内用户不使用代理更快一些。

安装完打开后，确认以下内容被勾选，并下一步

- Android SDK
- Android SDK Platform
- Android Virtual Device

为了保证后续Android Studio网络资源的请求，确保打开系统代理，并在 **Settings > Apperance & Behavior > System Settings > HTTP Proxy** 中 `Auto-detect proxy settings` 是选中状态。

点击下方 **Check connection** 输入 https://www.google.com/ 测试successful。


#### 2. 按照iOS的说明，安装nvm, node, homebrew, watchman
```bash
brew install watchman
```
#### 3. 安装JDK
```bash
brew install --cask zulu@17

# Get path to where cask was installed to find the JDK installer
brew info --cask zulu@17

# ==> zulu@17: <version number>
# https://www.azul.com/downloads/
# Installed
# /opt/homebrew/Caskroom/zulu@17/<version number> (185.8MB) (note that the path is /usr/local/Caskroom on non-Apple Silicon Macs)
# Installed using the formulae.brew.sh API on 2024-06-06 at 10:00:00

# Navigate to the folder
finder /opt/homebrew/Caskroom/zulu@17/<version number> # or /usr/local/Caskroom/zulu@17/<version number>
```
打开后双击安装JDK 17.pkg
在`.zshrc`中更新`JAVA_HOME`环境变量
```bash
export JAVA_HOME=/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home
```
#### 4. 配置Android SDK和ANDROID_HOME环境变量
**Android Studio > Settings > Language & Frameworks > Android SDK** 选择 **SDK Platforms** 的tab，并勾选右下角的 **Show Package Details** 复选框。选项框中选择以下选项：
- Android SDK Platform 34
- Google APIs ARM 64 v8a System Image

切换到 **SDK Tools** 确认 `34.0.0` 也已经被被选中，就可以点击 **Apply** 完成了。

将以下内容，添加到 `.zshrc` 中，完成 `ANDROID_HOME` 环境变量的配置。

```bash
export ANDROID_HOME=$HOME/Library/Android/sdk
export PATH=$PATH:$ANDROID_HOME/emulator
export PATH=$PATH:$ANDROID_HOME/platform-tools
```

### 检查环境

检查环境配置，征程配置完的`.zshrc`中应该有如下内容：
```bash
#nvm设置
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"  # This loads nvm

#homebrew设置
export PATH=/opt/homebrew/bin:$PATH

#JDK设置
export JAVA_HOME=/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home

#Android设置
export ANDROID_HOME=$HOME/Library/Android/sdk
export PATH=$PATH:$ANDROID_HOME/emulator
export PATH=$PATH:$ANDROID_HOME/platform-tools

#terminal proxy设置
export http_proxy=http://127.0.0.1:1087;export https_proxy=http://127.0.0.1:1087;export ALL_PROXY=socks5://127.0.0.1:1080
```

## 项目运行

### iOS

#### 1. 安装依赖

```bash
cd rmRN/realmaster/ios
pod install
```

#### 2. 在根目录运行命令行

```bash
npm run ios
```

如果出现app安装完，打不开，回退到主屏幕，并且报错信息为：

`SBMainWorkspace - The request was denied by service delegate`

需要在terminal中执行以下命令

```bash
softwareupdate --install-rosetta
```



### Android

#### 1. 在根目录运行命令

```bash
npm run android
```

正常就可以安装App了。

涉及到Android的打包，可以使用编辑器也可以使用命令行。

使用编辑器的情况下，需要按照下面的步骤2，将项目同步到编辑器，并且没有报错。

#### 2. 在Android Studio中打开android文件夹

正常等待编辑器同步依赖完成即可。后续可进行运行和打包操作。不使用编辑器的情况下，命令行也是一样的。

如果同步后出现以下报错：

`A problem occurred starting process 'command 'node''`

则需要在 **Settings > Build. Execution. Deployment > Build Tools > Gradle** 目录里，找到 **Gradle JDK** ，将**GRADLE_LOCAL_JAVA_HOME** 更换为 **zulu-17** 应用确认后，重新同步。

