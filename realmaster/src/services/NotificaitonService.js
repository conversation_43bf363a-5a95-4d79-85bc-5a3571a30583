import { Alert, Platform } from 'react-native';
import { Notifications } from 'react-native-notifications';
import { mainRequest } from '../utils/request';
import storageIns from '../utils/storage';
import Constants from '../config/constants';

class NotificationService {
  constructor() {
    this.initialized = false;
    this.handlePnToken = () => { };
    this.handleNotify = () => { };
    this.eventListeners = [];
  }

  //初始化远程通知
  async initial({ handlePnToken, handleNotify }) {
    if (this.initialized) return;

    // 移除之前的事件监听
    this.removeEvents();

    // 注册事件监听
    this.registerEvents();
    //double check, 注册成功一次，
    const hasPermissions = await Notifications.isRegisteredForRemoteNotifications();
    global.rmLog(`NotificaitonService.js:24~~~hasPermissions`, hasPermissions);
    if (hasPermissions) {
      this.initialized = true;
    }

    this.handlePnToken = handlePnToken;
    this.handleNotify = handleNotify;

    // 注册远程通知
    Notifications.registerRemoteNotifications();
    //注册安卓channel
    this.registerAndroidChannel();
    //是否是从通知冷启动
    this.getInitialNotify()



    global.rmLog(`NotificaitonService.js:41~~~postLocalNotification`, 'postLocalNotification');

  }

  removeEvents() {
    this.eventListeners.forEach(remove => remove());
    this.eventListeners = [];
  }

  registerEvents() {
    // 前台收到通知
    const foregroundListener = Notifications.events().registerNotificationReceivedForeground((notification, completion) => {
      console.log('Notification Received - Foreground', notification);
      notification.notiType = 'foreground'
      this.handleNotify(notification);
      completion({ alert: true, sound: false, badge: false });
    });
    this.eventListeners.push(() => foregroundListener.remove());

    // 后台收到通知但未点击
    const backgroundListener = Notifications.events().registerNotificationReceivedBackground((notification, completion) => {
      console.log('Notification Received - Background', notification);
      notification.notiType = 'background'
      this.handleNotify(notification);
      completion({ alert: true, sound: true, badge: false });
    });
    this.eventListeners.push(() => backgroundListener.remove());

    // 用户点击通知
    const openedListener = Notifications.events().registerNotificationOpened((notification, completion) => {
      console.log('Notification Opened', notification);
      notification.notiType = 'open'
      this.handleNotify(notification);
      completion();
    });
    this.eventListeners.push(() => openedListener.remove());

    // 注册成功时获取设备 Token
    const registeredListener = Notifications.events().registerRemoteNotificationsRegistered(event => {
      global.rmLog(`[NotificaitonService.js:92~deviceToken]`, event.deviceToken);
      this.handlePnToken(event.deviceToken);
    });
    this.eventListeners.push(() => registeredListener.remove());

    // 注册失败处理
    const registrationFailedListener = Notifications.events().registerRemoteNotificationsRegistrationFailed(event => {
      console.error('Remote Notification Registration Failed:', event);
    });
    this.eventListeners.push(() => registrationFailedListener.remove());
  }

  async getInitialNotify() {
    const res = await Notifications.getInitialNotification();
    if (res) {
      res.notiType = 'initial'
      this.handleNotify(res)
    }
  }



  registerAndroidChannel() {
    if (Platform.OS === 'android') {
      Notifications.setNotificationChannel({
        channelId: 'RealMaster-android-pn-channel-id',
        name: 'RealMaster channel',
        importance: 5,
        description: 'RealMaster channel for prop update and news',
        enableLights: true,
        enableVibration: true,
        showBadge: false,
        // soundFile: 'custom_sound.mp3',  // place this in <project_root>/android/app/src/main/res/raw/custom_sound.mp3
        vibrationPattern: [200, 1000, 500, 1000, 500],
      });
    }
  }

  async isRegisterRemoteNotifications() {
    const hasPermissions = await Notifications.isRegisteredForRemoteNotifications();
    return hasPermissions;
  }

  async handleBadge() {
    if (Platform.OS === 'android') return;
    const notifications = await Notifications.ios.getDeliveredNotifications();
    const badgeCount = notifications[0].length || 0;
    Notifications.ios.setBadgeCount(badgeCount)
    // 同步到服务器 - 确保服务器知道实际的角标数量
    this.syncBadgeToServer(badgeCount);
  }

  // 将角标数同步到服务器
  async syncBadgeToServer(badgeCount) {
    try {
      const pnTokenData = await storageIns.getItem(Constants.PnToken)
      const token = JSON.parse(pnTokenData)?.token || ''
      const data = {
        cnt: badgeCount,
        pnToken: token,
      }
      global.rmLog(`NotificaitonService.js:138~~~data`, data);
      await mainRequest({
        url: '/1.5/settings/badge',
        method: 'POST',
        data
      });
      global.rmLog(`NotificaitonService.js:134~~~syncBadgeToServer`, badgeCount);
    } catch (error) {
      global.rmLog(`NotificaitonService.js:141~~~syncBadgeToServer`, error, 'error');
    }
  }
}

export default new NotificationService();
